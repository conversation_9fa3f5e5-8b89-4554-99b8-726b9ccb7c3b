Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:07:08Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker4.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23440]  Target information:

Player connection [23440]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4174416662 [EditorId] 4174416662 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23440] Host joined multi-casting on [***********:54997]...
Player connection [23440] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 10.39 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.02 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56608
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002621 seconds.
- Loaded All Assemblies, in  0.689 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.639 seconds
Domain Reload Profiling: 1327ms
	BeginReloadAssembly (249ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (271ms)
		LoadAssemblies (247ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (267ms)
			TypeCache.Refresh (264ms)
				TypeCache.ScanAssembly (241ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (639ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (574ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.263 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.86 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.538 seconds
Domain Reload Profiling: 2797ms
	BeginReloadAssembly (264ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (813ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (468ms)
			TypeCache.Refresh (332ms)
				TypeCache.ScanAssembly (309ms)
			BuildScriptInfoCaches (104ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1538ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1196ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (223ms)
			ProcessInitializeOnLoadAttributes (846ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 4.92 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.5 MB). Loaded Objects now: 8334.
Memory consumption went from 208.5 MB to 199.0 MB.
Total: 15.630000 ms (FindLiveObjects: 0.963300 ms CreateObjectMapping: 0.934200 ms MarkObjects: 7.671600 ms  DeleteObjects: 6.058600 ms)

========================================================================
Received Import Request.
  Time since last request: 1812045.402334 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation
  artifactKey: Guid(a1b6eec16f5715c44b688a2777b9be10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation using Guid(a1b6eec16f5715c44b688a2777b9be10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2e314e8655c04dcc6fb85700153dc0b') in 0.0376101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.647627 seconds.
  path: Assets/Hivemind/MedievalKingdom/Polycount.asset
  artifactKey: Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/Polycount.asset using Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0e27c83c339e52bc44cd607f134194f') in 0.0322006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.042464 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)
  artifactKey: Guid(2edbad42fd3335f45abbfbfd14e1c853) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default) using Guid(2edbad42fd3335f45abbfbfd14e1c853) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f72d0f520dae53f0e42c41a4e0bfc22e') in 0.000829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.753576 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art
  artifactKey: Guid(e80132e5ae384bc48b05e407217cb93c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art using Guid(e80132e5ae384bc48b05e407217cb93c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c532639e75d89b686481be4f822b3b50') in 0.0007513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.133512 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials
  artifactKey: Guid(1a1484eece4fb30459cb74c53de03b2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials using Guid(1a1484eece4fb30459cb74c53de03b2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44087e66d996f4b6a93b7750c801d7e0') in 0.0008313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.031093 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BasicTextured.mat
  artifactKey: Guid(abc00000000000403124660741396823) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BasicTextured.mat using Guid(abc00000000000403124660741396823) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b520d5324e5cf079f40e707787d8d04') in 0.9619102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 3.567946 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fruit.mat
  artifactKey: Guid(abc00000000017826112794728260833) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fruit.mat using Guid(abc00000000017826112794728260833) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '693d94e564f084e6ad27fe0b709b8362') in 0.0725292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_InteriorCups.mat
  artifactKey: Guid(abc00000000002684236441138652053) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_InteriorCups.mat using Guid(abc00000000002684236441138652053) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d18ce0526c99b891f7d969872d94dc7') in 0.0899252 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.025273 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Moss.mat
  artifactKey: Guid(abc00000000013168663486102302862) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Moss.mat using Guid(abc00000000013168663486102302862) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd41427e4497bc40e349ad9e09efe2ff7') in 0.054925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_01.mat
  artifactKey: Guid(abc00000000009361911379653530679) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_01.mat using Guid(abc00000000009361911379653530679) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '242b4035d0be46b6589dd6b38dba149d') in 0.1141114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_02.mat
  artifactKey: Guid(abc00000000001475646603878286191) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_02.mat using Guid(abc00000000001475646603878286191) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b2e843b87d1de58c87882fc5b035d78a') in 0.0504733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_01.mat
  artifactKey: Guid(abc00000000009433890496998591782) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_01.mat using Guid(abc00000000009433890496998591782) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f51085c15bffa843d8ea5047ad223924') in 0.0754311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 1.409886 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit
  artifactKey: Guid(3ee0f3f1367da984bb269c838fc003f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit using Guid(3ee0f3f1367da984bb269c838fc003f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '884dd14f91fa66a948c688ff892e444d') in 0.0009984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.065106 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_01.mat
  artifactKey: Guid(abc00000000004818322303702504829) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_01.mat using Guid(abc00000000004818322303702504829) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98342fd45aa90e271c5c2deaa336a470') in 0.0370892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_05.mat
  artifactKey: Guid(abc00000000001298615087021295057) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_05.mat using Guid(abc00000000001298615087021295057) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b78470c55a1ca6ccf83a04a01509cc0') in 0.0484721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Fish_Splash_02.mat
  artifactKey: Guid(20492c95d4599c045b9e73ead39d559a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Fish_Splash_02.mat using Guid(20492c95d4599c045b9e73ead39d559a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '08b6f662f727a09099ebc60dea9b4e7c') in 0.0202803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 7.063718 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes
  artifactKey: Guid(bd97be27d18f5a64a9ec2625ccb9f742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes using Guid(bd97be27d18f5a64a9ec2625ccb9f742) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f6bc67ee146cb7ee78dde50bdf05071') in 0.0035496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.602878 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity
  artifactKey: Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity using Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94454b5bf7418bcef0de4cddc7d5a6d1') in 0.0306967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.030088 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown
  artifactKey: Guid(08dbd4b5a993b2846b3e5f4c13f12ada) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown using Guid(08dbd4b5a993b2846b3e5f4c13f12ada) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bab640f06cd0e25d34f3aa3afff2c53') in 0.0307144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.190802 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown
  artifactKey: Guid(4c4c63d7699f99142abfeaff60923b4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown using Guid(4c4c63d7699f99142abfeaff60923b4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '64f4c8e7619ef1125a45d8eba66d1470') in 0.0008065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.007574 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown/ReflectionProbe-0.exr
  artifactKey: Guid(f67cf6813d1666842920ea9d91e1c865) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown/ReflectionProbe-0.exr using Guid(f67cf6813d1666842920ea9d91e1c865) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6a86c3e0fa21f42f4ed74257cd4f7798') in 0.0788247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 95.504225 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '29d1f95e5409a945946d90d6d7610da9') in 0.6167738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000149 seconds.
  path: Assets/Settings/Mobile_RPAsset.asset
  artifactKey: Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/Mobile_RPAsset.asset using Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '63fe39a7d98ad387641f5cb04d7d2a0d') in 0.1230489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Maps/Scene.unity
  artifactKey: Guid(741b12c7941492e4394818ab3666f864) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Maps/Scene.unity using Guid(741b12c7941492e4394818ab3666f864) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2fa5f8d0e7f94fc73cdcbb4932ccf81a') in 0.0647816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN Variant.prefab
  artifactKey: Guid(e274568655e6f6b4cb8304cca3d20fdb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN Variant.prefab using Guid(e274568655e6f6b4cb8304cca3d20fdb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '60dcc7c4d8adfef3dfe6d241585eb874') in 0.5424119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 738

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_03.png
  artifactKey: Guid(e412244f295efd147a7aafc75fa68a0f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_03.png using Guid(e412244f295efd147a7aafc75fa68a0f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00d5da58324bf3fc56e8449d07c1e0f7') in 0.2305694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_02.mat
  artifactKey: Guid(fc6b014236a0aec47a3d99b1a5a21d51) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_02.mat using Guid(fc6b014236a0aec47a3d99b1a5a21d51) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10132666f2e9391f6121d1396552e031') in 0.4480774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_katana_Blade.FBX
  artifactKey: Guid(bf3739b4d53347c4ab88bad4e567aafa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_katana_Blade.FBX using Guid(bf3739b4d53347c4ab88bad4e567aafa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12066387a8cd6eb1a2ca464475ab7f5d') in 0.220942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000150 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity
  artifactKey: Guid(afd6880a243c0854882e6dc680d01b5c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity using Guid(afd6880a243c0854882e6dc680d01b5c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity additively'
Loaded scene 'Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity'
	Deserialize:            121.232 ms
	Integration:            977.161 ms
	Integration of assets:  2.521 ms
	Thread Wait Time:       0.364 ms
	Total Operation Time:   1101.278 ms
 -> (artifact id: 'c3374a6d5fefa765db1ae2539328f566') in 1.3714414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 926

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/playercontroller/Player.prefab
  artifactKey: Guid(04e8e2d0aed9b5143b616c0fa3bbf573) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/playercontroller/Player.prefab using Guid(04e8e2d0aed9b5143b616c0fa3bbf573) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd3aa580fe11d4290fdd469301dd272c8') in 0.2618175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 380

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_04.png
  artifactKey: Guid(99f3dd27e222a1f48ba528282bf27206) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_04.png using Guid(99f3dd27e222a1f48ba528282bf27206) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb6051fe1bac4bfd66dbe3daaea336fb') in 0.1159058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_01.prefab
  artifactKey: Guid(e7735de37e7a34e4f9b3862554646a69) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_01.prefab using Guid(e7735de37e7a34e4f9b3862554646a69) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '091630accd177ba3fc8558e69c8575b2') in 0.1394389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/Empty/Settings/HDRP Performant.asset
  artifactKey: Guid(168a2336534e4e043b2a210b6f8d379a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRP Performant.asset using Guid(168a2336534e4e043b2a210b6f8d379a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '101826a284ab1ab4bbcb3a5fd6be8128') in 0.0821574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab
  artifactKey: Guid(32d5f0cabfc0be846a4c217337d74479) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab using Guid(32d5f0cabfc0be846a4c217337d74479) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f3a6880e18642892a462910a71fc6df2') in 0.0692698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/IdaFaber/Demo/S_CameraController.cs
  artifactKey: Guid(1c5821ddd17ff5f4f993f1367a940b2a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/S_CameraController.cs using Guid(1c5821ddd17ff5f4f993f1367a940b2a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f7a3a148b6fa8864d4d9632f3287075') in 0.0285586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Demo/TC_Belfast_Sunset_2k.hdr
  artifactKey: Guid(2148792b71521ee4ba113babdc9849eb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/TC_Belfast_Sunset_2k.hdr using Guid(2148792b71521ee4ba113babdc9849eb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35dadcebf7fd9bfa7f63d700c24be2d4') in 0.0921301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Impact_01.prefab
  artifactKey: Guid(8b4234cca2becc64093145bb8df399ad) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Impact_01.prefab using Guid(8b4234cca2becc64093145bb8df399ad) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '17eca73d8a1425b9e634da7d4a3946c6') in 0.109502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Variant.prefab
  artifactKey: Guid(4831039ab58ea614ca7b4f076945ccc1) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Variant.prefab using Guid(4831039ab58ea614ca7b4f076945ccc1) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '508320c2e91b5ae1b27caf3b0a7be3a4') in 0.2325857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 772

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Scripts/Debug/HealthSystemTest.cs
  artifactKey: Guid(b7f4dbdaf9e54b646bb1351d8faacef2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Debug/HealthSystemTest.cs using Guid(b7f4dbdaf9e54b646bb1351d8faacef2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67c794c880fbcbe0e48d7785f2695932') in 0.0595361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Scripts/Controllers/PlayerMovementController.cs
  artifactKey: Guid(ebc85ea1e98575d47bd637c4ceaa4c0a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/PlayerMovementController.cs using Guid(ebc85ea1e98575d47bd637c4ceaa4c0a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e38fadbb69a82f2d83adda63393854c') in 0.0550018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.001124 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab
  artifactKey: Guid(acc85f946427de74fa6e3c695337c836) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab using Guid(acc85f946427de74fa6e3c695337c836) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8cefc808f795ff2d138d9d4ca0c3e01') in 0.0687487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 146

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_Big_Sword.FBX
  artifactKey: Guid(0466dd790c0cfd34db4afe171eadd2a6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_Big_Sword.FBX using Guid(0466dd790c0cfd34db4afe171eadd2a6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '563c4a063e375aed5efaa285af12a480') in 0.110533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/FoliageDiffusionProfile.asset
  artifactKey: Guid(879ffae44eefa4412bb327928f1a96dd) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/FoliageDiffusionProfile.asset using Guid(879ffae44eefa4412bb327928f1a96dd) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dbb5720a754c92c3a39f810141125602') in 0.0661781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/FoliageDiffusionProfile.asset
  artifactKey: Guid(e0df971ea161c4e4c9187e6b7b2f5dc9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/FoliageDiffusionProfile.asset using Guid(e0df971ea161c4e4c9187e6b7b2f5dc9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fa51cdc6fe1db1a7b69007ef2da64726') in 0.0389345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_RainExample.prefab
  artifactKey: Guid(ca286ace35093d04195fc5e017ba52c0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_RainExample.prefab using Guid(ca286ace35093d04195fc5e017ba52c0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '086570adb6a4a9068c61ff6ad0150158') in 0.1720775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 47

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity
  artifactKey: Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity using Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity additively'
Loaded scene 'Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity'
	Deserialize:            63.263 ms
	Integration:            50.697 ms
	Integration of assets:  0.435 ms
	Thread Wait Time:       0.008 ms
	Total Operation Time:   114.404 ms
 -> (artifact id: '43c2fe5a9fb33a61183f38fbc1e87692') in 0.1949533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterAnimated.prefab
  artifactKey: Guid(8714b7f0b38d36540b1b8396757bea0c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterAnimated.prefab using Guid(8714b7f0b38d36540b1b8396757bea0c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '87d189dad467bf48794f29cf52621e6a') in 0.0639846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 145

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SlashBasic_01.prefab
  artifactKey: Guid(4d1f34ac84d359d449c6290cc68baccb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SlashBasic_01.prefab using Guid(4d1f34ac84d359d449c6290cc68baccb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c2bffcda25a5aa3ef92a6bf9e0aadd19') in 0.3106415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_no_weapon.FBX
  artifactKey: Guid(d3ffbb53d382790469e6c8f926efe5f6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_no_weapon.FBX using Guid(d3ffbb53d382790469e6c8f926efe5f6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25e7cbd1febb8708baf87f7d5d64c974') in 0.139346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 148

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Demo/TC_Belfast_Sunset_8k.hdr
  artifactKey: Guid(ffee37df5f8612a45b5277ef595096a6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/TC_Belfast_Sunset_8k.hdr using Guid(ffee37df5f8612a45b5277ef595096a6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90afbcf7269445d07cc318582b46ddc3') in 0.1632457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Scripts/Debug/SimpleMovementTest.cs
  artifactKey: Guid(7618adb5e52761941a8eb383b70f8f5c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Debug/SimpleMovementTest.cs using Guid(7618adb5e52761941a8eb383b70f8f5c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9eff30891edd741df17c6f9ad4632133') in 0.0355179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab
  artifactKey: Guid(958cf4748c84fb948bc3400edc8005e9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab using Guid(958cf4748c84fb948bc3400edc8005e9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a039ad52c6b1d654a536b91b6c980ae3') in 0.1382171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_Normal.png
  artifactKey: Guid(2731a7a972bc4e246a270a6dacd23e7c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_Normal.png using Guid(2731a7a972bc4e246a270a6dacd23e7c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58040199284666a7edc6e573bf3ca273') in 0.1459777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_MaskMap.png
  artifactKey: Guid(f1e1e1bf386e3a1408983c93fed8e3be) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_MaskMap.png using Guid(f1e1e1bf386e3a1408983c93fed8e3be) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '162c8723374f779b868fcc33a79c0032') in 0.1117778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Demo/A_Idle_F.fbx
  artifactKey: Guid(b6e9290615533534f971471dc3e18523) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/A_Idle_F.fbx using Guid(b6e9290615533534f971471dc3e18523) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '18ac05334e690fc1ddad8ffc4b64beab') in 0.1022676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 351

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Impact.prefab
  artifactKey: Guid(02d97bb2a8216d0468d61f9a3a291bac) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Impact.prefab using Guid(02d97bb2a8216d0468d61f9a3a291bac) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5d68362cd63103f9a12412c2c69f137a') in 0.2853688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 108

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01_Dirty.mat
  artifactKey: Guid(4c214d70a511d8848a68e4dc18719299) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01_Dirty.mat using Guid(4c214d70a511d8848a68e4dc18719299) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '345d7363d22a611344df84c643bf6cd2') in 0.1931209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab
  artifactKey: Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab using Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f62650f07e7b615cde20bf88f846fe17') in 0.0515286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 158

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovablePlatform_Plank.prefab
  artifactKey: Guid(f47091266aceec646a411325962aaaec) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovablePlatform_Plank.prefab using Guid(f47091266aceec646a411325962aaaec) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3ea891229e091c149c2c9f8a356fd64f') in 0.0570334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_Include_Katana.FBX
  artifactKey: Guid(78619ea0e9e2ea84f9d02ae4880f9382) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_Include_Katana.FBX using Guid(78619ea0e9e2ea84f9d02ae4880f9382) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b4c84c83181b9b68dd9e6e660720fbd') in 0.251187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 154

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab
  artifactKey: Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab using Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '41cbd687e480ea17f4b2fdcdd6b33a9b') in 0.1546961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab
  artifactKey: Guid(83cdd184a3eace340b4dedb44e652290) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab using Guid(83cdd184a3eace340b4dedb44e652290) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4877020b539db9d8e5fc05ea6927c0dd') in 0.4901883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Green Variant.prefab
  artifactKey: Guid(a24c7503c7fa302409e94f731ff0c247) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Green Variant.prefab using Guid(a24c7503c7fa302409e94f731ff0c247) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6184ff23325e3015f5ef5ae17f208014') in 0.8501315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 772

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Scenes/Start 01.unity
  artifactKey: Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Scenes/Start 01.unity using Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Scenes/Start 01.unity additively'
Asset 'Playercontroller #D': Transition 'AnyState -> Running' in state 'AnyState' doesn't have an Exit Time or any condition, transition will be ignored
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.SceneManagement.EditorSceneManager:OpenScene (string,UnityEditor.SceneManagement.OpenSceneMode)
UnityEditor.Search.AssetIndexer:IndexScene (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

[C:\build\output\unity\unity\Editor\Src\Animation\StateMachine.cpp line 1862]

Loaded scene 'Assets/Scenes/Start 01.unity'
	Deserialize:            115.545 ms
	Integration:            595.712 ms
	Integration of assets:  16.681 ms
	Thread Wait Time:       -9.262 ms
	Total Operation Time:   718.676 ms
The method get_fontSharedMaterials was not found on PickupPromptText. This property will not be indexed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Rendering.Universal.MaterialReferenceBuilder:GetMaterialFromMethod (System.Reflection.MethodInfo,UnityEngine.Object,System.Func`3<string, string, string>) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs:133)
UnityEditor.Rendering.Universal.ConversionIndexers:ConversionIndexer (UnityEditor.Search.CustomObjectIndexerTarget,UnityEditor.Search.ObjectIndexer) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/ConversionIndexers.cs:21)
UnityEditor.Search.ObjectIndexer:CallCustomIndexers (string,int,UnityEngine.Object,UnityEditor.SerializedObject,bool)
UnityEditor.Search.ObjectIndexer:IndexCustomProperties (string,int,UnityEngine.Object)
UnityEditor.Search.AssetIndexer:IndexCustomGameObjectProperties (string,int,UnityEngine.GameObject)
UnityEditor.Search.AssetIndexer:IndexObjects (UnityEngine.GameObject[],string,string,string,bool)
UnityEditor.Search.AssetIndexer:IndexScene (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs Line: 133)

 -> (artifact id: '9a4db151ba8195dc674a8d1ed269d602') in 2.1901551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 550

========================================================================
Received Import Request.
  Time since last request: 0.000097 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab
  artifactKey: Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab using Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c0601934bed9da7f380ee2614aec122d') in 0.3062346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_BubbleExplode_01_3x3.mat
  artifactKey: Guid(cff5261eb60fe4f49995e308e9b79e0d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_BubbleExplode_01_3x3.mat using Guid(cff5261eb60fe4f49995e308e9b79e0d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b34faa66df8676528680d603ac9f46d4') in 0.1721006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_02.prefab
  artifactKey: Guid(67feafff91dbc76449463d379c9b2542) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_02.prefab using Guid(67feafff91dbc76449463d379c9b2542) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '578c66486259f15f93d6d02fac95d1b2') in 0.1946273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/Sound Pack Guide.pdf
  artifactKey: Guid(2ebed53a365058f49af935ad1599cd91) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/Sound Pack Guide.pdf using Guid(2ebed53a365058f49af935ad1599cd91) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '241db33d0db607d55890169748cd606a') in 0.0237226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Ground.prefab
  artifactKey: Guid(4f99107d6bbf321439eb378a8f2a2f7f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Ground.prefab using Guid(4f99107d6bbf321439eb378a8f2a2f7f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '75bb70603449d51898ee5b7f05d14539') in 0.1177449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset
  artifactKey: Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset using Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '65e020e635651c92659a191fe6d3dd8e') in 0.0852931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_LineaSlash_01.prefab
  artifactKey: Guid(11c2a8575fa23c6458d897033c85bafe) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_LineaSlash_01.prefab using Guid(11c2a8575fa23c6458d897033c85bafe) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '90bb0b86997b9c366696bc72515ba558') in 0.043818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineAsset.asset
  artifactKey: Guid(b9f3086da92434da0bc1518f19f0ce86) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineAsset.asset using Guid(b9f3086da92434da0bc1518f19f0ce86) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9c5c54542256e0be772a9d80c9cbd3aa') in 0.0537449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Empty/Settings/HDRP High Fidelity.asset
  artifactKey: Guid(36dd385e759c96147b6463dcd1149c11) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRP High Fidelity.asset using Guid(36dd385e759c96147b6463dcd1149c11) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f99e5263a44b20ccb3b843c8033905bf') in 0.0535261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity
  artifactKey: Guid(40eb938c820c1604fa12f8b824fc843c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity using Guid(40eb938c820c1604fa12f8b824fc843c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity additively'
Loaded scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity'
	Deserialize:            36.283 ms
	Integration:            526.691 ms
	Integration of assets:  0.163 ms
	Thread Wait Time:       0.020 ms
	Total Operation Time:   563.157 ms
 -> (artifact id: 'dffee4ce81c52d608c8c8d36e76c4acf') in 1.1696135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 889

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Charge.prefab
  artifactKey: Guid(6e0b19c854b7f6f42819d7435e6032c5) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Charge.prefab using Guid(6e0b19c854b7f6f42819d7435e6032c5) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3c48958139852ecb40a87a2ee35e82a0') in 0.0464905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Preexplosion.prefab
  artifactKey: Guid(3c04cc1754764144ab59fd862981bf9f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Preexplosion.prefab using Guid(3c04cc1754764144ab59fd862981bf9f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4a0cbef69ce2e2d0d1cf1204f3ddf1c1') in 0.1335451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_Flag_03.prefab
  artifactKey: Guid(abc00000000008533068516358505298) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_Flag_03.prefab using Guid(abc00000000008533068516358505298) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0d0f54cd38e4a3aa2d066eb6531232f1') in 0.0531048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_01.mat
  artifactKey: Guid(a42d4c918a924874fafde31f7476e829) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_01.mat using Guid(a42d4c918a924874fafde31f7476e829) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abdd69f4b1547cb05583c9e3947631be') in 0.2664425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Scripts/Controllers/PlayerController3DConfig.cs
  artifactKey: Guid(cc619a5c3c245974eb0fbe6bf2f47727) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/PlayerController3DConfig.cs using Guid(cc619a5c3c245974eb0fbe6bf2f47727) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79dbf22f1d0d11d40237d3e37c73a3cb') in 0.0382311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_02.prefab
  artifactKey: Guid(abc00000000002367564937674463636) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_02.prefab using Guid(abc00000000002367564937674463636) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6fad70590838d30f6c6a7c01a245023a') in 0.0565374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_02.png
  artifactKey: Guid(78c77c666a533404d938d708f7941609) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_02.png using Guid(78c77c666a533404d938d708f7941609) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0bacac4bdda0287c87609b74ca17f72') in 0.1255909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Apple.prefab
  artifactKey: Guid(abc00000000001693895618719668371) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Apple.prefab using Guid(abc00000000001693895618719668371) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'afe43c2414c511fc37a9b9df67752e2c') in 0.0455893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_05.prefab
  artifactKey: Guid(abc00000000011747994935644742707) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_05.prefab using Guid(abc00000000011747994935644742707) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '45ef7a79fe31a77d7912158baae4dc2d') in 0.0482583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_02.mat
  artifactKey: Guid(fb8e72e3fa61c3c4a807e73cf235d1c9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_02.mat using Guid(fb8e72e3fa61c3c4a807e73cf235d1c9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad5a26723c163df760762320300eb164') in 0.0796789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockB.prefab
  artifactKey: Guid(abc00000000010984285262532039862) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockB.prefab using Guid(abc00000000010984285262532039862) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd337d280366a7c6bb4a948efd2c2b3ad') in 0.067132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_01.mat
  artifactKey: Guid(43393d97e961e3141a826cd6abc334c1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_01.mat using Guid(43393d97e961e3141a826cd6abc334c1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2240bb96be248e1fd1f56b84e737e40f') in 0.066403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_A.prefab
  artifactKey: Guid(abc00000000015370793887961688754) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_A.prefab using Guid(abc00000000015370793887961688754) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a1be7a013a87ba8289415db30072c024') in 0.0541519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_01.prefab
  artifactKey: Guid(abc00000000010984909267591331595) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_01.prefab using Guid(abc00000000010984909267591331595) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e9e13aec26929f2891071a5632af798') in 0.031789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_01.mat
  artifactKey: Guid(44bbf14dad05cc7488a418f096a84e09) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_01.mat using Guid(44bbf14dad05cc7488a418f096a84e09) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ce277e2da3e3ea49187290720f3dd93') in 0.0575849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart.prefab
  artifactKey: Guid(abc00000000011749161118377129527) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart.prefab using Guid(abc00000000011749161118377129527) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'db205ae7de40bf69ed0e7c2bcfc30283') in 0.043844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_01_2x5.mat
  artifactKey: Guid(2ee7f5f46194fd642b91e014a56bb2a6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_01_2x5.mat using Guid(2ee7f5f46194fd642b91e014a56bb2a6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2545db8fff66febacf2f28feda64ebbb') in 0.0432671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_1.prefab
  artifactKey: Guid(abc00000000016341059340076686657) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_1.prefab using Guid(abc00000000016341059340076686657) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eaee61d8f77a32c3f1924481bcbe6502') in 0.0317619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_02.prefab
  artifactKey: Guid(abc00000000012753242278391718196) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_02.prefab using Guid(abc00000000012753242278391718196) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '865e2d49930ee8080046428458f54e4e') in 0.0440681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_01.prefab
  artifactKey: Guid(abc00000000003251423913722411099) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_01.prefab using Guid(abc00000000003251423913722411099) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f29f0d84ccafc646d41d3901ceb60d8') in 0.0641348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_04.prefab
  artifactKey: Guid(abc00000000009266294131336485834) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_04.prefab using Guid(abc00000000009266294131336485834) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7674bda84014e1e229c4a5a13f2b190e') in 0.0390511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50259b45dfd157b2402827a96f0aea9c') in 0.0437372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_12.prefab
  artifactKey: Guid(abc00000000008594485119313702979) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_12.prefab using Guid(abc00000000008594485119313702979) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '39cfdf907c543b47365d9140baa9ff35') in 0.0846044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01.prefab
  artifactKey: Guid(abc00000000009298666442671915430) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01.prefab using Guid(abc00000000009298666442671915430) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '83314dee11568da198f30347830a9e32') in 0.0698833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_7.prefab
  artifactKey: Guid(abc00000000015058747550851706431) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_7.prefab using Guid(abc00000000015058747550851706431) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e26ba83bc821f9de2f52a4b8a9cb3c66') in 0.040848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_8.prefab
  artifactKey: Guid(abc00000000014308775244425524208) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_8.prefab using Guid(abc00000000014308775244425524208) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3351bc6daeeeb7953c3d64c9aebbd1eb') in 0.0433547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3.mat
  artifactKey: Guid(33ef428c98aab8f48aebbb37bc27233a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3.mat using Guid(33ef428c98aab8f48aebbb37bc27233a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb97fe1a15ccbd8c543a8ac6554d1d42') in 0.0754931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3_Poison.mat
  artifactKey: Guid(56020f27b97d2db4abb11fa5d09a3b17) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3_Poison.mat using Guid(56020f27b97d2db4abb11fa5d09a3b17) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b8662e86b9097f8fc89ba8e512f4229') in 0.0768608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_01.prefab
  artifactKey: Guid(abc00000000017288310274170488947) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_01.prefab using Guid(abc00000000017288310274170488947) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a9be54accdb8fbdcfea9cac968a25494') in 0.0402001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_01.prefab
  artifactKey: Guid(abc00000000001138482080542250624) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_01.prefab using Guid(abc00000000001138482080542250624) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'da8732bb82c776032c91b2779c3d6abb') in 0.042134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_03_4x4.mat
  artifactKey: Guid(500846e0a03a9604e9a19f934178c4ba) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_03_4x4.mat using Guid(500846e0a03a9604e9a19f934178c4ba) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fc31da6997f9cd476e2e60fc6310aaa') in 0.0623317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_05_4x3.mat
  artifactKey: Guid(decef669d4e6d8c488a854d7ee1d0fb1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_05_4x3.mat using Guid(decef669d4e6d8c488a854d7ee1d0fb1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83b86a09e5c4e5afa519e2f9461cc8b9') in 0.0400417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair.prefab
  artifactKey: Guid(abc00000000013297240178136726284) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair.prefab using Guid(abc00000000013297240178136726284) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e0a63914f717be50a30b8c01933c36e6') in 0.0350965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConeRoof_01.prefab
  artifactKey: Guid(abc00000000017983411432195826884) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConeRoof_01.prefab using Guid(abc00000000017983411432195826884) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '98dee6bf2c98f011adfe1fabdca6aeca') in 0.0690749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong_Loose.prefab
  artifactKey: Guid(abc00000000004332266398211673837) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong_Loose.prefab using Guid(abc00000000004332266398211673837) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0d27d15d090618b78e40f4a18d60bde1') in 0.0648004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_23.prefab
  artifactKey: Guid(abc00000000016560756422256669264) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_23.prefab using Guid(abc00000000016560756422256669264) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '132280ba978fa0997955c106eae54842') in 0.1870511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_B.prefab
  artifactKey: Guid(abc00000000017232423169120149286) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_B.prefab using Guid(abc00000000017232423169120149286) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '39853c4647443246ff295f11c1aee4ed') in 0.0316354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_02.prefab
  artifactKey: Guid(abc00000000007279725207663366801) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_02.prefab using Guid(abc00000000007279725207663366801) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1bcac305624a7cb4ee1bc2bfe7475373') in 0.0362216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_01.prefab
  artifactKey: Guid(abc00000000006790571834125416237) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_01.prefab using Guid(abc00000000006790571834125416237) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '78fb08764ece6aa1c76da46a0492bd43') in 0.0385646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_13.prefab
  artifactKey: Guid(abc00000000017349817595143904948) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_13.prefab using Guid(abc00000000017349817595143904948) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4be361135f4c911032b98f612d321252') in 0.0389008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000128 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B.prefab
  artifactKey: Guid(abc00000000009243210316432420041) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B.prefab using Guid(abc00000000009243210316432420041) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea4a30cfb4688ce0602fad7b87dbb51b') in 0.0300757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_17.prefab
  artifactKey: Guid(abc00000000004371273187608930184) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_17.prefab using Guid(abc00000000004371273187608930184) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5555c5aeda6fd3f64fa3136bfc542507') in 0.046359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_16.prefab
  artifactKey: Guid(abc00000000017433115186810166673) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_16.prefab using Guid(abc00000000017433115186810166673) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '84c7aab1161f195e54cd457d9b6c8812') in 0.0479107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Burst.prefab
  artifactKey: Guid(f4309870c41615247af837f7c99ccfb8) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Burst.prefab using Guid(f4309870c41615247af837f7c99ccfb8) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'de0e98dbe1454b28ddc2b7461fd58ed0') in 0.0452997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_03_3x4.mat
  artifactKey: Guid(158dc891240e2ca45b8d3f181d8bb783) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_03_3x4.mat using Guid(158dc891240e2ca45b8d3f181d8bb783) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbcfc0dacfbcea10f95990de1571382a') in 0.0411649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterTrail_01.mat
  artifactKey: Guid(d9fd2139ac7ae8649a575df55965315c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterTrail_01.mat using Guid(d9fd2139ac7ae8649a575df55965315c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f573ebfd76d91cab824adbed45e62211') in 0.0850547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_MainFoam.prefab
  artifactKey: Guid(f58c1fb0643d4f048ba646dacce6a63d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_MainFoam.prefab using Guid(f58c1fb0643d4f048ba646dacce6a63d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '550c3019b0c8754c14b5741bf82c5e88') in 0.3191889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_05.mat
  artifactKey: Guid(1681e4900c36664459771c900f583b48) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_05.mat using Guid(1681e4900c36664459771c900f583b48) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5343d736c2496b0c38f570b6eeaf7641') in 0.027725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_FallingWater_01.prefab
  artifactKey: Guid(48216c1b9d28e7c4fb70a30171820180) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_FallingWater_01.prefab using Guid(48216c1b9d28e7c4fb70a30171820180) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e1eb6a59d3a3e68353a48a4e95d4d70') in 0.1563441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_01.prefab
  artifactKey: Guid(abc00000000005861316417145525211) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_01.prefab using Guid(abc00000000005861316417145525211) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9a988753023089ab414dbb3474b3a025') in 0.0302363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_01.prefab
  artifactKey: Guid(abc00000000003396156233384122082) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_01.prefab using Guid(abc00000000003396156233384122082) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3534b39972528f65d424621ca6270b27') in 0.0364256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_09.mat
  artifactKey: Guid(3433dc9182a801c4b89de8442ec0bb4a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_09.mat using Guid(3433dc9182a801c4b89de8442ec0bb4a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea47fffdc966797be75b92af9c92ab78') in 0.0353141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_03.prefab
  artifactKey: Guid(abc00000000017327626611667190952) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_03.prefab using Guid(abc00000000017327626611667190952) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '893ae7dfb4796c3f3f8412bfae80dc82') in 0.0699716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_E.prefab
  artifactKey: Guid(abc00000000008242963507806250587) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_E.prefab using Guid(abc00000000008242963507806250587) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c575122f1e0df86cdd98d8e2ad6d9391') in 0.0658927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_01.prefab
  artifactKey: Guid(e7735de37e7a34e4f9b3862554646a69) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_01.prefab using Guid(e7735de37e7a34e4f9b3862554646a69) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '18852bf4010f0ee7a77172b25caed8c6') in 0.1468307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000452 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12_1.prefab
  artifactKey: Guid(abc00000000012089362061524675311) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12_1.prefab using Guid(abc00000000012089362061524675311) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f742c44844fd768da28461a9814c470e') in 0.048294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_02.prefab
  artifactKey: Guid(abc00000000018123052294421716965) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_02.prefab using Guid(abc00000000018123052294421716965) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '96c31977c0e11a3bc3deb9a53a7b67e2') in 0.0744425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_NoiseWater_01.mat
  artifactKey: Guid(7d4c1045919823d4fad991c3b569889b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_NoiseWater_01.mat using Guid(7d4c1045919823d4fad991c3b569889b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3394f65e8306b91803c680c5ff0c64b2') in 0.0598626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05_1.prefab
  artifactKey: Guid(abc00000000002725194695316289400) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05_1.prefab using Guid(abc00000000002725194695316289400) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '988144bc4deb9519a0dff378f2913713') in 0.0394812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_2.prefab
  artifactKey: Guid(abc00000000007104105223252906786) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_2.prefab using Guid(abc00000000007104105223252906786) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '178b0b25cd79a45a57c40b9039c0bda7') in 0.0334253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterGround_01.prefab
  artifactKey: Guid(8078ef8b06c75fa4cbc83d588e1e91e9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterGround_01.prefab using Guid(8078ef8b06c75fa4cbc83d588e1e91e9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51ce79465c9e6db222cc14a80dd9aca3') in 0.1644555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 63

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_03.prefab
  artifactKey: Guid(abc00000000003228867782050657137) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_03.prefab using Guid(abc00000000003228867782050657137) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1fc2ae8511e57efb438c1d12a733c8e1') in 0.0359896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase_Corner.prefab
  artifactKey: Guid(abc00000000015053201230645522228) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase_Corner.prefab using Guid(abc00000000015053201230645522228) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f986aab8248485e02f5d3244303e89c6') in 0.0456317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterSimpleProjectile_Traveling.prefab
  artifactKey: Guid(6d4c2bd141c202242a96c86f3edee707) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterSimpleProjectile_Traveling.prefab using Guid(6d4c2bd141c202242a96c86f3edee707) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9509e85ed9d3a939a58a0bfbc0356ad') in 0.1553081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Impact.prefab
  artifactKey: Guid(02d97bb2a8216d0468d61f9a3a291bac) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Impact.prefab using Guid(02d97bb2a8216d0468d61f9a3a291bac) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd2623309ff6ebadc9986448f2ea8c2d') in 0.2079985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 92

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Impact_01.prefab
  artifactKey: Guid(8b4234cca2becc64093145bb8df399ad) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Impact_01.prefab using Guid(8b4234cca2becc64093145bb8df399ad) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33bef29044171e61857175141aff4129') in 0.1485733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_C.prefab
  artifactKey: Guid(abc00000000017437805951273207443) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_C.prefab using Guid(abc00000000017437805951273207443) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea5107152c59fa8759cc357a27b6cc54') in 0.027718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_ImpactGround.prefab
  artifactKey: Guid(512ba2be3c335144fa1a6eaaaeb2dafa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_ImpactGround.prefab using Guid(512ba2be3c335144fa1a6eaaaeb2dafa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85ef75d226d34da721e2ff1a9ffa75ad') in 0.2241903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 89

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x4.prefab
  artifactKey: Guid(abc00000000007686417641232609422) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x4.prefab using Guid(abc00000000007686417641232609422) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0813c2101c33b36fe1c3ca1a5384d3ce') in 0.0343324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_2.prefab
  artifactKey: Guid(abc00000000010042851532632003929) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_2.prefab using Guid(abc00000000010042851532632003929) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '65ceee5b4bc6a90afda82f47a2d940cc') in 0.0367356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_A_01.prefab
  artifactKey: Guid(abc00000000001314945204152803406) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_A_01.prefab using Guid(abc00000000001314945204152803406) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e9e147464a4bb2d101dd7227390ed814') in 0.0406103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_01.prefab
  artifactKey: Guid(abc00000000001869525803899943501) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_01.prefab using Guid(abc00000000001869525803899943501) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5eb73ba1dc15d93278832e58d93eb227') in 0.0332614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_03.prefab
  artifactKey: Guid(abc00000000015626663145792241089) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_03.prefab using Guid(abc00000000015626663145792241089) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9ce2d468461ab2fddb9204e18fd2a0b1') in 0.0359797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_12.prefab
  artifactKey: Guid(30b38a3a06bc38f4b9426be3ad33cfd0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_12.prefab using Guid(30b38a3a06bc38f4b9426be3ad33cfd0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '513f0417ee714c52bf1ddf6e18fecc8d') in 0.0428426 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_SmokeParticle.prefab
  artifactKey: Guid(5f824757289c01a4ab36648492fb6aba) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_SmokeParticle.prefab using Guid(5f824757289c01a4ab36648492fb6aba) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '29b34b10168d92ebc09d1f5a4e9956c9') in 0.0292479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Empty/Settings/SkyandFogSettingsProfile.asset
  artifactKey: Guid(8ba92e2dd7f884a0f88b98fa2d235fe7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/SkyandFogSettingsProfile.asset using Guid(8ba92e2dd7f884a0f88b98fa2d235fe7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4978654d44c3577587e8a77d060d338') in 0.0293692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab
  artifactKey: Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab using Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9a83b4f21bfbd782ae1db2b8cf53356') in 1.6789741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1026

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_02.mat
  artifactKey: Guid(4c99ecd0c2ea1a1458821662217b3609) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_02.mat using Guid(4c99ecd0c2ea1a1458821662217b3609) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6cd2686971c9306147bcbfde80c50d70') in 0.0599686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000380 seconds.
  path: Assets/IdaFaber/Demo/A_Idle_M.fbx
  artifactKey: Guid(f19a9419bc0c77f4495de9271f229793) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/A_Idle_M.fbx using Guid(f19a9419bc0c77f4495de9271f229793) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '340377a0fe10eb623bb78808cd1da8fd') in 0.1066068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 347

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_Add.mat
  artifactKey: Guid(a6dfdeac305027046b15decef3bed90e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_Add.mat using Guid(a6dfdeac305027046b15decef3bed90e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1532e729576f86c9e290836a3abe4a7') in 0.0312113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Poison_SimpleSplash_01.prefab
  artifactKey: Guid(578f4dc6bf2d25d42b056dfcdd6b2628) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Poison_SimpleSplash_01.prefab using Guid(578f4dc6bf2d25d42b056dfcdd6b2628) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cbafa5e6490ffef33871f6d7761bfe6f') in 0.0581663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 67

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.prefab
  artifactKey: Guid(cf9b0789e85e919479c197694c3020e5) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.prefab using Guid(cf9b0789e85e919479c197694c3020e5) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '601dcf0f01daebaa6aea5292c82fa52c') in 0.0408398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000128 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Hammer.prefab
  artifactKey: Guid(abc00000000015238524577280148859) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Hammer.prefab using Guid(abc00000000015238524577280148859) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b6fa6ba5a1363a724826c183192d71a3') in 0.0436016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab
  artifactKey: Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab using Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '31d339d07ff859a787be28effc5cf7a1') in 0.0507986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 147

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BannerPole_01.prefab
  artifactKey: Guid(abc00000000017523910351110215581) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BannerPole_01.prefab using Guid(abc00000000017523910351110215581) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ef49eae9c1bcd4ec06d70c26b5b38c54') in 0.0344747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hierarchy Designer/Editor/Saved Data/HierarchyDesigner_SavedData_Folders.json
  artifactKey: Guid(d531f852b4ff4bd4381516378142469a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Saved Data/HierarchyDesigner_SavedData_Folders.json using Guid(d531f852b4ff4bd4381516378142469a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '415b7078df4c00b0217b83852ad2933a') in 0.0488575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Postmodern II.png
  artifactKey: Guid(f758af7ec13b7c141bbbdce4a94876f2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Postmodern II.png using Guid(f758af7ec13b7c141bbbdce4a94876f2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3ca0ffb741d8178bd086a31f6350ff7') in 0.0637906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hierarchy Designer/Runtime/Scripts/HierarchyDesignerFolder.cs
  artifactKey: Guid(b1816795d2838f643acc6f6f815cfff9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Runtime/Scripts/HierarchyDesignerFolder.cs using Guid(b1816795d2838f643acc6f6f815cfff9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bcedfa410bfc1282c01bd172b26dcd2') in 0.0410084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_BODY_F_01.mat
  artifactKey: Guid(8a42abb20a779a047b66ee03821f6162) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_BODY_F_01.mat using Guid(8a42abb20a779a047b66ee03821f6162) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3abd6aad05f5b6506160b676444bcacd') in 0.3386537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Bottom.png
  artifactKey: Guid(a0549dea84c228f4e93cfaf2a1e89253) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Bottom.png using Guid(a0549dea84c228f4e93cfaf2a1e89253) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1926ba24408686a564df653dec7f84db') in 0.0871019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_BODY_F_ROCA_Markings.mat
  artifactKey: Guid(02ef5542126cca040a6affa493773dae) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_BODY_F_ROCA_Markings.mat using Guid(02ef5542126cca040a6affa493773dae) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0adaa8828165964c857cd4f6ebcef871') in 0.2775023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Left.png
  artifactKey: Guid(e537c5ea86cb41f42bad4053ee3aadf4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Left.png using Guid(e537c5ea86cb41f42bad4053ee3aadf4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14facbc317a7213ae9c54e713edca15a') in 0.0656648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_GameObject.cs
  artifactKey: Guid(ab4720efb8bd8a949bda7bb10e5c86a0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_GameObject.cs using Guid(ab4720efb8bd8a949bda7bb10e5c86a0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17370ca7f4cfdf9f14abf2cbd5e76931') in 0.0318919 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Promotional PicEase.png
  artifactKey: Guid(612b26c7986139845a9210523b133270) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Promotional PicEase.png using Guid(612b26c7986139845a9210523b133270) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95e523522fe3209fb02d64d6277005f4') in 0.0721845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved T.png
  artifactKey: Guid(3f1bff7bdfb248c4c80efdc962ffaef7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved T.png using Guid(3f1bff7bdfb248c4c80efdc962ffaef7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2f763f437ecce950ea219820e8ada03') in 0.1254308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.451200 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_25.mat
  artifactKey: Guid(dde649b6d7d8ad14fbc16f646cf604ee) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_25.mat using Guid(dde649b6d7d8ad14fbc16f646cf604ee) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87bd20b297abe54faf88c6407e100be3') in 0.1909485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LINGERIE.mat
  artifactKey: Guid(2c0b4309ce6fcfe4d9a62d7da2c2bfe5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LINGERIE.mat using Guid(2c0b4309ce6fcfe4d9a62d7da2c2bfe5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c04be522d2db1e6a57b8dc760cabbe3') in 0.4724188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000275 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_04.mat
  artifactKey: Guid(5c5df7fcb8475e74482e326f0263b555) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_04.mat using Guid(5c5df7fcb8475e74482e326f0263b555) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33b471ccf7ca360d8103db52be93b793') in 0.4005541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/IDA_Skin.shadergraph
  artifactKey: Guid(58bd24c3542ca78448b90ae35be39b26) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/IDA_Skin.shadergraph using Guid(58bd24c3542ca78448b90ae35be39b26) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b630da324d2f54d70ff799e17ee3c7c3') in 0.1723446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_01.mat
  artifactKey: Guid(14eca1960ca98e642b9f068a8d1f45fa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_01.mat using Guid(14eca1960ca98e642b9f068a8d1f45fa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a61b098560005e1c537e0233e7135cc0') in 0.1384447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_28.mat
  artifactKey: Guid(9cc719f4ea5ab4547bce75203f37f30a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_28.mat using Guid(9cc719f4ea5ab4547bce75203f37f30a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3b7efacafefaa939a08ac13b1193862') in 0.2881046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_02.mat
  artifactKey: Guid(0c39de438f450a54a8ab12c182fd20bc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_02.mat using Guid(0c39de438f450a54a8ab12c182fd20bc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03d142ffcf5e9a205538946fc023e2e0') in 0.3535587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Plane.mat
  artifactKey: Guid(14cd175f95319514ead6e8de8eb363ce) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Plane.mat using Guid(14cd175f95319514ead6e8de8eb363ce) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54af27aa4e5ba4ca8acaf64366bf21ef') in 0.0651443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_11.png
  artifactKey: Guid(9bbd151a013e0d147bc45cd62fa7473b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_11.png using Guid(9bbd151a013e0d147bc45cd62fa7473b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf81c9145867a815965ac228c7508868') in 0.0859601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Mask_Blood_02.png
  artifactKey: Guid(469627fdc2cf6e94481435d0475418cb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Mask_Blood_02.png using Guid(469627fdc2cf6e94481435d0475418cb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7e199f7089bf89a416ed10d8ab3547b') in 0.0983641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_SSS.png
  artifactKey: Guid(bfb78dcdc414cc24dbf3c9305c1ad5c5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_SSS.png using Guid(bfb78dcdc414cc24dbf3c9305c1ad5c5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46e0748b3a37400e4afd16c97118d7b2') in 0.0683698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_19.mat
  artifactKey: Guid(a823b02789a259648a8cc506ee0813e9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_19.mat using Guid(a823b02789a259648a8cc506ee0813e9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de070233baa9106b49d5c197b54aec31') in 0.4324218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_02.mat
  artifactKey: Guid(cb627d78b9cad4b419904d82a9383788) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_02.mat using Guid(cb627d78b9cad4b419904d82a9383788) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8523c44ab1bc47d86bd3eac004e536b2') in 0.2281336 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_01_Dirty.mat
  artifactKey: Guid(68af94b7c5c08684ebb14732803a6610) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_01_Dirty.mat using Guid(68af94b7c5c08684ebb14732803a6610) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8ecc5e73a67cd4df896fec46e0aea05') in 0.275217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultLookDevProfile.asset
  artifactKey: Guid(1d491ecba2daea848bcc943bec301f07) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultLookDevProfile.asset using Guid(1d491ecba2daea848bcc943bec301f07) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bac312dc5e73bfa9b5c8d36ee0fb7f7e') in 0.036536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_02.mat
  artifactKey: Guid(96c737b94b2de554cbd8fab5b84005ce) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_02.mat using Guid(96c737b94b2de554cbd8fab5b84005ce) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16f539bc4206eb252f3a408478e6f1aa') in 0.2529798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_08.mat
  artifactKey: Guid(53d8819915563f84faaa8ca14272d5f8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_08.mat using Guid(53d8819915563f84faaa8ca14272d5f8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84f355a50683b0c3374b0da0a8a5c8b6') in 0.3205882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000363 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Height.png
  artifactKey: Guid(dd81d4bd2eb004244b79734f4bf61572) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Height.png using Guid(dd81d4bd2eb004244b79734f4bf61572) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c22510cadc616b92070caa2c0c9a1f7d') in 0.0932695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_02.png
  artifactKey: Guid(f18de98c4a80ad347a5ea0d635793fc8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_02.png using Guid(f18de98c4a80ad347a5ea0d635793fc8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45aa2fa0c790ab9847cde4c51b4f9834') in 0.0645246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000646 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_07.png
  artifactKey: Guid(7e28cda96838d294dbfd58ae601a317b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_07.png using Guid(7e28cda96838d294dbfd58ae601a317b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bce27ea48ac832456816dd5797a2c43') in 0.0797585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Parallax_02.png
  artifactKey: Guid(4acdd0cc6f57baf458274b1a2c28bf10) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Parallax_02.png using Guid(4acdd0cc6f57baf458274b1a2c28bf10) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eca98ce2fe77235311e6b416d4a7d1b1') in 0.0758024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_30.png
  artifactKey: Guid(58d6e7805bdf0b74c9ba9e203364694c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_30.png using Guid(58d6e7805bdf0b74c9ba9e203364694c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ecdc0870b9c70696c6d3a6f374f7bbb5') in 0.0913312 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_11_01.mat
  artifactKey: Guid(8178242842d59c3419e2cd3dd938246b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_11_01.mat using Guid(8178242842d59c3419e2cd3dd938246b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44c94418b3da2873196e313307dc7ac2') in 0.2372935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22_Dirty.mat
  artifactKey: Guid(9b2dd74c7388cbe4e86cfebc1bb3f996) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22_Dirty.mat using Guid(9b2dd74c7388cbe4e86cfebc1bb3f996) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c40365615044d9408b56e08079b8c3c') in 0.3705241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_03.png
  artifactKey: Guid(0da72650af9db38438cc59a3997dab27) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_03.png using Guid(0da72650af9db38438cc59a3997dab27) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '356fb114668462f1d2a756f89b5ff1a5') in 0.095519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_Alternative.mat
  artifactKey: Guid(9cbd874342f4e4547a217c3d6543ae98) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_Alternative.mat using Guid(9cbd874342f4e4547a217c3d6543ae98) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9bcf2f2c0674c84e7eea0005ccc6f77b') in 0.2787689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_01.mat
  artifactKey: Guid(4a8442662ff979443b3b10bb5b93362a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_01.mat using Guid(4a8442662ff979443b3b10bb5b93362a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cee571fc2e8acd36d36fe37571fa8336') in 0.3653113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000165 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_09.png
  artifactKey: Guid(509366a399c952b479c75cef33eacb50) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_09.png using Guid(509366a399c952b479c75cef33eacb50) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8de316896c4658fde1fe2c5d6b564570') in 0.1644501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_06.png
  artifactKey: Guid(6a46eb4954112c24da502aa82725714a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_06.png using Guid(6a46eb4954112c24da502aa82725714a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4589f6e3e4cfd0e088c14bc5a9d62c37') in 0.113788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_04.png
  artifactKey: Guid(1be003b9a41cf5548a2e22b188a98e84) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_04.png using Guid(1be003b9a41cf5548a2e22b188a98e84) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cfe985c4d4bbe2e0cc844a49bce4a4fb') in 0.1600935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_MaskMap.png
  artifactKey: Guid(f7c0a4ec931438346a92e5e15324b836) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_MaskMap.png using Guid(f7c0a4ec931438346a92e5e15324b836) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd466277152baaba2911ee8c5e0be16a') in 0.1142585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_SKIN_TattooAtlas.png
  artifactKey: Guid(69440e308c8caba489acddbe6b02d139) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_SKIN_TattooAtlas.png using Guid(69440e308c8caba489acddbe6b02d139) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b69570412b03c5fbf846c0d29984c1a7') in 0.0805245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_08.png
  artifactKey: Guid(ab3619ab655e676449ae641b9f97675d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_08.png using Guid(ab3619ab655e676449ae641b9f97675d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52095fd122dd967267a3872b440ba99f') in 0.0652635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_07_02.png
  artifactKey: Guid(67433cbd043cf254aa99d970984ddbc5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_07_02.png using Guid(67433cbd043cf254aa99d970984ddbc5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dcfa52afa5284e5fbb9ad8d0e8d48abd') in 0.0601032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_SSS.png
  artifactKey: Guid(aeb8dc54a26e98048b494b34dd069280) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_SSS.png using Guid(aeb8dc54a26e98048b494b34dd069280) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a45b56be527a9f2a2a9a747af3b21032') in 0.0630931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab
  artifactKey: Guid(a94136958165ffb4e91f034a7974d498) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab using Guid(a94136958165ffb4e91f034a7974d498) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97e20127306c786deea368a9314a0601') in 0.0600947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_MatID.png
  artifactKey: Guid(379607bfb64588b48b60efaed42d0909) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_MatID.png using Guid(379607bfb64588b48b60efaed42d0909) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c43a8eb26149a6cf3b1ce65b080fb70') in 0.0592581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)Eyes.controller
  artifactKey: Guid(ac054854941e66044b2648ca7a630471) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)Eyes.controller using Guid(ac054854941e66044b2648ca7a630471) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aeaae00c3271205ed8c8635da312f198') in 0.0378141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineAsset.asset
  artifactKey: Guid(c6a867ee639a59c4a95e661e91ab54a2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineAsset.asset using Guid(c6a867ee639a59c4a95e661e91ab54a2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b21b46af29c14734d05e3c9ae6d50f76') in 0.0392018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_21.png
  artifactKey: Guid(2a751d40b9fa80d44af2b3a8e5fb923f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_21.png using Guid(2a751d40b9fa80d44af2b3a8e5fb923f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e12abcb0b43e05a7c7566b9d1e284412') in 0.0636662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.080403 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterSmall.prefab
  artifactKey: Guid(4990112ecf119944baa60b7f6690e8e4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterSmall.prefab using Guid(4990112ecf119944baa60b7f6690e8e4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b675348d411bfd4d095393e0fac6adf1') in 0.079436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundSplash_5x5_01.psd
  artifactKey: Guid(4ba7348ba80b8db45a73d7cbe9d9a847) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundSplash_5x5_01.psd using Guid(4ba7348ba80b8db45a73d7cbe9d9a847) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b3917377bdc70b15b4604c4d8d89529') in 0.0695988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterWhip_2x6_01.psd
  artifactKey: Guid(847319b660fb8a74b8bda39f2e9f3eb8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterWhip_2x6_01.psd using Guid(847319b660fb8a74b8bda39f2e9f3eb8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cfd32202985f13dca0ab9d9151af6ae7') in 0.0788123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DenseNoise_01.png
  artifactKey: Guid(eef497c105ae18a49b7fe1b4558ddb8a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DenseNoise_01.png using Guid(eef497c105ae18a49b7fe1b4558ddb8a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed9f861203e10ff3e4fe00d06dc225a3') in 0.0653742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab
  artifactKey: Guid(86804816d3c9e214c9a526e82ee95703) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab using Guid(86804816d3c9e214c9a526e82ee95703) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12b5fdbd91eec94b3ab24de8feb479d4') in 0.2935194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWaterSlide_4x4_01.psd
  artifactKey: Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWaterSlide_4x4_01.psd using Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13f4049aa7bdad1aac4d90a6454ec0ef') in 0.0791658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_04.png
  artifactKey: Guid(603619154c1c31240af774acbc3a3a95) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_04.png using Guid(603619154c1c31240af774acbc3a3a95) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df1a13eae3aa2f97d021dd7007366175') in 0.0514913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab
  artifactKey: Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab using Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8b5e46fbd0d15e4643a452398b6157f') in 0.0933173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TwirlingWater_2x8_01.psd
  artifactKey: Guid(3f08f83b9dda4054181c115d850984ab) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TwirlingWater_2x8_01.psd using Guid(3f08f83b9dda4054181c115d850984ab) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9c41f7917cef2c9ae43cfd430ce8e93') in 0.0800991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_4x4_02.psd
  artifactKey: Guid(0d1f9eb38e5ea8c4295816cb7fb1c406) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_4x4_02.psd using Guid(0d1f9eb38e5ea8c4295816cb7fb1c406) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2da87333c3d95bd83867260c319b7f92') in 0.1031573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamIndividual_4x4_02.psd
  artifactKey: Guid(2caf434defa8f7e418f582ecda1799fa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamIndividual_4x4_02.psd using Guid(2caf434defa8f7e418f582ecda1799fa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b6f12f8befae31817a47270dce4217a') in 0.0757117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000267 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_WaterWave.playable
  artifactKey: Guid(0d1b3d25c44e7dc449bda23c9683fe73) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_WaterWave.playable using Guid(0d1b3d25c44e7dc449bda23c9683fe73) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cffe6e2b5cdcb806825f200c8e83a08') in 0.0831497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_ImpactSplash_4x4_01.psd
  artifactKey: Guid(b79bc2aea1cfeb1489b47d91201b46c8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_ImpactSplash_4x4_01.psd using Guid(b79bc2aea1cfeb1489b47d91201b46c8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7698a0203e76249e373eda2a1ca61e69') in 0.061481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.350161 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Waterfall_Mask_02.png
  artifactKey: Guid(1b1d5394902464946a6576258a8d9352) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Waterfall_Mask_02.png using Guid(1b1d5394902464946a6576258a8d9352) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '61f96a30f1f5410be27d6afee61b3afe') in 0.0513604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x6_07.png
  artifactKey: Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x6_07.png using Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '261c0b567ab113f35e89f760c0e53f2a') in 0.0469501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_K.FBX
  artifactKey: Guid(08c0d59660469b049acceccba97f8868) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_K.FBX using Guid(08c0d59660469b049acceccba97f8868) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '076bb63d6a5331a0dd6a18bbaf4e9186') in 0.0815498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_06.png
  artifactKey: Guid(6ff89eedd8921e14897c9d580ffd175d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_06.png using Guid(6ff89eedd8921e14897c9d580ffd175d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d6835f12a66dc00e6bc9ecd061a4d8f') in 0.2244025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Input/InputReader.cs
  artifactKey: Guid(8a3c4de7d05bf894897bbb80292a9644) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Input/InputReader.cs using Guid(8a3c4de7d05bf894897bbb80292a9644) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66deb8a874335b332fc91b4917a2c5fd') in 0.036019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_01_Grayscale.psd
  artifactKey: Guid(ddb36cab21ed38246b9c9b15ed7ffb56) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_01_Grayscale.psd using Guid(ddb36cab21ed38246b9c9b15ed7ffb56) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b2a50cf1b059b6cb4ce163725de7ae8') in 0.0605308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_14.mat
  artifactKey: Guid(c0c9f4e1dbbc5d0449d29ff6244bfad8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_14.mat using Guid(c0c9f4e1dbbc5d0449d29ff6244bfad8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c4088bac7d8c9802d613b8d40250d7b') in 0.2225616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_Katana_Blade@Skill_H_return.FBX
  artifactKey: Guid(d20fe5e5ee766a24a878c8aeb047db69) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_Katana_Blade@Skill_H_return.FBX using Guid(d20fe5e5ee766a24a878c8aeb047db69) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b53ee73e6f42262af53a85de261e680') in 0.0823127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Human Body Upper Mask.mask
  artifactKey: Guid(142a22760aae7114f82e9cfc781709d0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Human Body Upper Mask.mask using Guid(142a22760aae7114f82e9cfc781709d0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f163d815b0528222fae423f658dd9cf7') in 0.0336675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_21.mat
  artifactKey: Guid(4989cf3701091644995da0988f0c85e0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_21.mat using Guid(4989cf3701091644995da0988f0c85e0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bcb648785df23f3fa4b26f930a23dc1') in 0.2484771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_4x4_01.png
  artifactKey: Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_4x4_01.png using Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72b5bfdf194949addecc0574564084b6') in 0.0891909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_H.FBX
  artifactKey: Guid(113815f8663b5f3418bdb3382644fbaa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_H.FBX using Guid(113815f8663b5f3418bdb3382644fbaa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35e56ad0bbb60ace9695913dcea07749') in 0.0856575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_ALL.FBX
  artifactKey: Guid(54aeb75085bbeb9479548598f7785b5f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_ALL.FBX using Guid(54aeb75085bbeb9479548598f7785b5f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87712963d48ceeee97f8d13253ae7f3c') in 0.0840768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_G.FBX
  artifactKey: Guid(30faae470f19d1942bb91ca8fee29364) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_G.FBX using Guid(30faae470f19d1942bb91ca8fee29364) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0a193690cbf59202361719309e9fd49') in 0.0749452 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_AiryString_1.wav
  artifactKey: Guid(6c2a988cfc0659e4195f81d8cbc3961f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_AiryString_1.wav using Guid(6c2a988cfc0659e4195f81d8cbc3961f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc32902ba3dd6fdbc517ed0c7df6862d') in 0.2229941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_05.png
  artifactKey: Guid(fbb2b5be7b5c49945aae88c889e5e186) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_05.png using Guid(fbb2b5be7b5c49945aae88c889e5e186) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f776a0e5cfed21655076d1b9697f6cb') in 0.0506713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Waterfall_Loop_2.wav
  artifactKey: Guid(d43db64a2e3bdff4288e678817071db1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Waterfall_Loop_2.wav using Guid(d43db64a2e3bdff4288e678817071db1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96b5facce24613c7241d54eaa5098492') in 0.1765554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_1.wav
  artifactKey: Guid(a318815dc81267d4d8f906402b3ee775) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_1.wav using Guid(a318815dc81267d4d8f906402b3ee775) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '288866cfa74909d6ba4827c10cdba3ec') in 0.1589212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterImpact_4x4_01_Grayscale.png
  artifactKey: Guid(30632ff51a495c541921ee3710e1b9d1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterImpact_4x4_01_Grayscale.png using Guid(30632ff51a495c541921ee3710e1b9d1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0250b856b26092086620b60ab16b423d') in 0.0669092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_03_Grayscale.png
  artifactKey: Guid(e2f268822fc698d4fa22b28b6b736dd9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_03_Grayscale.png using Guid(e2f268822fc698d4fa22b28b6b736dd9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afcd6fce6327d13511969870db0f549b') in 0.0446292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x6_06.png
  artifactKey: Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x6_06.png using Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8eaf97e9c31427bbc670b25453fb7205') in 0.0596137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_05.mat
  artifactKey: Guid(f3ea70960a5d0e64dbdbd09663e0fe58) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_05.mat using Guid(f3ea70960a5d0e64dbdbd09663e0fe58) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ae20059f8ededec92cb26a17a136219') in 0.1600392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_depth.png
  artifactKey: Guid(31171d3cf4ee7744d835f072e124487a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_depth.png using Guid(31171d3cf4ee7744d835f072e124487a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5bad883c43719f9c8bbaf1ed581cba69') in 0.0825695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_2.wav
  artifactKey: Guid(8fb58ecd8a1a3e74b9416798966d6f09) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_2.wav using Guid(8fb58ecd8a1a3e74b9416798966d6f09) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '920ba92736f006eaceb426b418c821d5') in 0.1534393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_A_Loop_1.wav
  artifactKey: Guid(9e58fa9dbada7984a9a34d9c2c043d97) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_A_Loop_1.wav using Guid(9e58fa9dbada7984a9a34d9c2c043d97) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a9fccc6f993b75fb54969663a8eb065') in 0.7427656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue_Ombre.png
  artifactKey: Guid(900a025807a5f50498785a6f2f4241cc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue_Ombre.png using Guid(900a025807a5f50498785a6f2f4241cc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e55cb61639244acd99f1fa22d668471e') in 0.0931746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL45_Root.FBX
  artifactKey: Guid(226234f5b9efab74fb550eb009a1acfb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL45_Root.FBX using Guid(226234f5b9efab74fb550eb009a1acfb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4df0f15d510f38b268094f81e419d8f2') in 0.099157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_B_Root.FBX
  artifactKey: Guid(dad2882ac8400924e9ddf0fe2924cd8a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_B_Root.FBX using Guid(dad2882ac8400924e9ddf0fe2924cd8a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '229d0cfdbd6b03428eb7c5c935f5ab35') in 0.062991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Seawave_Loop_1.wav
  artifactKey: Guid(aa23eb1ae5fa8614aa342437f178dc54) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Seawave_Loop_1.wav using Guid(aa23eb1ae5fa8614aa342437f178dc54) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fbd782b0735b24578dcdb911378bfa6') in 0.4995771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Front.FBX
  artifactKey: Guid(94fbe76a3268b6d49a883496d28f2327) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Front.FBX using Guid(94fbe76a3268b6d49a883496d28f2327) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17f584253ee05bb1a0eb08b283f5afbb') in 0.0573416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_02_Grayscale.png
  artifactKey: Guid(13ae042badc498940bb011d4f29dd8ec) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_02_Grayscale.png using Guid(13ae042badc498940bb011d4f29dd8ec) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8e176ff13c7296c76ad7c6da4489ba2') in 0.0651933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_19.mat
  artifactKey: Guid(3fea109eddcb4d34893d8987001d4c78) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_19.mat using Guid(3fea109eddcb4d34893d8987001d4c78) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5557f0cc0c50ffdd2b95e556743fda12') in 0.228442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Floor.png
  artifactKey: Guid(dca6a23036ffa724f84c70433be65f4c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Floor.png using Guid(dca6a23036ffa724f84c70433be65f4c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e66676bb56439a0ef6f4b8558c67470a') in 0.0702009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_Damage_ver_B_Root.FBX
  artifactKey: Guid(27033600627582442a103c7732f94112) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_Damage_ver_B_Root.FBX using Guid(27033600627582442a103c7732f94112) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '325ccacdbc783f237ac496c2d3ebdefe') in 0.0680692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR90_Root.FBX
  artifactKey: Guid(fd2b1c1d1d70a36458f8bd88924e76a5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR90_Root.FBX using Guid(fd2b1c1d1d70a36458f8bd88924e76a5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffdd9495605c36013a8fad6d4608fc59') in 0.0628277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Start.FBX
  artifactKey: Guid(48e1b2f47114213479d757a6e854bda7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Start.FBX using Guid(48e1b2f47114213479d757a6e854bda7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98595ab41224814486bc04e413db8b98') in 0.0652138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000161 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Front.FBX
  artifactKey: Guid(4e9e70fea21401b4b97307b639b84759) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Front.FBX using Guid(4e9e70fea21401b4b97307b639b84759) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c02f10cf618afe4420fa26b728450c3b') in 0.0786342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Start.FBX
  artifactKey: Guid(699f8c1a79281134fa982cc9dc9a66c8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Start.FBX using Guid(699f8c1a79281134fa982cc9dc9a66c8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23881003464209018620a2002dc8b331') in 0.0629762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Smash_Double_Inplace.FBX
  artifactKey: Guid(c5f51b1d7f2ea4d4f984fc4f7268d854) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Smash_Double_Inplace.FBX using Guid(c5f51b1d7f2ea4d4f984fc4f7268d854) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7f470edb17d36bc6c59433ece2595b5') in 0.0854578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_3.FBX
  artifactKey: Guid(767b33b2c087a5a4d9f2ae70fe712240) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_3.FBX using Guid(767b33b2c087a5a4d9f2ae70fe712240) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51e05d1d648228cc165f98802474a556') in 0.0614914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Down_Loop.FBX
  artifactKey: Guid(245c3391f34d01c408efffe3567c1338) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Down_Loop.FBX using Guid(245c3391f34d01c408efffe3567c1338) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c89179b10b4453627d695689c0581d11') in 0.0890675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_Root_ver_B.FBX
  artifactKey: Guid(9a4c6b38c48754a4082c05cfa8fc55bb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_Root_ver_B.FBX using Guid(9a4c6b38c48754a4082c05cfa8fc55bb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57a873a91e7134023b56ecfa62225ea2') in 0.0536167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_A_Idle_Root.FBX
  artifactKey: Guid(f4c413f937314e446b48fd541fa743b1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_A_Idle_Root.FBX using Guid(f4c413f937314e446b48fd541fa743b1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd577251346e5d884b736a1a3eccb0306') in 0.0553721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_Root_ver_B.FBX
  artifactKey: Guid(c446ed76b80fe064a9e70d0555a6d27f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_Root_ver_B.FBX using Guid(c446ed76b80fe064a9e70d0555a6d27f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '331fc34b22bc31fa783f1a785fa4b7f4') in 0.0616007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/StandUp_Revenge/M_Big_Sword@StandUp_Revenge.FBX
  artifactKey: Guid(9bc729be72dc3b7468cf9e9dc7c305e5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/StandUp_Revenge/M_Big_Sword@StandUp_Revenge.FBX using Guid(9bc729be72dc3b7468cf9e9dc7c305e5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '801dadc811d40205b8d80410bf79f7db') in 0.0699021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Flying_ver_A.FBX
  artifactKey: Guid(1d54a87af139bbb4f939b3e037e115d1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Flying_ver_A.FBX using Guid(1d54a87af139bbb4f939b3e037e115d1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef50c3b5711524f18fad6cf7d128641c') in 0.05718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/5__Upper_Attack/<EMAIL>
  artifactKey: Guid(a849ddc28882ee34a9e464e8b74d81a5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/5__Upper_Attack/<EMAIL> using Guid(a849ddc28882ee34a9e464e8b74d81a5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e85a0e5cb8a8e3b30b14cdd0fe9d77b') in 0.0617706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_ver_A.FBX
  artifactKey: Guid(38e94f084e0111341a0ef6ac60f43052) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_ver_A.FBX using Guid(38e94f084e0111341a0ef6ac60f43052) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c1497a11125048c8f58eb2ca27f298b9') in 0.074662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/4__Right/M_Big_Sword@Damage_Right_Small_ver_B.FBX
  artifactKey: Guid(44102e0e4596e5943aefd16ef83cd104) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/4__Right/M_Big_Sword@Damage_Right_Small_ver_B.FBX using Guid(44102e0e4596e5943aefd16ef83cd104) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0dc93027ef5446ff5339fda57394b07') in 0.0582079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR90.FBX
  artifactKey: Guid(cb051e2d0b4b2ae40a7ce55d18f27a83) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR90.FBX using Guid(cb051e2d0b4b2ae40a7ce55d18f27a83) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b6f5d9289c4d62874fc91d5834da1b7') in 0.0685644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_ambient.png
  artifactKey: Guid(8db715e578fca4444a88a4397ada29fe) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_ambient.png using Guid(8db715e578fca4444a88a4397ada29fe) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffc64521fa34058149e99fa1a4fde3bb') in 0.0691016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_09.png
  artifactKey: Guid(fcdd549aa24661a45a818218c4d377aa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_09.png using Guid(fcdd549aa24661a45a818218c4d377aa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fefaf4f363d8768f7d263e1054d56a1e') in 0.0657968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_ver_A.FBX
  artifactKey: Guid(738b43c856812d44485cce817b79ea06) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_ver_A.FBX using Guid(738b43c856812d44485cce817b79ea06) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '15f716fb5b126df29820fa65c448b441') in 0.0569271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 144

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_normal.png
  artifactKey: Guid(d26aec363a7ce734d95df755f9c26f12) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_normal.png using Guid(d26aec363a7ce734d95df755f9c26f12) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35c08235f5cbd08c80a3fc3ce0037597') in 0.0979031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_2_ZeroHeight.FBX
  artifactKey: Guid(784cdf2e0a625ae488661025068dc4bb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_2_ZeroHeight.FBX using Guid(784cdf2e0a625ae488661025068dc4bb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f868a284c14cdbd9451da6f09a24a72') in 0.0833802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 322

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop_ZeroHeight.FBX
  artifactKey: Guid(278a20928cf87054ebeeb1fb49277f3d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop_ZeroHeight.FBX using Guid(278a20928cf87054ebeeb1fb49277f3d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d3d2c94ec2d2684d23b9d4946fc2229') in 0.0931947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_End_ZeroHeight_Z0.FBX
  artifactKey: Guid(bf0f76e83e76a1e40868eb0bff0808df) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_End_ZeroHeight_Z0.FBX using Guid(bf0f76e83e76a1e40868eb0bff0808df) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d2ed22bfb9a28334f211458de62ba5e') in 0.0602853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_Attach_ZeroHeight.FBX
  artifactKey: Guid(47936e26ea410a742a81ecf40afec92f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_Attach_ZeroHeight.FBX using Guid(47936e26ea410a742a81ecf40afec92f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ffb2fe4867626542d3d8f7eb7255246') in 0.0736965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 322

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/M_katana_Blade@UpperAttack_ZeroHeight.FBX
  artifactKey: Guid(2637acc7d9f79ce448b4c5a97da272e3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/M_katana_Blade@UpperAttack_ZeroHeight.FBX using Guid(2637acc7d9f79ce448b4c5a97da272e3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46cd14bdc66c6def5521892180abdb31') in 0.069934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_B.FBX
  artifactKey: Guid(2047e28c372d7564ca25757e1b0c7a42) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_B.FBX using Guid(2047e28c372d7564ca25757e1b0c7a42) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08d6ab46d69c470e5e956a449058372d') in 0.0664503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End_ZeroHeight_Z0.FBX
  artifactKey: Guid(43bf8223f156eb046aa3e03a6a89b601) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End_ZeroHeight_Z0.FBX using Guid(43bf8223f156eb046aa3e03a6a89b601) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a44f0f05bf30d9d217bf7d08f5949137') in 0.0532072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_6_Attach.FBX
  artifactKey: Guid(7b1168e662c285e4da6d578e5345600e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_6_Attach.FBX using Guid(7b1168e662c285e4da6d578e5345600e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9af82c2160842dcd7f587e40180f5520') in 0.0688907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_3_Inplace.FBX
  artifactKey: Guid(7f46598d4cc8dfd49b2196a1f2c4927b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_3_Inplace.FBX using Guid(7f46598d4cc8dfd49b2196a1f2c4927b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '363c21822bc44ffa4abe723967746e18') in 0.0638226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_3.FBX
  artifactKey: Guid(9750ad9c43c60b549a309193b19d7300) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_3.FBX using Guid(9750ad9c43c60b549a309193b19d7300) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '984f53d299e7fa5f89a087e6cffcece7') in 0.0910521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_Attach.FBX
  artifactKey: Guid(afaf2b4d5f152264ba2fae2f7002b903) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_Attach.FBX using Guid(afaf2b4d5f152264ba2fae2f7002b903) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8d1908780df5084f47ef3ce4b12f741') in 0.2262449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/<EMAIL>
  artifactKey: Guid(024f67ce97696a64da4b6935b768cf21) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/<EMAIL> using Guid(024f67ce97696a64da4b6935b768cf21) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c34dca5815ef8dae1d88000649d1902e') in 0.0871746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_C.FBX
  artifactKey: Guid(f071657f8ba11e4419f706b7537b3bec) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_C.FBX using Guid(f071657f8ba11e4419f706b7537b3bec) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5d266cbcf16cbabec5e0841798f9cc3') in 0.0769788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmallSplashCenter_01_6x8.png
  artifactKey: Guid(823eccf74191b624292f96147a7b325f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmallSplashCenter_01_6x8.png using Guid(823eccf74191b624292f96147a7b325f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '163bb17bdec603166071aab8387765be') in 0.0551106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.FBX
  artifactKey: Guid(64d3edc443a8c1a48a1722999484635f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.FBX using Guid(64d3edc443a8c1a48a1722999484635f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3c8355d81ebb0efc9c54bdbe8d5fb1c') in 1.2302192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_65.prefab
  artifactKey: Guid(f5c74fabb9276d54790330d2338edc09) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_65.prefab using Guid(f5c74fabb9276d54790330d2338edc09) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ae7217deb3d3c54da2c0f80ecb81492') in 1.4773897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_7.fbx
  artifactKey: Guid(abc00000000011178157311759339839) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_7.fbx using Guid(abc00000000011178157311759339839) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cafac7aa5a02246647464c4869a991ab') in 0.0953446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_03.prefab
  artifactKey: Guid(abc00000000013911918069940478644) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_03.prefab using Guid(abc00000000013911918069940478644) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54d9d80e51b216910272d21f27d1c3dd') in 0.137458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books.prefab
  artifactKey: Guid(abc00000000012964185573243371773) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books.prefab using Guid(abc00000000012964185573243371773) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3bb37463ff5721b39c5a0b1305eda3df') in 0.0682573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_05.prefab
  artifactKey: Guid(abc00000000011747994935644742707) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_05.prefab using Guid(abc00000000011747994935644742707) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3272d1cee6d3a241e22c2eb3b868c5a') in 0.0880596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000153 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Ivy_A.mat
  artifactKey: Guid(abc00000000006349057990624982018) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Ivy_A.mat using Guid(abc00000000006349057990624982018) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c904e75c0ceac64de98a630106ad2df6') in 0.050274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Apple.prefab
  artifactKey: Guid(abc00000000001693895618719668371) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Apple.prefab using Guid(abc00000000001693895618719668371) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e91782bb61b05d1bfce76f9795fa6a7') in 0.096497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000107 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_12.FBX
  artifactKey: Guid(ec8c52408d4f2bb409808af539d5d716) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_12.FBX using Guid(ec8c52408d4f2bb409808af539d5d716) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f0a760766aac738c2f203faeaf269b3') in 0.1075382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.FBX
  artifactKey: Guid(a3ec491c15e6bcf46aec7ea7088eb06a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.FBX using Guid(a3ec491c15e6bcf46aec7ea7088eb06a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d487d29b042cb48499ae1db1876646a') in 0.0978614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_02.prefab
  artifactKey: Guid(abc00000000000686510749062123180) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_02.prefab using Guid(abc00000000000686510749062123180) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25be34690fda5cb0b5672a535e7b1790') in 0.1037581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_01.prefab
  artifactKey: Guid(abc00000000006619999490115597816) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_01.prefab using Guid(abc00000000006619999490115597816) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd853a535848aab4b02ad07a327af9dfd') in 0.0733767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack_1.prefab
  artifactKey: Guid(abc00000000010125621650837922687) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack_1.prefab using Guid(abc00000000010125621650837922687) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41ff88615660a2a35ed0c4478b7694d5') in 0.0642454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Carrot.prefab
  artifactKey: Guid(abc00000000005070647827898157856) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Carrot.prefab using Guid(abc00000000005070647827898157856) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b948df869275526229539026d0af9378') in 0.059582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_10.prefab
  artifactKey: Guid(abc00000000008244969905526030465) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_10.prefab using Guid(abc00000000008244969905526030465) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da7f1f3683e5b41c5fe9d959959d52d5') in 0.0685776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.prefab
  artifactKey: Guid(aabff765ac55f2d48aa652fdfaeb9ba1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.prefab using Guid(aabff765ac55f2d48aa652fdfaeb9ba1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b41819b99a7501b79494e13371fa6a8') in 0.1326506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_11.prefab
  artifactKey: Guid(abc00000000007911978258118896312) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_11.prefab using Guid(abc00000000007911978258118896312) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a887f7ad7b15d246d7ee97df66d2957') in 0.1798858 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01_1.prefab
  artifactKey: Guid(abc00000000006961437410837463222) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01_1.prefab using Guid(abc00000000006961437410837463222) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '174a5fc8030d5dbf7d43ee40c8f48c62') in 0.07612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cobblestone_A.mat
  artifactKey: Guid(abc00000000013236273414641559398) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cobblestone_A.mat using Guid(abc00000000013236273414641559398) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4ddc6ac2488a781b40fdc96ec8ede26') in 0.0484311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_15.prefab
  artifactKey: Guid(abc00000000002259975432115874212) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_15.prefab using Guid(abc00000000002259975432115874212) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27a20497636da20bfe10889f235a3c09') in 0.083379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000094 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles.mat
  artifactKey: Guid(abc00000000011734364806135559496) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles.mat using Guid(abc00000000011734364806135559496) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93ed6ed6934712e7e2fc9fc17685fe07') in 0.0452007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Metal.mat
  artifactKey: Guid(abc00000000015081879039839770399) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Metal.mat using Guid(abc00000000015081879039839770399) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9eb5ccc7c27388c027801a9914460e4') in 0.0587252 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_A_01.prefab
  artifactKey: Guid(abc00000000009120234373621427756) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_A_01.prefab using Guid(abc00000000009120234373621427756) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3ebf9bd1c9d1b1e7c77cb374ccf424b') in 0.0701504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_02.prefab
  artifactKey: Guid(abc00000000009808437911761296293) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_02.prefab using Guid(abc00000000009808437911761296293) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab3f09cee5ffc38aa7fe329342c60ea4') in 0.0749629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_02.prefab
  artifactKey: Guid(abc00000000017834578992296274955) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_02.prefab using Guid(abc00000000017834578992296274955) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ef21f9bcf5d7c532aea1f4eb0993947') in 0.0580619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_27.prefab
  artifactKey: Guid(abc00000000004106986877503133980) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_27.prefab using Guid(abc00000000004106986877503133980) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e6d0c3e6176ddf71fb9c4f38adaedcf2') in 0.0898641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_Pillar.prefab
  artifactKey: Guid(abc00000000009946615682477863275) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_Pillar.prefab using Guid(abc00000000009946615682477863275) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec740ad704852eec226d97c816fd51fa') in 0.0762728 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_15.prefab
  artifactKey: Guid(abc00000000013862869448088672586) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_15.prefab using Guid(abc00000000013862869448088672586) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '508a8555e0c964a337297a0cfeab72c0') in 0.0638948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_30.prefab
  artifactKey: Guid(abc00000000013918172512350973120) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_30.prefab using Guid(abc00000000013918172512350973120) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a96831c90c6a15b1e0969fe1e0b209e') in 0.1756106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Flagon_01.prefab
  artifactKey: Guid(abc00000000009001316097004371478) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Flagon_01.prefab using Guid(abc00000000009001316097004371478) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04c489896215ecd45d125046f9018aa8') in 0.0683232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000288 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_13.prefab
  artifactKey: Guid(abc00000000017349817595143904948) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_13.prefab using Guid(abc00000000017349817595143904948) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73568de00fe00448a906f0dbfa255c3e') in 0.0617397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch.prefab
  artifactKey: Guid(abc00000000006919238686442401839) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch.prefab using Guid(abc00000000006919238686442401839) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9639bb67a265bb60c59721438edc274c') in 0.074358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_02.mat
  artifactKey: Guid(2886fb331f3821f40a71faefe8c31260) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_02.mat using Guid(2886fb331f3821f40a71faefe8c31260) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6572486c31bba2289088969b353bcf8a') in 0.335365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart.prefab
  artifactKey: Guid(abc00000000011749161118377129527) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart.prefab using Guid(abc00000000011749161118377129527) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47051fc963135157545137c2e1d2db8a') in 0.0638967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_1.prefab
  artifactKey: Guid(abc00000000015828755011961175657) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_1.prefab using Guid(abc00000000015828755011961175657) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f51038b975b9a62d078573489ed1f599') in 0.0585332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ship_Cargo_01.prefab
  artifactKey: Guid(abc00000000013640382918445301060) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ship_Cargo_01.prefab using Guid(abc00000000013640382918445301060) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b051b334a99a93559524b746ddedfd44') in 0.0635928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_01.prefab
  artifactKey: Guid(abc00000000008651461517677355870) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_01.prefab using Guid(abc00000000008651461517677355870) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4084b6de7aa90e0ec10c1ed34cc2b1cd') in 0.0697199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01.prefab
  artifactKey: Guid(abc00000000014892343687679203641) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01.prefab using Guid(abc00000000014892343687679203641) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6f36514fa944613203b765273d63d30') in 0.0571045 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08.prefab
  artifactKey: Guid(abc00000000016303654725886764330) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08.prefab using Guid(abc00000000016303654725886764330) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c129239161c1197443cc6069ca4591eb') in 0.0641431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_01.prefab
  artifactKey: Guid(abc00000000013811827313343407570) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_01.prefab using Guid(abc00000000013811827313343407570) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2279476011d9f47d1e553c458603f60') in 0.0705737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05.prefab
  artifactKey: Guid(abc00000000005759585731758339980) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05.prefab using Guid(abc00000000005759585731758339980) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '583a8bb22293f468c5ae0deb32143de7') in 0.0659622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pot.prefab
  artifactKey: Guid(abc00000000014327047588651083102) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pot.prefab using Guid(abc00000000014327047588651083102) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad958a32d30ec08e37f1bf71b9b5e4eb') in 0.0633793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_03.prefab
  artifactKey: Guid(abc00000000006725378370871592864) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_03.prefab using Guid(abc00000000006725378370871592864) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35168608c183e0543a018a94170de563') in 0.0627594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_02.prefab
  artifactKey: Guid(abc00000000018123052294421716965) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_02.prefab using Guid(abc00000000018123052294421716965) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da6d2210e05bb1faa8077d70a04152b5') in 0.0564466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool_Short.prefab
  artifactKey: Guid(abc00000000007793134848975468829) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool_Short.prefab using Guid(abc00000000007793134848975468829) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63ddbacc11c5a45d86395d74aaee704f') in 0.0561387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WeaponStand.prefab
  artifactKey: Guid(abc00000000008052730962525161391) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WeaponStand.prefab using Guid(abc00000000008052730962525161391) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5bf71535c931229aeadd315ae13207c5') in 0.0694171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_B_01.prefab
  artifactKey: Guid(abc00000000008459799861029584559) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_B_01.prefab using Guid(abc00000000008459799861029584559) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd146ee9f40aa5fc0147695a823f2bd4') in 0.0764461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01_Splines_31.prefab
  artifactKey: Guid(abc00000000011714369972703239576) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01_Splines_31.prefab using Guid(abc00000000011714369972703239576) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7aa8f17ca8a6378fcfbdcab10eb7381a') in 0.1413785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/MudFootPrints.terrainlayer
  artifactKey: Guid(d44fae7a1d7960e4bbb7603c1338d537) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/MudFootPrints.terrainlayer using Guid(d44fae7a1d7960e4bbb7603c1338d537) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b035948bb0e6333313a794d77b35f15e') in 0.1056765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_04.prefab
  artifactKey: Guid(abc00000000003963479744631661368) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_04.prefab using Guid(abc00000000003963479744631661368) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2201009a1ef5754b127fbc2bcad6539') in 0.061928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000838 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Blend.shadergraph
  artifactKey: Guid(019fc7858e91d824d81e78aead3e2eee) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Blend.shadergraph using Guid(019fc7858e91d824d81e78aead3e2eee) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ccc32f91921b590b50b6b6c4db2f648') in 0.0445189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000237 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_02.prefab
  artifactKey: Guid(abc00000000001886984038499315504) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_02.prefab using Guid(abc00000000001886984038499315504) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74ff1b202cf4b8c0f1ac9c8a912e3dd6') in 0.0778989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Throne.prefab
  artifactKey: Guid(abc00000000000467259060501473599) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Throne.prefab using Guid(abc00000000000467259060501473599) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a40bb36c9b94644d041286e21fcb53ec') in 0.0781288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ancient_greek_stone_wall_basecolor.PNG
  artifactKey: Guid(abb691a238ce83348a3225fac0ff13c6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ancient_greek_stone_wall_basecolor.PNG using Guid(abb691a238ce83348a3225fac0ff13c6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9fa25e01e91b0c4f2318ccd4d8bedd8') in 0.0484222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sledge_Hammer.prefab
  artifactKey: Guid(abc00000000015290214657353769420) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sledge_Hammer.prefab using Guid(abc00000000015290214657353769420) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '743d51b8193cf6f1e595fabcea379488') in 0.0648088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Grass.terrainlayer
  artifactKey: Guid(efe40aba052e892478ef2998733559b7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Grass.terrainlayer using Guid(efe40aba052e892478ef2998733559b7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e63d714b9f9c10b726a042940e38340') in 0.0416772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_ORMH.PNG
  artifactKey: Guid(8342084fc99b07546883724e9b675329) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_ORMH.PNG using Guid(8342084fc99b07546883724e9b675329) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '555083d1f684815c13eb7e9e8277c469') in 0.0693138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_Mask_3.PNG
  artifactKey: Guid(fe205b6bfee14d44ca84f876f4b4808b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_Mask_3.PNG using Guid(fe205b6bfee14d44ca84f876f4b4808b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4652b0324751f732a97809b4f0c06c85') in 0.0835223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_01 - Copy.PNG
  artifactKey: Guid(948761d5d3e04c24ba02daae7853930a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_01 - Copy.PNG using Guid(948761d5d3e04c24ba02daae7853930a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d7777944a2d991daca22a4c6b125b00') in 0.0608944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(0ae9ed58cf129aa4c8bcae492bee827f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_OcclusionRoughnessMetallic.PNG using Guid(0ae9ed58cf129aa4c8bcae492bee827f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a907fd373c8d133a979a01514d87b747') in 0.0477723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_IndividualTile_normal.PNG
  artifactKey: Guid(21f37128242f3494da2db27336812ee3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_IndividualTile_normal.PNG using Guid(21f37128242f3494da2db27336812ee3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b27b66ebe6cf8d67b7a06e28217c63c2') in 0.045379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02_1.prefab
  artifactKey: Guid(abc00000000002548068665834369709) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02_1.prefab using Guid(abc00000000002548068665834369709) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2058ae81f08af773bddb86cd4345ea79') in 0.0667921 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HighBackChair.prefab
  artifactKey: Guid(abc00000000012652205390547645271) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HighBackChair.prefab using Guid(abc00000000012652205390547645271) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '853b12ca49a61769500b58d130acaab8') in 0.0621038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_Basecolour.PNG
  artifactKey: Guid(356aacd52dbc2a947bca1f0a360c3f53) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_Basecolour.PNG using Guid(356aacd52dbc2a947bca1f0a360c3f53) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a39bae87aad8aba2f5a2661163cc4b33') in 0.048548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_grass_Normal.PNG
  artifactKey: Guid(a558aa5f5d11dc74490f4c6669ce8189) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_grass_Normal.PNG using Guid(a558aa5f5d11dc74490f4c6669ce8189) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '920a9ddb00454cc8de623f0b44c887f4') in 0.0707636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_BC.PNG
  artifactKey: Guid(1c9f05536d281d04f864dd9466254f4b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_BC.PNG using Guid(1c9f05536d281d04f864dd9466254f4b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3d8279c1d919569714546358868307f') in 0.0795592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_Normal.PNG
  artifactKey: Guid(9ce4933cf1674cd42b72669f1d353a5b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_Normal.PNG using Guid(9ce4933cf1674cd42b72669f1d353a5b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd282436e21f9382bc0e1dcf70b25a2de') in 0.0547964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000181 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Prop_Mask_256.PNG
  artifactKey: Guid(78dd3fbb5530f5d49988cbaed300c1ed) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Prop_Mask_256.PNG using Guid(78dd3fbb5530f5d49988cbaed300c1ed) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3adcc6d3b1544731e2e9d4095ef71a31') in 0.1020336 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_ORM.PNG
  artifactKey: Guid(5b6cd9477e8e78c4187f2226346d12f5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_ORM.PNG using Guid(5b6cd9477e8e78c4187f2226346d12f5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a420c866c765525cf727eccffc9a7bb') in 0.100377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mudFootprints_BaseColor.PNG
  artifactKey: Guid(55f37bbf66b30a44b8a803820924f515) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mudFootprints_BaseColor.PNG using Guid(55f37bbf66b30a44b8a803820924f515) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56018866fbe4e8cbf652d96d61934bf7') in 0.0556501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_MetalTiling_01_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(a23022c5b19d1be43bb5e2f02a84559a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_MetalTiling_01_OcclusionRoughnessMetallic.PNG using Guid(a23022c5b19d1be43bb5e2f02a84559a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aae7b7b3d158b3d632aa0e40b3eac6e8') in 0.0501445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Perlin_01.PNG
  artifactKey: Guid(9e1ab92cc65029f4095ac011e4eea1c2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Perlin_01.PNG using Guid(9e1ab92cc65029f4095ac011e4eea1c2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40020457bec0359872b939f6d3bd80f0') in 0.0679307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_N_2.PNG
  artifactKey: Guid(3dce0648e26448949b041ff806efa181) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_N_2.PNG using Guid(3dce0648e26448949b041ff806efa181) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7958546dabe321ca6a21ce5fcd05044e') in 0.0491671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Perlin_03.PNG
  artifactKey: Guid(b3ccecd274f5cf14e8eeacfdb57410d7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Perlin_03.PNG using Guid(b3ccecd274f5cf14e8eeacfdb57410d7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98c934814bcc8cb0f278afbbb4f56120') in 0.0753576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_02_N.PNG
  artifactKey: Guid(ba4effaeb0a12e64793d799366d444b0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_02_N.PNG using Guid(ba4effaeb0a12e64793d799366d444b0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90ebe75fd40d4f3330c9225fbdb63624') in 0.0545564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_normal.PNG
  artifactKey: Guid(ada667aa21767c143a6dffc827320e84) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_normal.PNG using Guid(ada667aa21767c143a6dffc827320e84) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1972fc052f5ab7c17ed6de9da921107') in 0.072572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(2d5936fdb09b98e4598ef14654f0fb5b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_OcclusionRoughnessMetallic.PNG using Guid(2d5936fdb09b98e4598ef14654f0fb5b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09da1e8b74de8afc61bcaa3a9f4d1ef4') in 0.0461562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wind_Noise_01.PNG
  artifactKey: Guid(7f602cc958ff08a4e94bf460b9ea3ded) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wind_Noise_01.PNG using Guid(7f602cc958ff08a4e94bf460b9ea3ded) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bdef4a533969d8a2ce5f3c4604546489') in 0.0643187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000138 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(5042a66b3246fc840a4e683091267504) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(5042a66b3246fc840a4e683091267504) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e57ee4b783dc0f9ea47b655935752b1') in 0.0378306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL>
  artifactKey: Guid(e23ec565e5d40ad4d94e0af6010f22c8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL> using Guid(e23ec565e5d40ad4d94e0af6010f22c8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ededafdb0bfec73b1e1d7e54ca37d9a8') in 0.0695929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_ForwardRight.controller
  artifactKey: Guid(a88929c33e2ee094e951a9e838efe857) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_ForwardRight.controller using Guid(a88929c33e2ee094e951a9e838efe857) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad9e66aecba18932e31cbb23f92f2af8') in 0.0358614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Left.controller
  artifactKey: Guid(2aa7d8361dd94e74ca3b21df4f5c2bca) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Left.controller using Guid(2aa7d8361dd94e74ca3b21df4f5c2bca) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '929766d6839d7e95e68343465dfe6fec') in 0.0387803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Forward.controller
  artifactKey: Guid(a760f31009ba80249abbf70da8383e49) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Forward.controller using Guid(a760f31009ba80249abbf70da8383e49) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fb79ea69c7c39d4a8398dfa72cc7ed7') in 0.0338901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Backward.controller
  artifactKey: Guid(ca6e6e7474b077e48b5bba70014d57ad) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Backward.controller using Guid(ca6e6e7474b077e48b5bba70014d57ad) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd07672d298190a4d9c0952a82596d4f1') in 0.0364159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ground_Moss_N.PNG
  artifactKey: Guid(daab4a04e9dc48b4cae6e0399dd3ab45) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ground_Moss_N.PNG using Guid(daab4a04e9dc48b4cae6e0399dd3ab45) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e11d1f0a7a592e6a91f819d6dfa172f3') in 0.0483496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterNormal.png
  artifactKey: Guid(6c3effda0f2755345ab853f9f0756fac) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterNormal.png using Guid(6c3effda0f2755345ab853f9f0756fac) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62de6980cfe7a6a80712ca01ff137e78') in 0.04732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_L90_Root.FBX
  artifactKey: Guid(dc7744ee153323444a3e6d148a2fbcc1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_L90_Root.FBX using Guid(dc7744ee153323444a3e6d148a2fbcc1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7e8d8340dc98a718233bb93c07c880c') in 0.059655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_Turn_L90.FBX
  artifactKey: Guid(945b3d3413800004a8c208ca7982961b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_Turn_L90.FBX using Guid(945b3d3413800004a8c208ca7982961b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51dfe551addabe630f66ba8b75fcc5ff') in 0.0591159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_B.FBX
  artifactKey: Guid(64b28687318914d438c24badcbfe2e83) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_B.FBX using Guid(64b28687318914d438c24badcbfe2e83) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89e81fcf4703493ca8f5d51d0808e916') in 0.0720395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Melodic_2.wav
  artifactKey: Guid(9d9d02c7940bbdf4faadfa767ca0d80e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Melodic_2.wav using Guid(9d9d02c7940bbdf4faadfa767ca0d80e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e92fd6499a0b30b73c851083aebf524f') in 0.1274537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_B_Turn_L90.FBX
  artifactKey: Guid(d2b29e5ed8d78834886607c6c764ea39) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_B_Turn_L90.FBX using Guid(d2b29e5ed8d78834886607c6c764ea39) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dce0bcc81b994d5eae09d4f5c249fb8a') in 0.0587705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/SelfDestruct.cs
  artifactKey: Guid(42a3b1a9bd9f968458f42e69fe6dc509) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/SelfDestruct.cs using Guid(42a3b1a9bd9f968458f42e69fe6dc509) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fdf90cf6157f4fd2176e0a134484bec4') in 0.0343704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_B_Root.FBX
  artifactKey: Guid(4a958ed7d50d90746a3dba2375494668) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_B_Root.FBX using Guid(4a958ed7d50d90746a3dba2375494668) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0c0c1319b1b76e45996826a1c60e478') in 0.0963243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_R90.FBX
  artifactKey: Guid(582a652dbc7458d4f9db7209c23a4ffb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_R90.FBX using Guid(582a652dbc7458d4f9db7209c23a4ffb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbb1d9c1bd45b76506c643f83f0d5e50') in 0.0658927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_B_Turn_L90_Root.FBX
  artifactKey: Guid(3eee00f05c88a2c41a291a9198d62521) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_B_Turn_L90_Root.FBX using Guid(3eee00f05c88a2c41a291a9198d62521) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d89605cc61183365c1f089222db1aa7') in 0.0738913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_BR45.FBX
  artifactKey: Guid(bf51c96cd80a4ea4bbce7e196e51775e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_BR45.FBX using Guid(bf51c96cd80a4ea4bbce7e196e51775e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52495935f44ad91035efea3148a83a7b') in 0.0733205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_R90_Root.FBX
  artifactKey: Guid(07b1adfdf6d37364dad57bfa72807ff9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_R90_Root.FBX using Guid(07b1adfdf6d37364dad57bfa72807ff9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7de3abc7c30696574dddc65c004912c0') in 0.0610945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_A_Root.FBX
  artifactKey: Guid(d2547eff64769d4428b588e82291cd2b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_A_Root.FBX using Guid(d2547eff64769d4428b588e82291cd2b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66311a655c61316f36bc92bcc116d8eb') in 0.0617958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_Bwd.FBX
  artifactKey: Guid(42322e9eb45bbe44796033489e33f948) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_Bwd.FBX using Guid(42322e9eb45bbe44796033489e33f948) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '01afca47824c79d172e73a81270e17bd') in 0.099596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_L90.FBX
  artifactKey: Guid(49169bb93bc240341b825bd923da9c47) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_L90.FBX using Guid(49169bb93bc240341b825bd923da9c47) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '766f37797518a7e5ecd5418ead2ce136') in 0.0984412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_1.wav
  artifactKey: Guid(a35e0aa1bea32174f8620f08fad32241) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_1.wav using Guid(a35e0aa1bea32174f8620f08fad32241) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2707abdab278bccc6f33daa1b51255bf') in 0.0898754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L90_Root.FBX
  artifactKey: Guid(ae326781464224844bf52ad1cd0717ad) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L90_Root.FBX using Guid(ae326781464224844bf52ad1cd0717ad) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '636cf312366a16d9310ddb1839904050') in 0.0540465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_vol2.FBX
  artifactKey: Guid(664ae035356f9014ba387d69ffcea3cf) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_vol2.FBX using Guid(664ae035356f9014ba387d69ffcea3cf) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec8291c7a08664e0cf46dd48877cba04') in 0.0711337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_Root_vol2.FBX
  artifactKey: Guid(5e05fb7a9332fcf43a271bff046899df) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_Root_vol2.FBX using Guid(5e05fb7a9332fcf43a271bff046899df) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e2917f9866716f70413f8e4087964f3') in 0.0699175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_R90.FBX
  artifactKey: Guid(2d632430d6c9e7f4197960f403846d0a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_R90.FBX using Guid(2d632430d6c9e7f4197960f403846d0a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87ed2ca33ae4dc0b3d436e8127509fd3') in 0.0607357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_Root.FBX
  artifactKey: Guid(c2aa4866de4e19546966e3d2a319e7b7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_Root.FBX using Guid(c2aa4866de4e19546966e3d2a319e7b7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a871a4622755abc3c9d67e2990c5459') in 0.0554668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_Turn_L90.FBX
  artifactKey: Guid(2fbce467df4f1e043a949bd8d60bd61b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_Turn_L90.FBX using Guid(2fbce467df4f1e043a949bd8d60bd61b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e3675ae8fdddd79079cceab5c479bd8') in 0.0741912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_A_Root.FBX
  artifactKey: Guid(f286441fb1dbf0e419ea7b15b0a84014) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_A_Root.FBX using Guid(f286441fb1dbf0e419ea7b15b0a84014) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87f19561d18fc6a16d2050d54efbada6') in 0.0589497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90.FBX
  artifactKey: Guid(f16b1ac022eb64c43bdfa6aebe7152d8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90.FBX using Guid(f16b1ac022eb64c43bdfa6aebe7152d8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9178f8f9580fcfdc18d0a4e85db8941') in 0.0531747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000119 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90_vol2.FBX
  artifactKey: Guid(1883c20d0d807fd40878f4dd0b5a5d9a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90_vol2.FBX using Guid(1883c20d0d807fd40878f4dd0b5a5d9a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7430d8905eb600a7ff6a5fe0ed850f0a') in 0.0583213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L90.FBX
  artifactKey: Guid(2ccf25eadbaffb94696df3681a8e7635) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L90.FBX using Guid(2ccf25eadbaffb94696df3681a8e7635) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd9b2f5ab175817769d81b539fab025c') in 0.0618269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_A_Root.FBX
  artifactKey: Guid(76b513098cd934c458954bbb96f7b5d6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_A_Root.FBX using Guid(76b513098cd934c458954bbb96f7b5d6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ac7214ab6b64d5e3818554ae5b75740') in 0.059101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Walk_B.FBX
  artifactKey: Guid(8a8c49ae79576f645a0d38664e249f23) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Walk_B.FBX using Guid(8a8c49ae79576f645a0d38664e249f23) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fae1c49ea0061838c3ce9ba3a4fd2e1') in 0.058635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_B_Root.FBX
  artifactKey: Guid(1d33527831d905142b200cb7fb26b9c5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_B_Root.FBX using Guid(1d33527831d905142b200cb7fb26b9c5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'daecf6111bdb94b11ed6304413baffba') in 0.0728515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Jog_B_Root.FBX
  artifactKey: Guid(4f0e8079da752f74baa81b8378a8ffe6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Jog_B_Root.FBX using Guid(4f0e8079da752f74baa81b8378a8ffe6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b489b64be8a86e87fd90bb9d4fb09800') in 0.0617692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_A.FBX
  artifactKey: Guid(1a416920c0dd44f4595deabd17742db0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_A.FBX using Guid(1a416920c0dd44f4595deabd17742db0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64a77973ec028236cf4f60e8f045ad6c') in 0.0592118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_B_Root.FBX
  artifactKey: Guid(0601d27535a936740a7b732205766384) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_B_Root.FBX using Guid(0601d27535a936740a7b732205766384) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b41789dbb942345dd8f6bcf45bb75032') in 0.0721968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_B.FBX
  artifactKey: Guid(f88264b81b2858a449f77fdf64831f55) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_B.FBX using Guid(f88264b81b2858a449f77fdf64831f55) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '591776bb763a6e645168f876cc0a00e1') in 0.1466944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Walk_B_Turn_R90_Root.FBX
  artifactKey: Guid(82227ca8f78669c4f94692a611151512) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Walk_B_Turn_R90_Root.FBX using Guid(82227ca8f78669c4f94692a611151512) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ec32edf0f41f4fcac0742a35bb0a6b7') in 0.0592562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45_Root.FBX
  artifactKey: Guid(77a3d89d1deb05d43a6c6bbe5e083357) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45_Root.FBX using Guid(77a3d89d1deb05d43a6c6bbe5e083357) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ce0a7f88cece66f1bc20a93fd167a22') in 0.0676215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 144

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_FL45_Root.FBX
  artifactKey: Guid(c7a0c2dfebf6536499749cc8ee49556c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_FL45_Root.FBX using Guid(c7a0c2dfebf6536499749cc8ee49556c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c99cad3f804683c307b9ddeb4bb8f70e') in 0.0755363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Fwd_Root.FBX
  artifactKey: Guid(335d11e7ef564cc4c894e559ee3804c4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Fwd_Root.FBX using Guid(335d11e7ef564cc4c894e559ee3804c4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33e08438f7ef51de38fcc70e4a2b9d2d') in 0.060192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Run_B_Root.FBX
  artifactKey: Guid(303bdc56f2d7d064c92076c2e9748713) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Run_B_Root.FBX using Guid(303bdc56f2d7d064c92076c2e9748713) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8e448e2d1346168dc458d63df826743') in 0.0576284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45_Root.FBX
  artifactKey: Guid(6e3a361d41abf8e488923bc9558c0556) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45_Root.FBX using Guid(6e3a361d41abf8e488923bc9558c0556) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b3a9065aa75be860d4ab93157f215b8') in 0.0715973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Bwd_Root.FBX
  artifactKey: Guid(ccc4a3611e8e2e245a6e1a8abc291941) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Bwd_Root.FBX using Guid(ccc4a3611e8e2e245a6e1a8abc291941) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8687f75ed65b54b57af9d3a83207584') in 0.0629371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back.FBX
  artifactKey: Guid(9375c2b945d124841a646c89c572f444) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back.FBX using Guid(9375c2b945d124841a646c89c572f444) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d92f5534addce1f4110bf6a86c4790f') in 0.0553759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90.FBX
  artifactKey: Guid(67db3dba9ecc6404cb63114809e01cdd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90.FBX using Guid(67db3dba9ecc6404cb63114809e01cdd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c42517ba9c312658ea322343e63fe9d9') in 0.0562801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_Root.FBX
  artifactKey: Guid(60e1d0a3d59a3134d8da259d8c15933a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_Root.FBX using Guid(60e1d0a3d59a3134d8da259d8c15933a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f434c64364c52e64a246cc183e00099c') in 0.073707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90.FBX
  artifactKey: Guid(f275413e7865a514eb21a6e7a9c4fdd6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90.FBX using Guid(f275413e7865a514eb21a6e7a9c4fdd6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f600da02aa8969db21c42274065e96c') in 0.0599648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BL45.FBX
  artifactKey: Guid(1b933a4f12591ad4fbb05be589120bc6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BL45.FBX using Guid(1b933a4f12591ad4fbb05be589120bc6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '851c268a42c55fce23b98eb789a61859') in 0.0703021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BL45_Root.FBX
  artifactKey: Guid(b657a6495be320c498e3e8f17152a881) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BL45_Root.FBX using Guid(b657a6495be320c498e3e8f17152a881) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5dba3cb469761725c2a4a922cc44ac6c') in 0.0709042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_R90.FBX
  artifactKey: Guid(a8c6381b069fc1843a8a41896667959c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_R90.FBX using Guid(a8c6381b069fc1843a8a41896667959c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7c31ce1dcab2f34983f486bc1a0dd452') in 0.0645859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x4_01.fbx
  artifactKey: Guid(abc00000000016286373113789974431) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x4_01.fbx using Guid(abc00000000016286373113789974431) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26fc89d9aef921db435893e49d2d5f7d') in 0.0700568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_CobblePath_01.mat
  artifactKey: Guid(ee747888d36451d4f92c5dfbc4e7c1da) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_CobblePath_01.mat using Guid(ee747888d36451d4f92c5dfbc4e7c1da) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b5d10f530e9edd61d2acbabb5149fde') in 0.0595677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_06.mat
  artifactKey: Guid(abc00000000013376651248173773372) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_06.mat using Guid(abc00000000013376651248173773372) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b26bb3bf090c554d93a582612e28d68') in 0.0522152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_01.fbx
  artifactKey: Guid(abc00000000003553260543696848438) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_01.fbx using Guid(abc00000000003553260543696848438) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e766118617db913dedfe97113d2fc3bd') in 0.0705696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.fbx
  artifactKey: Guid(abc00000000003594834468008236435) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.fbx using Guid(abc00000000003594834468008236435) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '597c89c6c36ca2b8b69e304e18e8f5b9') in 0.0687799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_02.fbx
  artifactKey: Guid(abc00000000006800843991399951427) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_02.fbx using Guid(abc00000000006800843991399951427) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b690d4270502620b58348a1b9a8b22f0') in 0.0641962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Long_Rug.fbx
  artifactKey: Guid(abc00000000009521743668885644973) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Long_Rug.fbx using Guid(abc00000000009521743668885644973) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e531bb62b55959571605ca5f6a1636c') in 0.0767922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_WeaponStand.fbx
  artifactKey: Guid(abc00000000007725285439539085074) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_WeaponStand.fbx using Guid(abc00000000007725285439539085074) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '762b138ace0f94d7b79ec0dab8406923') in 0.0729714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/SM_Mountain_01.fbx
  artifactKey: Guid(abc00000000002337503925397360939) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/SM_Mountain_01.fbx using Guid(abc00000000002337503925397360939) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23e6d2bfec1919743e8cc1fa14a039e7') in 0.1212847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_TavernSign_01.fbx
  artifactKey: Guid(abc00000000000563410582851558729) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_TavernSign_01.fbx using Guid(abc00000000000563410582851558729) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afe94b7f81fb174349714a2877a0ae29') in 0.1185145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_02.prefab
  artifactKey: Guid(10c1bc824aeddf24abc4e903402f08b5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_02.prefab using Guid(10c1bc824aeddf24abc4e903402f08b5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efc599a6b952e5f58453a801bfb9baa5') in 0.0564522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/<EMAIL>
  artifactKey: Guid(73f1e892de373654ea6e3fff3ca15f76) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/<EMAIL> using Guid(73f1e892de373654ea6e3fff3ca15f76) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb8bc1594cdacccc92675295bd751a4e') in 0.0546019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Backward.fbx
  artifactKey: Guid(5694ab536c5e48a45873f22b86e57688) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Backward.fbx using Guid(5694ab536c5e48a45873f22b86e57688) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46801ccc786bb0aca1e3b61bf338625d') in 0.0927731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Turn/HumanF@Turn01_Left.fbx
  artifactKey: Guid(9cc63407e9eb8204babf8fc94b95f3e1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Turn/HumanF@Turn01_Left.fbx using Guid(9cc63407e9eb8204babf8fc94b95f3e1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f268466652280d5905a21c8335664af') in 0.0768989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Left.fbx
  artifactKey: Guid(435a4728904deaf41a2ea8928e6c2c35) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Left.fbx using Guid(435a4728904deaf41a2ea8928e6c2c35) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7239306ff045fe5f9b1202e69a02ab79') in 0.0592071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Cute_Generic_2.wav
  artifactKey: Guid(06a5b7cf155eba742a6dbdfd96ae0da6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Cute_Generic_2.wav using Guid(06a5b7cf155eba742a6dbdfd96ae0da6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7906ebf814b57e3a15e2c18ed7095386') in 0.1318523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_1.wav
  artifactKey: Guid(2462319ee1c57fc4685eb192626b5a6d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_1.wav using Guid(2462319ee1c57fc4685eb192626b5a6d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcf9ce7796a749e3bf36e95cf4f562c7') in 0.1682179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Watery_Generic_2.wav
  artifactKey: Guid(1f3f0922035f6c34593a1afd7ace65ab) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Watery_Generic_2.wav using Guid(1f3f0922035f6c34593a1afd7ace65ab) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e47f251a568f07e8626c2c980db85ba') in 0.1429388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Social/Conversation/<EMAIL>
  artifactKey: Guid(5277e5180e302184fb73653674a14c85) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Social/Conversation/<EMAIL> using Guid(5277e5180e302184fb73653674a14c85) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66c1bb106e53b183406dfb1cf83a0805') in 0.0672741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000312 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Cart.fbx
  artifactKey: Guid(abc00000000013612563247549863484) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Cart.fbx using Guid(abc00000000013612563247549863484) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c7a4335fb33c5d0f1e485f196ab80ae') in 0.0675737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Cart_Small.fbx
  artifactKey: Guid(abc00000000005158718346202127488) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Cart_Small.fbx using Guid(abc00000000005158718346202127488) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e89bdeaec692346b9c62e98e23a87fa6') in 0.0745199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_StoneWall_Algae1.mat
  artifactKey: Guid(f33b8c69286290842afbd16af6ed108f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_StoneWall_Algae1.mat using Guid(f33b8c69286290842afbd16af6ed108f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afedc64e0ca8ef482fa02d4774790ee0') in 0.0603347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Books.fbx
  artifactKey: Guid(abc00000000013468435735849733335) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Books.fbx using Guid(abc00000000013468435735849733335) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c132a9ab3b2c426fdb7235a39d4453d') in 0.0736279 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Book_Stack.fbx
  artifactKey: Guid(abc00000000008583438631122953529) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Book_Stack.fbx using Guid(abc00000000008583438631122953529) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd846d30a5fc04ea96cc40677df86a69a') in 0.067108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_XtraLong_Loose.fbx
  artifactKey: Guid(abc00000000008462815623000822561) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_XtraLong_Loose.fbx using Guid(abc00000000008462815623000822561) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e769dcf5a3a01ec004554a67537d9c64') in 0.0891534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneWall_Algae1.mat
  artifactKey: Guid(9be67b33fc054e64e9101b15d098ed85) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneWall_Algae1.mat using Guid(9be67b33fc054e64e9101b15d098ed85) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7c0be44f1c54b876bd8191a4361d8885') in 0.0545792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Bloking/Materials/No Name.mat
  artifactKey: Guid(147a69b91755bf24099827aede4beb35) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Bloking/Materials/No Name.mat using Guid(147a69b91755bf24099827aede4beb35) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c971007a8d98444e30ee68ef08ba550') in 0.056082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_InnerCorner_01.fbx
  artifactKey: Guid(abc00000000014681545349357883020) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_InnerCorner_01.fbx using Guid(abc00000000014681545349357883020) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a1a0282e8e0766f2f3f5a2739a9ab08') in 0.0810771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/SM_Rock_Large_A.fbx
  artifactKey: Guid(abc00000000012111391708470337783) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/SM_Rock_Large_A.fbx using Guid(abc00000000012111391708470337783) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b668790d9a6bd89177e7a2d92bec2929') in 0.0878629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_Forward [RM].fbx
  artifactKey: Guid(ece8783ced0147a47bd91b75de63ffc4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_Forward [RM].fbx using Guid(ece8783ced0147a47bd91b75de63ffc4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a2ecef68073b3d4767976b96da65082') in 0.0733529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/SM_Large_Rock_D.fbx
  artifactKey: Guid(abc00000000001560871307181178956) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/SM_Large_Rock_D.fbx using Guid(abc00000000001560871307181178956) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51699c024eb4fd8d8f0499d86cf1ae88') in 0.0640731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_XtraLong.fbx
  artifactKey: Guid(abc00000000004106359238430029228) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_XtraLong.fbx using Guid(abc00000000004106359238430029228) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a683319b5b366a4417970fa04d56a09b') in 0.1094322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_05.fbx
  artifactKey: Guid(abc00000000011824241670197936016) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_05.fbx using Guid(abc00000000011824241670197936016) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f68c9b762a0ff926f1c633d9b628a9d') in 0.0615616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Beams/Materials/No Name.mat
  artifactKey: Guid(e0b56fa5075fec6499aa90b6c2a3f6d4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Beams/Materials/No Name.mat using Guid(e0b56fa5075fec6499aa90b6c2a3f6d4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7b610e177b387f78f0f6874e097f8e2') in 0.056963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_01.fbx
  artifactKey: Guid(abc00000000017511999969406944559) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_01.fbx using Guid(abc00000000017511999969406944559) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '820d65ed69030c2eb781ccceba7cfd8f') in 0.0732431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Castle_SeamHider_01.fbx
  artifactKey: Guid(abc00000000006980907991274788880) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Castle_SeamHider_01.fbx using Guid(abc00000000006980907991274788880) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e94defbd02ae2d3bcabc83723f91e059') in 0.0912706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Portcullis.fbx
  artifactKey: Guid(abc00000000007980842520945534875) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Portcullis.fbx using Guid(abc00000000007980842520945534875) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b2c41de141c40b5def05204f77478be1') in 0.099245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_Lid_A_02.fbx
  artifactKey: Guid(abc00000000015065272054508630051) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_Lid_A_02.fbx using Guid(abc00000000015065272054508630051) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7707891b989f20c11ce63183b83ee536') in 0.0709172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/Fbx Default Material.mat
  artifactKey: Guid(8edf03e023570ec4bb85f6403bfdf8f1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/Fbx Default Material.mat using Guid(8edf03e023570ec4bb85f6403bfdf8f1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '365897aed0bc324753ac67e93bf88eea') in 0.0539212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/No Name.mat
  artifactKey: Guid(eee4eb23dc7426e4f86e6a4ba5f21c27) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/No Name.mat using Guid(eee4eb23dc7426e4f86e6a4ba5f21c27) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f53d7f14f89e21b68d85cf57c397fe31') in 0.0550973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Fish/Mesh/Materials/No Name.mat
  artifactKey: Guid(77fe72acacf2d8e45a09f8c026729fe0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Fish/Mesh/Materials/No Name.mat using Guid(77fe72acacf2d8e45a09f8c026729fe0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b842573126957073be6fc3eefcc969a') in 0.0563734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Beams/Materials/MI_Wood_Algae.mat
  artifactKey: Guid(01ee1c93a38dfec42a34790a9da9f477) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Beams/Materials/MI_Wood_Algae.mat using Guid(01ee1c93a38dfec42a34790a9da9f477) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59d3f8385ca411bd9051b6c738bfd52b') in 0.0567438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/No Name.mat
  artifactKey: Guid(3e42ddf8a5151be4bb26490b575104d8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/No Name.mat using Guid(3e42ddf8a5151be4bb26490b575104d8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f900413bb08744309a27d68d3e9402a') in 0.0574236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Metal.mat
  artifactKey: Guid(0954cb5e5f5afce41ba94cf2d846e024) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Metal.mat using Guid(0954cb5e5f5afce41ba94cf2d846e024) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b61b1c34d9a17d50b5e6bf05f8757e99') in 0.0757609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/Materials/MI_Rock_RVT.mat
  artifactKey: Guid(2af75ca1cd1152045951eee1f79f7222) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/Materials/MI_Rock_RVT.mat using Guid(2af75ca1cd1152045951eee1f79f7222) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '447397920f050a00316ca5183e124734') in 0.0589803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_Cloth_Plain.mat
  artifactKey: Guid(f3c36746f11962641883dd97b079ff0f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_Cloth_Plain.mat using Guid(f3c36746f11962641883dd97b079ff0f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '725aa10d7c31b62fe46a6e21b656b5a0') in 0.0734938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_04.fbx
  artifactKey: Guid(abc00000000013129872190769167783) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_04.fbx using Guid(abc00000000013129872190769167783) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9aa3547f364ccacb086f9de056c42b85') in 0.0598115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Stairs_01.fbx
  artifactKey: Guid(abc00000000012492020364798235311) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Stairs_01.fbx using Guid(abc00000000012492020364798235311) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1943026ccb0808dde95b67d134ca0d2') in 0.0752425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Right [RM].fbx
  artifactKey: Guid(7a1d4b6c231e27b43ba22925331a8f90) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Right [RM].fbx using Guid(7a1d4b6c231e27b43ba22925331a8f90) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d691a9175fb210da7057d332d4746d8') in 0.0906319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_04.fbx
  artifactKey: Guid(abc00000000002861795061667993457) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_04.fbx using Guid(abc00000000002861795061667993457) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30ce4bec802d16785831e630aab740ff') in 0.0823753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Slope_2x4.fbx
  artifactKey: Guid(abc00000000014750357501610966457) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Slope_2x4.fbx using Guid(abc00000000014750357501610966457) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67cbc1df435ed0065b92943be0031066') in 0.0615061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_01.fbx
  artifactKey: Guid(abc00000000017143056426277096606) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_01.fbx using Guid(abc00000000017143056426277096606) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8133a17523e5e15f47df165705fb876') in 0.0646703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_06.mat
  artifactKey: Guid(c1ab2517274d91847a9edb05a15b567b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_06.mat using Guid(c1ab2517274d91847a9edb05a15b567b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4419daf8cd729588e00ec58074894f44') in 0.0873358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_15.fbx
  artifactKey: Guid(abc00000000003304807238301529836) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_15.fbx using Guid(abc00000000003304807238301529836) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3d4d05d82eb4d9618df9afbf8d386d1') in 0.1602017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Carrot.fbx
  artifactKey: Guid(abc00000000016711439311637091356) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Carrot.fbx using Guid(abc00000000016711439311637091356) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e029f1743e8bc6618c23c90c451e4ea3') in 0.0591565 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_01.fbx
  artifactKey: Guid(abc00000000016477243398697852713) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_01.fbx using Guid(abc00000000016477243398697852713) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b23914fac84f0d70b197a442526b0f2c') in 0.1035585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Atlas_NW_01.mat
  artifactKey: Guid(abc00000000000799623985815525089) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Atlas_NW_01.mat using Guid(abc00000000000799623985815525089) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac0b7b89fda0e9842d2bf3e7c87da39b') in 0.0424146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/Materials/MI_IndividualRoofTiles.mat
  artifactKey: Guid(64371255753b55c49a29279fd5f59c5f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/Materials/MI_IndividualRoofTiles.mat using Guid(64371255753b55c49a29279fd5f59c5f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53dd8ac0b7ce2266160cd20cb59266bf') in 0.0691988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_M_02.fbx
  artifactKey: Guid(abc00000000000033437316901062097) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_M_02.fbx using Guid(abc00000000000033437316901062097) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb1eb388d8dcacf0af923756f7916dcd') in 0.0764139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_BrickWall.mat
  artifactKey: Guid(973955c623ce3ab4198e7f549db81413) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_BrickWall.mat using Guid(973955c623ce3ab4198e7f549db81413) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b29d78b1763cb498226d4b0cb622884d') in 0.0386863 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(ac9722c75e9c5fb4388da2fb3d14d3f7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_StoneFence_02.mat using Guid(ac9722c75e9c5fb4388da2fb3d14d3f7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f074e60613d5e663e438ae1054cefaf') in 0.0604465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_FlowerPot_01.fbx
  artifactKey: Guid(abc00000000004721932488470326536) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_FlowerPot_01.fbx using Guid(abc00000000004721932488470326536) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5eef28cb951cac566d14b09d027ae2f4') in 0.0839253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_02.fbx
  artifactKey: Guid(abc00000000003763859957521450703) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_02.fbx using Guid(abc00000000003763859957521450703) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd800bf99f779682f43e79383519fbf6') in 0.0589039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_08.fbx
  artifactKey: Guid(abc00000000003984723384546458393) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_08.fbx using Guid(abc00000000003984723384546458393) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06ecd672892304943bb2f44c3fd73e90') in 0.0884296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_02.fbx
  artifactKey: Guid(abc00000000012887918161437803992) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_02.fbx using Guid(abc00000000012887918161437803992) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54b4f31161f774d0af2237689d6df598') in 0.0716343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_plaster_01.mat
  artifactKey: Guid(80dab5d7aca4a1740a4e58b8cff249b9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_plaster_01.mat using Guid(80dab5d7aca4a1740a4e58b8cff249b9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81eaa6e80fb2abf56119bb92a800b3c7') in 0.0672042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_Roof_Tiles_02.mat
  artifactKey: Guid(33f14f6dd8c837f499d42e646512c199) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_Roof_Tiles_02.mat using Guid(33f14f6dd8c837f499d42e646512c199) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '737c353f22e83aead09404caa704222c') in 0.063677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_BackwardLeft [RM].fbx
  artifactKey: Guid(06db7ead6cfa3054aaa7cf24fa1d249b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_BackwardLeft [RM].fbx using Guid(06db7ead6cfa3054aaa7cf24fa1d249b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d4f5a4d2adb715ff44f7b2aeea51aa3') in 0.1026311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_R90.FBX
  artifactKey: Guid(69d74c92bd0d99a46b3efa70a7e5b08d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_R90.FBX using Guid(69d74c92bd0d99a46b3efa70a7e5b08d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed0e7f5dd6263a2037e01fbd5279f981') in 0.0615481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Run_B_Turn_R90_Root.FBX
  artifactKey: Guid(98c8a7b9e7e5ef44ea0cd38d5ba02ade) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Run_B_Turn_R90_Root.FBX using Guid(98c8a7b9e7e5ef44ea0cd38d5ba02ade) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34a761f352f83b9a007ae8dac406818c') in 0.0589694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_R90.FBX
  artifactKey: Guid(77cf625d37f114b4ba0e58a57f17dcec) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_R90.FBX using Guid(77cf625d37f114b4ba0e58a57f17dcec) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e03fee41b32ec3732ff8043b9108a49') in 0.0680043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_L45_Root.FBX
  artifactKey: Guid(fb6594a39a075f24a82dca782468e646) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_L45_Root.FBX using Guid(fb6594a39a075f24a82dca782468e646) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cac903cefb5acf89f5674d075b911d53') in 0.0688105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_End_01.prefab
  artifactKey: Guid(abc00000000002276231062997728989) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_End_01.prefab using Guid(abc00000000002276231062997728989) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02a649cb32871621ddc16a9a4f244b68') in 0.0669116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R90_Root.FBX
  artifactKey: Guid(78b88b4bedf2dcc4a9d1006af8d6903a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R90_Root.FBX using Guid(78b88b4bedf2dcc4a9d1006af8d6903a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57ac6379f65919562c087a1d97e8aa10') in 0.0652397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_FL45_Root.FBX
  artifactKey: Guid(f3e6ba35b7a31b841b25045d55669695) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_FL45_Root.FBX using Guid(f3e6ba35b7a31b841b25045d55669695) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0c76c10cf581da425ef661d973d2bce') in 0.0797996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_3.FBX
  artifactKey: Guid(916ee77d51b90d841b0a75b5c00f756b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_3.FBX using Guid(916ee77d51b90d841b0a75b5c00f756b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f1db9b3a9705c108860807d25b473fd') in 0.0878699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_BW_Rolling_StandUp.FBX
  artifactKey: Guid(db1b6c7555a7ad5489288e66de8cd5bd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_BW_Rolling_StandUp.FBX using Guid(db1b6c7555a7ad5489288e66de8cd5bd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3ff79fc0ec8ff62266a65801d036dc7') in 0.083176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_4.FBX
  artifactKey: Guid(71a03bdedd7a648428ddd1ac85086e72) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_4.FBX using Guid(71a03bdedd7a648428ddd1ac85086e72) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d0d49e7bf4137ea62043077bcf2c4e9') in 0.0553517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_A.FBX
  artifactKey: Guid(847a2095cf346bf45819aa87686e02b2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_A.FBX using Guid(847a2095cf346bf45819aa87686e02b2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95c004d7c82a72bfb52d95d360a24fb8') in 0.0730833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Small_ver_B.FBX
  artifactKey: Guid(cf58c17e5a396064d88de8a1c32822ce) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Small_ver_B.FBX using Guid(cf58c17e5a396064d88de8a1c32822ce) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c5e298638af6d9412736e07b0e415fa') in 0.1129766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Smash_Double_Inplace.FBX
  artifactKey: Guid(b8228c95bd497484997f59af4062c0cc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Smash_Double_Inplace.FBX using Guid(b8228c95bd497484997f59af4062c0cc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d1d25ee252ca6c25fcaeb567a58081e') in 0.0731851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_02.prefab
  artifactKey: Guid(abc00000000013741201486591237417) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_02.prefab using Guid(abc00000000013741201486591237417) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b85167064bedccdc2edb83aae80b9153') in 0.0638141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0