Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:19Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker7.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21400]  Target information:

Player connection [21400]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3386500963 [EditorId] 3386500963 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21400] Host joined multi-casting on [***********:54997]...
Player connection [21400] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.23 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.28 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56276
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004894 seconds.
- Loaded All Assemblies, in  1.092 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 2318ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (99ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (131ms)
	LoadAllAssembliesAndSetupDomain (496ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (489ms)
			TypeCache.Refresh (486ms)
				TypeCache.ScanAssembly (450ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1227ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1081ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (60ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (264ms)
			ProcessInitializeOnLoadAttributes (607ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.310 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.10 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.174 seconds
Domain Reload Profiling: 5481ms
	BeginReloadAssembly (436ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (119ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (109ms)
	LoadAllAssembliesAndSetupDomain (1607ms)
		LoadAssemblies (1009ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (833ms)
			TypeCache.Refresh (626ms)
				TypeCache.ScanAssembly (587ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (36ms)
	FinalizeReload (3174ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2451ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (21ms)
			BeforeProcessingInitializeOnLoad (563ms)
			ProcessInitializeOnLoadAttributes (1688ms)
			ProcessInitializeOnLoadMethodAttributes (161ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.25 seconds
Refreshing native plugins compatible for Editor in 7.83 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.2 MB). Loaded Objects now: 8334.
Memory consumption went from 209.9 MB to 199.7 MB.
Total: 190.726500 ms (FindLiveObjects: 2.282200 ms CreateObjectMapping: 2.760800 ms MarkObjects: 174.037100 ms  DeleteObjects: 11.643500 ms)

========================================================================
Received Import Request.
  Time since last request: 1812182.075143 seconds.
  path: Assets/IdaFaber/Demo/TC_Demo_01_2k.HDR
  artifactKey: Guid(c29983e817266bf4095f67a56fbcb636) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/TC_Demo_01_2k.HDR using Guid(c29983e817266bf4095f67a56fbcb636) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '423b0bd6e627fe352448d23bdd36fee4') in 3.404557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Maps/FeralGirlRoca.unity
  artifactKey: Guid(2348318492519774694d91f872d679fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Maps/FeralGirlRoca.unity using Guid(2348318492519774694d91f872d679fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a664d1e0d2dc14990776a9905e87afcf') in 0.1415249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Empty/Scene.unity
  artifactKey: Guid(8124e5870f4fd4c779e7a5f994e84ad1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Scene.unity using Guid(8124e5870f4fd4c779e7a5f994e84ad1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54db2a9c4ab838fb75225fa315d61b61') in 0.1851323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity
  artifactKey: Guid(4bd76b5e96cc1bf49b1b111e51d37762) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity using Guid(4bd76b5e96cc1bf49b1b111e51d37762) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity additively'
Loaded scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity'
	Deserialize:            34.856 ms
	Integration:            171.742 ms
	Integration of assets:  0.199 ms
	Thread Wait Time:       31.495 ms
	Total Operation Time:   238.292 ms
 -> (artifact id: '9edf65bfd4654d4452485e3f5b25fc0f') in 1.2356566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 133

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab
  artifactKey: Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab using Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a5bf85a1c3b3bd5694014fbba698c823') in 0.112962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/Artwork.png
  artifactKey: Guid(3bc3a50dde474b148927665f9867ede2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/Artwork.png using Guid(3bc3a50dde474b148927665f9867ede2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '11a3506048d93018e68cc1ea03a593c2') in 0.7831836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab
  artifactKey: Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab using Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '61cc341e775525a927bce9832c36d08e') in 0.059477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Maps/Scene.unity
  artifactKey: Guid(741b12c7941492e4394818ab3666f864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Maps/Scene.unity using Guid(741b12c7941492e4394818ab3666f864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/IdaFaber/Maps/Scene.unity additively'
Asset 'Playercontroller #D': Transition 'AnyState -> Running' in state 'AnyState' doesn't have an Exit Time or any condition, transition will be ignored
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.SceneManagement.EditorSceneManager:OpenScene (string,UnityEditor.SceneManagement.OpenSceneMode)
UnityEditor.Search.AssetIndexer:IndexScene (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

[C:\build\output\unity\unity\Editor\Src\Animation\StateMachine.cpp line 1862]

Loaded scene 'Assets/IdaFaber/Maps/Scene.unity'
	Deserialize:            10.184 ms
	Integration:            90.760 ms
	Integration of assets:  0.005 ms
	Thread Wait Time:       0.282 ms
	Total Operation Time:   101.231 ms
 -> (artifact id: '0638e75ab2dd2494075daa5917c1ca4d') in 0.323647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 791

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Scripts/Controllers/AlarmSystem.cs
  artifactKey: Guid(83c36d390ef891d449f5e3b8549fdedc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/AlarmSystem.cs using Guid(83c36d390ef891d449f5e3b8549fdedc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '542037bd5b08edc5d6c261d53187dd84') in 0.0693027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000127 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Spikes_01.mat
  artifactKey: Guid(31742df79150cea48af8897974bc0557) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Spikes_01.mat using Guid(31742df79150cea48af8897974bc0557) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cafd76db8d6462c8e3dcb9c01ff9fcab') in 0.7250079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Terrain.prefab
  artifactKey: Guid(9acf48a55782c5b4da0118d166df5563) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Terrain.prefab using Guid(9acf48a55782c5b4da0118d166df5563) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bc5704f4e535e35578c466e4d52ced3d') in 0.0528296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000130 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[Player].prefab
  artifactKey: Guid(282a4e884ccaec346abf296757b0d116) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[Player].prefab using Guid(282a4e884ccaec346abf296757b0d116) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4c080b13d2290a4eb858f31318cf2427') in 0.091801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 74

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset_Renderer.asset
  artifactKey: Guid(b7cb793f0e2198349833d4c0b91499b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset_Renderer.asset using Guid(b7cb793f0e2198349833d4c0b91499b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b8aa8625fdc486e3a035cec30a3f25e9') in 0.1628483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_Metallic.png
  artifactKey: Guid(6de9b8cfc7de7f640a0810c90ac0d3ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_Metallic.png using Guid(6de9b8cfc7de7f640a0810c90ac0d3ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b32e2633055c3a530f8489484e58804') in 0.1420501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset
  artifactKey: Guid(335855edb797e1a4bbdb1dd3439fee97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset using Guid(335855edb797e1a4bbdb1dd3439fee97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6f390418feedf80eda0a8363bae9d3b3') in 0.0785878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_14.prefab
  artifactKey: Guid(69d074200c80fc041be1fce32b9208da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_14.prefab using Guid(69d074200c80fc041be1fce32b9208da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d3d689605aa38dfd5354aa7f462476d') in 0.0557811 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_04.prefab
  artifactKey: Guid(abc00000000009837665421401278803) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_04.prefab using Guid(abc00000000009837665421401278803) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '733679b77e3ee725043176024e3d3aec') in 0.038234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Empty/Settings/HDRP High Fidelity.asset
  artifactKey: Guid(36dd385e759c96147b6463dcd1149c11) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRP High Fidelity.asset using Guid(36dd385e759c96147b6463dcd1149c11) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '90db26159ebaf852523e65307686b529') in 0.0332708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab
  artifactKey: Guid(a20a162ac11a35643bed0f3823718abb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab using Guid(a20a162ac11a35643bed0f3823718abb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '16124f0059c0d3cd3a04f74e29108ec3') in 0.03501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_AllPartsTogether Variant.prefab
  artifactKey: Guid(5947f6b95f35860429f95e95f8107658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_AllPartsTogether Variant.prefab using Guid(5947f6b95f35860429f95e95f8107658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '629e491edb878261c15a44216e7af884') in 0.2727881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 794

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_05_3x4.mat
  artifactKey: Guid(9ffd63d270b6b4e4d90f334d8d647a43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_05_3x4.mat using Guid(9ffd63d270b6b4e4d90f334d8d647a43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38193ce895c354ede504c82222e3f4b7') in 0.5873466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_04_3x4.mat
  artifactKey: Guid(cc4c754809e247c40be48d5fecc6af26) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_04_3x4.mat using Guid(cc4c754809e247c40be48d5fecc6af26) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa88d567ed16a89247d0251b987ca4b4') in 0.045822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart_Small.prefab
  artifactKey: Guid(abc00000000012945421884304799263) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cart_Small.prefab using Guid(abc00000000012945421884304799263) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c96cd690618626c77bc1dfe4750a3b07') in 0.0378568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_05.prefab
  artifactKey: Guid(abc00000000013454410769611356843) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_05.prefab using Guid(abc00000000013454410769611356843) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '67b049aac684eba4276b3f11672e76da') in 0.0791893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_05_Custom.mat
  artifactKey: Guid(881ad6fca7b679646a898f88441ad45c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_05_Custom.mat using Guid(881ad6fca7b679646a898f88441ad45c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd53fe72de36c713a2ae00d84a5d1c06c') in 0.6460444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_08.prefab
  artifactKey: Guid(abc00000000000978784064813139366) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_08.prefab using Guid(abc00000000000978784064813139366) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5468016b7f0015270f08880210e515bd') in 0.130857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_07.prefab
  artifactKey: Guid(abc00000000007606408962538882105) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_07.prefab using Guid(abc00000000007606408962538882105) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ed2f9f100d42687b032b2d323436425') in 0.0742056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long_Loose.prefab
  artifactKey: Guid(abc00000000017222846733412788914) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long_Loose.prefab using Guid(abc00000000017222846733412788914) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ce63db7b1b32acb74f633611dee1f1dc') in 0.0358703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med.prefab
  artifactKey: Guid(abc00000000001636842391021204683) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med.prefab using Guid(abc00000000001636842391021204683) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c1e982a3dc59e51db93776ef9d51decc') in 0.0391952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalGlow_Add_01.mat
  artifactKey: Guid(977b6f3c70cb1864aac31aeaebd46a60) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalGlow_Add_01.mat using Guid(977b6f3c70cb1864aac31aeaebd46a60) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '555f86b8b0fc66b638e2ecd59c54c088') in 0.0606114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Waterfall_Main_02.mat
  artifactKey: Guid(b7dc669150d55b4449685a5dc01249aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Waterfall_Main_02.mat using Guid(b7dc669150d55b4449685a5dc01249aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c007832961e08a4745f797843bcfe28') in 0.6214152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_04.prefab
  artifactKey: Guid(abc00000000016896129781801089531) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_04.prefab using Guid(abc00000000016896129781801089531) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '06f790a9afbd9d8d392b79c7d734389f') in 0.0892324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_Separator.prefab
  artifactKey: Guid(abc00000000014253747539517949331) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_Separator.prefab using Guid(abc00000000014253747539517949331) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ef90e19fe8bf7fdd7f5ba96d654e4959') in 0.033308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_01_VertexAlpha.mat
  artifactKey: Guid(07784be81ecdbec4eabb5b9caca4b30b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_01_VertexAlpha.mat using Guid(07784be81ecdbec4eabb5b9caca4b30b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02c834d1315240943cc840cbf1932aaf') in 0.0427814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_24.prefab
  artifactKey: Guid(abc00000000017506454398514029207) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_24.prefab using Guid(abc00000000017506454398514029207) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '18fe72def79582a1513c1297ca958b63') in 0.2137836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_03_4x3_Lava.mat
  artifactKey: Guid(377c26846ef1abb47bb9918917df6aaf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_03_4x3_Lava.mat using Guid(377c26846ef1abb47bb9918917df6aaf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c30f148eafbfb2935c80c01748749a22') in 0.5309066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_MainSlash_01_4x4.mat
  artifactKey: Guid(5c0bdf43d47c35d4f924a26257009cc6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_MainSlash_01_4x4.mat using Guid(5c0bdf43d47c35d4f924a26257009cc6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eabe86681c8482af67402481eeb6a61f') in 0.6013663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab
  artifactKey: Guid(acc85f946427de74fa6e3c695337c836) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab using Guid(acc85f946427de74fa6e3c695337c836) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '86471ea80a4bf680ac3137e3d5912d14') in 0.0543187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 146

========================================================================
Received Import Request.
  Time since last request: 0.000288 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterSmallSplash_01_3x4.mat
  artifactKey: Guid(8ca49a5d58185ca468c1b0cd7d0713ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterSmallSplash_01_3x4.mat using Guid(8ca49a5d58185ca468c1b0cd7d0713ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8028e415cf48910162053cd2647f680') in 0.2764596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterNoise_01.mat
  artifactKey: Guid(5e5f65ae2097a484cb2f245ff89b079d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterNoise_01.mat using Guid(5e5f65ae2097a484cb2f245ff89b079d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '030d1ce5a4747c93b1bbe3337c4f32c4') in 0.0383419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_02.prefab
  artifactKey: Guid(abc00000000016960943808521108304) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_02.prefab using Guid(abc00000000016960943808521108304) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e69fc8e8a905b23c341e8e8f8caae08') in 0.0269343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Horse_Shoe.prefab
  artifactKey: Guid(abc00000000012257470158374124914) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Horse_Shoe.prefab using Guid(abc00000000012257470158374124914) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3660ef1b1c3356360eea04051b4d8b08') in 0.0327376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_mountain.prefab
  artifactKey: Guid(abc00000000008986967732331309788) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_mountain.prefab using Guid(abc00000000008986967732331309788) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9125ce789303fa8e13801ba35e08331a') in 0.0396286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_IronChandelier.prefab
  artifactKey: Guid(abc00000000017479883573317886586) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_IronChandelier.prefab using Guid(abc00000000017479883573317886586) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7d700ef608c2a2224913d82beeae253c') in 0.0384047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_04_2x6.mat
  artifactKey: Guid(a7a0725e5eabe4b42ae5649027b8f460) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_04_2x6.mat using Guid(a7a0725e5eabe4b42ae5649027b8f460) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bb34edc0982a0833c444ad968e4b8b4') in 0.0645595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pillow.prefab
  artifactKey: Guid(abc00000000011212522534428047826) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pillow.prefab using Guid(abc00000000011212522534428047826) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '41fa89578006c7fd9a81b06ceaa6ed80') in 0.0393007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_B.prefab
  artifactKey: Guid(abc00000000006888821354428298396) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_B.prefab using Guid(abc00000000006888821354428298396) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '567306b4dd8e44010174d3f51989794a') in 0.0681569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_02_2x5.mat
  artifactKey: Guid(f180c5afb515efa43b27fad24ea37592) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_02_2x5.mat using Guid(f180c5afb515efa43b27fad24ea37592) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '219e69ae28e31b731220e8949f8657f8') in 0.0554812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLoop_01_5x5.mat
  artifactKey: Guid(9339cbddfa2af434ea43c5adaba3e6fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLoop_01_5x5.mat using Guid(9339cbddfa2af434ea43c5adaba3e6fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '437f80549eb9dc903b35b0d12e6dc493') in 0.3123316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000244 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_03.mat
  artifactKey: Guid(888e8c29a22bab14fac693c65d381701) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_03.mat using Guid(888e8c29a22bab14fac693c65d381701) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c983698d9a3f22ae06a17ba4448fb4be') in 0.0405661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01.prefab
  artifactKey: Guid(abc00000000008449700780517347840) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01.prefab using Guid(abc00000000008449700780517347840) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1455c02612064cc94d85abb308eb7bb9') in 0.0521626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07_1.prefab
  artifactKey: Guid(abc00000000002568350946993410206) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07_1.prefab using Guid(abc00000000002568350946993410206) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '78db9f6a76f16a798b29a2588ffcb907') in 0.0422098 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08.prefab
  artifactKey: Guid(abc00000000007833889246815510673) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08.prefab using Guid(abc00000000007833889246815510673) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '05a1fff9edeab218c7093dd4be8e934f') in 0.0427984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Burst.prefab
  artifactKey: Guid(8aaf150745b9e5249a8493d76a62e133) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Burst.prefab using Guid(8aaf150745b9e5249a8493d76a62e133) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af4739dcdea589656cca2c056a388dca') in 0.3855288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 92

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stair_Support_2M.prefab
  artifactKey: Guid(abc00000000012634657579491898552) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stair_Support_2M.prefab using Guid(abc00000000012634657579491898552) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dfd902f359fa84fbed3c311fc2107e7a') in 0.0346471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round_1.prefab
  artifactKey: Guid(abc00000000003244365005528674490) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round_1.prefab using Guid(abc00000000003244365005528674490) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6c1cc7bbbd944cc7c2699769e54ac2dd') in 0.0398856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/TextMesh Pro/Shaders/TMPro.cginc
  artifactKey: Guid(407bc68d299748449bbf7f48ee690f8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMPro.cginc using Guid(407bc68d299748449bbf7f48ee690f8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4eb5deaf179154ca78d5636f80e5e03e') in 0.0486689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/StandardTransparencyShader.shadergraph
  artifactKey: Guid(e4c314a5c99ab7845b9f7057f076ddf4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/StandardTransparencyShader.shadergraph using Guid(e4c314a5c99ab7845b9f7057f076ddf4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8805e677859be82be14d9150251d594e') in 0.0656658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle.prefab
  artifactKey: Guid(abc00000000008498653728592441981) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle.prefab using Guid(abc00000000008498653728592441981) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3e340a7096ef85de8a2a090757ea8257') in 0.0421281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_02.prefab
  artifactKey: Guid(abc00000000010276451470589532804) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_02.prefab using Guid(abc00000000010276451470589532804) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '28ced7eed402e9cc8d54dfdc7635702d') in 0.0415241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_A.prefab
  artifactKey: Guid(abc00000000002018264587820517585) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_A.prefab using Guid(abc00000000002018264587820517585) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '68e1411a60f238b5a1d8c035b0708bb7') in 0.0332509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_2x4_01.prefab
  artifactKey: Guid(abc00000000013309021883039131726) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_2x4_01.prefab using Guid(abc00000000013309021883039131726) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9268aab91d65c0c4d1a97dc32c3021c1') in 0.0451517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01.prefab
  artifactKey: Guid(abc00000000014606828031542901518) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01.prefab using Guid(abc00000000014606828031542901518) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3914bd03142344bd6b5608dc5fa7ea61') in 0.0545503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_02.prefab
  artifactKey: Guid(abc00000000013091237949394195981) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_02.prefab using Guid(abc00000000013091237949394195981) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f1aa3e07351ac9e1e3ccef369de37243') in 0.0388157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_2.prefab
  artifactKey: Guid(abc00000000006084286906293382597) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_2.prefab using Guid(abc00000000006084286906293382597) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '54736322ab5e2872f5c3a798c613cd26') in 0.0323081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_02.prefab
  artifactKey: Guid(abc00000000001886984038499315504) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_02.prefab using Guid(abc00000000001886984038499315504) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7710822c3fc587407903daaabf5b67f8') in 0.0978161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WeaponStand.prefab
  artifactKey: Guid(abc00000000008052730962525161391) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WeaponStand.prefab using Guid(abc00000000008052730962525161391) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cdd869996a2d0b00f3088d18c70840ce') in 0.0329234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_01.prefab
  artifactKey: Guid(abc00000000010286194390915798511) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_01.prefab using Guid(abc00000000010286194390915798511) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '33f2524db966e13e71557bf26e78baa7') in 0.0365684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Traveling.prefab
  artifactKey: Guid(7aea3730afb11ba4793afc1179183511) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Traveling.prefab using Guid(7aea3730afb11ba4793afc1179183511) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1a829ce5a03c814050b07659d0bf4979') in 0.059865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 94

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR White Variant.prefab
  artifactKey: Guid(2bc95ebca5112594a9d105d31b24bcd8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR White Variant.prefab using Guid(2bc95ebca5112594a9d105d31b24bcd8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd58d69eaef2be20f648567d41355e544') in 0.1820378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 772

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hierarchy Designer/Editor/Documentation/Hierarchy Designer Documentation.pdf
  artifactKey: Guid(d72dadf29c17cd649b9088b207b031de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Documentation/Hierarchy Designer Documentation.pdf using Guid(d72dadf29c17cd649b9088b207b031de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a67cb2e280e6482763bfddfbb5fde0b5') in 0.0276772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-URP Lit.shadergraph
  artifactKey: Guid(a3d800b099a06e0478fb790c5e79057a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-URP Lit.shadergraph using Guid(a3d800b099a06e0478fb790c5e79057a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b557c03507434d788d31c9554ddbc5d') in 0.1840552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Tooltip Light.png
  artifactKey: Guid(8f1b560f08c4cc244bb8529d853fde9c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Tooltip Light.png using Guid(8f1b560f08c4cc244bb8529d853fde9c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '152b84bfc184900fb4cfd91aa96534e7') in 0.0689293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Sprite.shader
  artifactKey: Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Sprite.shader using Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f79c94f622de4a8924050b0cce9d28a8') in 0.0285582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic II.png
  artifactKey: Guid(461cf6a2f13fff24c9f7d6abba922db2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic II.png using Guid(461cf6a2f13fff24c9f7d6abba922db2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '450027c976c3687c961f7b6068c2011b') in 0.0572316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/SkinDiffusionProfile.asset
  artifactKey: Guid(48e911a1e337b44e2b85dbc65b47a594) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRPDefaultResources/SkinDiffusionProfile.asset using Guid(48e911a1e337b44e2b85dbc65b47a594) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a002a09d4755dbfc4595d0a2ac270fb2') in 0.0246812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Reset Light.png
  artifactKey: Guid(4727a8bae4251c84b906aeaa29e36545) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Reset Light.png using Guid(4727a8bae4251c84b906aeaa29e36545) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9014b27a6dfdfabedcbe6a2ff4ab4342') in 0.0597332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern I.png
  artifactKey: Guid(9a7a00d28ed17b646a742b1301b4e02b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern I.png using Guid(9a7a00d28ed17b646a742b1301b4e02b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d8e0267619c4d097fec0d4fd8a94aa1') in 0.0877949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity
  artifactKey: Guid(40eb938c820c1604fa12f8b824fc843c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_03_Wave-Laser.unity using Guid(40eb938c820c1604fa12f8b824fc843c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb1f2486cfba4ca7d66eb906192d8964') in 0.0476468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_Metallic.png
  artifactKey: Guid(a04477318d1d3344e8d188028e74c374) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_Metallic.png using Guid(a04477318d1d3344e8d188028e74c374) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '028d1320bd211e07cf255569839e672b') in 0.0799745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_02.prefab
  artifactKey: Guid(abc00000000005111369591461232409) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_02.prefab using Guid(abc00000000005111369591461232409) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5c3463fd55697f975bb76215387b5e76') in 0.0949642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab
  artifactKey: Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab using Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '512074ae912d9713306e438c8ed8a178') in 0.0349749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleCone.prefab
  artifactKey: Guid(19a5c636edbcc1440b8ad974d36d9300) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleCone.prefab using Guid(19a5c636edbcc1440b8ad974d36d9300) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1f6363b2895ccc58a2893da32c934928') in 0.0306403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Settings/SampleSceneProfile.asset
  artifactKey: Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/SampleSceneProfile.asset using Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2de7bfd32c50f5106c4b39aee11f691') in 0.02842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab
  artifactKey: Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab using Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c01926dfc748e85be795ba405bfea789') in 0.0472167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader
  artifactKey: Guid(bc1ede39bf3643ee8e493720e4259791) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader using Guid(bc1ede39bf3643ee8e493720e4259791) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '620802a6bdd56c678aaa9f24257c938b') in 0.0311819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_03.mat
  artifactKey: Guid(5aeda54659e1dbb4b81ca1505d034ec0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_03.mat using Guid(5aeda54659e1dbb4b81ca1505d034ec0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c539f606c0fe78e73cc3cf9ba787a225') in 0.1366418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/SkinDiffusionProfile.asset
  artifactKey: Guid(f84f836b876b06244bc938065726d90c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/SkinDiffusionProfile.asset using Guid(f84f836b876b06244bc938065726d90c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '13fadf18cdbd12505218afceb0666e15') in 0.0581225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_BODY_F_01_Alternative.mat
  artifactKey: Guid(334f1f748f55ab2479e65a74be679344) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_BODY_F_01_Alternative.mat using Guid(334f1f748f55ab2479e65a74be679344) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '466e85ddca9a98943b80ccc15e093b8f') in 1.062903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default L.png
  artifactKey: Guid(9bf33d98fdcae924a9bc795beb9d502e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default L.png using Guid(9bf33d98fdcae924a9bc795beb9d502e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b61f969180d013cb5bf1d6cbd9bcd902') in 0.0667589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/URP/MedievalKingdomURP.txt
  artifactKey: Guid(6539444c9bcc4c643a5a949a8cde8c1d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/URP/MedievalKingdomURP.txt using Guid(6539444c9bcc4c643a5a949a8cde8c1d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8eb7ae27aedd09cf85fd200aec5476e') in 0.0445159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Main.cs
  artifactKey: Guid(c4d5dd42257b6ae46b22ba4f9ca1e1c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Main.cs using Guid(c4d5dd42257b6ae46b22ba4f9ca1e1c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78bb970ad84a763ba338bfae1eacbac5') in 0.0254284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented Terminal Bud.png
  artifactKey: Guid(0e5cda039e5fd3748954e5db7a5b05c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented Terminal Bud.png using Guid(0e5cda039e5fd3748954e5db7a5b05c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66914dec6477879d27b7b2c71c9bd94c') in 0.0673896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Sideways.png
  artifactKey: Guid(57523d702f2173142b65ac9d7548a2d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Sideways.png using Guid(57523d702f2173142b65ac9d7548a2d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b790b395cb78d5a41f70d8c14d350bf2') in 0.0747823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Demo/Materials/TC_Demo_01_2k.mat
  artifactKey: Guid(95dad3f3382b94943b8a26a7fd9b4958) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/Materials/TC_Demo_01_2k.mat using Guid(95dad3f3382b94943b8a26a7fd9b4958) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68ccbeb8345696bfcaf2449d091e57ed') in 0.1482746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.474131 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_33.mat
  artifactKey: Guid(cd7a6814bab509248908254d576bc892) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_33.mat using Guid(cd7a6814bab509248908254d576bc892) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e438270cebe9f38358add5c67e6cde6d') in 0.8241058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Black Variant.prefab
  artifactKey: Guid(2cbe962403989044783504d68897d6ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Black Variant.prefab using Guid(2cbe962403989044783504d68897d6ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31786035662c3b86b22160fc5f752bc8') in 0.2774678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_03.mat
  artifactKey: Guid(01593fbe3751a19458c8fe77f1560bd8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_03.mat using Guid(01593fbe3751a19458c8fe77f1560bd8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1eb9796dbdec08b0bde889b95d59afe') in 0.8852035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000204 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_12 1.mat
  artifactKey: Guid(c72c055f702a0a84ea7dee85978efb54) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_12 1.mat using Guid(c72c055f702a0a84ea7dee85978efb54) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e751f6634292fbadcbefa5c44d40a6ba') in 0.1742251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Platform.mat
  artifactKey: Guid(b9663acfbb85c6d408c0f39da7b0123b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Platform.mat using Guid(b9663acfbb85c6d408c0f39da7b0123b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19569fad2b2056ee384a4babba0e9ff8') in 0.668796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Normal_01.png
  artifactKey: Guid(95fa39b47d310c34d94a69cf88b6993e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Normal_01.png using Guid(95fa39b47d310c34d94a69cf88b6993e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '418a39d1c838a707e0665de4cc6a6236') in 0.0979375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Mask_Dirt_01.png
  artifactKey: Guid(f2ee175e1761ff74e9949dafdd01b499) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Mask_Dirt_01.png using Guid(f2ee175e1761ff74e9949dafdd01b499) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be6b58fec27e109cdd41e5bf039f1d0b') in 0.1182295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_ROCA_Markings.mat
  artifactKey: Guid(269d30defcb0f6e4c99e9fe570f52a0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_ROCA_Markings.mat using Guid(269d30defcb0f6e4c99e9fe570f52a0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd82aae6f103c8febfb78e5b1455ba417') in 0.47076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22.mat
  artifactKey: Guid(53cfef11bb6d06d4a94be3b2e0907d65) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22.mat using Guid(53cfef11bb6d06d4a94be3b2e0907d65) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f75c3a555a36cdff6c5620fd936fb50e') in 0.2955346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_03.mat
  artifactKey: Guid(f153547615cdcd649b2449ccd7163b99) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_03.mat using Guid(f153547615cdcd649b2449ccd7163b99) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '963c7668f1277aa90014f4bd0f13c7c0') in 0.144149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Surface.mat
  artifactKey: Guid(c589c0f42192b3043b30cfaa974007cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Surface.mat using Guid(c589c0f42192b3043b30cfaa974007cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bda4705c8b827006164ecc20f863eac4') in 0.0625055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_13.mat
  artifactKey: Guid(a3a4e776f09748c46a5d2aa073f872cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_13.mat using Guid(a3a4e776f09748c46a5d2aa073f872cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c0de11f0477ad64b485958609973985') in 0.1304792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN Variant.prefab
  artifactKey: Guid(e274568655e6f6b4cb8304cca3d20fdb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN Variant.prefab using Guid(e274568655e6f6b4cb8304cca3d20fdb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2d0733c00e06148456813b3a5c05023') in 0.2055063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 732

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_MatID.png
  artifactKey: Guid(8c82181ef64028e468978b1f2c491d2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_MatID.png using Guid(8c82181ef64028e468978b1f2c491d2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d61d306c58f1f9cd3b73c83ac65d5a5') in 0.0677301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_00.png
  artifactKey: Guid(98259d02426b91548b39139cf80b42a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_00.png using Guid(98259d02426b91548b39139cf80b42a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b21b00b0a19a42eb4e121a5a307099c') in 0.1131562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_15_01.png
  artifactKey: Guid(646c74f486c92d5468f0217daa916038) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_15_01.png using Guid(646c74f486c92d5468f0217daa916038) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8daf26005d63fe3bebb19eab40198f3a') in 0.1278722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_35.png
  artifactKey: Guid(1422c6e49f07c5a4b8df2b0e043a692b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_35.png using Guid(1422c6e49f07c5a4b8df2b0e043a692b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5dea43d747388fea89dc437a12575864') in 0.1027503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_MaskMap.png
  artifactKey: Guid(f8f8a32fb0cbc8244a32349d28374cc4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_MaskMap.png using Guid(f8f8a32fb0cbc8244a32349d28374cc4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e69f4e2ca8c9ca024fc5a8f8134fbea8') in 0.0829266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_PupilAtlas.png
  artifactKey: Guid(9568654fc5307934988eaf63bfd31a09) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_PupilAtlas.png using Guid(9568654fc5307934988eaf63bfd31a09) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f3c7195b0d867695e681fb75830f65f') in 0.0581431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_MatID_01.png
  artifactKey: Guid(d81203a8f560cd348b4986912cf37f5b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_MatID_01.png using Guid(d81203a8f560cd348b4986912cf37f5b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '945a7fc38158156216c394f37bf82fac') in 0.0725365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_15.mat
  artifactKey: Guid(05874d885e315db4a9822ae57ee9b255) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_15.mat using Guid(05874d885e315db4a9822ae57ee9b255) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aceaade12e59848c3167e82e317fc8c9') in 0.1242391 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_29.png
  artifactKey: Guid(1009982f448f18f488ce26359ac30990) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_29.png using Guid(1009982f448f18f488ce26359ac30990) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0a35161bf65e003d16d0444c5103a91') in 0.1038885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_26.png
  artifactKey: Guid(6f5e2b9eaf0f63e49bd45c89db60607c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_26.png using Guid(6f5e2b9eaf0f63e49bd45c89db60607c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c5bc3fdd77beae418727b316d237287') in 0.066819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_23.mat
  artifactKey: Guid(d21c0ab2ec33fb54bb181a4ddb2c8452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_23.mat using Guid(d21c0ab2ec33fb54bb181a4ddb2c8452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8dd3f58f54434955f2cc53d214e740fc') in 0.2926037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_15_02.png
  artifactKey: Guid(1b2c3f5a4346041428463e12c31ff509) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_15_02.png using Guid(1b2c3f5a4346041428463e12c31ff509) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3b22d9e53c4ab75c663b734a14f926f') in 0.0970318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_15_01.mat
  artifactKey: Guid(4c0028e7c3e5fc64d8665bb42972be6c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_15_01.mat using Guid(4c0028e7c3e5fc64d8665bb42972be6c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '638274691d2094728c2411f33a23a599') in 0.2698487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab
  artifactKey: Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab using Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3233c671c6d36b7f6549a5aab48157ed') in 0.1099502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/HumanBasicMotionsFREE_BlenderFiles.zip
  artifactKey: Guid(1b131c154aa621d409658ba4ed1c84c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/HumanBasicMotionsFREE_BlenderFiles.zip using Guid(1b131c154aa621d409658ba4ed1c84c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96ba45a69b72d4af177fed27cf95f468') in 0.0377005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/IdaFaber/Textures/Base/T_SKIN_Normal_02.png
  artifactKey: Guid(d027ea0ce1081154989c383d4bfc04b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_SKIN_Normal_02.png using Guid(d027ea0ce1081154989c383d4bfc04b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72ba14c22e66523578998a8999ba2701') in 0.0552224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_M_MaskMap.png
  artifactKey: Guid(74b7dad506b6b9c47b56401c7aa602b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_M_MaskMap.png using Guid(74b7dad506b6b9c47b56401c7aa602b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '291d252744354dc390737d2a3776f347') in 0.0955686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_Roughness.png
  artifactKey: Guid(dc2d31dc5cd746241bf5691012f7d835) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_Roughness.png using Guid(dc2d31dc5cd746241bf5691012f7d835) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '026072f7d3be83d101e696b2fd85538f') in 0.0924412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Grey.mat
  artifactKey: Guid(32391099aad710644a06cc941264b32a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Grey.mat using Guid(32391099aad710644a06cc941264b32a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd227fea56c88b828654d884bc73416d') in 0.0909803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_SKIN_Specular.png
  artifactKey: Guid(526fbe0e77873c94eb480f84689d1ac4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_SKIN_Specular.png using Guid(526fbe0e77873c94eb480f84689d1ac4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74241eabc0609b76619d0896107ba842') in 0.0879862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Character.prefab
  artifactKey: Guid(93b935bc5ccb53c47a8897a194af1e3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Character.prefab using Guid(93b935bc5ccb53c47a8897a194af1e3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63b4b1b60dfdf43b959636ab221eac11') in 0.0999778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000109 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)GreenClear.mat
  artifactKey: Guid(a83e62349221e8a4387de81b5430af6c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)GreenClear.mat using Guid(a83e62349221e8a4387de81b5430af6c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea534e98aeb5fe2c76f35b519dd55f60') in 0.0483374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Textures/Pattern/T_Pattern_03.png
  artifactKey: Guid(815521c3a605c7c4e8c0951ab9c7ca78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Pattern/T_Pattern_03.png using Guid(815521c3a605c7c4e8c0951ab9c7ca78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '469f14819efcc831bc5e257a87088a47') in 0.0690417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_3.mat
  artifactKey: Guid(4fa769696321da846a1897098e952791) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_3.mat using Guid(4fa769696321da846a1897098e952791) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e6f0a074eca636a3cbb4b9e2977377c') in 0.0328086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_MetallicSmoothness.png
  artifactKey: Guid(976f42b169bb3ae4e9e7b5620998f034) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_MetallicSmoothness.png using Guid(976f42b169bb3ae4e9e7b5620998f034) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5450760c3d77d4599785757c62f90b3a') in 0.1251299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_07.png
  artifactKey: Guid(b21b8477d7ba9b940839b0ddfc9a0218) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_07.png using Guid(b21b8477d7ba9b940839b0ddfc9a0218) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3fda6f60528331cf198db727430fcb0') in 0.0601227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.126328 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab
  artifactKey: Guid(fe987b373409bdd4e92698a48f7505b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab using Guid(fe987b373409bdd4e92698a48f7505b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb0784303e8efbd42438d54d3a655c02') in 0.12655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Animeimpact_Circular_01.png
  artifactKey: Guid(355b74f50f9a3af41982867d769362f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Animeimpact_Circular_01.png using Guid(355b74f50f9a3af41982867d769362f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c674324bd8eee6a0de7c16d7d1fd838b') in 0.0552483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralSplash_4x4_01.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralSplash_4x4_01.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a75620a2523d76d62b7cda622d542511') in 0.0695407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/AnimatedController.cs
  artifactKey: Guid(6401a0300cc05d74a953f65bf864fd51) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/AnimatedController.cs using Guid(6401a0300cc05d74a953f65bf864fd51) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bcd31d72cf3ce52b84c222584cb5e2d') in 0.0340136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactSimpleCurvy_01.png
  artifactKey: Guid(9da0b9e5f651bdd428b7db4069b82877) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactSimpleCurvy_01.png using Guid(9da0b9e5f651bdd428b7db4069b82877) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4da603421945d8374176cea12007687') in 0.0452418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CircleGround_Buff_01.png
  artifactKey: Guid(0e100f8489aadb04c81263d0efeabef4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CircleGround_Buff_01.png using Guid(0e100f8489aadb04c81263d0efeabef4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f222717af1e732bf9b82570fe24b6c90') in 0.0511751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SimpleCenterSplash_4x4_02.psd
  artifactKey: Guid(8d498fc16ecec444b810067d92f7ae90) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SimpleCenterSplash_4x4_02.psd using Guid(8d498fc16ecec444b810067d92f7ae90) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70c2c31246d37031486355428c705008') in 0.0850789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_BubbleMagic_5x5_01.psd
  artifactKey: Guid(5555c59473e763e4da0e9f5086132b6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_BubbleMagic_5x5_01.psd using Guid(5555c59473e763e4da0e9f5086132b6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fa25b13060a4549fb3cb68ab6b1ac6c2') in 0.0896271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000947 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterWhip_4x4_02.psd
  artifactKey: Guid(18a6adbee9176ed4ba0d6e5d715b8fec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterWhip_4x4_02.psd using Guid(18a6adbee9176ed4ba0d6e5d715b8fec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93d2c8e24d5c377db833c95a864aa439') in 0.0762328 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_3x4_01_FoamRender.psd
  artifactKey: Guid(883697af33806da4c8774a7554a63cdf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_3x4_01_FoamRender.psd using Guid(883697af33806da4c8774a7554a63cdf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b44471abc1165843bee9d246321ceb8d') in 0.0732247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DirectionalImpact_02.png
  artifactKey: Guid(8a4a1fa031206dd4cbca8592d16f71ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DirectionalImpact_02.png using Guid(8a4a1fa031206dd4cbca8592d16f71ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3bb1d23c360bc5d9abe163b5169ae0c5') in 0.0626427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamSmoke_4x4_01.psd
  artifactKey: Guid(834b721063ee3934bb9b6138cb5b54ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamSmoke_4x4_01.psd using Guid(834b721063ee3934bb9b6138cb5b54ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8fe4999a0fb4cfed4bf432a02512d7b') in 0.077382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab
  artifactKey: Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab using Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc63624e7cc1610c58ccb6a183dc45c7') in 0.0844085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Ground.prefab
  artifactKey: Guid(4f99107d6bbf321439eb378a8f2a2f7f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Ground.prefab using Guid(4f99107d6bbf321439eb378a8f2a2f7f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e10238b0cb0d1cc3dc04d5e905c74811') in 0.0787915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.443935 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc4fd11284188babf322774fb7394870') in 0.1830509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_13.mat
  artifactKey: Guid(4187249232d76c047a3d61ca6fe00e8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_13.mat using Guid(4187249232d76c047a3d61ca6fe00e8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1fd236cac153a811d6f9d99751c09666') in 1.295897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity
  artifactKey: Guid(ddea42e8828d7904dad6e7ac5e21e0ea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity using Guid(ddea42e8828d7904dad6e7ac5e21e0ea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6999af638ec55b5fedf58302dca80ed') in 2.2120381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Underwater_Loop_1.wav
  artifactKey: Guid(c934d50e950ebaa4c846c851adb761af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Underwater_Loop_1.wav using Guid(c934d50e950ebaa4c846c851adb761af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1de36407f834f811dcc0c074f2b67f7') in 0.2575106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_3.wav
  artifactKey: Guid(adaf6d8783395e7428d8a082d6d3fed7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_3.wav using Guid(adaf6d8783395e7428d8a082d6d3fed7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2257edf03afaa3e1ba2084531bef6256') in 0.1968323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_B_Loop_1.wav
  artifactKey: Guid(bb9a05d161e48cf4faf51c3e90c7c268) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_B_Loop_1.wav using Guid(bb9a05d161e48cf4faf51c3e90c7c268) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '022ad416f4db9a1df752cd6b9adfba8b') in 1.0614842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Masked Poses/<EMAIL>
  artifactKey: Guid(5e9b37dd2b8b41648a4731a21c853b2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Masked Poses/<EMAIL> using Guid(5e9b37dd2b8b41648a4731a21c853b2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcb12633f86962b5dc97be45b13a651a') in 0.1375195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_02.png
  artifactKey: Guid(587062da68d49c64abf3dfea50f2a764) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_02.png using Guid(587062da68d49c64abf3dfea50f2a764) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2526887a82c5efbcf4de16543c737a64') in 0.0582725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL45_Root.FBX
  artifactKey: Guid(d0671e1f5dda5b94a98964b9fef3f89e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL45_Root.FBX using Guid(d0671e1f5dda5b94a98964b9fef3f89e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8eab66abb14e4ab3b65626a47a53e6a') in 0.0742637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_3.FBX
  artifactKey: Guid(cf86ca3439ea609479beef4995243edf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_3.FBX using Guid(cf86ca3439ea609479beef4995243edf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca3c3d904f3488ad936c2ca7da020d19') in 0.0734949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_A_Root.FBX
  artifactKey: Guid(e69564554b36ac64aae5ea16602c43ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_A_Root.FBX using Guid(e69564554b36ac64aae5ea16602c43ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8362a9e960b59acafe64ffbe3c048a5e') in 0.0634641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL90.FBX
  artifactKey: Guid(cdb8fab8e389d0e48bb29bf3a9077bd6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL90.FBX using Guid(cdb8fab8e389d0e48bb29bf3a9077bd6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7649120703c817a4392c6fd6a69d4967') in 0.0699402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_J_Inplace.FBX
  artifactKey: Guid(2f631d342a318284db120ef732d0eedb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_J_Inplace.FBX using Guid(2f631d342a318284db120ef732d0eedb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6bdcf677584f587f016128ec9a7d9b34') in 0.1100994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Mystic_1.wav
  artifactKey: Guid(f408b69bec0cbd743bd6f9163f5dfab5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Mystic_1.wav using Guid(f408b69bec0cbd743bd6f9163f5dfab5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1de2867397d3d74293f9ed2fc44917dd') in 0.189286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_BrightShiny_1.wav
  artifactKey: Guid(2981e22109d2aab4ca7cc508420aaf08) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_BrightShiny_1.wav using Guid(2981e22109d2aab4ca7cc508420aaf08) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd3d3bad4c61cd2d4656e76cf714d881') in 0.2266766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000167 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Direction.png
  artifactKey: Guid(414df8ef9895e9c418adf928f00931b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Direction.png using Guid(414df8ef9895e9c418adf928f00931b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '719179eebb389377e10987595511b683') in 0.0951224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Body.png
  artifactKey: Guid(8af92863d7918754aa896a2cb2b0780f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Body.png using Guid(8af92863d7918754aa896a2cb2b0780f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83bb1ad7ded4e23753a1115910df4e80') in 0.0587278 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.cs
  artifactKey: Guid(54a3192235bad364e837761535e60335) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.cs using Guid(54a3192235bad364e837761535e60335) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8684bbfca82343fe694c8f2714319ad6') in 0.0284842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01_Alpha.png
  artifactKey: Guid(884e79dc34fbe7f48a564ddc9c1db12a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01_Alpha.png using Guid(884e79dc34fbe7f48a564ddc9c1db12a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bd5e6d971ab177dee527c9ed25b81e3') in 0.051983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FR90.FBX
  artifactKey: Guid(44b5b43b9af9be84ba25238f2105885d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FR90.FBX using Guid(44b5b43b9af9be84ba25238f2105885d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7e407a6d4c51051ac5dfd2bde03f644') in 0.0793198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR45.FBX
  artifactKey: Guid(7af85798edd86374faa0c149f2dcf111) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR45.FBX using Guid(7af85798edd86374faa0c149f2dcf111) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7b9aa48d2513903cba06f70add2eb9a') in 0.0670212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR90_Root.FBX
  artifactKey: Guid(5a0189af3cb4d2e4ba66392a5eb6fe96) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR90_Root.FBX using Guid(5a0189af3cb4d2e4ba66392a5eb6fe96) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1147f69cc2a872845b59b31ae003d3e9') in 0.0671544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_ver_B.FBX
  artifactKey: Guid(d9b150560a043b64180b27d84065813c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_ver_B.FBX using Guid(d9b150560a043b64180b27d84065813c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53feea2eef45d84364e099baf2de2a11') in 0.0632024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_Root_ver_A.FBX
  artifactKey: Guid(cde35d7ec90503a41850fefd1da60087) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Fast_Root_ver_A.FBX using Guid(cde35d7ec90503a41850fefd1da60087) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '679265683ec1cea85fc16cdb84954bb1') in 0.0742967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_B_ALL.FBX
  artifactKey: Guid(0c83b5a5d65a1bc48ae5f39d6ea168c4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_B_ALL.FBX using Guid(0c83b5a5d65a1bc48ae5f39d6ea168c4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '575d909d5c815d1451bb93214e038107') in 0.0601871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_B_Idle.FBX
  artifactKey: Guid(b3a1e585cc7d85a4c96737b750b26dc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_B_Idle.FBX using Guid(b3a1e585cc7d85a4c96737b750b26dc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7202fbbb82d84c5fe679219d79b334e3') in 0.0750899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_3_ZeroHeight.FBX
  artifactKey: Guid(22ea65b02571b944dab02df23d4069f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_3_ZeroHeight.FBX using Guid(22ea65b02571b944dab02df23d4069f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a28b3455059036fee71e60e91bb69b2') in 0.0685467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_5_Inplace.FBX
  artifactKey: Guid(c86e29772c0bf3e49a9863c20f7b1c59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_5_Inplace.FBX using Guid(c86e29772c0bf3e49a9863c20f7b1c59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eef1c187475306c50a08d8dad9415bc1') in 0.0565167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Root_ver_B.FBX
  artifactKey: Guid(606f426f3fffed8468c541db536f043b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Root_ver_B.FBX using Guid(606f426f3fffed8468c541db536f043b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b57babb3ba9c262544a8dfe07a6d0e9a') in 0.0527763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_A_ALL.FBX
  artifactKey: Guid(4f4f206bfba733449bd23b4fdd39cce8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_A_ALL.FBX using Guid(4f4f206bfba733449bd23b4fdd39cce8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bedca927fcdf1bb0435b5c67ab469bee') in 0.0680466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_ALL_ZeroHeight.FBX
  artifactKey: Guid(dcda70e09983e6d498852e6692116e4f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_ALL_ZeroHeight.FBX using Guid(dcda70e09983e6d498852e6692116e4f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6c8dfb4e937d3d4cbd2db133e5936ee') in 0.0585735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Spear_Inplace.FBX
  artifactKey: Guid(0419e092d2135104cae8d2643b302aba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Spear_Inplace.FBX using Guid(0419e092d2135104cae8d2643b302aba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66b50d56ec2ea00c346349fe224e89fa') in 0.0646887 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000107 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_ver_A.FBX
  artifactKey: Guid(7db0e880c1e13fc409041dff6f820922) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_ver_A.FBX using Guid(7db0e880c1e13fc409041dff6f820922) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb4b44ac366e72e38e5e6b273269cace') in 0.0619816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.003225 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/16__Equip_Unequip/<EMAIL>
  artifactKey: Guid(8b08bc846f71cc14f943e1e27af95cf5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/16__Equip_Unequip/<EMAIL> using Guid(8b08bc846f71cc14f943e1e27af95cf5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '286c90e429a73d6726154a2859ec6cc7') in 0.066132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000103 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR90.FBX
  artifactKey: Guid(1acde0722a75152429f3916c1bdb99d1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR90.FBX using Guid(1acde0722a75152429f3916c1bdb99d1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b90c2604884736c9158e606caefdd3b3') in 0.084853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Loop_ZeroHeight.FBX
  artifactKey: Guid(74aa4556f89829e41a61acc9de4c4441) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Loop_ZeroHeight.FBX using Guid(74aa4556f89829e41a61acc9de4c4441) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '832aec5696d6b5555654f21e74f6965d') in 0.0580072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_01_Grayscale.png
  artifactKey: Guid(2e81791f34a38ab43a1cbdfa72a59c5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_01_Grayscale.png using Guid(2e81791f34a38ab43a1cbdfa72a59c5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7cb7040a63c7774a4f167f2dc79b999') in 0.0639936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_Turn.FBX
  artifactKey: Guid(d3bd6fe31dd967b41a4fc697c20bd933) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_Turn.FBX using Guid(d3bd6fe31dd967b41a4fc697c20bd933) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e77fdf4c18ad878d3bc94b9fac040bb5') in 0.0720452 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90_Root.FBX
  artifactKey: Guid(805d4fa3f79f81e49b8ee69dccf06e06) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90_Root.FBX using Guid(805d4fa3f79f81e49b8ee69dccf06e06) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '222e54e3d7d338d7fbd77a797a9483d0') in 0.115661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_End_ZeroHeight.FBX
  artifactKey: Guid(6a92a180c4391d24cb53c32a5bedc09d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_End_ZeroHeight.FBX using Guid(6a92a180c4391d24cb53c32a5bedc09d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '092d09f073d10cf4692e7040ae45d29c') in 0.0682415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start.FBX
  artifactKey: Guid(6a714454c39b9bb41b54c78a30c2716f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start.FBX using Guid(6a714454c39b9bb41b54c78a30c2716f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '658fdb69064993168e45e8c76d7c1945') in 0.0725921 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/3__Left/M_katana_Blade@Damage_Left_Small_ver_A.FBX
  artifactKey: Guid(146cd813f6397d64f9111c1e6817fcfd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/3__Left/M_katana_Blade@Damage_Left_Small_ver_A.FBX using Guid(146cd813f6397d64f9111c1e6817fcfd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2cf3edc3d5b7637a06ae1c3fdf9bf6c0') in 0.0624875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/3__Dash_Attack/M_katana_Blade@Dash_Attack_ver_A.FBX
  artifactKey: Guid(35b2926bc0946d74a86943c6ee00f78c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/3__Dash_Attack/M_katana_Blade@Dash_Attack_ver_A.FBX using Guid(35b2926bc0946d74a86943c6ee00f78c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebbf88e0357fe99efcef4989f943c285') in 0.0636205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_ALL.FBX
  artifactKey: Guid(efc11491e80982b4ba81233df582a805) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_ALL.FBX using Guid(efc11491e80982b4ba81233df582a805) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aae74706684cfae360a82d5c986c00fe') in 0.0835667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_B.FBX
  artifactKey: Guid(d0da433578fc21b4a80f20186d8b00a2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_B.FBX using Guid(d0da433578fc21b4a80f20186d8b00a2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46a35e1b7103af0b7675dc1d10c7b001') in 0.0575817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_C.FBX
  artifactKey: Guid(17b09004b43306a40839dd72d0e89070) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_C.FBX using Guid(17b09004b43306a40839dd72d0e89070) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cec6159dbf881c490d51a3a22c0a2c4') in 0.0586775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_1.FBX
  artifactKey: Guid(892de13d6a6884a4e8b8965bf2fc27af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_1.FBX using Guid(892de13d6a6884a4e8b8965bf2fc27af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '365cd82c7d3a9d68b1f35176c3a7706e') in 0.1061408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_ZeroHeight.FBX
  artifactKey: Guid(2369636c83c2ec844b730bbb9e90ab16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_ZeroHeight.FBX using Guid(2369636c83c2ec844b730bbb9e90ab16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a5271d8dc7b0899dd048e2bcf49b139') in 0.0853257 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start_ZeroHeight.FBX
  artifactKey: Guid(c88529aecf507884c8f64971ba054338) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start_ZeroHeight.FBX using Guid(c88529aecf507884c8f64971ba054338) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2e91b6470e364830490ad66d66a4ba4') in 0.2183851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000145 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2_Inplace.FBX
  artifactKey: Guid(2dd5bf9f252aa904db0af56d8273e07c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2_Inplace.FBX using Guid(2dd5bf9f252aa904db0af56d8273e07c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de4854da4b55a82b7f9b271d762ec83b') in 0.0619053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL.FBX
  artifactKey: Guid(665a655f3dd97f94a9632d28c96d958d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL.FBX using Guid(665a655f3dd97f94a9632d28c96d958d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a28d854c8630162f2b890a5428ad8d84') in 0.1124951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue_Ombre_alpha.png
  artifactKey: Guid(9446dc7eb314f9349b7c05918b1fb721) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue_Ombre_alpha.png using Guid(9446dc7eb314f9349b7c05918b1fb721) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ad427928af6c75e8ad4cce6b84a1b9e') in 0.1272622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_03.mat
  artifactKey: Guid(abc00000000008734187500704770222) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_03.mat using Guid(abc00000000008734187500704770222) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '976bbe83e5c19c19fbf5769bdc0a5fb3') in 0.0566028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_67.fbx
  artifactKey: Guid(abc00000000011050354940699328837) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_67.fbx using Guid(abc00000000011050354940699328837) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cceb2bd080b399837293088c0e2d1023') in 0.1174706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.prefab
  artifactKey: Guid(e6a0d41974ec0d74cb1e546f4097294d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.prefab using Guid(e6a0d41974ec0d74cb1e546f4097294d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a58a2cca39c7d491e3f9ff76a092b9d9') in 0.3372847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.prefab
  artifactKey: Guid(bf71f120f7b93414a955e65b9279ec5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.prefab using Guid(bf71f120f7b93414a955e65b9279ec5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '709449a0ef12e11a3edf1d0467ade1e7') in 0.7364715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000193 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_01.mat
  artifactKey: Guid(abc00000000016133371816360579212) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_01.mat using Guid(abc00000000016133371816360579212) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73127c081a6d13be4cd62231ca658a61') in 0.3438164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000745 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Glass.mat
  artifactKey: Guid(abc00000000010553704239738727099) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Glass.mat using Guid(abc00000000010553704239738727099) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3056f20dfded386cc29f259e9dfd2781') in 0.0557929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_20.fbx
  artifactKey: Guid(abc00000000018069727397115326960) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_20.fbx using Guid(abc00000000018069727397115326960) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fde2ff90bc193e556fbcf9eb3eec2f99') in 0.1200598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_8.fbx
  artifactKey: Guid(abc00000000016314043521229772462) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_8.fbx using Guid(abc00000000016314043521229772462) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf31307be41f8e5f65a69d3614dc84f8') in 0.1139326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_13.fbx
  artifactKey: Guid(abc00000000016557738850493279634) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_13.fbx using Guid(abc00000000016557738850493279634) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb5ce084634c8ea82252f6763d832353') in 0.1102619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_17.fbx
  artifactKey: Guid(abc00000000008462744112295060854) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_17.fbx using Guid(abc00000000008462744112295060854) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b1d8390ab43b037c9ce3b36fb419ac2') in 0.0846709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_LighterPlanks.mat
  artifactKey: Guid(abc00000000000789036779032091370) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_LighterPlanks.mat using Guid(abc00000000000789036779032091370) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac0f40ab161d6f7e85e2f4691847ba3a') in 0.0657624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_RockMossy_02.mat
  artifactKey: Guid(abc00000000017644046680987046221) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_RockMossy_02.mat using Guid(abc00000000017644046680987046221) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e67ef8cb1db6adb847ee699db45b891') in 0.0737172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000330 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Red.mat
  artifactKey: Guid(abc00000000015474946832986465021) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Red.mat using Guid(abc00000000015474946832986465021) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55a8bc6182fa7e80c45135d9b13a8b4d') in 0.0783049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_02.mat
  artifactKey: Guid(abc00000000015761188811441295974) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_02.mat using Guid(abc00000000015761188811441295974) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22c04f03dcf44d31a40a4f6956f62a81') in 0.0981421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_05.prefab
  artifactKey: Guid(abc00000000013454410769611356843) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_05.prefab using Guid(abc00000000013454410769611356843) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9927dea0a79d939f4e5a59b88e58978') in 0.0807547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Small_01.prefab
  artifactKey: Guid(abc00000000002335111547179833352) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Small_01.prefab using Guid(abc00000000002335111547179833352) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74b71ed88a2f1bc044f9d260851cf0ab') in 0.0879544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 61

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_04.prefab
  artifactKey: Guid(abc00000000005319752499175810146) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_04.prefab using Guid(abc00000000005319752499175810146) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcf21af4b76ab723b8c5498b66e7f8aa') in 0.0653487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BrickA.prefab
  artifactKey: Guid(abc00000000015268156612552762677) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BrickA.prefab using Guid(abc00000000015268156612552762677) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f588eaee98744be7005732722c7ab858') in 0.0540385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_A.prefab
  artifactKey: Guid(abc00000000015370793887961688754) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_A.prefab using Guid(abc00000000015370793887961688754) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69c3926bf610afc86b9a5acfc3abe2b4') in 0.0702902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Banister.prefab
  artifactKey: Guid(abc00000000010415127047187200049) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Banister.prefab using Guid(abc00000000010415127047187200049) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c637aac8abb9f58cb9a11099a43f81fe') in 0.1346745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_ver_A.FBX
  artifactKey: Guid(a7f5f6b15f267844e8c82f29d3012a43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_ver_A.FBX using Guid(a7f5f6b15f267844e8c82f29d3012a43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec9a0d73f0a9194b121e65eaa54e5047') in 0.1217852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ArchWall_01_Splines_34.fbx
  artifactKey: Guid(abc00000000001035952726676874050) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ArchWall_01_Splines_34.fbx using Guid(abc00000000001035952726676874050) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04544e553c563acc817f04b8b6d6fa6d') in 0.0975247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_02.prefab
  artifactKey: Guid(abc00000000012735556429546858880) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_02.prefab using Guid(abc00000000012735556429546858880) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89a01549d40f08bc933537aa8ac7e82f') in 0.0633975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_01.mat
  artifactKey: Guid(abc00000000009433890496998591782) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_01.mat using Guid(abc00000000009433890496998591782) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e0ee5a601dd8b84d84507fafec324fc') in 0.0465929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Scale_Disp.mat
  artifactKey: Guid(abc00000000006585048051965788904) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Scale_Disp.mat using Guid(abc00000000006585048051965788904) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b73227f033aa216c9a23c2aa7da7fd4') in 0.0882346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockB.prefab
  artifactKey: Guid(abc00000000010984285262532039862) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockB.prefab using Guid(abc00000000010984285262532039862) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2957cccbff596c1ee62c5eafb9931cb') in 0.0964035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_B.prefab
  artifactKey: Guid(abc00000000017232423169120149286) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_B.prefab using Guid(abc00000000017232423169120149286) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f7ce6a1ca0c697c42837756a9c043c4') in 0.0724148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair_1.prefab
  artifactKey: Guid(abc00000000010102012648969553656) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair_1.prefab using Guid(abc00000000010102012648969553656) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65d18d2d14ff0681409a5497d2f4371a') in 0.0738624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01.prefab
  artifactKey: Guid(abc00000000011080943565026297348) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01.prefab using Guid(abc00000000011080943565026297348) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbfab39b6805d1c059313ee456e19b87') in 0.0583572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_Pillar.prefab
  artifactKey: Guid(abc00000000011696577099668539015) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_Pillar.prefab using Guid(abc00000000011696577099668539015) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fd39ef9e85bc3d6cccfcc04bfd99981') in 0.0645154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_01.prefab
  artifactKey: Guid(abc00000000006790571834125416237) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_01.prefab using Guid(abc00000000006790571834125416237) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '425bcd69b31b18eb0fbc2f73e3d61ef7') in 0.0665205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_01.prefab
  artifactKey: Guid(abc00000000017288310274170488947) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_01.prefab using Guid(abc00000000017288310274170488947) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f80d02c98742c05c93ce112591979ccb') in 0.0607992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_02.prefab
  artifactKey: Guid(abc00000000008384018515174714024) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_02.prefab using Guid(abc00000000008384018515174714024) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b88ff7854414620e3ee86a9b3b3c071') in 0.0650291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_01.prefab
  artifactKey: Guid(abc00000000003709479468941872714) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_01.prefab using Guid(abc00000000003709479468941872714) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a950712d490033921146f204d2b6dfa') in 0.0711179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_14.prefab
  artifactKey: Guid(abc00000000013275564946604705107) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_14.prefab using Guid(abc00000000013275564946604705107) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '706d5924ec433cfaeaad98e347734679') in 0.0813344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_03_Fix.prefab
  artifactKey: Guid(abc00000000001582411784641340072) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_03_Fix.prefab using Guid(abc00000000001582411784641340072) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd33d5a72fa2dce6c33ce153a5fee3945') in 0.0795451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneWall_Algae1.mat
  artifactKey: Guid(abc00000000000843061184023373885) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneWall_Algae1.mat using Guid(abc00000000000843061184023373885) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ede7ffdfaa9b850307ba9f4211d06349') in 0.153122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_2.FBX
  artifactKey: Guid(049fdb0f456732c48ae8f7514bd0c304) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_2.FBX using Guid(049fdb0f456732c48ae8f7514bd0c304) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '548ff21cafa86b99dac5587b79070cc7') in 0.0654268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SimpleImpactWater_4x4_01.png
  artifactKey: Guid(46337ecc2e95c9a4287478d0d583f24a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SimpleImpactWater_4x4_01.png using Guid(46337ecc2e95c9a4287478d0d583f24a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5af1cea3fc6b62df07ca3c79ca842122') in 0.0686678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Run_B.FBX
  artifactKey: Guid(2231819897da25d40b76d9ecd07ec020) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Run_B.FBX using Guid(2231819897da25d40b76d9ecd07ec020) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8166449ed62d8e5a649e9bcc6cca7e4') in 0.0613721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_04.prefab
  artifactKey: Guid(abc00000000009253551848793020775) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_04.prefab using Guid(abc00000000009253551848793020775) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dcc24fec2c2525df4cc9e83aa8ade8b4') in 0.0815533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_F.prefab
  artifactKey: Guid(abc00000000000895846825472282064) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_F.prefab using Guid(abc00000000000895846825472282064) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b7fdfa378378ce21f237b4f05e2ebc1') in 0.0586393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pillow.prefab
  artifactKey: Guid(abc00000000011212522534428047826) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pillow.prefab using Guid(abc00000000011212522534428047826) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2158e171b6d15ee5e474b1c71170657') in 0.0611828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2M.prefab
  artifactKey: Guid(abc00000000017181763308313172076) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2M.prefab using Guid(abc00000000017181763308313172076) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9216ba87b524b45b76d9d542e11e201') in 0.0592097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_02.prefab
  artifactKey: Guid(abc00000000000358624188340789798) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_02.prefab using Guid(abc00000000000358624188340789798) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c1c84b620a4c3fe4e416158dde978d34') in 0.0620454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve_Beam.prefab
  artifactKey: Guid(abc00000000012588386773910390050) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve_Beam.prefab using Guid(abc00000000012588386773910390050) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62a85aa30ed5786b3ce1e4d0f123d2c3') in 0.0644716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Goblet_01.prefab
  artifactKey: Guid(abc00000000002890729963569673488) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Goblet_01.prefab using Guid(abc00000000002890729963569673488) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63d275045f19feb17305d5df73e20dab') in 0.0587734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Hammer.prefab
  artifactKey: Guid(abc00000000015238524577280148859) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Hammer.prefab using Guid(abc00000000015238524577280148859) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7bf060b6178561b2e61c8fa407cfc9b') in 0.0612733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug_1.prefab
  artifactKey: Guid(abc00000000007717172125062927024) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug_1.prefab using Guid(abc00000000007717172125062927024) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '699a968777176b7b72a6e0ab68bbd64d') in 0.0622454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_4x4M.prefab
  artifactKey: Guid(abc00000000011717761348093138613) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_4x4M.prefab using Guid(abc00000000011717761348093138613) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a16e8794f837ba2ff8bb53ccd1890ae') in 0.0550844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02_1.prefab
  artifactKey: Guid(abc00000000005887249695792533851) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02_1.prefab using Guid(abc00000000005887249695792533851) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fdfd308b42f8a52c44fd9a7e03602edc') in 0.0694329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TavernSign_01.prefab
  artifactKey: Guid(abc00000000003296888411742535538) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TavernSign_01.prefab using Guid(abc00000000003296888411742535538) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96b04d6d3e8d22795b3d2ba5e945cd83') in 0.0606354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_B.prefab
  artifactKey: Guid(abc00000000016649879693021862794) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_B.prefab using Guid(abc00000000016649879693021862794) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '828f048a5baaaaa03c5d3461047fb04c') in 0.0619551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_1.prefab
  artifactKey: Guid(abc00000000016091956856614776893) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_1.prefab using Guid(abc00000000016091956856614776893) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e6d2315b6d87ca1fa98a1771592b81a') in 0.0636289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_03.prefab
  artifactKey: Guid(abc00000000011239063437705940543) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_03.prefab using Guid(abc00000000011239063437705940543) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bebcd49f65a7e906b5ef84a273671776') in 0.0538751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_D.prefab
  artifactKey: Guid(abc00000000008541853647417900671) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_D.prefab using Guid(abc00000000008541853647417900671) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f92c28577ce4d292a9e4a9b6a7ede5a') in 0.0650732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_M_01.prefab
  artifactKey: Guid(abc00000000014972953830511498521) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_M_01.prefab using Guid(abc00000000014972953830511498521) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c85fae8c0573dcd9b5c10c2350bd22b5') in 0.0693469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_BlendVertex.shadergraph
  artifactKey: Guid(4aff5582114a3234ca8b34292c71848a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_BlendVertex.shadergraph using Guid(4aff5582114a3234ca8b34292c71848a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65347990b64d4617a1ca3f7f1981a5fb') in 0.0350684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_03.prefab
  artifactKey: Guid(abc00000000015626663145792241089) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_03.prefab using Guid(abc00000000015626663145792241089) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2a78f1033d25d2a8223e36729d908b4') in 0.0514512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_02.prefab
  artifactKey: Guid(abc00000000001366254860434428519) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_02.prefab using Guid(abc00000000001366254860434428519) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4683b334152f9e1eeb8d1b3ff30012b') in 0.0596745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000145 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bark_01_normal.PNG
  artifactKey: Guid(84a7c7b21de4dfe4383304a5b72dd30a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bark_01_normal.PNG using Guid(84a7c7b21de4dfe4383304a5b72dd30a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f88372a4b143acfb3a06c7ccc139eb84') in 0.0445648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round.prefab
  artifactKey: Guid(abc00000000018338094561444558674) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round.prefab using Guid(abc00000000018338094561444558674) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc09418ce143b726d4fe633d12858250') in 0.0598033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_01.prefab
  artifactKey: Guid(abc00000000006091932806558464166) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_01.prefab using Guid(abc00000000006091932806558464166) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b88496b91d72792d247763223352fa29') in 0.0468908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_Normal.PNG
  artifactKey: Guid(1c0b225e2d263124f995b4e3880150e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_Normal.PNG using Guid(1c0b225e2d263124f995b4e3880150e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b7150b99a2a72a6256c62e95bbfc098') in 0.0452796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000139 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Droplet_01.PNG
  artifactKey: Guid(198b63ea6d3aa7e4bb38e0aa809ff928) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Droplet_01.PNG using Guid(198b63ea6d3aa7e4bb38e0aa809ff928) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e91d2bfe79af488bd9951f197ce8cb10') in 0.0627894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_M.PNG
  artifactKey: Guid(e3e43d86909208f419c780c08567ee5f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_M.PNG using Guid(e3e43d86909208f419c780c08567ee5f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af0bc2cde4c013ef3404ca7d0f264923') in 0.0627217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_N.PNG
  artifactKey: Guid(b354df02abf217c43a9d6d0135707a8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_N.PNG using Guid(b354df02abf217c43a9d6d0135707a8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2ff85042f7422c471065701095100d1') in 0.0623869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_plaster_01.PNG
  artifactKey: Guid(ba32dae632709b04d9dafd6c20cac904) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_plaster_01.PNG using Guid(ba32dae632709b04d9dafd6c20cac904) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd56119ee9912f9bbafe5368e8ae57166') in 0.0462772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Detail_Rocky_N.PNG
  artifactKey: Guid(14f96ad511fd41248b4467d3bae716ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Detail_Rocky_N.PNG using Guid(14f96ad511fd41248b4467d3bae716ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7e7d36649c1fb21b0ffbaca43207edc') in 0.073003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_Normal.PNG
  artifactKey: Guid(61d2aea3ae6f1ba458f03c2b75e5e043) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_Normal.PNG using Guid(61d2aea3ae6f1ba458f03c2b75e5e043) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd92e81ef08d8eb36a965c2543f42b9ec') in 0.0589984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_normal.PNG
  artifactKey: Guid(bd64e862ca9f0ab4db62405fa2d43efb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_normal.PNG using Guid(bd64e862ca9f0ab4db62405fa2d43efb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8cdd12cb37709385ac3746c2a4af93d') in 0.050976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_basecolor.PNG
  artifactKey: Guid(6d4e14b80a815b14e8b547d64d1c4e2a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_basecolor.PNG using Guid(6d4e14b80a815b14e8b547d64d1c4e2a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efcf27fee5fc7a898dde68ddb98541d7') in 0.050162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_LeavesFX_Leaf2A.PNG
  artifactKey: Guid(bd9655c8566f5e143887360f7aa39eba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_LeavesFX_Leaf2A.PNG using Guid(bd9655c8566f5e143887360f7aa39eba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d3c39e46fc752ccea205824e40c390f') in 0.0742934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ground_ORMH.PNG
  artifactKey: Guid(4a60aa865c716924cab8b9b222ac8d10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ground_ORMH.PNG using Guid(4a60aa865c716924cab8b9b222ac8d10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a5a4115dc81709e6d4e770c5ce726807') in 0.0708223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mudFootprints_Normal.PNG
  artifactKey: Guid(f02bf3ef99999e046ac3e9a8985c06c1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mudFootprints_Normal.PNG using Guid(f02bf3ef99999e046ac3e9a8985c06c1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '184333985e0a58d65f06bf0693bd0e5e') in 0.066126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_ORMH.PNG
  artifactKey: Guid(eb7d2041c655f5242978b37194dff6ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_ORMH.PNG using Guid(eb7d2041c655f5242978b37194dff6ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5d48a285572da4ffbf09183c12499f8') in 0.0731068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_M.PNG
  artifactKey: Guid(25a329308f5ae814f87ca97aad58985e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_M.PNG using Guid(25a329308f5ae814f87ca97aad58985e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '408c4328ba3dcb5a8ae24aad1ff4ac50') in 0.0826882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_ORMH.PNG
  artifactKey: Guid(421686c899bb8bb4fbc347b73ed6e724) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_ORMH.PNG using Guid(421686c899bb8bb4fbc347b73ed6e724) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f31a307e1d850f4ec1ecf311cccadd2a') in 0.0501495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000387 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_RMA.PNG
  artifactKey: Guid(d76fff1bf419cb444b000cbcb1ea9a27) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_RMA.PNG using Guid(d76fff1bf419cb444b000cbcb1ea9a27) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c1981c4931848dcfd633c503d51a1ce') in 0.065768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterFlow_01_Foam_Tiled.PNG
  artifactKey: Guid(e22e08d3e53a5614da9bf7194a257071) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterFlow_01_Foam_Tiled.PNG using Guid(e22e08d3e53a5614da9bf7194a257071) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ee9b8bb46a43b2fa698fd74cfe9f071') in 0.0626062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_BaseColor.PNG
  artifactKey: Guid(d6f1aa238c9585d448a3c65e37fd7220) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slabs_BaseColor.PNG using Guid(d6f1aa238c9585d448a3c65e37fd7220) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8aed35b6a5c3efd52bfa1d2052551687') in 0.0492574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_ARM - Copy.PNG
  artifactKey: Guid(7e5a2ae0c9a73d246941a22c7ee75f39) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_ARM - Copy.PNG using Guid(7e5a2ae0c9a73d246941a22c7ee75f39) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a0bfdc7b5e614c4dee43bd23b30efd0') in 0.0475889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_BaseColor.PNG
  artifactKey: Guid(25008ed9746b0c149800ff850e71d4f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_BaseColor.PNG using Guid(25008ed9746b0c149800ff850e71d4f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f340aefafecca50c9cc2019d9427ea52') in 0.0491494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_01.PNG
  artifactKey: Guid(48d266b7f8024ee4795b849d18dc4a43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_01.PNG using Guid(48d266b7f8024ee4795b849d18dc4a43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ad1ce6df75f9b677e4b4aed001a6928') in 0.0554325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_N.PNG
  artifactKey: Guid(0e5c7ef0871c47e4b804e203f86835ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_N.PNG using Guid(0e5c7ef0871c47e4b804e203f86835ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33a14a4d2f312fde86d2ca4eb809c2cb') in 0.0995338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hand Right Mask.mask
  artifactKey: Guid(742dda8cea256e347a5e36860b12ce91) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hand Right Mask.mask using Guid(742dda8cea256e347a5e36860b12ce91) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cacf4f3e11a0ba465cf780d891ee0318') in 0.0572439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(9ff8f9cbbbf3f9a4699940f5590cacaa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(9ff8f9cbbbf3f9a4699940f5590cacaa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d46fc1938d40e6d67462d2f1e83b442') in 0.08306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_Forward.controller
  artifactKey: Guid(54b97aa90e8bf7d49981d3dc9a71582f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_Forward.controller using Guid(54b97aa90e8bf7d49981d3dc9a71582f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d9686c9ebdd1a1d1bbda31242c219ca') in 0.0360223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(084ed73b7e7c0754eb337259e9209468) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(084ed73b7e7c0754eb337259e9209468) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab1044b830224422642f6807313765d6') in 0.0504467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_ORM.PNG
  artifactKey: Guid(487eae63a61d2044999968a22241d2d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_ORM.PNG using Guid(487eae63a61d2044999968a22241d2d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d1a0ff488a39913e44e321af131da40') in 0.0487287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL>
  artifactKey: Guid(383afa9ae798a0c4eb2fb43e0a84b66b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL> using Guid(383afa9ae798a0c4eb2fb43e0a84b66b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c079bff22a2206a89cfb8b1e0de70577') in 0.059512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_ForwardLeft.controller
  artifactKey: Guid(3422516925cab0248b2549dc0bcb22f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_ForwardLeft.controller using Guid(3422516925cab0248b2549dc0bcb22f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bea8fe7bf486a9db4535812bac25eef1') in 0.0348632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Backward.controller
  artifactKey: Guid(86eebe318b1b80940a9e05f142407895) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Backward.controller using Guid(86eebe318b1b80940a9e05f142407895) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce35fb126f027790713a9899f98fed40') in 0.0391959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_A.PNG
  artifactKey: Guid(3981feeaafaf06a4cac03a065f8fe8dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_A.PNG using Guid(3981feeaafaf06a4cac03a065f8fe8dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c96161e5dd622c08d80349b787d34dd1') in 0.0472895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_Turn_R90.FBX
  artifactKey: Guid(4b0a9ed22de3e0840a77f66bebb061bc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_Turn_R90.FBX using Guid(4b0a9ed22de3e0840a77f66bebb061bc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5aa503cf65a1f069259c7d5fcfcefe93') in 0.0682153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_A.FBX
  artifactKey: Guid(82de28a86155ce3498900b3bd2eba322) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_A.FBX using Guid(82de28a86155ce3498900b3bd2eba322) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6452a741d0c06154d5d07a801adb2e60') in 0.0622111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Jog_A_Root.FBX
  artifactKey: Guid(b12578a40f49ddb44a306823dfa2202f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Jog_A_Root.FBX using Guid(b12578a40f49ddb44a306823dfa2202f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0832f01238ce33af57305144a5e8f151') in 0.0643695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_2.wav
  artifactKey: Guid(379c1a188433be84f818d400b58f79bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_2.wav using Guid(379c1a188433be84f818d400b58f79bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f9b941f89a7f8ed8044d1d2edad2c2a') in 0.0962292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/ToggleController.cs
  artifactKey: Guid(ce86f32b3128ee042bbcc624a78054a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/ToggleController.cs using Guid(ce86f32b3128ee042bbcc624a78054a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc5a817709b0862b69518e07fdb261ac') in 0.0334153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Water_1.wav
  artifactKey: Guid(69d8743a80c27ec4593baf08174cffbf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Water_1.wav using Guid(69d8743a80c27ec4593baf08174cffbf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4769399562b556b61b8a8ad364618ef3') in 0.1528618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Jog_B.FBX
  artifactKey: Guid(0fb1b3e5bba000844b68b88b9605666c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Jog_B.FBX using Guid(0fb1b3e5bba000844b68b88b9605666c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e772670433ef12d068b19e5d4be3ac7') in 0.0751683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_B_to_Run_B.FBX
  artifactKey: Guid(c2613ecb9e9cb11468d366e0fb5ce33c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_B_to_Run_B.FBX using Guid(c2613ecb9e9cb11468d366e0fb5ce33c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9064b969412a490fac37d5ed0bc0d4ec') in 0.069383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Idle_Turn_R90_Root.FBX
  artifactKey: Guid(d15075f227748034aa4715e336ce8d1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Idle_Turn_R90_Root.FBX using Guid(d15075f227748034aa4715e336ce8d1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3847a2dde2ef2ac5d4dad578a0c0d10') in 0.0628803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_B_Root.FBX
  artifactKey: Guid(458b7b2c02eff2142811c72a65bd5aa7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_B_Root.FBX using Guid(458b7b2c02eff2142811c72a65bd5aa7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5560680cac5d40ecf033587abc6938e') in 0.0714861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R45.FBX
  artifactKey: Guid(d00f959b3625e014799295cf2d4db06a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R45.FBX using Guid(d00f959b3625e014799295cf2d4db06a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa8942d04f29a57f6e727c3fc79f1d08') in 0.0661994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_B_Root.FBX
  artifactKey: Guid(8d1e412187f559744b9c8bdd03ba793b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_B_Root.FBX using Guid(8d1e412187f559744b9c8bdd03ba793b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '11b9a42bb9f22983ead62cbe45c078a0') in 0.0700647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_BL45.FBX
  artifactKey: Guid(8f2de5404901a6b4a970192f3fa4d6b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_BL45.FBX using Guid(8f2de5404901a6b4a970192f3fa4d6b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8556c758d67350c6ed56ed1b04149f8') in 0.0591741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Run_B_Root.FBX
  artifactKey: Guid(1ee23f9497cb906408bd8e23b567fff1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Run_B_Root.FBX using Guid(1ee23f9497cb906408bd8e23b567fff1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49d0c82e422ec8e4192c5587fea5a5a2') in 0.074792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R45.FBX
  artifactKey: Guid(d453017615d32cd4a961a43dd69ddcb0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R45.FBX using Guid(d453017615d32cd4a961a43dd69ddcb0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '889164f96e147b37f145f614a11fe6ac') in 0.052456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_A_Root.FBX
  artifactKey: Guid(6db42a23cceb4ad458e864407b0cd989) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_A_Root.FBX using Guid(6db42a23cceb4ad458e864407b0cd989) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '262011fd31451079b919f251fcbfd80c') in 0.0625886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Notification_1.wav
  artifactKey: Guid(20a657152f1255c4ba9be5c232a078c4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Notification_1.wav using Guid(20a657152f1255c4ba9be5c232a078c4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7554278bbf91c0d81394fbb73e65b832') in 0.1708227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90.FBX
  artifactKey: Guid(f55f3229dd1625e469addd04479a5305) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90.FBX using Guid(f55f3229dd1625e469addd04479a5305) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7c8fdba834df8dcedf0f74775cca394') in 0.0593222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FL45.FBX
  artifactKey: Guid(f80c01dcf4c307f40b610590ad1ea628) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FL45.FBX using Guid(f80c01dcf4c307f40b610590ad1ea628) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc002051cbc50287bb485bdd8f70615b') in 0.0670196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90.FBX
  artifactKey: Guid(db0f0320226ef464684a68a224b73247) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90.FBX using Guid(db0f0320226ef464684a68a224b73247) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '487af31ef4562a17da3a70d5424e1c49') in 0.0602095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_B.FBX
  artifactKey: Guid(6443b6f2ac8220d459b5d9145787c623) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_B.FBX using Guid(6443b6f2ac8220d459b5d9145787c623) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1ec4de5571c6ff0f5ff0876ce2e2abc') in 0.0617107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_F.FBX
  artifactKey: Guid(f561c8c659cf37149891228483ea1eb3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_F.FBX using Guid(f561c8c659cf37149891228483ea1eb3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c740cdd38ef1cef455fcf56e7ecc3a23') in 0.069767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000126 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R45.FBX
  artifactKey: Guid(b67e9b69cd2d1bb43a4adbd5dd00145e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R45.FBX using Guid(b67e9b69cd2d1bb43a4adbd5dd00145e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ec0aa1a9aba42a4fd882f9b52950ddd') in 0.0622655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_Root_vol2.FBX
  artifactKey: Guid(bd7fe360e9a7ed245bf609419dfaacf3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_Root_vol2.FBX using Guid(bd7fe360e9a7ed245bf609419dfaacf3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7db2ffc49346d1d0cebafe60c024100a') in 0.0575986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_A_Turn_R90_Root.FBX
  artifactKey: Guid(f765f0df6e26bef4aa99cfcc77ec3e07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_A_Turn_R90_Root.FBX using Guid(f765f0df6e26bef4aa99cfcc77ec3e07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64957547830807b6f946c70654717aaa') in 0.0659559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Run_A_Turn_R90_Root.FBX
  artifactKey: Guid(3eb95b0bab2165f4a8a5052ee0989537) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Run_A_Turn_R90_Root.FBX using Guid(3eb95b0bab2165f4a8a5052ee0989537) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd71b126d15d5728d74987974519d3418') in 0.075609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_L90_Root.FBX
  artifactKey: Guid(4416703816e87a74c9079b3a80411132) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_L90_Root.FBX using Guid(4416703816e87a74c9079b3a80411132) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ccd8471941d6acbea93a983abba6001') in 0.0630674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_B.FBX
  artifactKey: Guid(c8111f94717854e42b81205ee0052e8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_B.FBX using Guid(c8111f94717854e42b81205ee0052e8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f979b6e007f8f56741665aa1ed07eeaf') in 0.06719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_A.FBX
  artifactKey: Guid(82a88803b2f76fb43a8d6e050765e23c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_A.FBX using Guid(82a88803b2f76fb43a8d6e050765e23c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '628f4a7a7a9e797815fe884001768932') in 0.0823576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_B_to_Run_A.FBX
  artifactKey: Guid(f416ca29f79980149a2b453c174b912a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_B_to_Run_A.FBX using Guid(f416ca29f79980149a2b453c174b912a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68a83dbc30f35dce04158d520b217571') in 0.0784482 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_B.FBX
  artifactKey: Guid(f68154593f312e946ba254d1a500af16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_B.FBX using Guid(f68154593f312e946ba254d1a500af16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7252feaa4a4263c9acb046f154fb7587') in 0.0742247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Jog_B.FBX
  artifactKey: Guid(e4ac3b879c4d48640b39809781df35bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Jog_B.FBX using Guid(e4ac3b879c4d48640b39809781df35bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b490916875a281a6070f49d05ffb6ac') in 0.1416308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_BR45_Root.FBX
  artifactKey: Guid(82c80b3651beac7468b3b9f75b59fdfe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_BR45_Root.FBX using Guid(82c80b3651beac7468b3b9f75b59fdfe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7cfa53cd5ed11315865268e585f4cc02') in 0.0595224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45.FBX
  artifactKey: Guid(4bbe169dcfd603d48b5964e57f1cdacf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45.FBX using Guid(4bbe169dcfd603d48b5964e57f1cdacf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc92e42eda54f83e6e05684ea7d013c1') in 0.0578241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 144

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_B_Root.FBX
  artifactKey: Guid(4d5e7bcf51c6ca14ca86f52a2d4a55fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_B_Root.FBX using Guid(4d5e7bcf51c6ca14ca86f52a2d4a55fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c72390f9090dc109b52ce7c65ad5bfe') in 0.0667078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_A.FBX
  artifactKey: Guid(2e678544eb6dd7140b82219c801ba697) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_A.FBX using Guid(2e678544eb6dd7140b82219c801ba697) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '636a31b0e318f0eaa162a9e30684329a') in 0.1013348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_FR45.FBX
  artifactKey: Guid(ce5ac1a5a162c01449c48c13f1bc28ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_FR45.FBX using Guid(ce5ac1a5a162c01449c48c13f1bc28ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95e5d84d19d4a6dc2096e6f64819269c') in 0.0598918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_A_Root.FBX
  artifactKey: Guid(2b41ba939e16ba842a001ffbf720e9ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_A_Root.FBX using Guid(2b41ba939e16ba842a001ffbf720e9ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dc10c0d76907f5a52b54feaf51eba49') in 0.0596484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_A_to_Run_B.FBX
  artifactKey: Guid(b9dd7b62c744e2144b408ee385e4d39b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_A_to_Run_B.FBX using Guid(b9dd7b62c744e2144b408ee385e4d39b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83c6350559176efb573c73afb60dd945') in 0.0689246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_L90_Root.FBX
  artifactKey: Guid(b07526eed0a35b14db12ad2fcd1bd301) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_L90_Root.FBX using Guid(b07526eed0a35b14db12ad2fcd1bd301) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74a5e175c5647a7fd5a063c39c6d61b2') in 0.0664366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R45_Root.FBX
  artifactKey: Guid(b83d51966ad32d94c8129f24f7ff40dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R45_Root.FBX using Guid(b83d51966ad32d94c8129f24f7ff40dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '445ba690b6d9752930f155372a3dd9a3') in 0.0613288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90.FBX
  artifactKey: Guid(eee699e1f975cd944a5b462a2a16cc0f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90.FBX using Guid(eee699e1f975cd944a5b462a2a16cc0f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a718f8df03d9e095f82fd60602a6d41') in 0.0563516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R90.FBX
  artifactKey: Guid(29c66b69a51fa714b98251901692238e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R90.FBX using Guid(29c66b69a51fa714b98251901692238e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9676405c6c3982fb723181e6cc0c4e8d') in 0.0659448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_Root_vol2.FBX
  artifactKey: Guid(64a4a59bc23ac304e9b628bc8bfdf767) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_Root_vol2.FBX using Guid(64a4a59bc23ac304e9b628bc8bfdf767) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f31d748a664ef7f96a50c880cb2ebee') in 0.05892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45_Root.FBX
  artifactKey: Guid(94be1451cf5c0ae4e93ffb72c0ab093a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45_Root.FBX using Guid(94be1451cf5c0ae4e93ffb72c0ab093a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fdc316a0af1725c71e653558c31ad389') in 0.0699145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000257 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L45_Root.FBX
  artifactKey: Guid(d9e8f5e2a6a708047a9a5ca329c59a9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L45_Root.FBX using Guid(d9e8f5e2a6a708047a9a5ca329c59a9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30bebecf840a2aab7944a19fb5fa0cc7') in 0.0760987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleFat_01.fbx
  artifactKey: Guid(abc00000000015148745820496095237) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleFat_01.fbx using Guid(abc00000000015148745820496095237) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09ffc6ff538f28a0f565b3fb1052976a') in 0.0744221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/SKM_Flag_03.fbx
  artifactKey: Guid(abc00000000012410491015809110139) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/SKM_Flag_03.fbx using Guid(abc00000000012410491015809110139) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23c2413c80ea0418c3adc53511d6c4d5') in 0.0577816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_03.fbx
  artifactKey: Guid(abc00000000011015970625176817909) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_03.fbx using Guid(abc00000000011015970625176817909) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf5454e300991d11b567c45efa5c082d') in 0.0739111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BrickA.fbx
  artifactKey: Guid(abc00000000005990758186216283694) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BrickA.fbx using Guid(abc00000000005990758186216283694) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '908fde0af16bca49945754f50ad76993') in 0.0598301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_D.fbx
  artifactKey: Guid(abc00000000015488143439190917297) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_D.fbx using Guid(abc00000000015488143439190917297) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9720e627f83c2e7a568bf0817f0c59f5') in 0.0560895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_InsideCorner_01.fbx
  artifactKey: Guid(abc00000000008973618831149238585) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_InsideCorner_01.fbx using Guid(abc00000000008973618831149238585) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3f453d58cad73ca18280fe848f55ee4') in 0.1119532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_B.fbx
  artifactKey: Guid(abc00000000011868328685341555375) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_B.fbx using Guid(abc00000000011868328685341555375) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '377223ce46ea5ace4663d5ddbe5e5df8') in 0.064658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_01.fbx
  artifactKey: Guid(abc00000000013959434600874991393) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_01.fbx using Guid(abc00000000013959434600874991393) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a44f88f80ebd8f51661442fc441b2f4e') in 0.1066343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_02.fbx
  artifactKey: Guid(abc00000000003882357367172173590) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_02.fbx using Guid(abc00000000003882357367172173590) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78add34ce13e1f519169ce2eab7c6a5e') in 0.0851294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ConicalCauldron_01.fbx
  artifactKey: Guid(abc00000000012290417655589196929) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ConicalCauldron_01.fbx using Guid(abc00000000012290417655589196929) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a7974f5f761aaa020a66a3ca4be9c42') in 0.0943685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_07.mat
  artifactKey: Guid(abc00000000017923775067250159663) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_07.mat using Guid(abc00000000017923775067250159663) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44a430b98de2d17c813040913d874cef') in 0.0493342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 - Begin.fbx
  artifactKey: Guid(abfd5440ec4350b46bdc9de604553b7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 - Begin.fbx using Guid(abfd5440ec4350b46bdc9de604553b7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a41279b0059fe9c081e1118c2cb773e') in 0.0740072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_ForwardRight.fbx
  artifactKey: Guid(a82fb70254a844243a7fe7e02b1ff809) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_ForwardRight.fbx using Guid(a82fb70254a844243a7fe7e02b1ff809) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aec44fde1273999a88d2cf9cbe6ede87') in 0.0719073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_BackwardLeft.fbx
  artifactKey: Guid(daf6bb02eddf35e4e8cfcb493de60917) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_BackwardLeft.fbx using Guid(daf6bb02eddf35e4e8cfcb493de60917) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6b25954055f777974f4f68f0521e803') in 0.0572987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM] - Land.fbx
  artifactKey: Guid(2d5dcba826921c444bb225aa66997abd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM] - Land.fbx using Guid(2d5dcba826921c444bb225aa66997abd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b54f9e74fb1e96d22ed4b2385055bc79') in 0.084246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Right.fbx
  artifactKey: Guid(0e7376aa8f8f38e4fb138892f92fe2e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Right.fbx using Guid(0e7376aa8f8f38e4fb138892f92fe2e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebde28fba58f935aa31c1eb930b4a396') in 0.0686538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_ForwardRight.fbx
  artifactKey: Guid(dd9cde7e792f5f946b091935d7903296) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_ForwardRight.fbx using Guid(dd9cde7e792f5f946b091935d7903296) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08fab3e48aa1e1d322fdcfa16478c7b3') in 0.0565403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_BackwardLeft.fbx
  artifactKey: Guid(5dcc71b9770f2554e8fd3ea0d6c1e1f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_BackwardLeft.fbx using Guid(5dcc71b9770f2554e8fd3ea0d6c1e1f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a883e13648acc6fabe7ec23a4791607') in 0.059776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000330 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_ForwardLeft.fbx
  artifactKey: Guid(3a4cf5e04ded562489d1c3b2da8c2d7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_ForwardLeft.fbx using Guid(3a4cf5e04ded562489d1c3b2da8c2d7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8c38f518710d9450d33a34c0992dac0') in 0.0690987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_BackwardRight.fbx
  artifactKey: Guid(55d43189338018e4c9e0c2ce1f608563) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_BackwardRight.fbx using Guid(55d43189338018e4c9e0c2ce1f608563) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c7b70174e0376b79c15a7975666e917') in 0.0581808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Metal.mat
  artifactKey: Guid(2a0813725c6e2554093fd0a433694695) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Metal.mat using Guid(2a0813725c6e2554093fd0a433694695) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0dd2cfa18b8e7a95dd866a1756eef16') in 0.0662034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Throne.fbx
  artifactKey: Guid(abc00000000013900546309092199213) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Throne.fbx using Guid(abc00000000013900546309092199213) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e81543c99e276915ef7d33452762e2d') in 0.0846774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Books_A.fbx
  artifactKey: Guid(abc00000000018288007217276856527) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Books_A.fbx using Guid(abc00000000018288007217276856527) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3918ef8803dbf1d0cd4354eb0d8256c') in 0.1368132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/Materials/MI_Mountain_Grass.mat
  artifactKey: Guid(d62d111a5889d754d98ea69dae5dfeea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/Materials/MI_Mountain_Grass.mat using Guid(d62d111a5889d754d98ea69dae5dfeea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8dd8d81424c8971a8596de0f59b66622') in 0.0631407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/No Name.mat
  artifactKey: Guid(5871e2040154eb3488c707d8d2ae4901) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/No Name.mat using Guid(5871e2040154eb3488c707d8d2ae4901) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1583baa76198d9248f15c5a3377d34d8') in 0.0576811 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Small.fbx
  artifactKey: Guid(abc00000000008597551560380296582) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Small.fbx using Guid(abc00000000008597551560380296582) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bb65268c0a42fa311f3484ff58d93c4') in 0.0676656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_Wood_A1.mat
  artifactKey: Guid(f3a94a35bd314144ba17199d0b9518e7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_Wood_A1.mat using Guid(f3a94a35bd314144ba17199d0b9518e7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36c699e6fb6b07162c58d13f176dae00') in 0.0577578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/<EMAIL>
  artifactKey: Guid(d0d93c131bda12e4bb9798ea4aefb6cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/<EMAIL> using Guid(d0d93c131bda12e4bb9798ea4aefb6cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b689f833ece1338225ca1b839a5264c') in 0.0637749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_BackwardLeft.fbx
  artifactKey: Guid(02fe127be7c987640bef36b78efaf2cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_BackwardLeft.fbx using Guid(02fe127be7c987640bef36b78efaf2cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2eae42afd750de657bfb64d9e7993f1e') in 0.0640163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Terracotta.mat
  artifactKey: Guid(65df7e54c3b5b574d9de8bcd8734c188) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Terracotta.mat using Guid(65df7e54c3b5b574d9de8bcd8734c188) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9698af7460b48f2ed8a5213268ca87a') in 0.0762847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Small.fbx
  artifactKey: Guid(abc00000000009580784855821925472) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Small.fbx using Guid(abc00000000009580784855821925472) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7898d93d20671483d0c1495b8009e95e') in 0.0535477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/Materials/MI_StoneFence_01.mat
  artifactKey: Guid(a60f19105db9ecd4e95f6e9faafa61fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/Materials/MI_StoneFence_01.mat using Guid(a60f19105db9ecd4e95f6e9faafa61fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d11d3bad3e553f0bc3bb21b86b97943') in 0.0624099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Pillow.fbx
  artifactKey: Guid(abc00000000000169206303256278026) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Pillow.fbx using Guid(abc00000000000169206303256278026) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44800fbb9dc3f38a1d903c8a273f199a') in 0.0680948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/SM_Lantern_Hanging.fbx
  artifactKey: Guid(abc00000000008865977456052000153) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/SM_Lantern_Hanging.fbx using Guid(abc00000000008865977456052000153) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4af081d3b1a15eb463ce84e24af23554') in 0.0913125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/Materials/No Name.mat
  artifactKey: Guid(3573efdedb265ec4087efb338bb1f31a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/Materials/No Name.mat using Guid(3573efdedb265ec4087efb338bb1f31a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67fb67ec642114d307f6c10a5889d62a') in 0.0552498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_04.fbx
  artifactKey: Guid(abc00000000007786589743757212811) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_04.fbx using Guid(abc00000000007786589743757212811) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e44cced36128856a6bf4a62306b725a') in 0.059317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_GrainSack.mat
  artifactKey: Guid(04ee18f262476634b950e1a7db591c0f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_GrainSack.mat using Guid(04ee18f262476634b950e1a7db591c0f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5538dee3ab2bda1bc8492238cc611008') in 0.072438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_03.fbx
  artifactKey: Guid(abc00000000015428202051574194882) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_03.fbx using Guid(abc00000000015428202051574194882) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a74ca01dbb9256ed3e73a634cf829fd') in 0.0858513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_02.fbx
  artifactKey: Guid(abc00000000014983738814852955221) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_02.fbx using Guid(abc00000000014983738814852955221) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2f9c30b9cc10bc29d2103e494ca5cd3') in 0.0710504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Backward [RM].fbx
  artifactKey: Guid(78375be644e8f3a45bdc574dcc4fdb45) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Backward [RM].fbx using Guid(78375be644e8f3a45bdc574dcc4fdb45) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae30ccd587ca9f4087b699075e3ed615') in 0.0778738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_07.mat
  artifactKey: Guid(c36e33123c64a5d4eb2a8cbfda60245b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_07.mat using Guid(c36e33123c64a5d4eb2a8cbfda60245b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69769d426ac4fe28f74f6742e8d3a108') in 0.0657608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/MI_Stab_Algae.mat
  artifactKey: Guid(b1c20e91a569f954d8099fa34e2dc092) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/MI_Stab_Algae.mat using Guid(b1c20e91a569f954d8099fa34e2dc092) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd8f1730d162d06e696999eb773c0e8c') in 0.0520881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/MI_StoneFence_05.mat
  artifactKey: Guid(1a6fb1ebdb8a6d040aa9704ed3a55ba6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/MI_StoneFence_05.mat using Guid(1a6fb1ebdb8a6d040aa9704ed3a55ba6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30599d99804dea85611c551e173b0db2') in 0.034304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_03.mat
  artifactKey: Guid(f53667f09f9792940b8a418bae35ccf2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_03.mat using Guid(f53667f09f9792940b8a418bae35ccf2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a1be9149d650d878ce8bfdbe2b2adb9') in 0.0647847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/Materials/MI_Fruit.mat
  artifactKey: Guid(bda418481e1228c4ea86032016e8413a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/Materials/MI_Fruit.mat using Guid(bda418481e1228c4ea86032016e8413a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0e70b496ad37b340d84310d66136e70') in 0.0713809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000213 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_InteriorCups.mat
  artifactKey: Guid(0a423f92059f84043b0e8b8866497dad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_InteriorCups.mat using Guid(0a423f92059f84043b0e8b8866497dad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1499c5cb9751910411633084223fc164') in 0.0577148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Chair.fbx
  artifactKey: Guid(abc00000000011438385616858865960) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Chair.fbx using Guid(abc00000000011438385616858865960) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba89b7dfc14733c193935cc644f422ff') in 0.0827386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Door_02.fbx
  artifactKey: Guid(abc00000000001365811951907279839) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Door_02.fbx using Guid(abc00000000001365811951907279839) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f329997f0aca1a87aaec77a596cacb9e') in 0.0711902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Staircase.fbx
  artifactKey: Guid(abc00000000015994822698776280386) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Staircase.fbx using Guid(abc00000000015994822698776280386) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51b46a33fcfc5d00f1e36790a7722395') in 0.0891415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_07.fbx
  artifactKey: Guid(abc00000000000355123988355932061) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_07.fbx using Guid(abc00000000000355123988355932061) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afc993cb678fa608c9d9453caf8475b0') in 0.0625981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000143 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_B_01.fbx
  artifactKey: Guid(abc00000000008231839084180961039) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_B_01.fbx using Guid(abc00000000008231839084180961039) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c93791298ac4ea6ea7199bb99aadee5') in 0.0740908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_04.fbx
  artifactKey: Guid(abc00000000007355659954481028894) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_04.fbx using Guid(abc00000000007355659954481028894) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46c4bb6d713acca079ce0797ada461da') in 0.1052059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_02.fbx
  artifactKey: Guid(abc00000000006717611676607273159) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_02.fbx using Guid(abc00000000006717611676607273159) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3167013ef31552f11e4e2f141bec600d') in 0.0853276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_12.fbx
  artifactKey: Guid(abc00000000002160915120409094753) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_12.fbx using Guid(abc00000000002160915120409094753) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2d176c12664dac275020c497cb7b0d8') in 0.1370215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_14.fbx
  artifactKey: Guid(abc00000000002867943825952970807) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_14.fbx using Guid(abc00000000002867943825952970807) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a80325a7b561ce20303c48e64d579fe') in 0.1507154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_09.fbx
  artifactKey: Guid(abc00000000004270753370864287817) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_09.fbx using Guid(abc00000000004270753370864287817) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc405d511802d0a582d38959015a062d') in 0.1466337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(688965bbe569f0d4893044716758e4c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_StoneFence_02.mat using Guid(688965bbe569f0d4893044716758e4c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6cbc200da0bee274ebea3bfd89da9e1') in 0.0667899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_02.fbx
  artifactKey: Guid(abc00000000003393361683669531608) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_02.fbx using Guid(abc00000000003393361683669531608) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e82a2d404cdde88cfbbc823f9ebf621') in 0.1022388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_01.fbx
  artifactKey: Guid(abc00000000002514632831483069760) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_01.fbx using Guid(abc00000000002514632831483069760) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3af440c01d71dd809c80516d91b5284c') in 0.0541296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/MI_Ivy_A.mat
  artifactKey: Guid(711d539cc49593a4482ef00fa5664104) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/MI_Ivy_A.mat using Guid(711d539cc49593a4482ef00fa5664104) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '995bff5b57b39cd25ba7117d09b0e622') in 0.0540871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_Metal.mat
  artifactKey: Guid(7e578ee7480d6d0418a2fe99ea8f8bcd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_Metal.mat using Guid(7e578ee7480d6d0418a2fe99ea8f8bcd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9915823da480077a0b3fa10162d75967') in 0.0629014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(5e94aad3643ac7d4fa5d38f71582df41) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_StoneFence_02.mat using Guid(5e94aad3643ac7d4fa5d38f71582df41) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06acb70efb6d837737db686d3348c7c1') in 0.0574273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Rug.fbx
  artifactKey: Guid(abc00000000010094762503398481764) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Rug.fbx using Guid(abc00000000010094762503398481764) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c18d50723e4f732055575694b5a5e0a') in 0.0790805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/No Name.mat
  artifactKey: Guid(2df8bac2ac4868549b4dc294390dfbc4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/No Name.mat using Guid(2df8bac2ac4868549b4dc294390dfbc4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bde9a37a5dd0faba3033ce978e49b424') in 0.0552564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_01.fbx
  artifactKey: Guid(abc00000000007525055802756573481) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_01.fbx using Guid(abc00000000007525055802756573481) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ee86792ed15cef5bc0e9517ccf8cc45') in 0.0687801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/No Name.mat
  artifactKey: Guid(ebf5e878ce121ad4ba2267e174d9b761) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/No Name.mat using Guid(ebf5e878ce121ad4ba2267e174d9b761) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af7c5e04be656f2c8c68c4a8b62d034f') in 0.0620672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_StoneFence_03.mat
  artifactKey: Guid(79cd9e197ba94844bafff11a539755bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_StoneFence_03.mat using Guid(79cd9e197ba94844bafff11a539755bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6bc6e4183cb1529b1de445b71af83357') in 0.0627181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/Materials/MI_SilverFir_Bark_01.mat
  artifactKey: Guid(79baf8fb71bdc224d86c3ff98e21eda7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/Materials/MI_SilverFir_Bark_01.mat using Guid(79baf8fb71bdc224d86c3ff98e21eda7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbb57d8be64d06129dd6f150e4102fdb') in 0.0531868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000232 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Bark_01.mat
  artifactKey: Guid(abc00000000006514300709433027238) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Bark_01.mat using Guid(abc00000000006514300709433027238) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ede5041ec9ecb7e3a0edcd248ea9cfd8') in 0.0420217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/SM_Fence_01c.fbx
  artifactKey: Guid(abc00000000006791748827284986935) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/SM_Fence_01c.fbx using Guid(abc00000000006791748827284986935) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c51f61cc477a435452b000a074705a40') in 0.0930268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R45.FBX
  artifactKey: Guid(66c06930f90d8a74ba326ddc3eed5685) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R45.FBX using Guid(66c06930f90d8a74ba326ddc3eed5685) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b229f0e2e9644dc8b6f70835acd5bcb') in 0.0570132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/SM_Mountain_03.fbx
  artifactKey: Guid(abc00000000008069195115178968953) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/SM_Mountain_03.fbx using Guid(abc00000000008069195115178968953) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a210c7de333f2f80b0a2f9281ed7d30') in 0.0821885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/Materials/No Name.mat
  artifactKey: Guid(d475214034863354798b04fc3855666e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/Materials/No Name.mat using Guid(d475214034863354798b04fc3855666e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff8281796fef07f880cb8d6cdedfaf6d') in 0.0565054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_B.FBX
  artifactKey: Guid(5d8abf63cd7b73045a92ffdfecfd3c48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_B.FBX using Guid(5d8abf63cd7b73045a92ffdfecfd3c48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53d5ffcbd27021df2bd54cff5d2c89bb') in 0.0721911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Jump01 [RM].controller
  artifactKey: Guid(2041c0db49de8e94ea644176ce03232c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Jump01 [RM].controller using Guid(2041c0db49de8e94ea644176ce03232c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0240b2ea81fd4d4a2cad5cbe91a6b6c') in 0.0324085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lantern_ORM.PNG
  artifactKey: Guid(c6db396725b65114b9eb64c0f6e1a421) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lantern_ORM.PNG using Guid(c6db396725b65114b9eb64c0f6e1a421) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b4cb82c2966e40cfdb4eee621526b17') in 0.051077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_B_To_Crouch.FBX
  artifactKey: Guid(6998c5a82ddef3a4bbfb13154e8090a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_B_To_Crouch.FBX using Guid(6998c5a82ddef3a4bbfb13154e8090a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a115214727f0784a01b1e1cd1634b0bd') in 0.0671339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_ALL.FBX
  artifactKey: Guid(434b1704a6ba9c1458b8d0f2c9448e6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_ALL.FBX using Guid(434b1704a6ba9c1458b8d0f2c9448e6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3671dff30194e79ed1a5aafd7e154b22') in 0.0843738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fire_MV_01.PNG
  artifactKey: Guid(c4094c67cbf09a2428eed2145f995a18) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fire_MV_01.PNG using Guid(c4094c67cbf09a2428eed2145f995a18) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '166e9b302574addc90df935b3b69f085') in 0.0803712 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_01.png
  artifactKey: Guid(7339f85875d8ff64eb0cff82abdad3c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_01.png using Guid(7339f85875d8ff64eb0cff82abdad3c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c049108eec2b8699b6dc9e7eea5c6050') in 0.0776271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_1_Inplace.FBX
  artifactKey: Guid(9b7d649f82b34544e92cb4f402d0f424) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_1_Inplace.FBX using Guid(9b7d649f82b34544e92cb4f402d0f424) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90046c159c9c2a8f1031c2bfbb37e3ae') in 0.0685661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_03.prefab
  artifactKey: Guid(abc00000000010198080389812662350) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_03.prefab using Guid(abc00000000010198080389812662350) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8427bf7944f0f3ed29c29a51191f6f6d') in 0.0956802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_ALL.FBX
  artifactKey: Guid(042e058fddc662c4693e02cf0443a992) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_ALL.FBX using Guid(042e058fddc662c4693e02cf0443a992) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '786173c20cd9eeddf76d3a6ff513e74c') in 0.1132927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0