Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:19Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker5.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16308]  Target information:

Player connection [16308]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1186174500 [EditorId] 1186174500 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [16308] Host joined multi-casting on [***********:54997]...
Player connection [16308] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.92 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.63 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56404
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004347 seconds.
- Loaded All Assemblies, in  1.108 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.950 seconds
Domain Reload Profiling: 2057ms
	BeginReloadAssembly (314ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (132ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (131ms)
	LoadAllAssembliesAndSetupDomain (492ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (483ms)
			TypeCache.Refresh (480ms)
				TypeCache.ScanAssembly (443ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (814ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (57ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (160ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.317 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 8.64 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.014 seconds
Domain Reload Profiling: 5328ms
	BeginReloadAssembly (436ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (104ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (106ms)
	LoadAllAssembliesAndSetupDomain (1636ms)
		LoadAssemblies (842ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1044ms)
			TypeCache.Refresh (817ms)
				TypeCache.ScanAssembly (763ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (35ms)
	FinalizeReload (3015ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2396ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (359ms)
			ProcessInitializeOnLoadAttributes (1783ms)
			ProcessInitializeOnLoadMethodAttributes (228ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 8.30 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.6 MB). Loaded Objects now: 8334.
Memory consumption went from 209.9 MB to 199.3 MB.
Total: 73.987800 ms (FindLiveObjects: 2.456500 ms CreateObjectMapping: 44.695500 ms MarkObjects: 14.806700 ms  DeleteObjects: 12.026300 ms)

========================================================================
Received Import Request.
  Time since last request: 1812180.556390 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_UnderwaterFoam.prefab
  artifactKey: Guid(dc2259ef953c36249b7ee875f2ab9bd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_UnderwaterFoam.prefab using Guid(dc2259ef953c36249b7ee875f2ab9bd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9cbe53607f675aac6d8d74a5ab7b70e0') in 3.4538983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Settings/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acbafadf30507047fda7ec957c823dad') in 1.144364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_MainLaser.prefab
  artifactKey: Guid(9e068b519e04d9b4d8c4ddb01fcb0230) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_MainLaser.prefab using Guid(9e068b519e04d9b4d8c4ddb01fcb0230) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '764ca08be0b4df2acba2e8ac43ceb193') in 0.8964782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 167

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset
  artifactKey: Guid(14b392ee213d25a48b1feddbd9f5a9be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset using Guid(14b392ee213d25a48b1feddbd9f5a9be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6fcc176814620e27d7a736a695e632be') in 0.380663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_01.mat
  artifactKey: Guid(ca8713e47e18a4c4d8a69b3ce2f93125) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_01.mat using Guid(ca8713e47e18a4c4d8a69b3ce2f93125) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25d8e0d969dc9f0f4a11e1cf3da18c6f') in 1.4208197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_SimpleWaterImpact_01.prefab
  artifactKey: Guid(8cf651a7455605447a72526bde98a106) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_SimpleWaterImpact_01.prefab using Guid(8cf651a7455605447a72526bde98a106) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e13e616ee91c3cb7828e70a4461691d1') in 0.0678494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 53

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN_Nude Variant.prefab
  artifactKey: Guid(29733b8cadf4a5641b3c540355fd5562) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN_Nude Variant.prefab using Guid(29733b8cadf4a5641b3c540355fd5562) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '80661bbb986b4e630e8b733114ce299a') in 0.2163222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 606

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Settings/PC_RPAsset.asset
  artifactKey: Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/PC_RPAsset.asset using Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b09dfeb8c13fe8fe6caa0e30023874ca') in 0.0290186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_03.mat
  artifactKey: Guid(dc7f5e3fcb0099442a355af24094faef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_03.mat using Guid(dc7f5e3fcb0099442a355af24094faef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0e467173b76332aafee3f34def00a59') in 0.7500219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/TextMesh Pro/Resources/Style Sheets/Default Style Sheet.asset
  artifactKey: Guid(f952c082cb03451daed3ee968ac6c63e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/TextMesh Pro/Resources/Style Sheets/Default Style Sheet.asset using Guid(f952c082cb03451daed3ee968ac6c63e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0b4c5b2e8f82e76799ea37a062f168d1') in 0.0296963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_MaskMap.png
  artifactKey: Guid(ff9f8ab7d6a0dd24590ef42fc80f07a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_MaskMap.png using Guid(ff9f8ab7d6a0dd24590ef42fc80f07a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '915a6d13c15a388a3c3c27416d463014') in 0.2144221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Burst.prefab
  artifactKey: Guid(8aaf150745b9e5249a8493d76a62e133) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Burst.prefab using Guid(8aaf150745b9e5249a8493d76a62e133) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0cec4b22a27815c4c35c60636e70f7d8') in 0.0917228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 108

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Cast.prefab
  artifactKey: Guid(58a7e320279bf39449f0b6bb2277fef5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Cast.prefab using Guid(58a7e320279bf39449f0b6bb2277fef5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '89a5886d013e6e25d7678f02091ccac6') in 0.0593587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.prefab
  artifactKey: Guid(df30ede524bb3004ca77db09fb3d842f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.prefab using Guid(df30ede524bb3004ca77db09fb3d842f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '38daf735fef5effe9ed227eeeea8dbdb') in 0.3267792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Speed.prefab
  artifactKey: Guid(e263a650d9724a04fb16a49a08c8db3d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Speed.prefab using Guid(e263a650d9724a04fb16a49a08c8db3d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '46e4930e8d95bae1f22e7966bdb0ae09') in 0.0778651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01_1.prefab
  artifactKey: Guid(abc00000000006961437410837463222) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01_1.prefab using Guid(abc00000000006961437410837463222) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'adc3ec616a09b8ce82362c04143df66e') in 0.0358094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Anvil.prefab
  artifactKey: Guid(abc00000000000663048425925687157) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Anvil.prefab using Guid(abc00000000000663048425925687157) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0feb8313ead7bea5c0a2799370fd8946') in 0.0343767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_02.png
  artifactKey: Guid(b472b7b827d95fc43b32f1b65ce3d4e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_02.png using Guid(b472b7b827d95fc43b32f1b65ce3d4e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '025ffc1136255b762bedcb917f7bd21a') in 0.0625436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Bridge.prefab
  artifactKey: Guid(bab102f11e052da4f812440b4e2552f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Bridge.prefab using Guid(bab102f11e052da4f812440b4e2552f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd17936f5da80fbc7037a100ecf33497d') in 0.0576193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RainDrop_01.mat
  artifactKey: Guid(e47373fcd76d03e43bc54f9b144dfd82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RainDrop_01.mat using Guid(e47373fcd76d03e43bc54f9b144dfd82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f87114e00611a3c7c8dedeae172a0a04') in 0.0599956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_02.prefab
  artifactKey: Guid(abc00000000012482503717944588971) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_02.prefab using Guid(abc00000000012482503717944588971) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e74c237c4529ceb11bc8af68962e211e') in 0.0366089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_06_3x4.mat
  artifactKey: Guid(3bb9dcd66e72e8c47847d2b77ae00886) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_06_3x4.mat using Guid(3bb9dcd66e72e8c47847d2b77ae00886) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e58bf67bddb64bf2132de6da7657479') in 0.5639547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bucket_01.prefab
  artifactKey: Guid(abc00000000007708634073316547047) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bucket_01.prefab using Guid(abc00000000007708634073316547047) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '47a8933d524fe5a0437a2f5778b879e8') in 0.0353872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cannon.prefab
  artifactKey: Guid(abc00000000011630753976887870194) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cannon.prefab using Guid(abc00000000011630753976887870194) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5958d1307f4f051950c3f51060969963') in 0.0399713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_02.prefab
  artifactKey: Guid(abc00000000005620762606997660925) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_02.prefab using Guid(abc00000000005620762606997660925) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8bcb3c3aa98b6c54d797929add3ac333') in 0.0378292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_2.prefab
  artifactKey: Guid(abc00000000003552623074302713318) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_2.prefab using Guid(abc00000000003552623074302713318) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ea823cc63e308fd52a5123398b169cb') in 0.0373197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000192 seconds.
  path: Assets/playercontroller/Player.prefab
  artifactKey: Guid(04e8e2d0aed9b5143b616c0fa3bbf573) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/playercontroller/Player.prefab using Guid(04e8e2d0aed9b5143b616c0fa3bbf573) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d5a8c1bf53c8e12cc64b17400dca10d') in 0.3398809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 392

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A.prefab
  artifactKey: Guid(abc00000000001070414280521708689) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A.prefab using Guid(abc00000000001070414280521708689) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2d6b04dd36651edf230a42946ec8def2') in 0.0344759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_10.prefab
  artifactKey: Guid(abc00000000008244969905526030465) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_10.prefab using Guid(abc00000000008244969905526030465) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5042248fddb44563689673bc0712862b') in 0.04625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_0.prefab
  artifactKey: Guid(abc00000000007783163857777427256) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_0.prefab using Guid(abc00000000007783163857777427256) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0ba9ee791863387d0aa0d01834b9e89a') in 0.0438879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_05.prefab
  artifactKey: Guid(abc00000000007776285115681869499) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_05.prefab using Guid(abc00000000007776285115681869499) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '248367697742100cc4ce806f0661f664') in 0.0442776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_68.prefab
  artifactKey: Guid(abc00000000007327313589950001394) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_68.prefab using Guid(abc00000000007327313589950001394) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bf3ad7cd6c3aacbcd915331bfb63c2e1') in 0.0375931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_67.prefab
  artifactKey: Guid(abc00000000000619213052653483789) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_67.prefab using Guid(abc00000000000619213052653483789) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e9e41a1e563d6a91720811ceac8cb609') in 0.0398604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_09_3x4.mat
  artifactKey: Guid(ec1b6f05acf41d24e922ae1d6feeb2e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_09_3x4.mat using Guid(ec1b6f05acf41d24e922ae1d6feeb2e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84c2bd28c145fefb5988e1b042eb92c7') in 0.6059108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8_Poison.mat
  artifactKey: Guid(47fabcbbd6c5cb442804654dbef3c8a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8_Poison.mat using Guid(47fabcbbd6c5cb442804654dbef3c8a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5df2dba251b21487b287ad866531118f') in 0.5463486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_10_3x4.mat
  artifactKey: Guid(a5a7392333eb2b24090ef2cea193852f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_10_3x4.mat using Guid(a5a7392333eb2b24090ef2cea193852f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '998a4e6e40a23b94a13adfb3416f01f4') in 0.0430031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_21.prefab
  artifactKey: Guid(abc00000000006136644422052381714) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_21.prefab using Guid(abc00000000006136644422052381714) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8e4a08b18c81d854896eafeaeaa19b42') in 0.291426 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_29.prefab
  artifactKey: Guid(abc00000000018100680694111923930) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_29.prefab using Guid(abc00000000018100680694111923930) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8f36d2b3a9719b6a4f7e6e201e823b96') in 0.0599562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Doorway_2M.prefab
  artifactKey: Guid(abc00000000016025576490165946346) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Doorway_2M.prefab using Guid(abc00000000016025576490165946346) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '78d8d3200f5d66b7c38980616fae542b') in 0.040807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_1.prefab
  artifactKey: Guid(abc00000000015334481626655366618) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_1.prefab using Guid(abc00000000015334481626655366618) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a2d08f1b6b778dd7c373a80aa0522af0') in 0.037651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Human Basic Motions 2.4 FREE.pdf
  artifactKey: Guid(9fefcdfef4f1bde45bef8b66dd81e417) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Human Basic Motions 2.4 FREE.pdf using Guid(9fefcdfef4f1bde45bef8b66dd81e417) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '508a2656a0f2f0b4f9768cd86712a682') in 0.0288072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Flagon_01.prefab
  artifactKey: Guid(abc00000000009001316097004371478) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Flagon_01.prefab using Guid(abc00000000009001316097004371478) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b84628a4d340721e09be63f99a3149cb') in 0.0451619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fireplace_02.prefab
  artifactKey: Guid(abc00000000012776357510189468995) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fireplace_02.prefab using Guid(abc00000000012776357510189468995) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6c9f19d5012c9cecbc49ee0e4232263f') in 0.0640911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab
  artifactKey: Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab using Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2e7327615915ad55cf61e636118b1d91') in 0.0627929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 158

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_01_4x3.mat
  artifactKey: Guid(960a18adee4d1634d87136cca049cc20) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_01_4x3.mat using Guid(960a18adee4d1634d87136cca049cc20) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb15fb0b664f0ed8f8497b3513da256f') in 0.5512106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_08.mat
  artifactKey: Guid(27bb66f9e3db74c418879daa880272f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_08.mat using Guid(27bb66f9e3db74c418879daa880272f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '650e7f2a045317a662a9850a8cb821c7') in 0.470538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterTrail_02.mat
  artifactKey: Guid(e75dd7f578e1e104899e4f53daa1e4e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterTrail_02.mat using Guid(e75dd7f578e1e104899e4f53daa1e4e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e493646e6b6cf7c639ea66bdc001318f') in 0.5238067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug.prefab
  artifactKey: Guid(abc00000000000690484132462057524) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug.prefab using Guid(abc00000000000690484132462057524) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '39449bd9da8c5894d9a5391da434421a') in 0.0362988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis.prefab
  artifactKey: Guid(abc00000000015397822618480844408) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis.prefab using Guid(abc00000000015397822618480844408) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3aedbbcd4f1cb5aa6b45c1767178d643') in 0.0414021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rock_Large_A.prefab
  artifactKey: Guid(abc00000000012409974318942294950) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rock_Large_A.prefab using Guid(abc00000000012409974318942294950) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6252e6cbc4b328fa7a2426f770275da1') in 0.0402895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12.prefab
  artifactKey: Guid(abc00000000017704846649272887479) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12.prefab using Guid(abc00000000017704846649272887479) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7005e31da96769e0bb15e1afa2594b90') in 0.0515743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_14_3x4.mat
  artifactKey: Guid(97f446cae07af514ca4d4755d84cfef3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_14_3x4.mat using Guid(97f446cae07af514ca4d4755d84cfef3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd6708708cb05b33f35d3a23a0902195') in 0.0412092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_01.prefab
  artifactKey: Guid(abc00000000001624541851004027658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_01.prefab using Guid(abc00000000001624541851004027658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5e8763869645ce0ad5a48e197b4a6a27') in 0.0417555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07.prefab
  artifactKey: Guid(abc00000000014093375833404509006) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07.prefab using Guid(abc00000000014093375833404509006) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a0acdb44fd9179e903b7b14ac483bc63') in 0.0446096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01_1.prefab
  artifactKey: Guid(abc00000000008997333683276363716) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01_1.prefab using Guid(abc00000000008997333683276363716) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a84a4fff77ef3e877aa6e8b969035be6') in 0.0513247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans.ttf
  artifactKey: Guid(e3265ab4bf004d28a9537516768c1c75) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans.ttf using Guid(e3265ab4bf004d28a9537516768c1c75) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a45ac04c5c4ba1d32d3c401f2b61ae7') in 0.055848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_M_01.prefab
  artifactKey: Guid(abc00000000014972953830511498521) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_M_01.prefab using Guid(abc00000000014972953830511498521) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c9fbe1b729374367b27d895d3bed5bc') in 0.0412468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/TextMesh Pro/Resources/TMP Settings.asset
  artifactKey: Guid(3f5b5dff67a942289a9defa416b206f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/TMP Settings.asset using Guid(3f5b5dff67a942289a9defa416b206f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46a8f6743f5717a7c2d9b0bcd0160c8d') in 0.1052637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/Transparencies+Gradient+VertexOffset.shadergraph
  artifactKey: Guid(07eb0950e71536d409abd1c04a8244dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/Transparencies+Gradient+VertexOffset.shadergraph using Guid(07eb0950e71536d409abd1c04a8244dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c1f5bf0b752cd426d37a96160fb349c1') in 0.0509019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle_1.prefab
  artifactKey: Guid(abc00000000002300554496726977217) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle_1.prefab using Guid(abc00000000002300554496726977217) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8de2220683a2473ae6036b1254c14c5f') in 0.0410128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_D.prefab
  artifactKey: Guid(abc00000000008541853647417900671) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_D.prefab using Guid(abc00000000008541853647417900671) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'aed87d544d238c31e0ca485d8c6e92a1') in 0.0674547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_S_01.prefab
  artifactKey: Guid(abc00000000014208238075552695327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_S_01.prefab using Guid(abc00000000014208238075552695327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6089e6203ee39c1d0f99c0deaee0aa8b') in 0.0362519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_01.prefab
  artifactKey: Guid(abc00000000017237621719740605727) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_01.prefab using Guid(abc00000000017237621719740605727) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '71c5408a0938c57a0f9a9e5622ddc466') in 0.0686617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/Water_AnimeCascade.shadergraph
  artifactKey: Guid(0053c5719979df14a9c275c846bd278c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/Water_AnimeCascade.shadergraph using Guid(0053c5719979df14a9c275c846bd278c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf25c4adfecf9edaaec55bcc0e661a58') in 0.5849668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ship_Cargo_01.prefab
  artifactKey: Guid(abc00000000013640382918445301060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ship_Cargo_01.prefab using Guid(abc00000000013640382918445301060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ac362de36c272a0b64364cd10ce0755') in 0.0387576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_A_01.prefab
  artifactKey: Guid(abc00000000012814655491640248950) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_A_01.prefab using Guid(abc00000000012814655491640248950) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '28d27228ff0d4a08dc64070b13672b7c') in 0.0398681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wheel.prefab
  artifactKey: Guid(abc00000000000591457375326423934) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wheel.prefab using Guid(abc00000000000591457375326423934) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ad0d33bca0d85594ff5ebc6b12f9aac2') in 0.0366408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_C_01.prefab
  artifactKey: Guid(abc00000000004928823807164677240) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_C_01.prefab using Guid(abc00000000004928823807164677240) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '75be13ac96591531ae960a489d616195') in 0.0390739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000976 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab
  artifactKey: Guid(9b059dc6477a3be49baa04addc732e67) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab using Guid(9b059dc6477a3be49baa04addc732e67) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '61a86f42cb667a48b4adbb1fcb066730') in 0.0347481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.prefab
  artifactKey: Guid(e538a836618cf084abf085e296e78e5d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.prefab using Guid(e538a836618cf084abf085e296e78e5d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e64c226cdb09d3f0ae5370958be1ad4') in 0.0664911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_01_4x4.mat
  artifactKey: Guid(a69f527a05f221c4294f81bb7d4800be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_01_4x4.mat using Guid(a69f527a05f221c4294f81bb7d4800be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '18eb99c0efa35eec80ac99c5e4b7962e') in 0.0448301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_Door.prefab
  artifactKey: Guid(abc00000000013475826865379448896) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_Door.prefab using Guid(abc00000000013475826865379448896) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '04fb3aaf7db9757b843f656b5d72e7b4') in 0.0418877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Font Regular.ttf
  artifactKey: Guid(3b0aa80b038734c4d810d1513eb1f221) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Font Regular.ttf using Guid(3b0aa80b038734c4d810d1513eb1f221) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '650f40a54fa492972c11129538893399') in 0.1480826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000124 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern III.png
  artifactKey: Guid(b8e4e3d4bd07c324580da71f1ddd7b01) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern III.png using Guid(b8e4e3d4bd07c324580da71f1ddd7b01) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8ab0036ad9a89d1ddcf82e552824ed0') in 0.0717286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP UNLIT.shadergraph
  artifactKey: Guid(f63d574838ccfb44f84acc05fed0af48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP UNLIT.shadergraph using Guid(f63d574838ccfb44f84acc05fed0af48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4b589acca17726382f021691bc1a0c2') in 0.0580282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000100 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Neo Outline.png
  artifactKey: Guid(e7f04c889f6f38a41b3886a1e8bd719b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Neo Outline.png using Guid(e7f04c889f6f38a41b3886a1e8bd719b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef6c5d16d1bcc108ea342c341068c40c') in 0.0641026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset
  artifactKey: Guid(14b392ee213d25a48b1feddbd9f5a9be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset using Guid(14b392ee213d25a48b1feddbd9f5a9be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c17bd381f4ae6b8ae1e5a4eecfa17b5') in 0.0290876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_Normal.png
  artifactKey: Guid(c5d50c709185a6c42b43532c58296adc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_Normal.png using Guid(c5d50c709185a6c42b43532c58296adc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8cc2b7d6b92db8702284c171186e86a8') in 0.1079442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab
  artifactKey: Guid(59089dd7f73b56f489328596f173dc5a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab using Guid(59089dd7f73b56f489328596f173dc5a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '82331aa44d52e54eaa62146060f70612') in 0.0657227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineAsset.asset
  artifactKey: Guid(b9f3086da92434da0bc1518f19f0ce86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineAsset.asset using Guid(b9f3086da92434da0bc1518f19f0ce86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44d4978c51a7adabf98a46b054ef9925') in 0.0407893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_03.mat
  artifactKey: Guid(8254031cf06ba6f40a9b62c60c523fc9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_03.mat using Guid(8254031cf06ba6f40a9b62c60c523fc9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffe9540cca2d917364cee796ea40a72f') in 0.036346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_05.mat
  artifactKey: Guid(d6649f8ff4d889d42ab6427d8a31ddf1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_05.mat using Guid(d6649f8ff4d889d42ab6427d8a31ddf1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f4be17d6e25dae9a2359b36d36e3130') in 0.14774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity
  artifactKey: Guid(ddea42e8828d7904dad6e7ac5e21e0ea) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity using Guid(ddea42e8828d7904dad6e7ac5e21e0ea) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity additively'
Loaded scene 'Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_Overview.unity'
	Deserialize:            2455.063 ms
	Integration:            11787.955 ms
	Integration of assets:  2.184 ms
	Thread Wait Time:       -1.653 ms
	Total Operation Time:   14243.549 ms
 -> (artifact id: '4740fe36fa314474cea31a7b3a079664') in 70.4179355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8959

========================================================================
Received Import Request.
  Time since last request: 0.000301 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_01.mat
  artifactKey: Guid(e6a81f648f093e74f9aca67dc1a0666a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_01.mat using Guid(e6a81f648f093e74f9aca67dc1a0666a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b23b7bce17292c53f46ec8d12e5f621') in 0.1764648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000139 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_06.fbx
  artifactKey: Guid(abc00000000016158641219722755420) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_06.fbx using Guid(abc00000000016158641219722755420) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e51dd52a98e68a24ef8ec69441b71ce4') in 0.3140757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_Metal.mat
  artifactKey: Guid(17b381b826087b049893b7ea70c9c839) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_Metal.mat using Guid(17b381b826087b049893b7ea70c9c839) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0799cb97f3907ce4910172e35bdcff28') in 0.0821806 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Atlas_01.mat
  artifactKey: Guid(abc00000000006176104684140605281) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Atlas_01.mat using Guid(abc00000000006176104684140605281) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17004fd9ea3326b6cdc21a40be04bce9') in 0.0455828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/M_Ivy_A.mat
  artifactKey: Guid(7e3022b01866fb34d8a94d2a3219af3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/M_Ivy_A.mat using Guid(7e3022b01866fb34d8a94d2a3219af3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31143c42831a1cb21d296b317353fa3b') in 0.0544112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_L_01.fbx
  artifactKey: Guid(abc00000000002800636713516460969) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_L_01.fbx using Guid(abc00000000002800636713516460969) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26643a6d2fdfa82cce6595fd0418a967') in 0.077094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(0f8afdf9380217c4d85830b579dcbb10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/MI_WoodPlanks.mat using Guid(0f8afdf9380217c4d85830b579dcbb10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4cf2f411d7283aecd353508585cd989c') in 0.0725782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_07.mat
  artifactKey: Guid(ac89c2c1cb9ffd74fa99c30bd0bc5f7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_07.mat using Guid(ac89c2c1cb9ffd74fa99c30bd0bc5f7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08e43b962d1f09637474a89fb624190d') in 0.0653306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/Materials/No Name.mat
  artifactKey: Guid(3e0602fbfc88c0744be2f4aa6bbe104e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/Materials/No Name.mat using Guid(3e0602fbfc88c0744be2f4aa6bbe104e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6cb081088c068408bf26791c7e9663cc') in 0.0601591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(dfccc4a4b4324784aa5ce70162acaa31) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_WoodPlanks.mat using Guid(dfccc4a4b4324784aa5ce70162acaa31) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '342a0349e79c417b92a77449a0f8c3bd') in 0.0647778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_03.fbx
  artifactKey: Guid(abc00000000014696469002492382205) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_03.fbx using Guid(abc00000000014696469002492382205) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '283764f2b7a21d0760e2621fa5664adb') in 0.0873845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_ChimneyPots.mat
  artifactKey: Guid(f01a575628346c94d83ef74deae48e63) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_ChimneyPots.mat using Guid(f01a575628346c94d83ef74deae48e63) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2e7fff795b5cd8cbe8f4c2d4da2d5a1') in 0.0634307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/Materials/MI_SilverFir_Atlas_01.mat
  artifactKey: Guid(b0fbcfe6d62d67440b7d929432309fd0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/Materials/MI_SilverFir_Atlas_01.mat using Guid(b0fbcfe6d62d67440b7d929432309fd0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6611513839a022829d3968828de6450') in 0.0579198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickStoneWall_Disp.mat
  artifactKey: Guid(57ddc678ce132a041bce4a6167d3138b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickStoneWall_Disp.mat using Guid(57ddc678ce132a041bce4a6167d3138b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3806456ef6ea86efe61cba243395c815') in 0.0537852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_E.fbx
  artifactKey: Guid(abc00000000015522028723809930630) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_E.fbx using Guid(abc00000000015522028723809930630) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fef1cc37cc6f0cdc4ccb67e970aea5f9') in 0.0874031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_R90.FBX
  artifactKey: Guid(ba1827b354512a94283d43e44bcf1afd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_R90.FBX using Guid(ba1827b354512a94283d43e44bcf1afd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32a6b93049081f3af851314dbcdbacc7') in 0.0887984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_B.FBX
  artifactKey: Guid(2fc1a43a8219e0f46a4f48cc49e2a60b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_B.FBX using Guid(2fc1a43a8219e0f46a4f48cc49e2a60b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf0f49e829adf5d7bfc9311a961f1a2d') in 0.059768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_A_to_Run_A_Root.FBX
  artifactKey: Guid(8c89f002799334a47942de5439adbe2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_A_to_Run_A_Root.FBX using Guid(8c89f002799334a47942de5439adbe2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1819ea220729f32963619afc06306c5a') in 0.0682782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Idle_Turn_R90_Root.FBX
  artifactKey: Guid(d0f7a65b2b7f37d4fac98e40b5848dcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Idle_Turn_R90_Root.FBX using Guid(d0f7a65b2b7f37d4fac98e40b5848dcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec01de0751a6f80f6dd59856f547eb09') in 0.0650204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R90_Root.FBX
  artifactKey: Guid(5d344878ff0527a47a678763c5be1607) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R90_Root.FBX using Guid(5d344878ff0527a47a678763c5be1607) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79f709d60a6bc95156cdc125626eb140') in 0.0582997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_D.prefab
  artifactKey: Guid(abc00000000008915781062323504628) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_D.prefab using Guid(abc00000000008915781062323504628) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ffe47715bc5949133086154d1fb5a26') in 0.0695528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_L90.FBX
  artifactKey: Guid(58d9b309a68003047b5c46c797a80a46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_L90.FBX using Guid(58d9b309a68003047b5c46c797a80a46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6e02fb91a63541b0899154ca37ffd31') in 0.0753286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamLoop_4x6_01.png
  artifactKey: Guid(f7c3464c436abe548bf052cd9cc3149c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamLoop_4x6_01.png using Guid(f7c3464c436abe548bf052cd9cc3149c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '01bc2fa931afe17768013c35cc254a0e') in 0.0621364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Wind.png
  artifactKey: Guid(82c2bbf7a130c004e822e75298ada996) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Wind.png using Guid(82c2bbf7a130c004e822e75298ada996) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '18e97669f9a2df36e0701c1547419456') in 0.0736743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1_Inplace.FBX
  artifactKey: Guid(baebea265e9e97d47b9159bc422017d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1_Inplace.FBX using Guid(baebea265e9e97d47b9159bc422017d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64d9e37156c74606d2e6b704e03567e8') in 0.0813909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_03.prefab
  artifactKey: Guid(abc00000000004900310109673188669) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_03.prefab using Guid(abc00000000004900310109673188669) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e35aece7c2445aa8d9e7bea111710ef') in 0.0635664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000153 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Caustics_Animated_01_HDR.EXR
  artifactKey: Guid(7ceab7350d6e1ff4ea2176c031c2cbed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Caustics_Animated_01_HDR.EXR using Guid(7ceab7350d6e1ff4ea2176c031c2cbed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cf150952a0afd921ebc7fa69099f96b') in 0.0747464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Caustics_Tiling_01_HDR.EXR
  artifactKey: Guid(f3916a59abf757e4c8ba74cb74bfe1ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Caustics_Tiling_01_HDR.EXR using Guid(f3916a59abf757e4c8ba74cb74bfe1ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6afcde361ba96474313abf0801f83d35') in 0.0695527 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_C.prefab
  artifactKey: Guid(abc00000000013476178078290774999) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_C.prefab using Guid(abc00000000013476178078290774999) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03300dc7c6a4b34015b043c448b231f4') in 0.0761801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0