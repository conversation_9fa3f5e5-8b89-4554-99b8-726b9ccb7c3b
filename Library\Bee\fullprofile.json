{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 11252, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 11252, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 11252, "tid": 11, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 11252, "tid": 11, "ts": 1754049837850144, "dur": 2138, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837860877, "dur": 1256, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 11252, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 11252, "tid": 1, "ts": 1754049836575425, "dur": 48033, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11252, "tid": 1, "ts": 1754049836623467, "dur": 150929, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11252, "tid": 1, "ts": 1754049836774408, "dur": 113611, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837862138, "dur": 3899, "ph": "X", "name": "", "args": {}}, {"pid": 11252, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836571650, "dur": 28251, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836599904, "dur": 1230681, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836601748, "dur": 4502, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836606259, "dur": 924, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836607188, "dur": 10317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836617512, "dur": 218, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836617735, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836617824, "dur": 1364, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836619194, "dur": 19304, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836638506, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836638511, "dur": 295, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836638809, "dur": 1074, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836639931, "dur": 323, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836640258, "dur": 92, "ph": "X", "name": "ProcessMessages 14034", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836640354, "dur": 678, "ph": "X", "name": "ReadAsync 14034", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641095, "dur": 4, "ph": "X", "name": "ProcessMessages 2054", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641196, "dur": 141, "ph": "X", "name": "ReadAsync 2054", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641341, "dur": 11, "ph": "X", "name": "ProcessMessages 5846", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641354, "dur": 153, "ph": "X", "name": "ReadAsync 5846", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641574, "dur": 2, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641578, "dur": 68, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641648, "dur": 2, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641651, "dur": 41, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641694, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641748, "dur": 42, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641792, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641795, "dur": 40, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641836, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836641837, "dur": 2104, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836643946, "dur": 17, "ph": "X", "name": "ProcessMessages 16972", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836643964, "dur": 46, "ph": "X", "name": "ReadAsync 16972", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644012, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644014, "dur": 53, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644072, "dur": 2, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644076, "dur": 79, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644157, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644160, "dur": 47, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644212, "dur": 2, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644216, "dur": 74, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644294, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644297, "dur": 66, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644368, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644371, "dur": 49, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644424, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644427, "dur": 56, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644486, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644489, "dur": 62, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644555, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644559, "dur": 64, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644626, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644629, "dur": 56, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644689, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644693, "dur": 57, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644755, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644759, "dur": 62, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644824, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644827, "dur": 71, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644902, "dur": 4, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644908, "dur": 84, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836644996, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645000, "dur": 61, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645064, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645068, "dur": 54, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645126, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645129, "dur": 63, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645195, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645198, "dur": 54, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645255, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645259, "dur": 53, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645316, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645318, "dur": 40, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645362, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645364, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645431, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645435, "dur": 55, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645493, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645496, "dur": 51, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645550, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645553, "dur": 57, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645613, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645616, "dur": 39, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645658, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645662, "dur": 54, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645719, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645722, "dur": 39, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645765, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645767, "dur": 58, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645831, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645903, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645907, "dur": 80, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645990, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836645993, "dur": 54, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646051, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646055, "dur": 53, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646110, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646112, "dur": 39, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646152, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646154, "dur": 35, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646191, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646193, "dur": 37, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646233, "dur": 50, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646286, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646289, "dur": 48, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646340, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646343, "dur": 49, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646395, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646398, "dur": 35, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646436, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646439, "dur": 61, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646503, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646505, "dur": 46, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646553, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646556, "dur": 56, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646615, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646617, "dur": 42, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646663, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836646665, "dur": 497, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647169, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647242, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647245, "dur": 47, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647294, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647297, "dur": 57, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647356, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647359, "dur": 57, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647420, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647424, "dur": 55, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647482, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647485, "dur": 65, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647552, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647555, "dur": 49, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647607, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647609, "dur": 40, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647652, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647655, "dur": 48, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647705, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647708, "dur": 49, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647761, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647764, "dur": 65, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647835, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647838, "dur": 52, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647894, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647898, "dur": 50, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836647953, "dur": 76, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648033, "dur": 2, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648037, "dur": 51, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648092, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648095, "dur": 52, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648150, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648154, "dur": 41, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648198, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648201, "dur": 51, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648255, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648258, "dur": 58, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648319, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648322, "dur": 51, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648375, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648378, "dur": 55, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648437, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648440, "dur": 50, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648492, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648494, "dur": 33, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648529, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648530, "dur": 32, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648566, "dur": 37, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648605, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648607, "dur": 37, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648645, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648647, "dur": 32, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648682, "dur": 34, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648719, "dur": 31, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648752, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648754, "dur": 40, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648797, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648800, "dur": 57, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648861, "dur": 56, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648920, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648922, "dur": 46, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648972, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836648975, "dur": 59, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649037, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649039, "dur": 51, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649094, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649097, "dur": 54, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649155, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649161, "dur": 48, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649213, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649215, "dur": 38, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649255, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649258, "dur": 48, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649309, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649311, "dur": 138, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649454, "dur": 3, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649458, "dur": 53, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649513, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649515, "dur": 43, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649561, "dur": 46, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649610, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649612, "dur": 58, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649674, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649677, "dur": 59, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649739, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649741, "dur": 53, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649798, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649801, "dur": 42, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649848, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649851, "dur": 46, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649900, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649903, "dur": 74, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649980, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836649983, "dur": 52, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650038, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650040, "dur": 53, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650096, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650104, "dur": 49, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650156, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650158, "dur": 52, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650214, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650216, "dur": 52, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650272, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650275, "dur": 52, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650330, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650333, "dur": 52, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650389, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650392, "dur": 65, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650460, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650463, "dur": 66, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650532, "dur": 2, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650536, "dur": 48, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650587, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650590, "dur": 48, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650643, "dur": 59, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650704, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650706, "dur": 35, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650742, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650744, "dur": 44, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650793, "dur": 36, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650832, "dur": 38, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650871, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650874, "dur": 39, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650915, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650917, "dur": 64, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650985, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836650988, "dur": 38, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651028, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651030, "dur": 35, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651068, "dur": 40, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651112, "dur": 56, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651171, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651222, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651224, "dur": 37, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651264, "dur": 36, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651304, "dur": 42, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651349, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651351, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651405, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651406, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651449, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651451, "dur": 42, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651496, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651499, "dur": 45, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651549, "dur": 41, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651595, "dur": 2, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651598, "dur": 55, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651657, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651660, "dur": 55, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651718, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651720, "dur": 43, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651765, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651768, "dur": 42, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651812, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651815, "dur": 30, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651847, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651850, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651883, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651885, "dur": 54, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651945, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651990, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836651993, "dur": 35, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652031, "dur": 26, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652062, "dur": 50, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652119, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652179, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652182, "dur": 50, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652235, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652239, "dur": 53, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652295, "dur": 4, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652301, "dur": 44, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652347, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652349, "dur": 147, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652501, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652504, "dur": 75, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652586, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652590, "dur": 65, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652660, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652664, "dur": 78, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652744, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652746, "dur": 45, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652797, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652800, "dur": 48, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652852, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652899, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652903, "dur": 42, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836652948, "dur": 494, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653448, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653451, "dur": 57, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653511, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653515, "dur": 81, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653602, "dur": 61, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653667, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653670, "dur": 46, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653720, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653722, "dur": 60, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653787, "dur": 44, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653837, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653840, "dur": 61, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653904, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653906, "dur": 41, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653950, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836653952, "dur": 48, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654002, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654005, "dur": 47, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654053, "dur": 1, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654056, "dur": 60, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654119, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654122, "dur": 42, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654167, "dur": 1, "ph": "X", "name": "ProcessMessages 5", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654170, "dur": 121, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654296, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654299, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654352, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654355, "dur": 44, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654404, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654407, "dur": 52, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654463, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654466, "dur": 47, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654516, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654518, "dur": 41, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654563, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654565, "dur": 70, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654639, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654641, "dur": 69, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654714, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654717, "dur": 59, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654779, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654781, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654840, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654843, "dur": 54, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654901, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654904, "dur": 54, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654961, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836654964, "dur": 52, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655019, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655022, "dur": 56, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655081, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655084, "dur": 49, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655135, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655138, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655186, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655229, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655232, "dur": 42, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655276, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655279, "dur": 45, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655329, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655332, "dur": 42, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655378, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655380, "dur": 69, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655452, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655454, "dur": 60, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655517, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655519, "dur": 72, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655597, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655601, "dur": 60, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655664, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655668, "dur": 52, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655722, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655726, "dur": 43, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655771, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655773, "dur": 34, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655811, "dur": 38, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655852, "dur": 33, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655888, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655924, "dur": 5, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655931, "dur": 49, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655984, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836655986, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656031, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656033, "dur": 186, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656224, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656227, "dur": 44, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656274, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656277, "dur": 48, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656327, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656329, "dur": 47, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656380, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656383, "dur": 45, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656431, "dur": 2, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656434, "dur": 109, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656547, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656551, "dur": 48, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656603, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656605, "dur": 43, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656651, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656655, "dur": 55, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656713, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656715, "dur": 53, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656771, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656775, "dur": 51, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656830, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656833, "dur": 44, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656880, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836656882, "dur": 40, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657001, "dur": 64, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657068, "dur": 3, "ph": "X", "name": "ProcessMessages 1232", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657073, "dur": 51, "ph": "X", "name": "ReadAsync 1232", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657128, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657131, "dur": 53, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657188, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657191, "dur": 54, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657248, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657251, "dur": 51, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657305, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657308, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657359, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657362, "dur": 77, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657444, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657493, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657494, "dur": 56, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657553, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657555, "dur": 47, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657604, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657605, "dur": 42, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657652, "dur": 1, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657655, "dur": 59, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657717, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657720, "dur": 42, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657765, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657768, "dur": 42, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657813, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657815, "dur": 47, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657867, "dur": 40, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657911, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657913, "dur": 46, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657964, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836657967, "dur": 57, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658027, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658030, "dur": 55, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658087, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658090, "dur": 45, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658138, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658141, "dur": 49, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658193, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658194, "dur": 48, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658249, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658306, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658310, "dur": 59, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658373, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658376, "dur": 55, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658435, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658437, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658495, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658497, "dur": 53, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658553, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658556, "dur": 41, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658601, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658604, "dur": 55, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658662, "dur": 52, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658716, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658718, "dur": 175, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658896, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658899, "dur": 67, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658970, "dur": 4, "ph": "X", "name": "ProcessMessages 1370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836658976, "dur": 59, "ph": "X", "name": "ReadAsync 1370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659039, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659042, "dur": 42, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659088, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659090, "dur": 55, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659148, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659150, "dur": 53, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659207, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659210, "dur": 50, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659263, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659267, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659315, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659317, "dur": 39, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659360, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659362, "dur": 37, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659405, "dur": 44, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659453, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659455, "dur": 40, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659498, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659501, "dur": 56, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659560, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659565, "dur": 49, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659617, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659621, "dur": 52, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659677, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659681, "dur": 37, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659722, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659725, "dur": 56, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659783, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659785, "dur": 41, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659829, "dur": 35, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659867, "dur": 32, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659903, "dur": 36, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659942, "dur": 33, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659977, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836659978, "dur": 34, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660015, "dur": 42, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660060, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660063, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660132, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660136, "dur": 77, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660217, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660221, "dur": 48, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660273, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660275, "dur": 43, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660321, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660324, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660368, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660373, "dur": 42, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660417, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660420, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660460, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660463, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660508, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660511, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660556, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660559, "dur": 40, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660602, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660605, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660692, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660695, "dur": 60, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660759, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660763, "dur": 57, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660823, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660824, "dur": 47, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660875, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660878, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660924, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660926, "dur": 46, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660975, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836660978, "dur": 44, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661025, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661028, "dur": 44, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661074, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661076, "dur": 49, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661128, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661129, "dur": 55, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661186, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661189, "dur": 52, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661244, "dur": 29, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661278, "dur": 42, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661327, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661390, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661394, "dur": 50, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661447, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661450, "dur": 61, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661516, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661520, "dur": 55, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661578, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661582, "dur": 44, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661629, "dur": 3, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836661634, "dur": 1644, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663282, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663285, "dur": 219, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663507, "dur": 24, "ph": "X", "name": "ProcessMessages 17424", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663533, "dur": 53, "ph": "X", "name": "ReadAsync 17424", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663589, "dur": 2, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663592, "dur": 48, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663643, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663646, "dur": 76, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663725, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663727, "dur": 47, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663777, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663779, "dur": 40, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663822, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663824, "dur": 54, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663881, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663884, "dur": 44, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663932, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663936, "dur": 52, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663992, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836663995, "dur": 34, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664034, "dur": 33, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664069, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664072, "dur": 65, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664143, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664201, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664203, "dur": 77, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664285, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664289, "dur": 50, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664343, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664345, "dur": 43, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664392, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664398, "dur": 86, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664488, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664492, "dur": 41, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664580, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664583, "dur": 51, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664638, "dur": 1, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664642, "dur": 82, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664750, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664752, "dur": 79, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664834, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664837, "dur": 40, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664883, "dur": 72, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664961, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836664963, "dur": 71, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665061, "dur": 2, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665065, "dur": 53, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665120, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665124, "dur": 71, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665198, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665201, "dur": 49, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665258, "dur": 5, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665266, "dur": 97, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665365, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665371, "dur": 44, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665417, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665421, "dur": 74, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665497, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665500, "dur": 43, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665545, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665549, "dur": 46, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665600, "dur": 40, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665641, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836665644, "dur": 1158, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836666806, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836666811, "dur": 145, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836666960, "dur": 12, "ph": "X", "name": "ProcessMessages 6916", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836666973, "dur": 62, "ph": "X", "name": "ReadAsync 6916", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667044, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667104, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667109, "dur": 97, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667209, "dur": 2, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667213, "dur": 41, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667255, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667258, "dur": 41, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667300, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667302, "dur": 45, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667351, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667353, "dur": 51, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667406, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667408, "dur": 45, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667456, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667458, "dur": 48, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667510, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667543, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667545, "dur": 169, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667721, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667778, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667780, "dur": 52, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667835, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667838, "dur": 52, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667893, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667897, "dur": 91, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836667994, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668051, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668054, "dur": 52, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668111, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668115, "dur": 49, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668167, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668170, "dur": 99, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668275, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668511, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668515, "dur": 65, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668584, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668589, "dur": 65, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668721, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668723, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668783, "dur": 3, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668788, "dur": 137, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836668931, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836669031, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836669034, "dur": 2050, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671090, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671095, "dur": 188, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671286, "dur": 14, "ph": "X", "name": "ProcessMessages 9009", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671302, "dur": 63, "ph": "X", "name": "ReadAsync 9009", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671368, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671372, "dur": 76, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671452, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671456, "dur": 42, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671501, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671504, "dur": 87, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671597, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671696, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671701, "dur": 69, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671774, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671778, "dur": 76, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671859, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671932, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671935, "dur": 60, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836671998, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672001, "dur": 37, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672040, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672043, "dur": 168, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672216, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672271, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672274, "dur": 70, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672348, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672351, "dur": 47, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672401, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672406, "dur": 116, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672527, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672583, "dur": 2, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672587, "dur": 48, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672639, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672643, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672678, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672681, "dur": 154, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672841, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672899, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672902, "dur": 54, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672959, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836672962, "dur": 46, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673013, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673016, "dur": 158, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673180, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673249, "dur": 56, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673309, "dur": 47, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673361, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836673365, "dur": 92, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836676650, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836676654, "dur": 320, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836676976, "dur": 23, "ph": "X", "name": "ProcessMessages 19497", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836677001, "dur": 144, "ph": "X", "name": "ReadAsync 19497", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836677151, "dur": 3, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836677155, "dur": 251, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836677598, "dur": 76, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836677676, "dur": 359, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836678039, "dur": 36, "ph": "X", "name": "ProcessMessages 2451", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836678076, "dur": 102, "ph": "X", "name": "ReadAsync 2451", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836678280, "dur": 1216, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679501, "dur": 141, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679647, "dur": 11, "ph": "X", "name": "ProcessMessages 6928", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679660, "dur": 52, "ph": "X", "name": "ReadAsync 6928", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679716, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679719, "dur": 85, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679812, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679863, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679867, "dur": 61, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679931, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679934, "dur": 41, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679978, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836679981, "dur": 96, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680084, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680131, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680134, "dur": 45, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680183, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680189, "dur": 36, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680228, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680230, "dur": 98, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680335, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680418, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680422, "dur": 55, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680480, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680484, "dur": 46, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680533, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680537, "dur": 144, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680686, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680744, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680749, "dur": 27, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680779, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680781, "dur": 49, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680832, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680834, "dur": 34, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680871, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680872, "dur": 30, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836680907, "dur": 191, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681104, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681157, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681159, "dur": 41, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681202, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681204, "dur": 43, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681250, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681252, "dur": 41, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681297, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681300, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681351, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681354, "dur": 131, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681489, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681491, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681562, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681565, "dur": 68, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681636, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681640, "dur": 51, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681694, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681697, "dur": 120, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681824, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681886, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681891, "dur": 74, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681970, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836681974, "dur": 30, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682006, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682010, "dur": 130, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682146, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682201, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682205, "dur": 52, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682261, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682265, "dur": 47, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682315, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836682319, "dur": 1424, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836683747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836683750, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836683827, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836684510, "dur": 215, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836684729, "dur": 373, "ph": "X", "name": "ProcessMessages 8915", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685106, "dur": 87, "ph": "X", "name": "ReadAsync 8915", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685197, "dur": 3, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685202, "dur": 152, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685361, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685438, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685441, "dur": 55, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685499, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685502, "dur": 53, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685559, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685563, "dur": 44, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685610, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685614, "dur": 168, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685789, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685848, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685851, "dur": 47, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685903, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685906, "dur": 47, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685955, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836685958, "dur": 45, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686006, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686008, "dur": 177, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686191, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686254, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686256, "dur": 30, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686289, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686292, "dur": 47, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686342, "dur": 47, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686391, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686393, "dur": 45, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686443, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686447, "dur": 369, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686825, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686887, "dur": 3, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686892, "dur": 55, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686950, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836686953, "dur": 77, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687033, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687037, "dur": 56, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687095, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687098, "dur": 47, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687148, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836687150, "dur": 1467, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688628, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688764, "dur": 8, "ph": "X", "name": "ProcessMessages 4318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688773, "dur": 49, "ph": "X", "name": "ReadAsync 4318", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688826, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688829, "dur": 62, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688894, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688897, "dur": 56, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688957, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836688959, "dur": 45, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689007, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689010, "dur": 109, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689122, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689125, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689206, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689210, "dur": 61, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689280, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689283, "dur": 118, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689409, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689484, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689487, "dur": 64, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689555, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689557, "dur": 57, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689617, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689620, "dur": 46, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689669, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689672, "dur": 109, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689784, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689786, "dur": 44, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689832, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689834, "dur": 41, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689878, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689881, "dur": 35, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689918, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689921, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836689963, "dur": 36, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690000, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690002, "dur": 163, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690168, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690210, "dur": 52, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690265, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690267, "dur": 61, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690332, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690335, "dur": 60, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690399, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690404, "dur": 65, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690473, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690475, "dur": 37, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690516, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690645, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690702, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690706, "dur": 63, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690772, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690775, "dur": 51, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690828, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690831, "dur": 43, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690878, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690881, "dur": 55, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690941, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690945, "dur": 42, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690990, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836690992, "dur": 38, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691034, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691036, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691081, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691084, "dur": 149, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691237, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691240, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691315, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691318, "dur": 62, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691383, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691388, "dur": 53, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691444, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691446, "dur": 57, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691508, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691512, "dur": 63, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691579, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691582, "dur": 44, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691629, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691633, "dur": 43, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691680, "dur": 40, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691723, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691725, "dur": 113, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691843, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691899, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836691902, "dur": 167, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836692072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836692075, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836692126, "dur": 541, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836694838, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836694961, "dur": 16, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836694980, "dur": 44, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695027, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695030, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695095, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695099, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695179, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695182, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695242, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695245, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695288, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695291, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695345, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695383, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695385, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695518, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695522, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695579, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695582, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695638, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695641, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695690, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695693, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695744, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695747, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695811, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695891, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695895, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695949, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836695952, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696004, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696008, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696085, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696089, "dur": 85, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696179, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696183, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696236, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696254, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696307, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696310, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696386, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836696389, "dur": 1122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697516, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697519, "dur": 150, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697676, "dur": 20, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697700, "dur": 62, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697766, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697770, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697828, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836697831, "dur": 3346, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701185, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701191, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701381, "dur": 38, "ph": "X", "name": "ProcessMessages 3248", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701422, "dur": 105, "ph": "X", "name": "ReadAsync 3248", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701535, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701608, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701724, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701728, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701780, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701783, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701819, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701823, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701880, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701934, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701987, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836701991, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702049, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702053, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702106, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702109, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702158, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702162, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702216, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702266, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702272, "dur": 151, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702427, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702430, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702485, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702491, "dur": 59, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702553, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702558, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702615, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702619, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702672, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702677, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702742, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702746, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702803, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702806, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702857, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836702860, "dur": 416, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703280, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703283, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703412, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703416, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703475, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703478, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703527, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836703529, "dur": 519, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704056, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704095, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704159, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704162, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704212, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704214, "dur": 280, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704501, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704554, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836704557, "dur": 1425, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836705988, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836705993, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836706182, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836706189, "dur": 20961, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836727163, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836727169, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836727250, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836727254, "dur": 12100, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739367, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739556, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739658, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836739821, "dur": 461, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836740340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836740342, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836740576, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836740579, "dur": 2926, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743512, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743515, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743615, "dur": 8, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743625, "dur": 46, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743675, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743678, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743722, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743724, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743770, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743773, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743870, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743873, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743918, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836743921, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744009, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744051, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744180, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744224, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744226, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744343, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744384, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744387, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744521, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744572, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744575, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744698, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744739, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744915, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836744964, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745015, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745057, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745060, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745166, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745211, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745213, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745254, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745256, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745295, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745298, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745379, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745426, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745471, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745549, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745644, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745724, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745796, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745799, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745879, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745936, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836745939, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746003, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746053, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746055, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746097, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746100, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746181, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746183, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746247, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746250, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746305, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746362, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746508, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746559, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746563, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746633, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746673, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746677, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746728, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746768, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746771, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746814, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746855, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746898, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746900, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746939, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836746942, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747011, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747049, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747052, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747166, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747213, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747216, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747351, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747402, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747405, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747454, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747457, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747513, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747556, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747559, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747667, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747723, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747726, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747783, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747786, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747844, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747846, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747889, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836747892, "dur": 212, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748107, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748109, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748181, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748184, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748238, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748241, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748372, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748425, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748428, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748521, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748572, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748576, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748623, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748626, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748673, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748676, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748722, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748725, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748770, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748773, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748826, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748869, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748872, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748952, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836748995, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749067, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749111, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749113, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749167, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749170, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749282, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749344, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749347, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749434, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749436, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749525, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749611, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749615, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836749686, "dur": 1133, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836750825, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836750908, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836750914, "dur": 130, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751050, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751108, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751156, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751205, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751208, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751288, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751360, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751363, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751575, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751633, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751636, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751702, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751749, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751751, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751788, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751791, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836751944, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752014, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752017, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752070, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752073, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752358, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752456, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752460, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752523, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836752526, "dur": 539, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753073, "dur": 378, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753457, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753462, "dur": 76, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753544, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753549, "dur": 61, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753694, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753698, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753841, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753908, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836753911, "dur": 1225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755148, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755223, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755227, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755299, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755364, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755367, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755503, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755613, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755618, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755692, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755696, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836755794, "dur": 2405, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758203, "dur": 6, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758211, "dur": 63, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758279, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758282, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758338, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758341, "dur": 264, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758613, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758668, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758673, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758781, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758784, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758836, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758839, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836758887, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759179, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759230, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759262, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759536, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836759577, "dur": 617, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760198, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760238, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760293, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760295, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760409, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760452, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760454, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760610, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836760649, "dur": 951, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761609, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761677, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761680, "dur": 247, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761934, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836761938, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762018, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762023, "dur": 450, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762481, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762527, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762530, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762920, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762966, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836762969, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836763021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836763028, "dur": 296, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836763331, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836763382, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836763384, "dur": 733, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764128, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764205, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764866, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764923, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049836764927, "dur": 995607, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837760547, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837760554, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837760619, "dur": 3068, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837763694, "dur": 18419, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837782119, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837782124, "dur": 20840, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837802971, "dur": 8, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837802981, "dur": 95, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837803080, "dur": 7, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837803090, "dur": 1519, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837804615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837804618, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837804673, "dur": 9, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837804684, "dur": 1107, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837805797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837805801, "dur": 465, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837806272, "dur": 19, "ph": "X", "name": "ProcessMessages 1508", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837806292, "dur": 93, "ph": "X", "name": "ReadAsync 1508", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837806389, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837806394, "dur": 18, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837806415, "dur": 618, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807041, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807076, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807152, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807155, "dur": 725, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807885, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807887, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807955, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837807959, "dur": 1205, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837809170, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837809173, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837809204, "dur": 557, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 11252, "tid": 12884901888, "ts": 1754049837809765, "dur": 19379, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837866041, "dur": 2888, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 11252, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 11252, "tid": 8589934592, "ts": 1754049836565591, "dur": 322474, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 11252, "tid": 8589934592, "ts": 1754049836888068, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 11252, "tid": 8589934592, "ts": 1754049836888076, "dur": 2156, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837868931, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 11252, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 11252, "tid": 4294967296, "ts": 1754049836328794, "dur": 1503450, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 11252, "tid": 4294967296, "ts": 1754049836340666, "dur": 213013, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 11252, "tid": 4294967296, "ts": 1754049837832639, "dur": 13087, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 11252, "tid": 4294967296, "ts": 1754049837839442, "dur": 4215, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 11252, "tid": 4294967296, "ts": 1754049837845844, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837868941, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754049836595666, "dur": 88, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049836595806, "dur": 37303, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049836633123, "dur": 1807, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049836635193, "dur": 177, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754049836635370, "dur": 1420, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049836637659, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_ADC6375A459370C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836639361, "dur": 395, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_83EBBF7D350CC28F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836640899, "dur": 232, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9DBC299B4F2CF264.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836641326, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836642179, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836642316, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836642535, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836644665, "dur": 230, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_F99DC928B1D72E5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836644920, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836645025, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836645170, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836645450, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836646314, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836646395, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836646796, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836647512, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836648124, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836648327, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836648715, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836648913, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836649827, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836650427, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_5D92000A132D4840.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836651402, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836651588, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836652117, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836652270, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836652449, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Recorder.Base.ref.dll_EF7F95D386759C59.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836652609, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836653259, "dur": 186, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836653457, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836653608, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836653815, "dur": 529, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836654555, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836654843, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836655010, "dur": 214, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836655362, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836656337, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836656420, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836656637, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836656986, "dur": 167, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836657289, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836657348, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836658223, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836658605, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836658772, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836658922, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836659604, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836659921, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836661083, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836661161, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836661553, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836661654, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836661717, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836662120, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836662285, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836662468, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836664279, "dur": 168, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754049836664789, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836664945, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836665169, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836665245, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754049836665911, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836667716, "dur": 262, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836668104, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836668305, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836669877, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836671390, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1064914946794821560.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836672060, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836672265, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836672813, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836672878, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836673138, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836673231, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836674147, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836677607, "dur": 300, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836677946, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754049836678105, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836678930, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Sequences.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836680467, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836681312, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836681640, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836681714, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836682112, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836682198, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836682536, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836684284, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836684826, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836685215, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836685490, "dur": 452, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836686308, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Recorder.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836686378, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836686452, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2842047945745124412.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836686810, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836687078, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836687151, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836687902, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836688355, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836689793, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836690347, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836690511, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836690714, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691098, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691160, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691226, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691302, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691640, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836691853, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754049836692288, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754049836636861, "dur": 55910, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049836692800, "dur": 1116168, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049837808970, "dur": 499, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049837809470, "dur": 94, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049837809865, "dur": 130, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049837810048, "dur": 2988, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754049836636785, "dur": 56033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836692878, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836693017, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_84D16C1BAB0B8DFB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836693991, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836694338, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836695124, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_048C4F21FA8C0554.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836697310, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754049836697673, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836698128, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754049836698254, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836698410, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754049836698745, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836699431, "dur": 27238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836726671, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836726773, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836726868, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836727218, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836727529, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836728201, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836729021, "dur": 1200, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnParticleCollisionMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754049836729000, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836730985, "dur": 1001, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Statistics\\StudentDistributionHelper.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754049836730596, "dur": 2634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836733231, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836734244, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836735898, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836736876, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836737438, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754049836737438, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836739582, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836740285, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836740347, "dur": 2487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836742837, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836743445, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836743583, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836743832, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836744086, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836744337, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836745112, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836745802, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836745969, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836746098, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836746847, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836746976, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836747060, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836747152, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836747258, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836747491, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836748118, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836749446, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836749595, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836749723, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_35E0707C8501A09D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836749826, "dur": 2074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836751900, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836753019, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836754073, "dur": 2042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836756118, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836756376, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836756480, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836757865, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754049836758152, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754049836758846, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836759060, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049836759124, "dur": 1023736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049837782861, "dur": 25861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754049837808724, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754049837808831, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836638174, "dur": 55122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836693306, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_FEC67F92E851F4CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836693939, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836694118, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6840782162FA032C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836694196, "dur": 1071, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836695493, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_17ED065E70DE8657.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836696035, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_63D342BA05953C77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836696956, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754049836697180, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836697255, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836698126, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836698372, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754049836698661, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836698781, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836699586, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836699717, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836699788, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836700052, "dur": 522, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836700638, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836700741, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754049836701236, "dur": 5619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836707549, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754049836706873, "dur": 2365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836709239, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836709591, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836710747, "dur": 864, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxDocument.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836710728, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836713131, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\RendererFeatures\\ScreenSpaceShadowsEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836713071, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836714052, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836714589, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\LoadTexture3D.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836714500, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836715463, "dur": 1264, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\GraphView\\VFXViewWindow.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836715440, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836717123, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836717421, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836717899, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836718257, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836718577, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836718874, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836719308, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836720437, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836720934, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Volume\\VolumesPreferences.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836721918, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Volume\\VolumeParameterDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836720745, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836722539, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836722828, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836723108, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836723644, "dur": 1681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Units\\UnitEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836723578, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836725569, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836725937, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836726496, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Meta\\MemberMetadata.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836726275, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836727298, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836728315, "dur": 1874, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Lighting\\ProbeVolume\\ProbeReferenceVolume.ReflProbeNormalization.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836727697, "dur": 2677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836730374, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836731447, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\Switch\\SwitchProControllerHID.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836731150, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836732425, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\Dialog.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836733475, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\ControlPicker\\InputControlPickerState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836732262, "dur": 2422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836734684, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836736263, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836737236, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\GUI\\Views\\TestListGUIBase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754049836736872, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836738047, "dur": 2321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836740369, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836741671, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836741925, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836742271, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836742514, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836742733, "dur": 3127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836745891, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754049836747525, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836747664, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836747942, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754049836748846, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836749538, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836749600, "dur": 2556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836752157, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836753076, "dur": 3898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836756975, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836757886, "dur": 6041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049836763930, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754049836764280, "dur": 1039850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754049837804132, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754049837805644, "dur": 1079, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754049837806724, "dur": 2087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836636826, "dur": 56019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836692878, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836693339, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_734AC013386B0E03.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754049836694329, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836694622, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836694700, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A5C8AE5F4CFB39F9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754049836694905, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836695103, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836695538, "dur": 1288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836696899, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754049836697078, "dur": 1362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836698441, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754049836699193, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836699936, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836700037, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754049836700501, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836700901, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836701045, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836701964, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2015906760990926514.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754049836702034, "dur": 1617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836703652, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2015906760990926514.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754049836703782, "dur": 1282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836705899, "dur": 1550, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorlib.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754049836705081, "dur": 2860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836707942, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754049836708579, "dur": 1146, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754049836710031, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@6aff1dd08a0c\\Unity.Burst.CodeGen\\ILPostProcessingLegacy.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836707942, "dur": 3222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836711164, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836712325, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836713204, "dur": 1040, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\CinemachineComponentBase.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836712852, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836714947, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836715465, "dur": 1852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\Collision\\CollisionDepth.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836715368, "dur": 2162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836717531, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836718096, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836718380, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836718663, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836718962, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836719361, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Data\\Graphs\\PropertyConnectionStateMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836719284, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836720264, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836720712, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836722421, "dur": 1300, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\HeaderFoldout.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836721576, "dur": 2315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836723891, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836724206, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836724806, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836725107, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836725573, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836725870, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836726163, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836726614, "dur": 1446, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\Special\\ReflectedInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836726466, "dur": 2237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836728893, "dur": 1286, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\IOptimizedAccessor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836728704, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836730989, "dur": 1159, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\NameAndParameters.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836732225, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\FourCC.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836730850, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836733149, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836734641, "dur": 743, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Gluon\\ChangeTreeViewItem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836734228, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836736926, "dur": 1049, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d\\Editor\\ConversionSystem\\EditorStyles.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754049836735975, "dur": 2160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836738136, "dur": 2173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836740309, "dur": 2512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836742823, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754049836743116, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754049836743772, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836744601, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836744657, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754049836745365, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836745451, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754049836749064, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836749509, "dur": 2361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836751870, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836753041, "dur": 3657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836756699, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836757869, "dur": 4698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836762570, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754049836762849, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754049836763321, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049836763385, "dur": 1002623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837766011, "dur": 2933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837768946, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837769022, "dur": 3264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837772288, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837772351, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837775256, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837775326, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Alembic.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837778196, "dur": 3870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837782119, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837784989, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837785055, "dur": 21729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837806786, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754049837806837, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754049837806978, "dur": 1984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836636996, "dur": 55900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836692916, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_650D6EC4EB774A3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836693823, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836693943, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836694039, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_3B813FEE47F58FD1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836694121, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836695161, "dur": 998, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836696167, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_1E69813DA69CA7A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836696316, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C96B3339A8B335F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836696774, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836697149, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836697385, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754049836697637, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836698536, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754049836699165, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836699246, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836699648, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754049836699849, "dur": 3882, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754049836703767, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836704441, "dur": 1318, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\DocCodeSamples.Tests\\GamepadHapticsExample.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836705876, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049836706682, "dur": 1329, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\ucrtbase.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049836708012, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049836709000, "dur": 842, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049836704441, "dur": 5507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836709948, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836711385, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836712942, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836713730, "dur": 1107, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\2D\\CinemachineUniversalPixelPerfectEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836713665, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836715119, "dur": 2414, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Contexts\\Implementations\\VFXComposedParticleOutput.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836717687, "dur": 1688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\VFXBlock.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836719375, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\VFXBlockUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836715119, "dur": 5999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836721118, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836722376, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@fa3a0bab2b90\\Editor\\treeview\\TrackGui\\TimelineTrackBaseGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836722270, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836723943, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836724250, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836724676, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836725312, "dur": 2629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Minimum.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836725001, "dur": 3032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836728254, "dur": 2467, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836728033, "dur": 3188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836731222, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836731599, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836733327, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\UITKAssetEditor\\Views\\DropManipulator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836731959, "dur": 2013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836734497, "dur": 812, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\PendingChanges\\DrawCommentTextArea.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754049836733973, "dur": 2606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836736580, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836737866, "dur": 2431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836740298, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836741381, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836741631, "dur": 2956, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836744596, "dur": 6091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836750689, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836750966, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836751253, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836752758, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836752981, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836753274, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836754035, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836754199, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836754414, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836754755, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836755970, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836756716, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836756968, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836757069, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836759424, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836759580, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836759755, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836761236, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836761364, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836761545, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836762357, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836762551, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836762856, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836763754, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836763908, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754049836764262, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836764946, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049836765037, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754049836765779, "dur": 1038320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754049837804120, "dur": 1493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049837805650, "dur": 1284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754049837806935, "dur": 2009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836637062, "dur": 55858, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836692936, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_2EB0AE6A3E8D504C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836693918, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836693986, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8BAC1436F7E60332.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836694044, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836694422, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_74AE5D92A5FF7976.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836694591, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836694703, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_81396563D4EC25D3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836695298, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836695562, "dur": 1241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836696810, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754049836696915, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836697356, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836697775, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836698504, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836698589, "dur": 1171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754049836699764, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836700280, "dur": 4413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754049836704695, "dur": 3086, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754049836708172, "dur": 900, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754049836709145, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754049836704695, "dur": 5333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836710496, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\ProjectGeneration\\ProjectPart.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836711291, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\ProjectGeneration\\PackageManagerTracker.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836710028, "dur": 3079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836713292, "dur": 1379, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\Decal\\DecalShaderGraphGUI.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836713108, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836714855, "dur": 5003, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\BitwiseComplement.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836720372, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\Add.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836721404, "dur": 1012, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Deprecated\\VFXDecalOutput.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836722457, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Deprecated\\SphereVolumeDeprecated.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836714795, "dur": 8441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836723378, "dur": 3262, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\Decal\\DecalDrawErrorRenderPass.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836723237, "dur": 3574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836726966, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Descriptors\\DescriptorProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836726811, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836728279, "dur": 1805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Common\\CoreAttributes.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836730199, "dur": 1762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\CommandBuffers\\RasterCommandBuffer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836732442, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\CommandBuffers\\CommandBufferHelpers.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836728011, "dur": 4965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836732977, "dur": 1014, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Actions\\Interactions\\PressInteraction.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836735377, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Actions\\InputActionProperty.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754049836732977, "dur": 3142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836736120, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836737119, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836737514, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754049836737514, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836738986, "dur": 1309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836740296, "dur": 1126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836744557, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836746689, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754049836747907, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836748719, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836748786, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836749022, "dur": 594, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836749617, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754049836750323, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836750510, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754049836750778, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754049836751513, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754049836752156, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836752229, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836753057, "dur": 3331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836756389, "dur": 1610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049836757999, "dur": 1030618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049837788620, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754049837791445, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049837791510, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754049837794259, "dur": 12097, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049837806528, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754049837806910, "dur": 2049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836637114, "dur": 55829, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836692958, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_823E5CFE5A7A65DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836694333, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836694634, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836694694, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EF34EB1AF64AD4AA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836694785, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836695031, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836695353, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D5133E55C4A3A360.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836695561, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836695827, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_590F6735060F0267.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836696361, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_25EB544A0D10D8EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836696515, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836696770, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836697048, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_CEF96CF5640B5F47.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836697115, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836697281, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836697871, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836698025, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836698078, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836698359, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754049836698555, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754049836699037, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836699410, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836699603, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836699892, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836700028, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836701680, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836701952, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836702410, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4370463482389937417.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836702728, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836702922, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836703124, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836703217, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836703637, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836703693, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754049836703818, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836704352, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836704820, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836706082, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836707579, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836709648, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836710120, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836710632, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836711344, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836712108, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836712790, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836713326, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836713683, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836714068, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836714522, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836715464, "dur": 1229, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Contexts\\Implementations\\VFXPlanarPrimitiveOutput.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754049836714987, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836716944, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836717264, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836717628, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836718013, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836718327, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836718608, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836718904, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836719397, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836719726, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836720349, "dur": 742, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.formats.alembic@9d89ad59ac86\\Runtime\\Scripts\\Timeline\\AlembicShotAsset.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754049836720103, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836721957, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836722279, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836722776, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836723059, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836723464, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836723993, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836724350, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836724723, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836725078, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836725519, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836725842, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836726161, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836726483, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836727087, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836727426, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836728009, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836728918, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836729230, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836729547, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836729866, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836730302, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836731077, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@fa3a0bab2b90\\Runtime\\AssetUpgrade\\AnimationPlayableAssetUpgrade.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754049836730588, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836732119, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836732511, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836732885, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836734396, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836735620, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836736791, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836737870, "dur": 2484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836740355, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836741418, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836741819, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754049836742749, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836742878, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836742942, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836743184, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836743296, "dur": 7496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754049836750803, "dur": 1306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836752128, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836752243, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836752492, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754049836753335, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836753412, "dur": 2957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836756371, "dur": 1490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836757863, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754049836758141, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754049836758812, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836758947, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049836759004, "dur": 1024104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049837783111, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837785785, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049837785852, "dur": 3343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837789197, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049837789258, "dur": 3460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Bindings.OpenImageIO.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837792720, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049837792793, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837795535, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754049837795685, "dur": 2787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837798528, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837801299, "dur": 5420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754049837806794, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754049837806902, "dur": 2020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836637160, "dur": 55850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836693071, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_ADC6375A459370C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836693973, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836694087, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_61F8702E9DB69E0F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836694390, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836694690, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836696258, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_EA9DCD3E33A895B9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836696342, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836696407, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E99DF097C6061369.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836696536, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836696675, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836696805, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754049836697073, "dur": 1339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836698412, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754049836698845, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754049836699192, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836699283, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836699784, "dur": 3868, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754049836703655, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754049836703957, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12808675674847790379.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754049836704238, "dur": 1197, "ph": "X", "name": "File", "args": {"detail": "Assets\\Hierarchy Designer\\Editor\\Scripts\\HD_Common_Texture.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836705625, "dur": 1869, "ph": "X", "name": "File", "args": {"detail": "Assets\\Hierarchy Designer\\Editor\\Scripts\\HD_Common_Constants.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836707494, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Assets\\Hierarchy Designer\\Editor\\Scripts\\HD_Common_Color.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836708340, "dur": 846, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Debug\\HealthSystemTest.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836704218, "dur": 5022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836709240, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836709731, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836711560, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxScene.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836710510, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836712401, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836713300, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836713640, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836714025, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836715182, "dur": 3159, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\LookAt.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836714430, "dur": 3929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836718360, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836718670, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836719318, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Data\\Legacy\\SlotReference0.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836718997, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836720541, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.recorder@979a3db2a781\\Editor\\Sources\\EXRCompressionTypeDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836720092, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836721915, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836722625, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836723181, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\RendererFeatures\\FullScreenPassRendererFeature.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836722905, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836723931, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836724700, "dur": 1370, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Runtime\\Utilities\\PropertyBinding\\ExposedProperty.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836724244, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836726093, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836726709, "dur": 876, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\GuidInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836726475, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836727848, "dur": 1006, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerBitField.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836727829, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836729150, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836729453, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836730024, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Runtime\\RamerDouglasPeucker.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836729809, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836731186, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\XInput\\IXboxOneRumble.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836731794, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\Users\\InputUserPairingOptions.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836731037, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836732771, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\Commands\\RequestResetCommand.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836732771, "dur": 2444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836735571, "dur": 833, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Inspector\\DrawInspectorOperations.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836737071, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Gluon\\UpdateReport\\ErrorListViewItem.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754049836735216, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836737954, "dur": 2328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836740282, "dur": 1381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836741666, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836742029, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754049836744687, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836744847, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836744998, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754049836748938, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836749018, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754049836749119, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836749326, "dur": 2605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836751932, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836752977, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836753257, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836753394, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754049836753981, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836754058, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836754160, "dur": 1959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836756122, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754049836756530, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754049836757504, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836757606, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836757665, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049836758007, "dur": 1045902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049837803911, "dur": 2681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754049837806622, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754049837807229, "dur": 2054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836637199, "dur": 55896, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836693106, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A741EED5E2831858.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836694118, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836694279, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836694688, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_A0774CF5DA9A68D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836695400, "dur": 1318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_263FF8DF2C82EF19.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836696719, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836697002, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836697155, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836697436, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836698035, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836698199, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836698380, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754049836699365, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836699771, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836699906, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836700059, "dur": 540, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754049836701034, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836701132, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836701538, "dur": 1370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836702917, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836703030, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836703151, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836703338, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836703477, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836703713, "dur": 602, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754049836704559, "dur": 881, "ph": "X", "name": "File", "args": {"detail": "Assets\\Hierarchy Designer\\Runtime\\Scripts\\HierarchyDesignerFolder.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836705682, "dur": 868, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\Converter\\PPv2\\EffectConverters\\GrainConverter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836704317, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836706723, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Razor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754049836707597, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754049836706723, "dur": 2196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836708920, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836709459, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\ProjectGeneration\\TypeCacheHelper.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836709215, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836711937, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxObject.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836710527, "dur": 2473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836713001, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836713406, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836714003, "dur": 1118, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\TemplateWindow\\VFXTemplateHelper.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836713783, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836715430, "dur": 1324, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\Orientation\\Orient.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836715239, "dur": 2016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836717256, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836717673, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836718237, "dur": 842, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Drawing\\Views\\PreviewSceneResources.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836718035, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836719179, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836720461, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\2D\\LightUtility.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836720460, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836722060, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836722625, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836723177, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\Passes\\FinalBlitPass.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836722916, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836723910, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836724311, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836724678, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836725013, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836725454, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836725781, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836726106, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836726976, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\Dropdowns\\DropdownSeparator.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836726428, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836727684, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836728451, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836728963, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836729330, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836729673, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836729995, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836730934, "dur": 1106, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Common\\IOnboardingSectionAnalyticsProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836732373, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\Observables\\WhereObservable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754049836730782, "dur": 2314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836733097, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836734347, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836735318, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836736764, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836737818, "dur": 2465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836740284, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836741377, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836741646, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836741740, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754049836742503, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836742652, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836743908, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754049836744536, "dur": 1874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836746411, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836746807, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754049836748111, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836748267, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836748431, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836748674, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836749020, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836749187, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754049836749840, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836750085, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836751848, "dur": 1179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836753027, "dur": 3143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836756171, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836756740, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754049836757135, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754049836757744, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836757889, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836757970, "dur": 133189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836893819, "dur": 189, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.53f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1754049836894009, "dur": 1051, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.53f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1754049836895060, "dur": 62, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.53f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1754049836891160, "dur": 3972, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049836895133, "dur": 871184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837766320, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837768965, "dur": 2954, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837771930, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.BuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837774643, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837777374, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837777437, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837780170, "dur": 10215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837790387, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837790616, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837793397, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837796189, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837796295, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837799215, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837801986, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837802045, "dur": 3438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754049837805485, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837806256, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754049837806323, "dur": 1002, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754049837807332, "dur": 1801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836637265, "dur": 56103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836693369, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_9C84D86D3038AEC8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836694185, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836694295, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836694829, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EBC8FA6D14ACD26C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836695050, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_E86D6B6B2D06F651.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836695198, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_E86D6B6B2D06F651.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836695458, "dur": 1806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_6E282F50BC06A837.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836697266, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836697317, "dur": 1381, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_6E282F50BC06A837.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836698753, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836699035, "dur": 27901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836727083, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836727392, "dur": 12767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836740323, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836740418, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836740621, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836741235, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836741406, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836741647, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836741769, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836742656, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836742772, "dur": 1147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836743932, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836744191, "dur": 3158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836747350, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836747480, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754049836747749, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754049836748379, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836748453, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754049836749819, "dur": 157, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049836750584, "dur": 1010887, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754049837766017, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837768973, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049837769049, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837771726, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049837771795, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837774565, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049837774646, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837777423, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837780180, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837782751, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049837782817, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837785580, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837788183, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754049837788295, "dur": 2570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837790919, "dur": 16188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754049837807208, "dur": 2052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836637563, "dur": 55577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836693157, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A5608A7556CF9151.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836693883, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836693956, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_EFC8B3FDA908E96C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836694014, "dur": 1229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836695384, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_03BCBFB6DE22588E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836695607, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836695815, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B5F486FCE5A58E52.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836696062, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836697271, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836697633, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836698204, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836698260, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836698423, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836698962, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836699244, "dur": 464, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754049836699763, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836700045, "dur": 431, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836700578, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836701014, "dur": 1654, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836702794, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13803227643786574021.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836703673, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754049836703799, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836704307, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836705031, "dur": 2351, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754049836707620, "dur": 2035, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.OpenSsl.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754049836704676, "dur": 5167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836709844, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836710395, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836711653, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Windows\\WaveformWindow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836711131, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836713184, "dur": 1084, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\ShaderGUI\\ParticleGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836712993, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836714418, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\SampleCurve.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836715469, "dur": 1275, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\Round.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836716745, "dur": 738, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\Rotate3D.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836714417, "dur": 3382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836717800, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836718115, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836718428, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836718740, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836719139, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836719913, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836720560, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836720852, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836722500, "dur": 1227, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Analytics\\VolumeProfileOverridesAnalytic.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836723728, "dur": 718, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Analytics\\VolumePriorityUsageAnalytic.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836721793, "dur": 2686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836724480, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836724827, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836725305, "dur": 1692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\ManualEventUnit.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836725152, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836727225, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836728392, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836729115, "dur": 1117, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\PlusHandler.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836728713, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836730538, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@fa3a0bab2b90\\Runtime\\Events\\Signals\\SignalReceiver.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754049836730429, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836732088, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836733758, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836735215, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836736349, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836737824, "dur": 2481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836740306, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836741406, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836741711, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754049836742339, "dur": 1028, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836743382, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836743438, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836743683, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836743934, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836744198, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754049836746849, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836746991, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836747135, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836747429, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754049836748246, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836748782, "dur": 2189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836750974, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836751225, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836751472, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754049836752095, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836752179, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836753031, "dur": 3967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836756999, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836757867, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836758811, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754049836759181, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754049836759645, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836759719, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049836759782, "dur": 1011597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837771381, "dur": 2976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837774358, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837774486, "dur": 2713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837777200, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837777304, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837780034, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837782798, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837782921, "dur": 12002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837794983, "dur": 3137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837798182, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837800876, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837800959, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754049837803683, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837803786, "dur": 749, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837804788, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754049837805508, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837805595, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754049837806237, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837806520, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754049837806732, "dur": 2194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836637640, "dur": 55535, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836693197, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_4208F1FBA58951D9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754049836694017, "dur": 1298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836697189, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754049836698085, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836698411, "dur": 1498, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754049836700061, "dur": 2413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754049836702625, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754049836702960, "dur": 1984, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836704976, "dur": 3251, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754049836708405, "dur": 1021, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754049836709639, "dur": 1013, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754049836704976, "dur": 6171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836711147, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836712923, "dur": 1767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Editors\\CinemachineCameraEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836711933, "dur": 3257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836715406, "dur": 1382, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\Size\\ScreenSpaceSize.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836716788, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\SetAttribute.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836717685, "dur": 1686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\Position\\PositionMesh.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836719372, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Blocks\\Implementations\\Position\\PositionLine.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836715191, "dur": 4968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836720358, "dur": 784, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.formats.alembic@9d89ad59ac86\\Runtime\\Scripts\\Importer\\AlembicMaterial.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836720159, "dur": 2374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836722534, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836722837, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836723759, "dur": 4372, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\Documentation.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836723131, "dur": 5240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836728810, "dur": 2157, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Unity\\SceneSingleton.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836730996, "dur": 1455, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Unity\\LudiqScriptableObject.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836732494, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Unity\\ISingleton.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836728372, "dur": 5576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836733948, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836735205, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836736230, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@6aff1dd08a0c\\Runtime\\Editor\\BurstAssemblyDisable.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836737025, "dur": 876, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@6aff1dd08a0c\\Runtime\\CompilerServices\\Constant.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754049836735945, "dur": 2195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836738141, "dur": 2147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836740289, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836741386, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754049836741690, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754049836742550, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836742710, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754049836742957, "dur": 782, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836743746, "dur": 4846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754049836748593, "dur": 1019, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836749679, "dur": 2203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836751883, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836753000, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754049836753313, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754049836754140, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836754272, "dur": 2408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836756681, "dur": 1333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049836758014, "dur": 1024876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837782892, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837785678, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837785769, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837788550, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837788813, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837791530, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837794313, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Sequences.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837797175, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837797242, "dur": 2709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837799952, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837800032, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837802805, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837802896, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754049837806171, "dur": 700, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754049837806915, "dur": 2045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836637936, "dur": 55264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836693212, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_081B16419DD60304.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836694052, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836694195, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_1DF3E0561B6BAF2A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836695322, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836695887, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_183A467554C43873.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836696002, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836696147, "dur": 1665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836698001, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836698526, "dur": 1096, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754049836699623, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754049836699819, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836699936, "dur": 2684, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754049836702623, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13168698087097753381.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754049836702840, "dur": 2243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836705716, "dur": 914, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754049836705167, "dur": 2294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836707679, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754049836708398, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754049836709393, "dur": 1478, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-stdio-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754049836711883, "dur": 956, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-locale-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754049836707462, "dur": 5445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836713234, "dur": 1238, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineSplineRoll.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836714590, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineGroupFraming.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836715162, "dur": 3810, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineFreeLookModifier.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836712908, "dur": 6115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836719023, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836720419, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.recorder@979a3db2a781\\Editor\\Sources\\Recorders\\_Inputs\\GameView\\GameViewInput.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836719620, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836721236, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836722357, "dur": 951, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@fa3a0bab2b90\\Editor\\TimelineSelection.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836722295, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836723632, "dur": 1047, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\State.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836723536, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836724892, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836725300, "dur": 2644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnKeyboardInput.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836728188, "dur": 1959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Hierarchy\\OnTransformChildrenChanged.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836730662, "dur": 1148, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerUp.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836725300, "dur": 6588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836731889, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836733838, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\Haptics\\IHaptics.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836734551, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\Gamepad.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836732675, "dur": 2899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836737064, "dur": 926, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetsUtils\\SaveAssets.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754049836735575, "dur": 2416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836737991, "dur": 2285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836740289, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836740361, "dur": 2462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836742824, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754049836743576, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836744294, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836744529, "dur": 1636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836746172, "dur": 3948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754049836750121, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836750212, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836750275, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836750962, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836751497, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836751608, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754049836752520, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836752640, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836752700, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836753156, "dur": 3534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836756690, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836757857, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754049836758157, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754049836759076, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836759143, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049836759200, "dur": 1012133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837771337, "dur": 3080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754049837774478, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754049837777221, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837777296, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754049837779996, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837780074, "dur": 2757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754049837782833, "dur": 22762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837805596, "dur": 1052, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754049837806703, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837807980, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754049837808043, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836638038, "dur": 55192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836693247, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_80E8C23EC838CE67.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754049836694064, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836695037, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_74B915A8BF894D1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754049836695375, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_8EFF91179547960D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754049836697081, "dur": 1364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836698522, "dur": 885, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754049836699603, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836700081, "dur": 4713, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754049836704930, "dur": 2597, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754049836707528, "dur": 911, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754049836708863, "dur": 1814, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754049836704796, "dur": 5986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836710782, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836712062, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836713355, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836714881, "dur": 2676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\Models\\Operators\\Implementations\\SquareWave.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836714167, "dur": 3517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836717855, "dur": 1190, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\AssetPostProcessors\\MaterialPostprocessor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836719046, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Generation\\TargetResources\\Structs.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836719849, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Generation\\TargetResources\\BlockFields.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836717684, "dur": 2834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836720519, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836720831, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836721721, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Debugging\\UIFoldoutEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836722501, "dur": 4149, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Debugging\\DebugUIDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836721660, "dur": 5272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836726933, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836727708, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Lighting\\ProbeVolume\\IProbeVolumeEnabledRenderPipeline.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836728695, "dur": 1359, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Debugging\\ShaderDebugPrintManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836730666, "dur": 1171, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerVector2.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836727707, "dur": 4624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836732413, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\AssetImporter\\InputActionAssetIconLoader.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836733255, "dur": 806, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\AssetEditor\\InputActionEditorWindow.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754049836732331, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836734274, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836736094, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836737159, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836737819, "dur": 2479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836740299, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836741392, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754049836741702, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754049836742396, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836742527, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754049836745151, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836745337, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836745848, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754049836748590, "dur": 1625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754049836750216, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836750517, "dur": 1448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836751965, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836753095, "dur": 3285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836756381, "dur": 1601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836757983, "dur": 137160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049836895144, "dur": 888041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049837783200, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Alembic.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754049837785873, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049837785976, "dur": 19803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754049837806189, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754049837806826, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754049837806937, "dur": 1987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836638092, "dur": 55162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836693272, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_8D756FC5A2012D68.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836694105, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836694511, "dur": 544, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_56EC1B490EAB34E2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836695059, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_427073BAC40C54FA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836695211, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836695504, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_2DFBF898AD047716.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836695598, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836695863, "dur": 1225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_38EA74ACABC664A3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836697093, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754049836697243, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754049836697510, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836698137, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836698394, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836699884, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836699960, "dur": 2687, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754049836702697, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836703074, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15611487661579288874.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754049836703637, "dur": 1770, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836705422, "dur": 2796, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Configuration.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754049836705422, "dur": 3820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836709243, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836709660, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a73638a9fb22\\Editor\\TMP\\PropertyDrawers\\TMP_TextAlignmentDrawer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836710560, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a73638a9fb22\\Editor\\TMP\\PropertyDrawers\\TMP_MarkToBaseAdjustmentRecordPropertyDrawer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836711833, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a73638a9fb22\\Editor\\TMP\\HDRP\\TMP_SDF_HDRPUnlitShaderGUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836709584, "dur": 3345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836712930, "dur": 862, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineDecollider.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836713809, "dur": 1189, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineConfiner2D.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836715130, "dur": 3886, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\VFXGraph\\VFXURPLitPlanarPrimitiveOutput.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836712930, "dur": 6516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836719448, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836720282, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.recorder@979a3db2a781\\Editor\\Sources\\Recorders\\MovieRecorder\\Encoder\\CoreEncoderSettingsPropertyDrawer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836719753, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836721553, "dur": 892, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\LookDev\\DisplayWindow.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836722446, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\LookDev\\Context.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836721473, "dur": 2359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836723833, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836724137, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836724657, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836724491, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836725490, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836725822, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836726119, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836726675, "dur": 3540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\LudiqGUIUtility.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836726418, "dur": 4182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836730622, "dur": 1368, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Metadata.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836732461, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Measurements\\FramesMeasurement.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836730601, "dur": 2607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836733209, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836735739, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\OverlayRect.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836734505, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836737262, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\LegacyPlayerRunTask.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754049836736697, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836738444, "dur": 1995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836740439, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836744553, "dur": 1846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836746400, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836746484, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754049836749735, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836749891, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836749998, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836750965, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836751244, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754049836753430, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836754063, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836754292, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754049836756135, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836756272, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754049836756535, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754049836757337, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836757495, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049836757991, "dur": 1030621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837788616, "dur": 2760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754049837791378, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837791461, "dur": 2847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754049837794310, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837794380, "dur": 2851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754049837797233, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837797358, "dur": 4238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754049837801597, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837801701, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754049837805233, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837805527, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754049837806578, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754049837806982, "dur": 1975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836638143, "dur": 55129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836693292, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_69B76AABF417A202.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836694754, "dur": 1101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836695856, "dur": 1562, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_69B76AABF417A202.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836697446, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754049836697531, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836698353, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836698672, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836698830, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754049836699405, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754049836699575, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754049836699868, "dur": 4681, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754049836704612, "dur": 1249, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754049836705897, "dur": 1896, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754049836707794, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754049836708697, "dur": 808, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754049836704551, "dur": 5631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836710638, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\Analytics\\MultiplayerCenterAnalyticsFactory.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836711232, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\Analytics\\MultiplayerCenterAnalytics.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836710183, "dur": 2129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836712313, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836713756, "dur": 1147, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathUtility.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836713595, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836715462, "dur": 1225, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\GraphView\\Views\\Properties\\SpaceablePropertiesRM.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836716688, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\GraphView\\Views\\Properties\\SimplePropertiesRM.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836715413, "dur": 2570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836717983, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836718289, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836718590, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836720457, "dur": 1103, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.recorder@979a3db2a781\\Editor\\Sources\\_RecorderComponent.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836719488, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836722379, "dur": 2876, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Lighting\\LightUI.Skin.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836725256, "dur": 1817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Editor\\Lighting\\LightUI.Drawers.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836721561, "dur": 5534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836727095, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836727671, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@3a084e1eb46b\\Runtime\\Utilities\\HaltonSequence.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836727397, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836728715, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836729475, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836729801, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836730937, "dur": 1024, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\Jobs\\IJobParallelForBatch.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836730136, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836732015, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836733976, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\_Deprecated\\CollabMigration\\MigrateCollabProject.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836733765, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836735652, "dur": 709, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetsUtils\\Processor\\UnityCloudProjectLinkMonitor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836735580, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836737248, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\CommandLineTest\\RunStateCallbacks.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754049836736947, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836738364, "dur": 2060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836740425, "dur": 2448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836742875, "dur": 1876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836744800, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836746020, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836746110, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836746240, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836746311, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836746657, "dur": 2544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836749202, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836749294, "dur": 2664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836751958, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836752974, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836753288, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836753904, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836754067, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836754300, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836756554, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836756663, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836756766, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836756854, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836757418, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836758659, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836758730, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836758797, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836759191, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836760041, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836760173, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754049836760476, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754049836761074, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049836761193, "dur": 1006814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049837768009, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837770665, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Sequences.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837773343, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049837773410, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837776105, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049837776169, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837778623, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837781348, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754049837781444, "dur": 26438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754049837808010, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836636898, "dur": 55975, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836692895, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_E1BD970E2E459EC7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836694259, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_11B48E3E18B4DF5C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836694324, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836694616, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836694988, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836695138, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836695627, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836695763, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_FB3CF841D0FAADE8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836695932, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836696286, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_2C4652F9B05FAB19.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836696378, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836696498, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836696620, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836696700, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836696874, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836696963, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836697017, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836697576, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_17294FFA6BA9CB7E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836697940, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836698075, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836698162, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836698385, "dur": 1349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836699736, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836699994, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836700093, "dur": 2826, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836702921, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836703048, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836703148, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836703349, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1139381448384656069.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836703484, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836703611, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754049836704066, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836704816, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836705913, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836709413, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Antiforgery.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754049836707318, "dur": 2823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836710142, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836710575, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836711345, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836712196, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836713224, "dur": 1047, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Debug\\DebugText.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836712524, "dur": 1879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836714404, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836714971, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836715474, "dur": 1267, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@20b96884abef\\Editor\\GraphView\\Views\\VFXVCSDropdownButton.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836715372, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836717076, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836717385, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836717880, "dur": 1184, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8939c506bcb4\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderUtils.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836717678, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836719227, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836719587, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836719954, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836722061, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836722622, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836723179, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\RTHandleUtils.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836723794, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@63585d5be1b1\\Runtime\\RenderTargetHandle.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836722903, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836724317, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836724954, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836725432, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836725757, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836726113, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836726447, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836727040, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836727408, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836727782, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836728906, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836729247, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836729554, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836729871, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836730162, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836730994, "dur": 1195, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\State\\InputStateBlock.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836730851, "dur": 2013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836732865, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836734230, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836736262, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836736780, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\StoreSceneSetupTask.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754049836736398, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836737392, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\6000.0.53f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754049836737283, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836738906, "dur": 1388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836740295, "dur": 2522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836742833, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836743178, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836743854, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836744690, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836745341, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836745633, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836745964, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836746280, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836746330, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836746419, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836747497, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836747571, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836747837, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836748464, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836748591, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836748806, "dur": 2160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836750979, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836751260, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836751883, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836751985, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836752086, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836752988, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836753296, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836754077, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836754212, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836754514, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836754780, "dur": 1135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836755917, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836756123, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836756412, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836757282, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836757615, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754049836757963, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754049836759086, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049836759234, "dur": 1009759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049837768995, "dur": 24788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754049837793841, "dur": 2715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754049837796557, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049837796628, "dur": 2758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754049837799430, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754049837802264, "dur": 3977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754049837806243, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049837806523, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754049837807303, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754049837824185, "dur": 4847, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 11252, "tid": 11, "ts": 1754049837869778, "dur": 4222, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 11252, "tid": 11, "ts": 1754049837874076, "dur": 4519, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 11252, "tid": 11, "ts": 1754049837857341, "dur": 22976, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}