%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ebe1dfb0bfb2af46920106fe0238e4b, type: 3}
  m_Name: ActionList
  m_EditorClassIdentifier: 
  nodes:
    _keys:
    - 03ff13f5-0937-4ccc-8c15-0eb5105d7a23
    - c72f54d2-2b0e-4eb2-a383-ec55a47a5700
    - 4a4be282-608e-4f49-9334-b624d46de5e3
    - 2854c638-d12a-47f8-baa8-1fd8f115b6e0
    - 7db2d2dd-e8db-458f-9aa1-ece531f46663
    _values:
    - id: 03ff13f5-0937-4ccc-8c15-0eb5105d7a23
      nodeConnection: []
      iterator: -1
      globalWeighting: 0.75
      localWeighting: 0.75
      ActionType: 0
      itemsNeeded: 0
      itemType: 0
      itemOutput: {fileID: 5088141190527474511, guid: 9e86f9a7c3e997f46905c3efa2f9590f, type: 3}
      consumeActiveItem: 0
      LookAt: 0
      ownershipMode: 0
      SetItemInUse: 1
      SetItemReserved: 0
      NoResetItemOnEnd: 0
      UpdateEachLoop: 0
      endpointBehaviour:
        lengthEnforcementMode: 0
        lengthAim: []
        animationGroup:
          clipSelectionMode: 0
          rigidSets: 0
          collections: []
      itemSearch:
        rid: 3724099947858231878
      slotTypeRequired:
        value: 0
        hasValue: 0
    - id: c72f54d2-2b0e-4eb2-a383-ec55a47a5700
      nodeConnection: []
      iterator: -1
      globalWeighting: 0.75
      localWeighting: 0.75
      ActionType: 9
      itemsNeeded: 0
      itemType: 0
      itemOutput: {fileID: 0}
      consumeActiveItem: 0
      LookAt: 1
      ownershipMode: 0
      SetItemInUse: 1
      SetItemReserved: 0
      NoResetItemOnEnd: 0
      UpdateEachLoop: 0
      endpointBehaviour:
        lengthEnforcementMode: 0
        lengthAim: []
        animationGroup:
          clipSelectionMode: 0
          rigidSets: 0
          collections: []
      itemSearch:
        rid: 3724099947858231879
      slotTypeRequired:
        value: 0
        hasValue: 0
    - id: 4a4be282-608e-4f49-9334-b624d46de5e3
      nodeConnection: []
      iterator: -1
      globalWeighting: 0.75
      localWeighting: 0.75
      ActionType: 2
      itemsNeeded: 0
      itemType: 0
      itemOutput: {fileID: 0}
      consumeActiveItem: 0
      LookAt: 1
      ownershipMode: 0
      SetItemInUse: 1
      SetItemReserved: 0
      NoResetItemOnEnd: 0
      UpdateEachLoop: 0
      endpointBehaviour:
        lengthEnforcementMode: 0
        lengthAim: []
        animationGroup:
          clipSelectionMode: 0
          rigidSets: 0
          collections: []
      itemSearch:
        rid: 3724099947858231880
      slotTypeRequired:
        value: 0
        hasValue: 0
    - id: 2854c638-d12a-47f8-baa8-1fd8f115b6e0
      nodeConnection: []
      iterator: -1
      globalWeighting: 0.75
      localWeighting: 0.75
      ActionType: 5
      itemsNeeded: 0
      itemType: 0
      itemOutput: {fileID: 0}
      consumeActiveItem: 0
      LookAt: 1
      ownershipMode: 0
      SetItemInUse: 1
      SetItemReserved: 1
      NoResetItemOnEnd: 1
      UpdateEachLoop: 0
      endpointBehaviour:
        lengthEnforcementMode: 0
        lengthAim: []
        animationGroup:
          clipSelectionMode: 0
          rigidSets: 0
          collections: []
      itemSearch:
        rid: 3724099947858231881
      slotTypeRequired:
        value: 0
        hasValue: 0
    - id: 7db2d2dd-e8db-458f-9aa1-ece531f46663
      nodeConnection: []
      iterator: -1
      globalWeighting: 0.75
      localWeighting: 0.75
      ActionType: 0
      itemsNeeded: 0
      itemType: 0
      itemOutput: {fileID: 0}
      consumeActiveItem: 0
      LookAt: 1
      ownershipMode: 0
      SetItemInUse: 1
      SetItemReserved: 0
      NoResetItemOnEnd: 0
      UpdateEachLoop: 0
      endpointBehaviour:
        lengthEnforcementMode: 1
        lengthAim:
        - 9.94
        animationGroup:
          clipSelectionMode: 0
          rigidSets: 0
          collections: []
      itemSearch:
        rid: 3724099947858231882
      slotTypeRequired:
        value: 0
        hasValue: 0
  references:
    version: 2
    RefIds:
    - rid: 3724099947858231878
      type: {class: ItemSearch, ns: AISystem.Common.Inventory, asm: RPG-AI-SYSTERM}
      data:
        NameKey:
          value: 16
          hasValue: 1
        TypeKey:
          value: 0
          hasValue: 0
        SizeKey:
          value: 0
          hasValue: 0
        InUse:
          value: 0
          hasValue: 0
        Reserved:
          value: 0
          hasValue: 0
    - rid: 3724099947858231879
      type: {class: ItemSearch, ns: AISystem.Common.Inventory, asm: RPG-AI-SYSTERM}
      data:
        NameKey:
          value: 7
          hasValue: 1
        TypeKey:
          value: 0
          hasValue: 0
        SizeKey:
          value: 0
          hasValue: 0
        InUse:
          value: 0
          hasValue: 0
        Reserved:
          value: 0
          hasValue: 0
    - rid: 3724099947858231880
      type: {class: ItemSearch, ns: AISystem.Common.Inventory, asm: RPG-AI-SYSTERM}
      data:
        NameKey:
          value: 16
          hasValue: 1
        TypeKey:
          value: 0
          hasValue: 0
        SizeKey:
          value: 0
          hasValue: 0
        InUse:
          value: 0
          hasValue: 0
        Reserved:
          value: 0
          hasValue: 0
    - rid: 3724099947858231881
      type: {class: ItemSearch, ns: AISystem.Common.Inventory, asm: RPG-AI-SYSTERM}
      data:
        NameKey:
          value: 15
          hasValue: 1
        TypeKey:
          value: 0
          hasValue: 0
        SizeKey:
          value: 0
          hasValue: 0
        InUse:
          value: 0
          hasValue: 0
        Reserved:
          value: 0
          hasValue: 0
    - rid: 3724099947858231882
      type: {class: ItemSearch, ns: AISystem.Common.Inventory, asm: RPG-AI-SYSTERM}
      data:
        NameKey:
          value: 0
          hasValue: 0
        TypeKey:
          value: 0
          hasValue: 0
        SizeKey:
          value: 0
          hasValue: 0
        InUse:
          value: 0
          hasValue: 0
        Reserved:
          value: 0
          hasValue: 0
