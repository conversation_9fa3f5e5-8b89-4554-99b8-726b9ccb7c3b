Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-07T12:51:42Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker8.log
-srvPort
51437
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19680]  Target information:

Player connection [19680]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 398393167 [EditorId] 398393167 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19680] Host joined multi-casting on [***********:54997]...
Player connection [19680] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
Default GameObject Tag: Player already registered
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 10.06 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.22 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56704
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002880 seconds.
- Loaded All Assemblies, in  0.622 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.588 seconds
Domain Reload Profiling: 1210ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (270ms)
		LoadAssemblies (206ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (265ms)
			TypeCache.Refresh (263ms)
				TypeCache.ScanAssembly (239ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (588ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (260ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.412 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 8.10 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.765 seconds
Domain Reload Profiling: 3176ms
	BeginReloadAssembly (297ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (946ms)
		LoadAssemblies (578ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (536ms)
			TypeCache.Refresh (395ms)
				TypeCache.ScanAssembly (371ms)
			BuildScriptInfoCaches (112ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (255ms)
			ProcessInitializeOnLoadAttributes (968ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Default GameObject Tag: Player already registered
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 5.81 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.16 ms.
Unloading 263 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8186 unused Assets / (9.6 MB). Loaded Objects now: 8884.
Memory consumption went from 217.2 MB to 207.6 MB.
Total: 18.250500 ms (FindLiveObjects: 1.169600 ms CreateObjectMapping: 1.474500 ms MarkObjects: 7.883100 ms  DeleteObjects: 7.721600 ms)

========================================================================
Received Import Request.
  Time since last request: 434497.124632 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/System/Objects/SK_ROCA_HUMAN_Nude.prefab
  artifactKey: Guid(8678f1ff3a882874da3bbc0743e646c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/System/Objects/SK_ROCA_HUMAN_Nude.prefab using Guid(8678f1ff3a882874da3bbc0743e646c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0fcb04dcb74210047b4533d321263452') in 1.5373594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 701

========================================================================
Received Import Request.
  Time since last request: 3.293299 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/Spawn/619bf493-14db-4621-8363-c0f7e98baa07.asset
  artifactKey: Guid(6857a9b8c6d245b4abfb0ac8f2d3087b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/Spawn/619bf493-14db-4621-8363-c0f7e98baa07.asset using Guid(6857a9b8c6d245b4abfb0ac8f2d3087b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b076bc3c38f95de422fa3add8e33d17') in 0.0024011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 33.413186 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/Spawn/331ef76b-3e47-40ed-9e99-24227bafc535.asset
  artifactKey: Guid(f4f9bb0251c3e0f4c9dd976c8f0150b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/Spawn/331ef76b-3e47-40ed-9e99-24227bafc535.asset using Guid(f4f9bb0251c3e0f4c9dd976c8f0150b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d41cf6120478c4f844a003fbebbafa6') in 0.0024474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.289 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.36 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.705 seconds
Domain Reload Profiling: 2998ms
	BeginReloadAssembly (413ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (750ms)
		LoadAssemblies (529ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (333ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1705ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1388ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (242ms)
			ProcessInitializeOnLoadAttributes (1013ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 5.18 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (12.1 MB). Loaded Objects now: 8932.
Memory consumption went from 205.8 MB to 193.7 MB.
Total: 21.408900 ms (FindLiveObjects: 1.242800 ms CreateObjectMapping: 1.489700 ms MarkObjects: 8.202700 ms  DeleteObjects: 10.471700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 99.822786 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/Spawn/e1547a8d-b024-43ab-abbc-70090a97d4c6.asset
  artifactKey: Guid(85c9da20b091b8c4aa6ed6511f43e290) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/Spawn/e1547a8d-b024-43ab-abbc-70090a97d4c6.asset using Guid(85c9da20b091b8c4aa6ed6511f43e290) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc21b78ef2bd962d79f3668c59c7e78e') in 0.0604102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 153

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/Spawn/Base Character Pool Service.asset
  artifactKey: Guid(c40a127a9b66b294ebedef5693fd006d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/Spawn/Base Character Pool Service.asset using Guid(c40a127a9b66b294ebedef5693fd006d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '917ba77c85b5630f8be19b642e2b6f0e') in 0.0250731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 463

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.235 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.18 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.671 seconds
Domain Reload Profiling: 2911ms
	BeginReloadAssembly (356ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (757ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (387ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (347ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (251ms)
			ProcessInitializeOnLoadAttributes (1014ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 4.60 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (10.6 MB). Loaded Objects now: 8934.
Memory consumption went from 203.9 MB to 193.3 MB.
Total: 18.757600 ms (FindLiveObjects: 1.420900 ms CreateObjectMapping: 1.632100 ms MarkObjects: 7.683600 ms  DeleteObjects: 8.019300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.180 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.59 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.813 seconds
Domain Reload Profiling: 2999ms
	BeginReloadAssembly (324ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (736ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (319ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1814ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (302ms)
			ProcessInitializeOnLoadAttributes (1076ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 5.67 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (10.9 MB). Loaded Objects now: 8936.
Memory consumption went from 203.9 MB to 193.1 MB.
Total: 21.637800 ms (FindLiveObjects: 1.512600 ms CreateObjectMapping: 1.625600 ms MarkObjects: 8.803900 ms  DeleteObjects: 9.693400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 6311.038018 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Resources/System/ModuleSettings.asset
  artifactKey: Guid(de70c7bbb6ef12d42984a2ffcae16474) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Resources/System/ModuleSettings.asset using Guid(de70c7bbb6ef12d42984a2ffcae16474) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd9fadc714c7d5f23c721c407252af015') in 0.4797881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 619

========================================================================
Received Import Request.
  Time since last request: 1145.488768 seconds.
  path: Assets/ModuleSettings.asset
  artifactKey: Guid(f5c5c913c9d255544bb07f48cd4d5558) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ModuleSettings.asset using Guid(f5c5c913c9d255544bb07f48cd4d5558) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc88d13be78e0561ab2a97c0e144aea5') in 0.0184907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 8823.831608 seconds.
  path: ProjectSettings/TagManager.asset
  artifactKey: Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing ProjectSettings/TagManager.asset using Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Default GameObject Tag: Player already registered
 -> (artifact id: '6bd565563f17034113931be1d88e3d24') in 0.0427485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.917 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.59 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.805 seconds
Domain Reload Profiling: 3729ms
	BeginReloadAssembly (706ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (268ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1063ms)
		LoadAssemblies (781ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (505ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (456ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (258ms)
			ProcessInitializeOnLoadAttributes (1095ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 6.29 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8183 unused Assets / (11.0 MB). Loaded Objects now: 8938.
Memory consumption went from 204.0 MB to 193.0 MB.
Total: 22.596300 ms (FindLiveObjects: 1.294400 ms CreateObjectMapping: 1.616400 ms MarkObjects: 9.356400 ms  DeleteObjects: 10.327200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.279 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.70 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.663 seconds
Domain Reload Profiling: 2946ms
	BeginReloadAssembly (354ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (797ms)
		LoadAssemblies (564ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (382ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (343ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1365ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (244ms)
			ProcessInitializeOnLoadAttributes (989ms)
			ProcessInitializeOnLoadMethodAttributes (113ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 5.99 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (10.5 MB). Loaded Objects now: 8940.
Memory consumption went from 204.1 MB to 193.6 MB.
Total: 20.698400 ms (FindLiveObjects: 1.261600 ms CreateObjectMapping: 1.560000 ms MarkObjects: 9.095500 ms  DeleteObjects: 8.779600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.283 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.04 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.629 seconds
Domain Reload Profiling: 2917ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (794ms)
		LoadAssemblies (550ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (405ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1326ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (254ms)
			ProcessInitializeOnLoadAttributes (955ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 6.77 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (10.8 MB). Loaded Objects now: 8942.
Memory consumption went from 204.1 MB to 193.3 MB.
Total: 20.427000 ms (FindLiveObjects: 1.312700 ms CreateObjectMapping: 1.668700 ms MarkObjects: 8.308900 ms  DeleteObjects: 9.134200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.264 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.06 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.760 seconds
Domain Reload Profiling: 3029ms
	BeginReloadAssembly (351ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (796ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (423ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (377ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1760ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1457ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (267ms)
			ProcessInitializeOnLoadAttributes (1036ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 5.37 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (11.2 MB). Loaded Objects now: 8944.
Memory consumption went from 204.1 MB to 192.9 MB.
Total: 22.028700 ms (FindLiveObjects: 1.388700 ms CreateObjectMapping: 1.718600 ms MarkObjects: 8.130300 ms  DeleteObjects: 10.789200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 549.330447 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Scenes/Performance Scene.unity
  artifactKey: Guid(a04ba9faa5cf7a640b0d282e49a7ed5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Scenes/Performance Scene.unity using Guid(a04ba9faa5cf7a640b0d282e49a7ed5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6595b4c2cd9dec36431ec416cc4be185') in 0.1192072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.285 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.39 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.764 seconds
Domain Reload Profiling: 3054ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (819ms)
		LoadAssemblies (541ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (418ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (375ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (265ms)
			ProcessInitializeOnLoadAttributes (1033ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 6.15 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (11.1 MB). Loaded Objects now: 8946.
Memory consumption went from 204.1 MB to 193.0 MB.
Total: 24.779100 ms (FindLiveObjects: 1.874200 ms CreateObjectMapping: 1.702100 ms MarkObjects: 10.180300 ms  DeleteObjects: 11.020000 ms)

Prepare: number of updated asset objects reloaded= 0
