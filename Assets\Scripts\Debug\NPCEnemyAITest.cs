/*
 * Script: NPCEnemyAITest
 * Purpose: Test script to verify NPC Enemy AI system integration and functionality
 * 
 * Setup Instructions:
 * 1. Attach this script to any GameObject in the scene (preferably a manager object)
 * 2. Ensure NPCEnemyAI entities are present in the scene
 * 3. Run the scene and use keyboard shortcuts to test AI functionality
 * 4. Check console output for test results and status updates
 * 
 * CRITICAL: Default Values Must Work Out-of-Box
 * - All tests use safe default parameters
 * - Keyboard shortcuts use number keys for easy access
 * - Console output provides clear feedback on test results
 * 
 * Dependencies:
 * - NPCEnemyAI script in scene
 * - PlayerController3D script on player
 * - AIDebugVisualizer for visual feedback
 * 
 * Usage:
 * Attach to a test manager GameObject and run tests during development.
 * Use to verify AI behavior and integration with existing systems.
 * 
 * Test Controls:
 * - 1: Test AI Detection Systems
 * - 2: Test State Transitions
 * - 3: Test Pathfinding
 * - 4: Test Performance
 * - 5: Run All Tests
 * 
 * Author: Claude AI Assistant
 * Created: 2025-08-01
 * Last Modified: 2025-08-01
 */

using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Blazeout.Debug
{
    /// <summary>
    /// Test suite for NPC Enemy AI system
    /// Attach this script to: Test Manager GameObject
    /// </summary>
    public class NPCEnemyAITest : MonoBehaviour
    {
        #region Inspector Settings
        [Header("Test Configuration")]
        [Tooltip("Enable automatic testing on start")]
        [SerializeField] private bool runTestsOnStart = false;
        
        [Tooltip("Show detailed test output in console")]
        [SerializeField] private bool verboseLogging = true;
        
        [Tooltip("Test duration for performance tests")]
        [SerializeField] private float testDuration = 10.0f;
        
        [Header("Test Targets")]
        [Tooltip("Specific AI to test (leave empty to test all)")]
        [SerializeField] private Blazeout.AI.NPCEnemyAI[] testTargets;
        #endregion

        #region Private Variables
        private Blazeout.AI.NPCEnemyAI[] allAI;
        private Transform playerTransform;
        private Blazeout.Controllers.PlayerController3D playerController;
        private bool testsRunning = false;
        
        // Test results tracking
        private Dictionary<string, bool> testResults = new Dictionary<string, bool>();
        private int totalTests = 0;
        private int passedTests = 0;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize test system
        /// </summary>
        private void Start()
        {
            InitializeTestSystem();
            
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
        }

        /// <summary>
        /// Handle test input
        /// </summary>
        private void Update()
        {
            if (testsRunning) return;
            
            HandleTestInput();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the test system
        /// </summary>
        private void InitializeTestSystem()
        {
            // Find all AI in scene
            allAI = FindObjectsOfType<Blazeout.AI.NPCEnemyAI>();
            
            // Use specific test targets if provided, otherwise test all
            if (testTargets.Length == 0)
            {
                testTargets = allAI;
            }
            
            // Find player
            GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
            if (playerObject != null)
            {
                playerTransform = playerObject.transform;
                playerController = playerObject.GetComponent<Blazeout.Controllers.PlayerController3D>();
            }
            
            Log($"[NPCEnemyAITest] Test system initialized. Found {allAI.Length} AI entities, testing {testTargets.Length}");
            
            if (playerTransform == null)
            {
                LogError("No player found! Tests may not work correctly.");
            }
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle keyboard input for tests
        /// </summary>
        private void HandleTestInput()
        {
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                StartCoroutine(TestDetectionSystems());
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                StartCoroutine(TestStateTransitions());
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                StartCoroutine(TestPathfinding());
            }
            else if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                StartCoroutine(TestPerformance());
            }
            else if (Input.GetKeyDown(KeyCode.Alpha5))
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
            
            // Display controls
            if (Input.GetKeyDown(KeyCode.H))
            {
                DisplayTestControls();
            }
        }
        #endregion

        #region Test Methods
        /// <summary>
        /// Test AI detection systems
        /// </summary>
        private IEnumerator TestDetectionSystems()
        {
            testsRunning = true;
            Log("=== Testing AI Detection Systems ===");
            
            foreach (var ai in testTargets)
            {
                if (ai == null) continue;
                
                Log($"Testing detection for {ai.name}");
                
                // Test 1: Basic property access
                bool test1 = TestBasicProperties(ai);
                RecordTest($"{ai.name}_BasicProperties", test1);
                
                // Test 2: Distance calculation
                bool test2 = TestDistanceCalculation(ai);
                RecordTest($"{ai.name}_DistanceCalculation", test2);
                
                // Test 3: State access
                bool test3 = TestStateAccess(ai);
                RecordTest($"{ai.name}_StateAccess", test3);
                
                yield return new WaitForSeconds(0.5f);
            }
            
            Log("=== Detection Systems Test Complete ===");
            testsRunning = false;
        }

        /// <summary>
        /// Test AI state transitions
        /// </summary>
        private IEnumerator TestStateTransitions()
        {
            testsRunning = true;
            Log("=== Testing AI State Transitions ===");
            
            foreach (var ai in testTargets)
            {
                if (ai == null) continue;
                
                Log($"Testing state transitions for {ai.name}");
                
                // Record initial state
                var initialState = ai.CurrentState;
                Log($"Initial state: {initialState}");
                
                // Test state changes
                ai.SetState(Blazeout.AI.AIState.Alert);
                yield return new WaitForSeconds(0.5f);
                bool alertTest = ai.CurrentState == Blazeout.AI.AIState.Alert;
                RecordTest($"{ai.name}_AlertState", alertTest);
                
                ai.SetState(Blazeout.AI.AIState.Search);
                yield return new WaitForSeconds(0.5f);
                bool searchTest = ai.CurrentState == Blazeout.AI.AIState.Search;
                RecordTest($"{ai.name}_SearchState", searchTest);
                
                // Return to initial state
                ai.SetState(initialState);
                yield return new WaitForSeconds(0.5f);
            }
            
            Log("=== State Transitions Test Complete ===");
            testsRunning = false;
        }

        /// <summary>
        /// Test pathfinding functionality
        /// </summary>
        private IEnumerator TestPathfinding()
        {
            testsRunning = true;
            Log("=== Testing AI Pathfinding ===");
            
            foreach (var ai in testTargets)
            {
                if (ai == null) continue;
                
                Log($"Testing pathfinding for {ai.name}");
                
                // Test NavMeshAgent access
                var navAgent = ai.GetComponent<UnityEngine.AI.NavMeshAgent>();
                bool navAgentTest = navAgent != null;
                RecordTest($"{ai.name}_NavMeshAgent", navAgentTest);
                
                if (navAgent != null)
                {
                    // Test movement capability
                    bool isOnNavMesh = navAgent.isOnNavMesh;
                    RecordTest($"{ai.name}_OnNavMesh", isOnNavMesh);
                    
                    // Test movement properties
                    bool hasValidSpeed = navAgent.speed > 0;
                    RecordTest($"{ai.name}_ValidSpeed", hasValidSpeed);
                }
                
                yield return new WaitForSeconds(0.5f);
            }
            
            Log("=== Pathfinding Test Complete ===");
            testsRunning = false;
        }

        /// <summary>
        /// Test performance metrics
        /// </summary>
        private IEnumerator TestPerformance()
        {
            testsRunning = true;
            Log("=== Testing AI Performance ===");
            
            float startTime = Time.time;
            int frameCount = 0;
            float totalFrameTime = 0;
            
            while (Time.time - startTime < testDuration)
            {
                float frameStart = Time.realtimeSinceStartup;
                
                // Simulate AI updates
                foreach (var ai in testTargets)
                {
                    if (ai != null)
                    {
                        // Access properties to simulate update load
                        var state = ai.CurrentState;
                        var distance = ai.DistanceToPlayer;
                        var canSee = ai.CanSeePlayer;
                        var alertLevel = ai.AlertLevel;
                    }
                }
                
                float frameTime = Time.realtimeSinceStartup - frameStart;
                totalFrameTime += frameTime;
                frameCount++;
                
                yield return null;
            }
            
            float averageFrameTime = totalFrameTime / frameCount;
            float fps = 1.0f / averageFrameTime;
            
            Log($"Performance Test Results:");
            Log($"- Average frame time: {averageFrameTime * 1000:F2}ms");
            Log($"- Average FPS: {fps:F1}");
            Log($"- AI entities tested: {testTargets.Length}");
            
            bool performanceTest = averageFrameTime < 0.016f; // 60 FPS threshold
            RecordTest("PerformanceTest", performanceTest);
            
            Log("=== Performance Test Complete ===");
            testsRunning = false;
        }

        /// <summary>
        /// Run all tests in sequence
        /// </summary>
        private IEnumerator RunAllTestsCoroutine()
        {
            testsRunning = true;
            testResults.Clear();
            totalTests = 0;
            passedTests = 0;
            
            Log("=== Running All AI Tests ===");
            
            yield return StartCoroutine(TestDetectionSystems());
            yield return new WaitForSeconds(1.0f);
            
            yield return StartCoroutine(TestStateTransitions());
            yield return new WaitForSeconds(1.0f);
            
            yield return StartCoroutine(TestPathfinding());
            yield return new WaitForSeconds(1.0f);
            
            yield return StartCoroutine(TestPerformance());
            
            // Display final results
            DisplayTestResults();
            testsRunning = false;
        }
        #endregion

        #region Test Utilities
        /// <summary>
        /// Test basic AI properties
        /// </summary>
        private bool TestBasicProperties(Blazeout.AI.NPCEnemyAI ai)
        {
            try
            {
                var state = ai.CurrentState;
                var canSee = ai.CanSeePlayer;
                var distance = ai.DistanceToPlayer;
                var alertLevel = ai.AlertLevel;
                var isMoving = ai.IsMoving;
                var lastKnown = ai.LastKnownPlayerPosition;
                
                Log($"  ✓ Basic properties accessible");
                return true;
            }
            catch (System.Exception e)
            {
                LogError($"  ✗ Basic properties failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test distance calculation
        /// </summary>
        private bool TestDistanceCalculation(Blazeout.AI.NPCEnemyAI ai)
        {
            if (playerTransform == null) return false;
            
            float aiDistance = ai.DistanceToPlayer;
            float actualDistance = Vector3.Distance(ai.transform.position, playerTransform.position);
            float difference = Mathf.Abs(aiDistance - actualDistance);
            
            bool accurate = difference < 0.1f; // Allow small floating point differences
            
            if (accurate)
                Log($"  ✓ Distance calculation accurate: {aiDistance:F2}");
            else
                LogError($"  ✗ Distance calculation inaccurate: AI={aiDistance:F2}, Actual={actualDistance:F2}");
            
            return accurate;
        }

        /// <summary>
        /// Test state access
        /// </summary>
        private bool TestStateAccess(Blazeout.AI.NPCEnemyAI ai)
        {
            try
            {
                var currentState = ai.CurrentState;
                bool validState = System.Enum.IsDefined(typeof(Blazeout.AI.AIState), currentState);
                
                if (validState)
                    Log($"  ✓ Valid state: {currentState}");
                else
                    LogError($"  ✗ Invalid state: {currentState}");
                
                return validState;
            }
            catch (System.Exception e)
            {
                LogError($"  ✗ State access failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Record test result
        /// </summary>
        private void RecordTest(string testName, bool passed)
        {
            testResults[testName] = passed;
            totalTests++;
            if (passed) passedTests++;
        }

        /// <summary>
        /// Display test results summary
        /// </summary>
        private void DisplayTestResults()
        {
            Log("=== FINAL TEST RESULTS ===");
            Log($"Total Tests: {totalTests}");
            Log($"Passed: {passedTests}");
            Log($"Failed: {totalTests - passedTests}");
            Log($"Success Rate: {(float)passedTests / totalTests * 100:F1}%");
            
            if (verboseLogging)
            {
                Log("Detailed Results:");
                foreach (var result in testResults)
                {
                    string status = result.Value ? "PASS" : "FAIL";
                    Log($"  {result.Key}: {status}");
                }
            }
            
            Log("=== TEST COMPLETE ===");
        }

        /// <summary>
        /// Display test controls
        /// </summary>
        private void DisplayTestControls()
        {
            Log("=== AI Test Controls ===");
            Log("1: Test Detection Systems");
            Log("2: Test State Transitions");
            Log("3: Test Pathfinding");
            Log("4: Test Performance");
            Log("5: Run All Tests");
            Log("H: Show this help");
        }
        #endregion

        #region Logging
        /// <summary>
        /// Log message with test prefix
        /// </summary>
        private void Log(string message)
        {
            if (verboseLogging)
            {
                UnityEngine.Debug.Log($"[AITest] {message}");
            }
        }

        /// <summary>
        /// Log error message
        /// </summary>
        private void LogError(string message)
        {
            UnityEngine.Debug.LogError($"[AITest] {message}");
        }
        #endregion
    }
}
