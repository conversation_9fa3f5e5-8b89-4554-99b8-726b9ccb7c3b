Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker9
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker9.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [7132]  Target information:

Player connection [7132]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1236003097 [EditorId] 1236003097 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [7132] Host joined multi-casting on [***********:54997]...
Player connection [7132] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 10.54 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.79 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56316
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005274 seconds.
- Loaded All Assemblies, in  1.057 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.104 seconds
Domain Reload Profiling: 2160ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (122ms)
	RebuildNativeTypeToScriptingClass (38ms)
	initialDomainReloadingComplete (120ms)
	LoadAllAssembliesAndSetupDomain (468ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (458ms)
				TypeCache.ScanAssembly (422ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1105ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (959ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (56ms)
			SetLoadedEditorAssemblies (20ms)
			BeforeProcessingInitializeOnLoad (261ms)
			ProcessInitializeOnLoadAttributes (486ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.318 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 8.44 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.090 seconds
Domain Reload Profiling: 5405ms
	BeginReloadAssembly (470ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (118ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (1588ms)
		LoadAssemblies (1004ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (840ms)
			TypeCache.Refresh (611ms)
				TypeCache.ScanAssembly (571ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (37ms)
	FinalizeReload (3091ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2440ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (586ms)
			ProcessInitializeOnLoadAttributes (1678ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Refreshing native plugins compatible for Editor in 18.17 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.16 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.5 MB). Loaded Objects now: 8334.
Memory consumption went from 210.0 MB to 200.6 MB.
Total: 171.829400 ms (FindLiveObjects: 2.504900 ms CreateObjectMapping: 2.784300 ms MarkObjects: 155.441900 ms  DeleteObjects: 11.094300 ms)

========================================================================
Received Import Request.
  Time since last request: 1812182.032091 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultLookDevProfile.asset
  artifactKey: Guid(1d491ecba2daea848bcc943bec301f07) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultLookDevProfile.asset using Guid(1d491ecba2daea848bcc943bec301f07) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7a693b75205dfb6a2cc54fb82cab31b0') in 3.3034587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e6adb229b2a8da9dd9beaac0d2cffabe') in 0.1300288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab
  artifactKey: Guid(f340455adb5398544a6d24914132484f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab using Guid(f340455adb5398544a6d24914132484f) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9f6f85312e317b95facccfdaf40fddff') in 0.1244289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Demo/TC_Industrial_Sunset_02_puresky_8k.hdr
  artifactKey: Guid(a3571048b72ad6046b658621213b67d1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/TC_Industrial_Sunset_02_puresky_8k.hdr using Guid(a3571048b72ad6046b658621213b67d1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74d7cc57c1189ba3f4b730d742c0ce28') in 1.0794838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_01_4x4.mat
  artifactKey: Guid(f93fbb5d51e6cbd498cbe491558a7a22) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_01_4x4.mat using Guid(f93fbb5d51e6cbd498cbe491558a7a22) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31baf45bf680ae7895ba3226ab7beba6') in 0.3177707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab
  artifactKey: Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab using Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)The method get_fontSharedMaterials was not found on topleft. This property will not be indexed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Rendering.Universal.MaterialReferenceBuilder:GetMaterialFromMethod (System.Reflection.MethodInfo,UnityEngine.Object,System.Func`3<string, string, string>) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs:133)
UnityEditor.Rendering.Universal.ConversionIndexers:ConversionIndexer (UnityEditor.Search.CustomObjectIndexerTarget,UnityEditor.Search.ObjectIndexer) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/ConversionIndexers.cs:21)
UnityEditor.Search.ObjectIndexer:CallCustomIndexers (string,int,UnityEngine.Object,UnityEditor.SerializedObject,bool)
UnityEditor.Search.ObjectIndexer:IndexCustomProperties (string,int,UnityEngine.Object)
UnityEditor.Search.AssetIndexer:IndexCustomGameObjectProperties (string,int,UnityEngine.GameObject)
UnityEditor.Search.AssetIndexer:IndexObjects (UnityEngine.GameObject[],string,string,string,bool)
UnityEditor.Search.AssetIndexer:IndexPrefab (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs Line: 133)

The method get_fontSharedMaterials was not found on topleft_desc. This property will not be indexed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Rendering.Universal.MaterialReferenceBuilder:GetMaterialFromMethod (System.Reflection.MethodInfo,UnityEngine.Object,System.Func`3<string, string, string>) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs:133)
UnityEditor.Rendering.Universal.ConversionIndexers:ConversionIndexer (UnityEditor.Search.CustomObjectIndexerTarget,UnityEditor.Search.ObjectIndexer) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/ConversionIndexers.cs:21)
UnityEditor.Search.ObjectIndexer:CallCustomIndexers (string,int,UnityEngine.Object,UnityEditor.SerializedObject,bool)
UnityEditor.Search.ObjectIndexer:IndexCustomProperties (string,int,UnityEngine.Object)
UnityEditor.Search.AssetIndexer:IndexCustomGameObjectProperties (string,int,UnityEngine.GameObject)
UnityEditor.Search.AssetIndexer:IndexObjects (UnityEngine.GameObject[],string,string,string,bool)
UnityEditor.Search.AssetIndexer:IndexPrefab (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs Line: 133)

The method get_fontSharedMaterials was not found on topleft. This property will not be indexed.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Rendering.Universal.MaterialReferenceBuilder:GetMaterialFromMethod (System.Reflection.MethodInfo,UnityEngine.Object,System.Func`3<string, string, string>) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs:133)
UnityEditor.Rendering.Universal.ConversionIndexers:ConversionIndexer (UnityEditor.Search.CustomObjectIndexerTarget,UnityEditor.Search.ObjectIndexer) (at ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/ConversionIndexers.cs:21)
UnityEditor.Search.ObjectIndexer:CallCustomIndexers (string,int,UnityEngine.Object,UnityEditor.SerializedObject,bool)
UnityEditor.Search.ObjectIndexer:IndexCustomProperties (string,int,UnityEngine.Object)
UnityEditor.Search.AssetIndexer:IndexCustomGameObjectProperties (string,int,UnityEngine.GameObject)
UnityEditor.Search.AssetIndexer:IndexObjects (UnityEngine.GameObject[],string,string,string,bool)
UnityEditor.Search.AssetIndexer:IndexPrefab (string,bool)
UnityEditor.Search.AssetIndexer:IndexSceneDocument (string,bool)
UnityEditor.Search.AssetIndexer:IndexDocument (string,bool)
UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset (UnityEditor.AssetImporters.AssetImportContext)
UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData (UnityEditor.AssetImporters.AssetImportContext)

(Filename: ./Library/PackageCache/com.unity.render-pipelines.universal@63585d5be1b1/Editor/Converter/MaterialReferenceBuilder.cs Line: 133)

 -> (artifact id: '03ffed7501e373a5c861d1149c457953') in 0.713368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_04.mat
  artifactKey: Guid(9259b81c7e9debc44a41366b55665fad) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_04.mat using Guid(9259b81c7e9debc44a41366b55665fad) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6320eb38ec5c680e6e739799f56f5396') in 0.8249188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_02_4x4.mat
  artifactKey: Guid(0c5a3bf967ce85f4d90cf117ab242978) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_02_4x4.mat using Guid(0c5a3bf967ce85f4d90cf117ab242978) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba1e5f806395132a906a9ac712648345') in 0.0390017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_02.prefab
  artifactKey: Guid(f863d9e8791748141a0cce0fb6d9eb3c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_02.prefab using Guid(f863d9e8791748141a0cce0fb6d9eb3c) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '011c3caf98ae94295871940e9134cfc1') in 0.0644715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.prefab
  artifactKey: Guid(aabff765ac55f2d48aa652fdfaeb9ba1) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.prefab using Guid(aabff765ac55f2d48aa652fdfaeb9ba1) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '752bbdb62e85c1dc1201fef9f1023319') in 0.0403636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000161 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_01.mat
  artifactKey: Guid(6b5bf5c4f0b4f4b4684801a3242fbffa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_01.mat using Guid(6b5bf5c4f0b4f4b4684801a3242fbffa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8427bbc7f0bccd024926aeb50bd8c8ed') in 0.6578494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterGround_01.prefab
  artifactKey: Guid(8078ef8b06c75fa4cbc83d588e1e91e9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterGround_01.prefab using Guid(8078ef8b06c75fa4cbc83d588e1e91e9) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e52847a62b4a3e8de9b4f4812ed1c80') in 0.1097364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 72

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01.prefab
  artifactKey: Guid(abc00000000007349718103727096582) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01.prefab using Guid(abc00000000007349718103727096582) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a74b295a685894beaf3eef4d0a2844a4') in 0.0788414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset
  artifactKey: Guid(c41005c129ba4d66911b75229fd70b45) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset using Guid(c41005c129ba4d66911b75229fd70b45) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8811dd21eeb51e1863101116aa56da2d') in 0.0770791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000322 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab
  artifactKey: Guid(9e4757aed32f476419bb6acd9168541a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab using Guid(9e4757aed32f476419bb6acd9168541a) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '404225be5acde715648ea0f861aa46ad') in 0.0717355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)FluidArea.prefab
  artifactKey: Guid(3b2a872352c65b444b94cf4fc53a49b0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)FluidArea.prefab using Guid(3b2a872352c65b444b94cf4fc53a49b0) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bacb8a2e1294bcf64ee29fd416bfc1ea') in 0.0348914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_22.prefab
  artifactKey: Guid(abc00000000010477518808022030609) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_22.prefab using Guid(abc00000000010477518808022030609) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'df90c2f746bc7661a6a88a119e2bfc7f') in 0.0527502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab
  artifactKey: Guid(027f7676d85eaff4db64945788d561c4) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab using Guid(027f7676d85eaff4db64945788d561c4) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd6975de8b328239fb128ad4c88fe7394') in 0.0405909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_05.mat
  artifactKey: Guid(5ee246312a6060e419fc1497268c0bb5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_05.mat using Guid(5ee246312a6060e419fc1497268c0bb5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c2d3cbd5a6941894c73ec2fe77e3f66') in 0.7541829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_C.prefab
  artifactKey: Guid(abc00000000005698991249062767216) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_C.prefab using Guid(abc00000000005698991249062767216) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e3d333a540a0f0b8a3834c22a6cfdf68') in 0.0351248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bottle_01.prefab
  artifactKey: Guid(abc00000000014666369635587024777) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bottle_01.prefab using Guid(abc00000000014666369635587024777) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b6fcd9d4f4e0b289d0e157ad9769cf5d') in 0.0346391 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleGround_01_4x4.mat
  artifactKey: Guid(6528c2bc971f54840bd337271d16d51c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleGround_01_4x4.mat using Guid(6528c2bc971f54840bd337271d16d51c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75d0e3f3a45aff1f2dd65f263406990d') in 0.2613273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_02.prefab
  artifactKey: Guid(abc00000000012735556429546858880) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_02.prefab using Guid(abc00000000012735556429546858880) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f937813e4e159a37862f074e0cfa8f6') in 0.0340499 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_03.prefab
  artifactKey: Guid(abc00000000004900310109673188669) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_03.prefab using Guid(abc00000000004900310109673188669) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d69ac8f21c8702db9d081b23811f133') in 0.0285881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Scripts/Systems/HealthSystem.cs
  artifactKey: Guid(615cbcd94e3ec7e4d9ab363e738e42fa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Systems/HealthSystem.cs using Guid(615cbcd94e3ec7e4d9ab363e738e42fa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '610cfc597a1d50ab27aed943d0dbdb23') in 0.0526232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_02.prefab
  artifactKey: Guid(abc00000000015967625890693701188) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_02.prefab using Guid(abc00000000015967625890693701188) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9ef0fcd0ea3c4a10a8a7a1254e37269a') in 0.0461887 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_02.prefab
  artifactKey: Guid(abc00000000000823487211768580286) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_02.prefab using Guid(abc00000000000823487211768580286) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ad4038ef87eb69188dc512fa4202f382') in 0.0357128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_02.prefab
  artifactKey: Guid(abc00000000017755707725320845443) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_02.prefab using Guid(abc00000000017755707725320845443) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e1896f3f3d00046941acf9422f63353') in 0.0431099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron.prefab
  artifactKey: Guid(abc00000000012840463066036346035) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron.prefab using Guid(abc00000000012840463066036346035) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '174d1efda520adee8d962c624e45bbf1') in 0.0431369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_03.prefab
  artifactKey: Guid(abc00000000008070227094333872325) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_03.prefab using Guid(abc00000000008070227094333872325) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1016d81cc4040c7cf30b0469d6e18202') in 0.1237857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_01.prefab
  artifactKey: Guid(abc00000000010351259951333084775) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_01.prefab using Guid(abc00000000010351259951333084775) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '955f6dfc99e60b4fdb8dfc5832ebd5da') in 0.0466992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_69.prefab
  artifactKey: Guid(abc00000000000417408960686482107) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_69.prefab using Guid(abc00000000000417408960686482107) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'de5fce07e41348a4278805110e67b0d7') in 0.040578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med_Loose.prefab
  artifactKey: Guid(abc00000000000716624330981334820) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med_Loose.prefab using Guid(abc00000000000716624330981334820) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '693f02fd6137243d6464088362e4ee74') in 0.0747862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4_Poison.mat
  artifactKey: Guid(e7eda1cdf488af24eb04b47c811aca9b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4_Poison.mat using Guid(e7eda1cdf488af24eb04b47c811aca9b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e7f7429e4b7974610e39713cd34a952') in 0.5576737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000962 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3_Lava.mat
  artifactKey: Guid(ea88c8d70ce1daa4d950e0e240edb093) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_02_3x3_Lava.mat using Guid(ea88c8d70ce1daa4d950e0e240edb093) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e3dd5d15d3f09eed2f6c6ceb74dad14') in 0.0482085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8_Lava.mat
  artifactKey: Guid(23953020dd3fcaa4e8f2ca4ccd4e4b9f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8_Lava.mat using Guid(23953020dd3fcaa4e8f2ca4ccd4e4b9f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc0f6c7b4e18e97e026c009eabf39f5c') in 0.0386188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_06_4x3.mat
  artifactKey: Guid(687b02c2a0efa5e48bc1a4b5f8096a03) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_06_4x3.mat using Guid(687b02c2a0efa5e48bc1a4b5f8096a03) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d337634b59bcde7382b10241c29df18') in 0.0544513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_B.prefab
  artifactKey: Guid(abc00000000004350169994041876357) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_B.prefab using Guid(abc00000000004350169994041876357) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ecd2e26206be4ac0d5106d4af8ffc29') in 0.0677216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_30.prefab
  artifactKey: Guid(abc00000000013918172512350973120) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_30.prefab using Guid(abc00000000013918172512350973120) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b12490ee4d3e4cbe453eef2be6eb53f3') in 0.2914789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_01.prefab
  artifactKey: Guid(abc00000000005328908562217007902) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_01.prefab using Guid(abc00000000005328908562217007902) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bf86158f2f846d4fecc8381aec53358c') in 0.0374751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_drinkstable.prefab
  artifactKey: Guid(abc00000000008452398761007879384) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_drinkstable.prefab using Guid(abc00000000008452398761007879384) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '627424792540e5f5bf5e467ade3fdbc8') in 0.0436174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_XL_02.prefab
  artifactKey: Guid(abc00000000012231957801027281667) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_XL_02.prefab using Guid(abc00000000012231957801027281667) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '23adddb91c95fce225de285a6c6082f1') in 0.0526096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 37

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_02.prefab
  artifactKey: Guid(abc00000000018238010764999236038) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_02.prefab using Guid(abc00000000018238010764999236038) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5c45ad3830c136b9ebd5cf0a7a58b0e6') in 0.0405214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Goblet_01.prefab
  artifactKey: Guid(abc00000000002890729963569673488) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Goblet_01.prefab using Guid(abc00000000002890729963569673488) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea98e5fa3f8bd3947dee174ad1172359') in 0.0391187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A.prefab
  artifactKey: Guid(abc00000000006362557841762442139) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A.prefab using Guid(abc00000000006362557841762442139) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '16108e934674f77432363ecbcf6829ed') in 0.0392718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Arch.prefab
  artifactKey: Guid(abc00000000007272907398863773049) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Arch.prefab using Guid(abc00000000007272907398863773049) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c0772b0491f4e302f8c805848d56e081') in 0.0449343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab
  artifactKey: Guid(a20a162ac11a35643bed0f3823718abb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab using Guid(a20a162ac11a35643bed0f3823718abb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5f31084184486256521f5b6af0edba7') in 0.114527 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/Meshes/FX_MS_TwirlShapes_01.fbx
  artifactKey: Guid(98605abcfa6e82349b3bbb3954a6990d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Meshes/FX_MS_TwirlShapes_01.fbx using Guid(98605abcfa6e82349b3bbb3954a6990d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41903a4479f28d0469bd2eac0b8f5dac') in 0.1104598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_07.mat
  artifactKey: Guid(2a44eec2e7bd667419556929c061f95d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_07.mat using Guid(2a44eec2e7bd667419556929c061f95d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f05d5184eb9e49d28ce7e5bef840721') in 0.0432658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_E.prefab
  artifactKey: Guid(abc00000000018103128526035574089) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_E.prefab using Guid(abc00000000018103128526035574089) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22d97951cf234cfdf282bf6fa62e460a') in 0.0539915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_01_2x5.mat
  artifactKey: Guid(0f39ca2af089fd347b7b3a2284455acb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_01_2x5.mat using Guid(0f39ca2af089fd347b7b3a2284455acb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1842178f3374826f50cd660fb69e4814') in 0.0558762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch.prefab
  artifactKey: Guid(abc00000000016636803207453149333) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch.prefab using Guid(abc00000000016636803207453149333) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ce539272debe5f6d4e1afb3566b40e53') in 0.0476403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Main_Stairs.prefab
  artifactKey: Guid(abc00000000009185449643404600751) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Main_Stairs.prefab using Guid(abc00000000009185449643404600751) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '03cda9f47478f343e43c4506f92eccf5') in 0.0444577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 37

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch_1.prefab
  artifactKey: Guid(abc00000000004724551570809325481) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch_1.prefab using Guid(abc00000000004724551570809325481) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '79971ee5180debd66ea1918a1a8730fe') in 0.038979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lsarge_Rock_B.prefab
  artifactKey: Guid(abc00000000005713074273989172594) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lsarge_Rock_B.prefab using Guid(abc00000000005713074273989172594) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '81b49067bacd4832a1fae8ed2234ad24') in 0.0459102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_MagicWater_OrbLoop.prefab
  artifactKey: Guid(3978f9137b47bba40a4ea58f2f46d448) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_MagicWater_OrbLoop.prefab using Guid(3978f9137b47bba40a4ea58f2f46d448) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4cf81477555d9403eced2abcf0be7525') in 0.3903291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 107

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern_Hanging.prefab
  artifactKey: Guid(abc00000000006982472483048244726) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern_Hanging.prefab using Guid(abc00000000006982472483048244726) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ff9b8d3a212c495f73c48405d36e11f2') in 0.0315667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_04.prefab
  artifactKey: Guid(abc00000000006585424960363013979) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_04.prefab using Guid(abc00000000006585424960363013979) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '08183ccb38ac4781aa53bc4c0221bd58') in 0.0388174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01_1.prefab
  artifactKey: Guid(abc00000000003611456838156076667) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01_1.prefab using Guid(abc00000000003611456838156076667) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fd92954fde4839bae425fd51e727a233') in 0.0468906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_02.prefab
  artifactKey: Guid(abc00000000007864659359926381920) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_02.prefab using Guid(abc00000000007864659359926381920) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '83d1250b4eb5e9f14ffca6f51ecb68df') in 0.0421646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NUDE Variant.prefab
  artifactKey: Guid(f9b74c6fb02af1e43b45a08c26a8111b) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NUDE Variant.prefab using Guid(f9b74c6fb02af1e43b45a08c26a8111b) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e5afbbacc4789c999e0df56d91c84140') in 0.2140335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 703

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08_1.prefab
  artifactKey: Guid(abc00000000017152206545133094477) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08_1.prefab using Guid(abc00000000017152206545133094477) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4ff5073290480fff339a8bf17f2b03b9') in 0.0559651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Scripts/Editor/PlayerController3DCursor.cs
  artifactKey: Guid(7f4e8a2b9c1d3e4f5a6b7c8d9e0f1a2b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Editor/PlayerController3DCursor.cs using Guid(7f4e8a2b9c1d3e4f5a6b7c8d9e0f1a2b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be90fa33ec189e929593fddfe7076804') in 0.0332821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader
  artifactKey: Guid(48bb5f55d8670e349b6e614913f9d910) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader using Guid(48bb5f55d8670e349b6e614913f9d910) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37d1989aa2e83ffad517691fd5977f75') in 0.0390544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs.prefab
  artifactKey: Guid(abc00000000011763392490455100587) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs.prefab using Guid(abc00000000011763392490455100587) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0dd79518ad78bb77182d97b9ba21558c') in 0.0661665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Charge.prefab
  artifactKey: Guid(6e0b19c854b7f6f42819d7435e6032c5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Charge.prefab using Guid(6e0b19c854b7f6f42819d7435e6032c5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62854a636a8ad03b8db281d94afaee2b') in 0.1451516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool.prefab
  artifactKey: Guid(abc00000000000461347809624088054) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool.prefab using Guid(abc00000000000461347809624088054) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '99bd50402d14cd129026c482cf6da184') in 0.033503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/TextMesh Pro/Shaders/TMPro_Mobile.cginc
  artifactKey: Guid(c334973cef89a9840b0b0c507e0377ab) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMPro_Mobile.cginc using Guid(c334973cef89a9840b0b0c507e0377ab) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b626f202341517a928b9898a9360653c') in 0.0509986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_02.prefab
  artifactKey: Guid(abc00000000005242270154054048716) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_02.prefab using Guid(abc00000000005242270154054048716) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5a664d99833a4a56faa91d5f149fbd80') in 0.0444485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round.prefab
  artifactKey: Guid(abc00000000018338094561444558674) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Round.prefab using Guid(abc00000000018338094561444558674) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '714bdf05f6e154780df48f241ad5133d') in 0.0439145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_02.prefab
  artifactKey: Guid(abc00000000001366254860434428519) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_02.prefab using Guid(abc00000000001366254860434428519) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e2872fa4035f7311a564ffe4af9a8793') in 0.0467825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/Transparencies_Gradient_Noise_Add.shadergraph
  artifactKey: Guid(a08e970a70d21e340afe132c91161a9c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/Transparencies_Gradient_Noise_Add.shadergraph using Guid(a08e970a70d21e340afe132c91161a9c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f457cfcb3e9e4e1c568739552a6b05bc') in 0.6103777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/TextMesh Pro/Shaders/TMPro_Properties.cginc
  artifactKey: Guid(3997e2241185407d80309a82f9148466) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMPro_Properties.cginc using Guid(3997e2241185407d80309a82f9148466) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ab6c1db614f5b8038e6702944d47c06') in 0.0465354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_03.prefab
  artifactKey: Guid(abc00000000016551305415872494976) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_03.prefab using Guid(abc00000000016551305415872494976) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e0bc327c7dde1c3af87a6e54b8b067a4') in 0.0525063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_01.prefab
  artifactKey: Guid(abc00000000016185023768972930501) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_01.prefab using Guid(abc00000000016185023768972930501) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b5fb2ae9ea955c22f9969fa234c34501') in 0.0297928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/ComplexGradientTransparencies.shadergraph
  artifactKey: Guid(81a925152c3a1724286eb724c3ab0f15) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/ComplexGradientTransparencies.shadergraph using Guid(81a925152c3a1724286eb724c3ab0f15) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d608b2e41ed3a29d00f2bcb9ad4e06f') in 0.0423985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.prefab
  artifactKey: Guid(765c662294e2d934c800874481979a1d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.prefab using Guid(765c662294e2d934c800874481979a1d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c00204420b16d87184a4e79f4746964') in 0.0593624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_D.prefab
  artifactKey: Guid(abc00000000000558807816154458515) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_D.prefab using Guid(abc00000000000558807816154458515) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fe94a88f40882b67f3da56f47b1c5669') in 0.0952413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.prefab
  artifactKey: Guid(bf71f120f7b93414a955e65b9279ec5d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.prefab using Guid(bf71f120f7b93414a955e65b9279ec5d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '80f8118a7129b3241c3238e67eadb8a4') in 0.5407197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern Outline.png
  artifactKey: Guid(167a6bea744b29c4985ed090c7783049) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Modern Outline.png using Guid(167a6bea744b29c4985ed090c7783049) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '479be6a0755971037f0cea6d9198b0d2') in 0.0968626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Lock Dark.png
  artifactKey: Guid(8bc644fe6f184424e8ef112286b7a9be) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Lock Dark.png using Guid(8bc644fe6f184424e8ef112286b7a9be) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98d06e14bf8989e52043e7803c7341ad') in 0.0665159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Banister.prefab
  artifactKey: Guid(abc00000000010415127047187200049) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Banister.prefab using Guid(abc00000000010415127047187200049) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'da9d9cd2c70516a7e82939ff9bfec67e') in 0.0435502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.prefab
  artifactKey: Guid(e6a0d41974ec0d74cb1e546f4097294d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.prefab using Guid(e6a0d41974ec0d74cb1e546f4097294d) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6685d3c65aaae11d2a170d771b9cf12a') in 0.2195481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.png
  artifactKey: Guid(dffef66376be4fa480fb02b19edbe903) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.png using Guid(dffef66376be4fa480fb02b19edbe903) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c6c9615b8e3cd8fd8d212d6a5999219') in 0.0655073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Scripts/Debug/PlayerController3DDebugger.cs
  artifactKey: Guid(a544c756ed33de849a4180204db416e2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Debug/PlayerController3DDebugger.cs using Guid(a544c756ed33de849a4180204db416e2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '006325961e4a6f6a1d2b3757d5ebd65f') in 0.0265713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_01.mat
  artifactKey: Guid(b1275b10d61b0a74db4eddd2fede9885) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_01.mat using Guid(b1275b10d61b0a74db4eddd2fede9885) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '029e611f0527fce60578b6bd222e60cd') in 0.0618342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C_1.prefab
  artifactKey: Guid(abc00000000010892442399679327472) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C_1.prefab using Guid(abc00000000010892442399679327472) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '403ebf3f3eef014d17662db0d9cc4627') in 0.0406451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern.prefab
  artifactKey: Guid(abc00000000009577592741665352263) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern.prefab using Guid(abc00000000009577592741665352263) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd387c78156ff7105351fcaad373fe2e7') in 0.0358027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Projectile_Cast.prefab
  artifactKey: Guid(3664140494b88e3468dcca84b6dbde23) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Projectile_Cast.prefab using Guid(3664140494b88e3468dcca84b6dbde23) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '81ca88f129135b0280c0d5566ff66287') in 0.0515376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 59

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Documentation - Physics Character Controller.pdf
  artifactKey: Guid(97289747f4777834d8ea20056a0dfc73) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Documentation - Physics Character Controller.pdf using Guid(97289747f4777834d8ea20056a0dfc73) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5996f21a3ba7f960b682f2bda407835') in 0.0255161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset
  artifactKey: Guid(28733647ae58e1245891229aca418864) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset using Guid(28733647ae58e1245891229aca418864) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5e39f918d73d16218f49ab1b2b5848e') in 0.0337856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/TutorialInfo/Icons/HDRP.png
  artifactKey: Guid(d19680cd422524695938fbe55cc3b3bd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TutorialInfo/Icons/HDRP.png using Guid(d19680cd422524695938fbe55cc3b3bd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f5f39da9dcb625ced1ce437e3af3e3b') in 0.077001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab
  artifactKey: Guid(fe987b373409bdd4e92698a48f7505b8) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab using Guid(fe987b373409bdd4e92698a48f7505b8) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6c6f9f2149209d059ca75aca33acb130') in 0.0431176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Right.png
  artifactKey: Guid(849b2ae1eeccecd4fbaa4717413d5b96) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default Faded Right.png using Guid(849b2ae1eeccecd4fbaa4717413d5b96) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '647acbfb54a0a45a3d9a624f1e8f257d') in 0.0747897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented T.png
  artifactKey: Guid(bf502fdf114d73c40ad50174de8ffc67) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented T.png using Guid(bf502fdf114d73c40ad50174de8ffc67) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f4d1838d030ceb787d786d55c50ad3b') in 0.0740004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved Terminal Bud.png
  artifactKey: Guid(46f3323b2d720044cbf78a4ee3017c95) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved Terminal Bud.png using Guid(46f3323b2d720044cbf78a4ee3017c95) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58ec8ce3edab412a2a85cea6acb770b6') in 0.090602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Constants.cs
  artifactKey: Guid(f034e053819fe8642996e247fd78f358) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Constants.cs using Guid(f034e053819fe8642996e247fd78f358) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3849d5cf4ad881f98430efb2d20b5d46') in 0.0362185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000155 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Shortcuts.cs
  artifactKey: Guid(f5681c0e772cbd1448923757e7fa1008) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Shortcuts.cs using Guid(f5681c0e772cbd1448923757e7fa1008) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba4f64dd3f24b92f011c091641dd241f') in 0.0347395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_13.mat
  artifactKey: Guid(e4b8c90034db1f24caada5379f2eab31) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_13.mat using Guid(e4b8c90034db1f24caada5379f2eab31) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4a170261d953bf4c16f29c5da086533') in 0.6549036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000330 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Checker.cs
  artifactKey: Guid(b50c1f53d37048f45b505c06c39f21d6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Checker.cs using Guid(b50c1f53d37048f45b505c06c39f21d6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f29c6129a49f93b64234b584cd55d224') in 0.0268112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Component.cs
  artifactKey: Guid(ccbb7272b47c1be4ea2605c38eca6cf2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Component.cs using Guid(ccbb7272b47c1be4ea2605c38eca6cf2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '253d5f644869c812d78752dc01312eb6') in 0.0269479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_Initializer.cs
  artifactKey: Guid(05aa725c187da6b43b86fac65cfd394e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_Initializer.cs using Guid(05aa725c187da6b43b86fac65cfd394e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bea4c71e74a6df4a805df91fc5c444b') in 0.0323966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Serializable.cs
  artifactKey: Guid(10d20d39867e7e34d84325f5bbd5c62a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Serializable.cs using Guid(10d20d39867e7e34d84325f5bbd5c62a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '218ef3640cb5e31d50fa950517227690') in 0.0298337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_GUI.cs
  artifactKey: Guid(a8fdfb0b5d1998d43b9128b9da2e6ba6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_GUI.cs using Guid(a8fdfb0b5d1998d43b9128b9da2e6ba6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d869e71c03e057e2caff6cc69c5138a') in 0.0372361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/Polycount.asset
  artifactKey: Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/Polycount.asset using Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(**********,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0b0a3b4430cbada834c40969b53dec54') in 0.0360923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.377212 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_32.mat
  artifactKey: Guid(9836873eb5958d74caaf1ce8da4368da) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_32.mat using Guid(9836873eb5958d74caaf1ce8da4368da) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5c6dad21359f9ef2e794fba0f809e85') in 0.1441088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Purple Variant.prefab
  artifactKey: Guid(8dbe21b9a1a525a40b0b0d5ab31261dc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Purple Variant.prefab using Guid(8dbe21b9a1a525a40b0b0d5ab31261dc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '********************************') in 0.1676033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_AllPartsTogether Variant.prefab
  artifactKey: Guid(5947f6b95f35860429f95e95f8107658) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_AllPartsTogether Variant.prefab using Guid(5947f6b95f35860429f95e95f8107658) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64c013dd558b804766ef05cc5c6990e3') in 0.1658145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 788

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_03.mat
  artifactKey: Guid(9ff2dd4c5367e2b49b1f8b4c50a7c292) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_03.mat using Guid(9ff2dd4c5367e2b49b1f8b4c50a7c292) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f426b221554625fdee80617f3c577528') in 1.0998201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Particle.mat
  artifactKey: Guid(f1ad80c9ab59df94fbe9115002698d94) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Particle.mat using Guid(f1ad80c9ab59df94fbe9115002698d94) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8840350dff9ab24595de72d75a744a17') in 0.0940612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_04.mat
  artifactKey: Guid(510d6044eb349cd43bf3d2ad0e65a6be) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_04.mat using Guid(510d6044eb349cd43bf3d2ad0e65a6be) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '096f5246200771eb04b5e37ef36b6caf') in 0.7510276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_20.png
  artifactKey: Guid(e9fb8ac34838ab748a7fa2f8153cd20f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_20.png using Guid(e9fb8ac34838ab748a7fa2f8153cd20f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d4b9651c6eb0ef51e616efee7477ecd') in 0.0908085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_DetailWeight_01.png
  artifactKey: Guid(61766c7bc1312114882ebbdd5e6093d2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_DetailWeight_01.png using Guid(61766c7bc1312114882ebbdd5e6093d2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '234ef7ac40bbe3ce009be56820e5a2ee') in 0.1044434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000152 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Normal_02.png
  artifactKey: Guid(b3a9fb3d649f97e41aa2ff08e446ab43) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Normal_02.png using Guid(b3a9fb3d649f97e41aa2ff08e446ab43) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '426007780bd9d59587733ff1d805e3f7') in 0.0902811 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_07.mat
  artifactKey: Guid(d4de2bb7957826b4d84f53b5d8cb4168) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_07.mat using Guid(d4de2bb7957826b4d84f53b5d8cb4168) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb2c71bfa70ff5e5e1e86f828d2a716a') in 0.7789591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22_Alternative.mat
  artifactKey: Guid(44b9b9d0470e0d64c97105addd708ca2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_22_Alternative.mat using Guid(44b9b9d0470e0d64c97105addd708ca2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34484c97fc9cd81aaf5cd614f865a1f3') in 0.2594251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_03.mat
  artifactKey: Guid(1fb49f55bf682cf40b628c1993e86ad5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_03.mat using Guid(1fb49f55bf682cf40b628c1993e86ad5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af2540186b35a19d323af06fd11143a9') in 0.3018236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000164 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_01.mat
  artifactKey: Guid(75088d33c9afa2d478811a2db83344ac) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_01.mat using Guid(75088d33c9afa2d478811a2db83344ac) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b08f3b53453fa548bc2dee1b331efa4') in 0.3076859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_26.png
  artifactKey: Guid(6525c3771e8e667429d3b44774ce2876) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_26.png using Guid(6525c3771e8e667429d3b44774ce2876) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e94c94d647b70b4d213edd81c0c08126') in 0.0766713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_06.png
  artifactKey: Guid(cc3e6b4ab7ba62349a05362c02409647) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_06.png using Guid(cc3e6b4ab7ba62349a05362c02409647) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4cb5b3e7ea836b565d13e924cf3d397') in 0.0736766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_01.png
  artifactKey: Guid(86ec67f2e38fe3c4e85338aa3071f812) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_01.png using Guid(86ec67f2e38fe3c4e85338aa3071f812) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '606f8bceb54097f4109f004e9c8a256d') in 0.0716787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000125 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Blood_01.png
  artifactKey: Guid(520f0522809adfd419838b6f407dcef3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Blood_01.png using Guid(520f0522809adfd419838b6f407dcef3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3cab1edcfaabc841bce1484ec924fdf') in 0.0750816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_05.png
  artifactKey: Guid(a51f0e42c9f84e1469460e4921e49918) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_05.png using Guid(a51f0e42c9f84e1469460e4921e49918) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0b25b7c8f20683f64147baaf49ed0b6') in 0.0918447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_01.png
  artifactKey: Guid(56758d884cd884448b38e787531e357e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_01.png using Guid(56758d884cd884448b38e787531e357e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd16466ed9e88ca0acfc7adedff4493fa') in 0.0610584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_01.png
  artifactKey: Guid(36a3e75ec0471e94c902cf7715c3cd77) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_01.png using Guid(36a3e75ec0471e94c902cf7715c3cd77) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26a3e7af304ba9330f4cfe148c0901bf') in 0.1005133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Skybox_02.mat
  artifactKey: Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Skybox_02.mat using Guid(********************************) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '799dadf11043e53e4719d4508bf67be9') in 0.1407552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_02_Dirty.mat
  artifactKey: Guid(3b59d64d2fc2abb418bd1eedafba9548) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_02_Dirty.mat using Guid(3b59d64d2fc2abb418bd1eedafba9548) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30773467581d74d487fefe93c694c2cf') in 0.9485693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_32.mat
  artifactKey: Guid(7c602e7cff55d5e4a9c1c1d907338549) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_32.mat using Guid(7c602e7cff55d5e4a9c1c1d907338549) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aed334d93d6ab68ebd9f5c373880f09b') in 0.2444414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Scripts/SpineProxy.cs
  artifactKey: Guid(dd95fd526fbaddd4e96feb1b5b051f7f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Scripts/SpineProxy.cs using Guid(dd95fd526fbaddd4e96feb1b5b051f7f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec20dadc50d619b15f0469a09a24fa0e') in 0.056664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/HumanF_Model.fbx
  artifactKey: Guid(1841c298173cdad4db8df8602c8f1c8d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/HumanF_Model.fbx using Guid(1841c298173cdad4db8df8602c8f1c8d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4351769fdab04d1cba16520122c3307b') in 0.0823367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 124

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyFace.mat
  artifactKey: Guid(c70c7660792bc524193d77a2df89539a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyFace.mat using Guid(c70c7660792bc524193d77a2df89539a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c699c0c0fb5dbe0425d1ad825cf2bfec') in 0.0583704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_OcclusionRoughnessMetallic.png
  artifactKey: Guid(cdbed0f74655515459d7e4250fcc0827) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_OcclusionRoughnessMetallic.png using Guid(cdbed0f74655515459d7e4250fcc0827) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16f2c1ca3222bc9be3d70ff2fd44cd7a') in 0.0955984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)DarkGrey.mat
  artifactKey: Guid(7f187a645b06e36419e453cfa9085266) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)DarkGrey.mat using Guid(7f187a645b06e36419e453cfa9085266) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32dfac48d898f426be1b770612b84c82') in 0.0550978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_10.png
  artifactKey: Guid(e23d450b33b7e0a4c8db069648022f55) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_10.png using Guid(e23d450b33b7e0a4c8db069648022f55) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b7b2ef551dfc2c0a1ca8a9724900298') in 0.1009306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_11.png
  artifactKey: Guid(80cb7f02126a24f4995c4d528bfb0a96) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_11.png using Guid(80cb7f02126a24f4995c4d528bfb0a96) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7cddb7fc63cd6eae67a98b36d2894933') in 0.0717064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab
  artifactKey: Guid(32d5f0cabfc0be846a4c217337d74479) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab using Guid(32d5f0cabfc0be846a4c217337d74479) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '527e7d270d8ce765f270e81b25e27517') in 0.0645286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)HangingIdle.fbx
  artifactKey: Guid(9a08a88c3b0406f4e884aa5442eaabbe) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)HangingIdle.fbx using Guid(9a08a88c3b0406f4e884aa5442eaabbe) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '139c82a658f2a620c36efa656fc4a529') in 0.1383112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Msh)Slope.fbx
  artifactKey: Guid(0569d2801232dba438e5ba4e051b5fa2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Msh)Slope.fbx using Guid(0569d2801232dba438e5ba4e051b5fa2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '223f71b5b48f4e08b9ff2ff96d4c9cac') in 0.0566751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchedWalking.fbx
  artifactKey: Guid(bed18790e98ab204bbd6e0b7682be266) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchedWalking.fbx using Guid(bed18790e98ab204bbd6e0b7682be266) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e76d192b12360921af8b16f55d55ec3f') in 0.075372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyEyes.mat
  artifactKey: Guid(32bb946ef33bf9a4eabd0af551277452) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyEyes.mat using Guid(32bb946ef33bf9a4eabd0af551277452) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd165d3a59ddf289449ae1c20b5aa2be3') in 0.0374259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Parallax_01.png
  artifactKey: Guid(9bba2a6781541e14fa0784f74b738a1e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Parallax_01.png using Guid(9bba2a6781541e14fa0784f74b738a1e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eca41faf44490c02365624eda6b1bac6') in 0.0769505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.099576 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab
  artifactKey: Guid(2508951d6b5158047b8c53425a720d81) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab using Guid(2508951d6b5158047b8c53425a720d81) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e83faf6b532b07715dea90faf0d04eee') in 0.1675466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GlowADD_01.png
  artifactKey: Guid(348e9497da930ed46bf99436ace645fa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GlowADD_01.png using Guid(348e9497da930ed46bf99436ace645fa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77d78f4e5d6878b23f0573819ba7d754') in 0.0598381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_VerticalSplash_3x3_01.psd
  artifactKey: Guid(b397fb283c2c1be4eb8b2fe09bbe3ba6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_VerticalSplash_3x3_01.psd using Guid(b397fb283c2c1be4eb8b2fe09bbe3ba6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a4b87a3615b4dd98f3cb80fa4dcd00e') in 0.0868013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeCone.prefab
  artifactKey: Guid(180bc4f9625b9044a85a20709235bfa6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeCone.prefab using Guid(180bc4f9625b9044a85a20709235bfa6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dfbfd1d47743078fe10aa714e541cfb7') in 0.0650261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TwirlTiled_4x5_01.psd
  artifactKey: Guid(844d955deb74a154d8a20d3f10b0e5c6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TwirlTiled_4x5_01.psd using Guid(844d955deb74a154d8a20d3f10b0e5c6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '119871d3b53f892309480b2dfda53bba') in 0.0726682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_02_4x4.psd
  artifactKey: Guid(579c315c1efb71a4fbcd2d24a24921c6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_02_4x4.psd using Guid(579c315c1efb71a4fbcd2d24a24921c6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0e6d6fa06231fa73b596e5b15d78f3b') in 0.0804034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SmallWaterGroundSplash_01_4x4.psd
  artifactKey: Guid(a418c64c39790324c94bfe636b3eda9f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SmallWaterGroundSplash_01_4x4.psd using Guid(a418c64c39790324c94bfe636b3eda9f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6d8acf62c825da9c13503452a9a376c') in 0.0838591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_03_3x3.psd
  artifactKey: Guid(1ac6d1979d8c16149b2713592118bba5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_03_3x3.psd using Guid(1ac6d1979d8c16149b2713592118bba5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd7a149dc45b0e5930a68ca5bafb7aff') in 0.0714496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_TrailHead.png
  artifactKey: Guid(9d68ffb47cd2aae44aefd63e21ca1ece) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_TrailHead.png using Guid(9d68ffb47cd2aae44aefd63e21ca1ece) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c732ab44c2a4ad8e1377a253f6eb40e7') in 0.0524131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab
  artifactKey: Guid(f340455adb5398544a6d24914132484f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab using Guid(f340455adb5398544a6d24914132484f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f09215f32b9ce622e63a0ca73ea8d026') in 0.0727822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterMask_4x6_01_FoamRender.psd
  artifactKey: Guid(61438dd794517be46a77f2bebefae99b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterMask_4x6_01_FoamRender.psd using Guid(61438dd794517be46a77f2bebefae99b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ca2b78358af2e704cc5aff69c96f6db') in 0.0691042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab
  artifactKey: Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab using Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7990b6071236ae9533db05ce4d2681f') in 0.0750962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Char/Char_TX_Dummy_01.png
  artifactKey: Guid(e2334fba0f0bf7a408c5c4c4779a6ec2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Char/Char_TX_Dummy_01.png using Guid(e2334fba0f0bf7a408c5c4c4779a6ec2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '798f08c0d38ede24fcb6ae40529f8f22') in 0.0554475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.404075 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat
  artifactKey: Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat using Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05536409e60200dba73cbd3cb9220857') in 0.1323835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_06.mat
  artifactKey: Guid(00bf45d2dea30c24eae40d923cbaa3cb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_06.mat using Guid(00bf45d2dea30c24eae40d923cbaa3cb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea3bf97e7597fb3348658b4a97d7bacb') in 1.3951528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.inputactions
  artifactKey: Guid(010bd85fdf203f34d94abf6f5ad3e876) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.inputactions using Guid(010bd85fdf203f34d94abf6f5ad3e876) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '337bdb23cee66525acf5a6e92fb651b0') in 0.699335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FallingWater_4x3_02.png
  artifactKey: Guid(8f3bc7d29092d3d449ab1d85befac174) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FallingWater_4x3_02.png using Guid(8f3bc7d29092d3d449ab1d85befac174) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d9d09d9e5a5948cec3eef56d12b2816') in 0.0546865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000111 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_2x5_03.png
  artifactKey: Guid(22802f5775f7fab42845c15e4dd38e1a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_2x5_03.png using Guid(22802f5775f7fab42845c15e4dd38e1a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33a9a560f89b4de6325e741ab892b53d') in 0.1141658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_12.png
  artifactKey: Guid(29b8c9978de52944dbaf63f0b5a62492) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_12.png using Guid(29b8c9978de52944dbaf63f0b5a62492) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '155e191ab63c2cd91d45bf13eb848be4') in 0.0672314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.001020 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_19.png
  artifactKey: Guid(8ab20ec07e3019d4e8b669b14fd23504) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_19.png using Guid(8ab20ec07e3019d4e8b669b14fd23504) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ba3f40af6e44d9114f15076bbc2f397') in 0.0485094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Back_Root.FBX
  artifactKey: Guid(1c27741fb8669474c968cad9ec9e54b9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Back_Root.FBX using Guid(1c27741fb8669474c968cad9ec9e54b9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0f8473ce6d3216f6639f597bca8b4ba') in 0.0637609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Forest_Loop_1.wav
  artifactKey: Guid(9a2329f1aecd1c24381d396f1bca49db) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Forest_Loop_1.wav using Guid(9a2329f1aecd1c24381d396f1bca49db) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b48a0aa03d887bef129b22ac9a06612') in 0.5091839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterWhip_2x6_01.png
  artifactKey: Guid(aa01cafc305fff44f9106731cef326f9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterWhip_2x6_01.png using Guid(aa01cafc305fff44f9106731cef326f9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb421c170e5a6941cb43eaf12e13b2ab') in 0.0502118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_2.wav
  artifactKey: Guid(f6d2806fe3b5d6e4f9088f4aaaa78d39) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_2.wav using Guid(f6d2806fe3b5d6e4f9088f4aaaa78d39) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '921d54686622fd2eed786082dea8d1ee') in 0.1337309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_1.wav
  artifactKey: Guid(439990700ad755342bffd07e728c08e4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_1.wav using Guid(439990700ad755342bffd07e728c08e4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5112a5edb019c4ef18d9b3bd84185ab4') in 0.1637427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_LevelStart_2.wav
  artifactKey: Guid(de4fe11540afa7249acece01f975f2ca) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_LevelStart_2.wav using Guid(de4fe11540afa7249acece01f975f2ca) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5465708cbf103dc29b2296994bb131d2') in 0.1535031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_translucency.png
  artifactKey: Guid(fd972f4b2880e9043ab3634915a80ba3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_translucency.png using Guid(fd972f4b2880e9043ab3634915a80ba3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f80f105b35f685b5d74116cc3aaf3427') in 0.0846286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_E.FBX
  artifactKey: Guid(bf12677a6be1e77428d098f7aba7ab86) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_E.FBX using Guid(bf12677a6be1e77428d098f7aba7ab86) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b2f5cc3563eb911f9b234cae64779da') in 0.0638569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_01.mat
  artifactKey: Guid(c26feaf4af5befe4e85db956ecb643c9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_01.mat using Guid(c26feaf4af5befe4e85db956ecb643c9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcef1a923c93eafe5b55e2389b6fa7db') in 0.1750859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_15.mat
  artifactKey: Guid(ae138474f077b9548ad8fc44874a9c42) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_15.mat using Guid(ae138474f077b9548ad8fc44874a9c42) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4407303f643659f41d8d2d72adda1252') in 0.1478747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_02.png
  artifactKey: Guid(dd7eaba08e0335c448e25d354d1f5e70) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_02.png using Guid(dd7eaba08e0335c448e25d354d1f5e70) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8614061eb4d50e361afea8b67e3b320e') in 0.0709972 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_04.png
  artifactKey: Guid(596ec342411c7604495ee5da1033b32f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_04.png using Guid(596ec342411c7604495ee5da1033b32f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f4461e2730320db710ce219c01d80db') in 0.097985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_A_Root.FBX
  artifactKey: Guid(13d433e832ff273488ce153cfab5c2fe) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_A_Root.FBX using Guid(13d433e832ff273488ce153cfab5c2fe) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b54c9db386ae229711673997d56cbd46') in 0.0600963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1B_Inplace.FBX
  artifactKey: Guid(5775c997176ae044e85646f07b2b84f5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1B_Inplace.FBX using Guid(5775c997176ae044e85646f07b2b84f5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b71f8e0b34f0e5fb6b5d5924aa6351d') in 0.0652943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_B_Root.FBX
  artifactKey: Guid(905ee45ad6815f44faeb13fbb5947a2f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_B_Root.FBX using Guid(905ee45ad6815f44faeb13fbb5947a2f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f1fbedadc5c5d0422467d5fefc55a65') in 0.0600005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Loop.FBX
  artifactKey: Guid(8640df76824b4924eae65f6e5ce0c73b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Loop.FBX using Guid(8640df76824b4924eae65f6e5ce0c73b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e96c04d7f92a1c3c3c8bd1cbc8de9073') in 0.0776994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_20.mat
  artifactKey: Guid(4d50ee8427e49864481433bed97fff42) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_20.mat using Guid(4d50ee8427e49864481433bed97fff42) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de40dfd8d6d7bdf3ebbeb98cd4f7a432') in 0.2142148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_04.mat
  artifactKey: Guid(82bb1895f98353145957fc7107345a56) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_04.mat using Guid(82bb1895f98353145957fc7107345a56) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af611b39898c6cdf9b3cd394310ff206') in 0.1704082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000453 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundWaterSplash_4x5_03.png
  artifactKey: Guid(aadda1c4fa8ac1e489cd38a1948b7875) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundWaterSplash_4x5_03.png using Guid(aadda1c4fa8ac1e489cd38a1948b7875) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88cdc1d1154b6dbd8ab7d5c7defef4e2') in 0.0802119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_2.wav
  artifactKey: Guid(14fa200aec6267047966b19f9a66e526) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_2.wav using Guid(14fa200aec6267047966b19f9a66e526) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '755179ca1d83396df7a059bb3ee7fa4d') in 0.117275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Start_ZeroHeight.FBX
  artifactKey: Guid(2917859be9ef8fa4c9edda8d1350d146) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Start_ZeroHeight.FBX using Guid(2917859be9ef8fa4c9edda8d1350d146) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13fa3f8cedc72af92a0aea4ac67c38cb') in 0.09924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000170 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_18.png
  artifactKey: Guid(0f6b4e5bb2a08d9449fabd2cfaee8a87) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_18.png using Guid(0f6b4e5bb2a08d9449fabd2cfaee8a87) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a7a9ba5c3a993c5645844ab3dfae894') in 0.0503369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_14.mat
  artifactKey: Guid(7127193cc981fae40bd2a2f42b6236cc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_14.mat using Guid(7127193cc981fae40bd2a2f42b6236cc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c767465b1afbb64392bc4be84e63142') in 0.1597976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_2_Inplace.FBX
  artifactKey: Guid(b80c913343c01f14096d7ce299c388df) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_2_Inplace.FBX using Guid(b80c913343c01f14096d7ce299c388df) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37c096486c3eda50240929aa313d5e30') in 0.0714593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR90_Root.FBX
  artifactKey: Guid(42dbb483c6713e749be9b9d3cb036be5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR90_Root.FBX using Guid(42dbb483c6713e749be9b9d3cb036be5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '401198080a6b258589f68701f709a39a') in 0.1286982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_to_A.FBX
  artifactKey: Guid(ca1f6f3ed0540484090b90047d94dbb8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_to_A.FBX using Guid(ca1f6f3ed0540484090b90047d94dbb8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a492a7feda8a926721b6cd596ceb0c7d') in 0.0658568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_ver_B.FBX
  artifactKey: Guid(04f0842c5d1dba749a6ed3e3091bf41b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_ver_B.FBX using Guid(04f0842c5d1dba749a6ed3e3091bf41b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9935869b879d362db84cf371c991ef2d') in 0.0649254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_A_Idle_Root.FBX
  artifactKey: Guid(2832f598b8f4d2e4989b0777e459dffa) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_A_Idle_Root.FBX using Guid(2832f598b8f4d2e4989b0777e459dffa) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3abe7175f792c94e079e4c4738d9fbf3') in 0.0656547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Flying_ver_B.FBX
  artifactKey: Guid(c9f8dfbef3b0dc54b9c74df6dec0009f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Flying_ver_B.FBX using Guid(c9f8dfbef3b0dc54b9c74df6dec0009f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb38a8265d8c7ef03baaf8f1313c4470') in 0.0793989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_B_To_Crouch_ver_A_Idle_Root.FBX
  artifactKey: Guid(ad8a7c7e8e909584ea7e2d30501f7344) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_B_To_Crouch_ver_A_Idle_Root.FBX using Guid(ad8a7c7e8e909584ea7e2d30501f7344) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed323f82c74997e0832020b52b109c91') in 0.0612814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Big_ver_A.FBX
  artifactKey: Guid(efbf930f2554cc241b8c1467c3aff1b3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Big_ver_A.FBX using Guid(efbf930f2554cc241b8c1467c3aff1b3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd598f2e4a49ddba5d7066817374cd22b') in 0.0681661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_ver_A.FBX
  artifactKey: Guid(9d939b4dc8d32184ab9543853bcfef03) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_ver_A.FBX using Guid(9d939b4dc8d32184ab9543853bcfef03) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c43a6680b5910ff254f0a36bc66a42c3') in 0.0726518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000164 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_B_To_Crouch_ver_B_Idle.FBX
  artifactKey: Guid(df68a207dcf7a234183a50ca57fac79f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_B_To_Crouch_ver_B_Idle.FBX using Guid(df68a207dcf7a234183a50ca57fac79f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09d78617b6183f64a11f07abe5d0c630') in 0.0642588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Big.FBX
  artifactKey: Guid(e060a76e0e192ff48befff60b939aa94) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Big.FBX using Guid(e060a76e0e192ff48befff60b939aa94) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '15a0487440d6ffffe957382310372ea9') in 0.063087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Smash.FBX
  artifactKey: Guid(b324a13fb9a6ce6428c25aa79da0658f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Smash.FBX using Guid(b324a13fb9a6ce6428c25aa79da0658f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31906a1f805b2ae867932a0ac8b66e4a') in 0.0822767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR45_Root.FBX
  artifactKey: Guid(1ade8a2371659d3428161c0703c6381f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FR45_Root.FBX using Guid(1ade8a2371659d3428161c0703c6381f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bfbc1b67f90312dd6bdaa3d050cb23d') in 0.0844656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL90.FBX
  artifactKey: Guid(19daee643230bc44395a1193c9fbf1bb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL90.FBX using Guid(19daee643230bc44395a1193c9fbf1bb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52ef41045bf5f44e52601e96803fc0da') in 0.0814447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_7_Inplace.FBX
  artifactKey: Guid(a49d110b3c2651a4194e2d5e58436d13) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_7_Inplace.FBX using Guid(a49d110b3c2651a4194e2d5e58436d13) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe73bdaf94ee0c2b770e168dcc6fc922') in 0.0661004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_B_Idle.FBX
  artifactKey: Guid(485841849273a6a4d91658d11635bfae) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_B_Idle.FBX using Guid(485841849273a6a4d91658d11635bfae) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc0a7e848bb01c9a1e19a6302ec998a7') in 0.0865185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL90_Root.FBX
  artifactKey: Guid(4d02d722f9544304692834944cc1ddc8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL90_Root.FBX using Guid(4d02d722f9544304692834944cc1ddc8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a4b9f2917cc3f5f1a48bf32f01c7498') in 0.0835688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Run_A.FBX
  artifactKey: Guid(c6bc6ec23ee90d5478f0b4eff911b0b1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Run_A.FBX using Guid(c6bc6ec23ee90d5478f0b4eff911b0b1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f443c480e9523792a847df8bf44f2e6') in 0.0681495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_Loop.FBX
  artifactKey: Guid(45437695e5c37c04bbe75c635b391d5a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_Loop.FBX using Guid(45437695e5c37c04bbe75c635b391d5a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ad502a427f35f9d921014e7bf2d1cc1') in 0.0909916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_ALL.FBX
  artifactKey: Guid(c6046de7c34f0de44baa79b4b1902985) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_ALL.FBX using Guid(c6046de7c34f0de44baa79b4b1902985) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24abc65a51d88661e95fb6fea3b3b329') in 0.0628648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End_ZeroHeight.FBX
  artifactKey: Guid(cd17a9873511f184b813330b43b38634) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End_ZeroHeight.FBX using Guid(cd17a9873511f184b813330b43b38634) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de8393e4f60b2aaf62bdaaa873e2da6e') in 0.0603626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90.FBX
  artifactKey: Guid(cec811c9b5409924e93c6c41d3604dee) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90.FBX using Guid(cec811c9b5409924e93c6c41d3604dee) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96b478e136f23587f4a907161a342c6e') in 0.0762955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Dodge_Back.FBX
  artifactKey: Guid(3e067541000ce9e47a877222db11c176) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Dodge_Back.FBX using Guid(3e067541000ce9e47a877222db11c176) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8be6883df15783fd7a38ee8b23c4600') in 0.0873606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_FW_Rolling_StandUp.FBX
  artifactKey: Guid(17d2157fed6c7a54fa746a4e8ad0dfcf) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_FW_Rolling_StandUp.FBX using Guid(17d2157fed6c7a54fa746a4e8ad0dfcf) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0359da87abfb8848c64fb476c63ad03c') in 0.0772542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2.FBX
  artifactKey: Guid(129f05c2fdd00fd4fb608dfbf4231c89) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2.FBX using Guid(129f05c2fdd00fd4fb608dfbf4231c89) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14757c4834a42dd4727b961808cf7615') in 0.0715872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL_ZeroHeight_Z0.FBX
  artifactKey: Guid(66e74ddf9adf4984f921d1e7b6c3d240) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL_ZeroHeight_Z0.FBX using Guid(66e74ddf9adf4984f921d1e7b6c3d240) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '767b723d9d79b7dfe22f0d35ec45db46') in 0.0619561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_5_Inplace.FBX
  artifactKey: Guid(cfcf1746a8ac3d246bf56c1eb543ba8b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_5_Inplace.FBX using Guid(cfcf1746a8ac3d246bf56c1eb543ba8b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6750f1794ceeb7fe1554b8d6a4a5b22c') in 0.2541559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_B.FBX
  artifactKey: Guid(957d887a081276a45ad946335bc27be2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_B.FBX using Guid(957d887a081276a45ad946335bc27be2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c31183f1809a78b4b04bb1610a9e786') in 0.0817625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_5.FBX
  artifactKey: Guid(c602f00c43168bf4eab643c3f02df739) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_5.FBX using Guid(c602f00c43168bf4eab643c3f02df739) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '178ec58ec88c283783f86a5d510cd996') in 0.0644129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_4.FBX
  artifactKey: Guid(58e6b7c008efa8446b9a6fb126226358) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_4.FBX using Guid(58e6b7c008efa8446b9a6fb126226358) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb9e509e902872de13b57ee8d92e9015') in 0.1018801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BedSheet.mat
  artifactKey: Guid(abc00000000009288061145056192450) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BedSheet.mat using Guid(abc00000000009288061145056192450) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c99f97eabc23745dda775cd4cb47503f') in 0.078909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_1.fbx
  artifactKey: Guid(abc00000000010471410081192285801) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_1.fbx using Guid(abc00000000010471410081192285801) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d805713f8d49c6573cb80b4b44af1b8') in 0.0788743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/StandUp_Revenge/M_katana_Blade@StandUp_Revenge.FBX
  artifactKey: Guid(f5eadc5c35388b444a184a9c744cdba9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/StandUp_Revenge/M_katana_Blade@StandUp_Revenge.FBX using Guid(f5eadc5c35388b444a184a9c744cdba9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f565a57c362cb652843172844de981b9') in 0.1573028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_GrainSack.mat
  artifactKey: Guid(abc00000000008700112698404816241) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_GrainSack.mat using Guid(abc00000000008700112698404816241) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f21f2f89b007c92d63793dd9fe9779c5') in 0.0686146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.001984 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat
  artifactKey: Guid(abc00000000011170058242184240894) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat using Guid(abc00000000011170058242184240894) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a9619e4f586849c40cf834bf5094971') in 0.2136895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_68.fbx
  artifactKey: Guid(abc00000000003336756689311021584) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_68.fbx using Guid(abc00000000003336756689311021584) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1895ce4f0faca6a2bec4b08d80208a2') in 0.1751488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_6.fbx
  artifactKey: Guid(abc00000000005135748256228962804) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_6.fbx using Guid(abc00000000005135748256228962804) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2996fc95f11bbc3446c814be82a582f6') in 0.0977863 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die_Loop.FBX
  artifactKey: Guid(7a9bf00a71e289f438d10eb366614822) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die_Loop.FBX using Guid(7a9bf00a71e289f438d10eb366614822) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8cef2f677aa1ce7dc82ebcb368fac31') in 0.0883943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fish_Splash_02.mat
  artifactKey: Guid(abc00000000003432095235311046902) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fish_Splash_02.mat using Guid(abc00000000003432095235311046902) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cca506317188cc09d17f2ca29760cece') in 0.1326987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.FBX
  artifactKey: Guid(9a47a100b9b698a49934ba487c7e8bdd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.FBX using Guid(9a47a100b9b698a49934ba487c7e8bdd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7dd65f057ebbfdfebc40af4b56fb206b') in 1.2295535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_1.prefab
  artifactKey: Guid(abc00000000004952311748196858445) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_1.prefab using Guid(abc00000000004952311748196858445) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c0f1f3af2580fea8c669ac264bfe08e') in 0.0707124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_11.fbx
  artifactKey: Guid(abc00000000013413156961462772104) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_11.fbx using Guid(abc00000000013413156961462772104) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a2576babf585588977d573bba9df540') in 0.0967177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_06.prefab
  artifactKey: Guid(ee17262267755af4093e1353f90edc28) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_06.prefab using Guid(ee17262267755af4093e1353f90edc28) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b60b974e8f565f92ed6826f0af698d01') in 0.5575923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_01.prefab
  artifactKey: Guid(abc00000000010743531460957361430) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_01.prefab using Guid(abc00000000010743531460957361430) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b230eedb4bd2b013b5c345244937ad8f') in 0.057149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_04.prefab
  artifactKey: Guid(abc00000000014978965448417498893) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_04.prefab using Guid(abc00000000014978965448417498893) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea1093034db29f6414ad0b029dc84962') in 0.0681017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_01.prefab
  artifactKey: Guid(abc00000000010351259951333084775) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_01.prefab using Guid(abc00000000010351259951333084775) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3bc6b9eee3d556b281ca1a3ae6e4c464') in 0.0662491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B_1.prefab
  artifactKey: Guid(abc00000000003812536690341633685) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B_1.prefab using Guid(abc00000000003812536690341633685) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '15b8dc9c2311f59a9f52ad62cc98ef2c') in 0.0974524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_07.prefab
  artifactKey: Guid(abc00000000015176355037887582961) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_07.prefab using Guid(abc00000000015176355037887582961) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1797bd7046044dd0fe91a8136d1229e3') in 0.0958964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_04.prefab
  artifactKey: Guid(abc00000000003605172642646372016) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_04.prefab using Guid(abc00000000003605172642646372016) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff65acbc8cb352100a5097f04405b624') in 0.0866818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Spear.FBX
  artifactKey: Guid(bd1a5eb53babb01458774def9a5713cd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Spear.FBX using Guid(bd1a5eb53babb01458774def9a5713cd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22884ba95177b623c4e5c998e65f99c7') in 0.0653991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_03.mat
  artifactKey: Guid(abc00000000002917747318130589308) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_03.mat using Guid(abc00000000002917747318130589308) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b565bcde15359cb7d66a9ccccc50aac') in 0.0543727 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Axe_01.prefab
  artifactKey: Guid(abc00000000000346792517797210715) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Axe_01.prefab using Guid(abc00000000000346792517797210715) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '192083cb6405e9fe94ffadc1d042520a') in 0.0704271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_31.FBX
  artifactKey: Guid(d5829054c9203054b921a971700b642d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_31.FBX using Guid(d5829054c9203054b921a971700b642d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '999bb18debcbc563fd3f7aa1473b9fe6') in 0.586218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_corbels.prefab
  artifactKey: Guid(abc00000000001716561692332122495) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_corbels.prefab using Guid(abc00000000001716561692332122495) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1ad7cd23d1a4d898d1bd486946281e8') in 0.0598805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a.prefab
  artifactKey: Guid(abc00000000016747308749705262770) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a.prefab using Guid(abc00000000016747308749705262770) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebdc84a35aa3cfaa65bce58ee905bf8d') in 0.0626407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000100 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_04.prefab
  artifactKey: Guid(abc00000000016896129781801089531) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_04.prefab using Guid(abc00000000016896129781801089531) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7ac70464ee2ff085baa54ed31357d94') in 0.0665218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_A.prefab
  artifactKey: Guid(abc00000000005784984238246329485) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_A.prefab using Guid(abc00000000005784984238246329485) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48c2832ffa939b16eca956253f690765') in 0.0574205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_11.prefab
  artifactKey: Guid(abc00000000006801270852270562234) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_11.prefab using Guid(abc00000000006801270852270562234) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '849760d9c5ac07da951749c8757b8025') in 0.0676528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron_Lid.prefab
  artifactKey: Guid(abc00000000003516320758811333365) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron_Lid.prefab using Guid(abc00000000003516320758811333365) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75ede1f0bbb76c7e05ffa008f6ffc0de') in 0.0622516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_0.prefab
  artifactKey: Guid(abc00000000007783163857777427256) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_0.prefab using Guid(abc00000000007783163857777427256) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32710a9d0f12d2e8168c3c37b7520d29') in 0.0587147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_C.prefab
  artifactKey: Guid(abc00000000016523175045301749308) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_C.prefab using Guid(abc00000000016523175045301749308) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '797533b657f80b855132cfec2f12df1d') in 0.0541088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_03.prefab
  artifactKey: Guid(abc00000000008938765446196406868) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_03.prefab using Guid(abc00000000008938765446196406868) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e98a020fbf53549ced273d65c4be629') in 0.0606974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_A.FBX
  artifactKey: Guid(c84d966f99d87e342a391cb893e8e1eb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_A.FBX using Guid(c84d966f99d87e342a391cb893e8e1eb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2643c637280345fc0b1a641c1204207') in 0.1152886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_D.FBX
  artifactKey: Guid(31c83dae193f1f843a1048f804feb60a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_D.FBX using Guid(31c83dae193f1f843a1048f804feb60a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0350b14e680fcf2361cfa02e478c222d') in 0.0612363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_03.prefab
  artifactKey: Guid(abc00000000010392827481872026946) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_03.prefab using Guid(abc00000000010392827481872026946) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b508d981de4cba198e134d00b51fd132') in 0.0809095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08.prefab
  artifactKey: Guid(abc00000000007833889246815510673) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08.prefab using Guid(abc00000000007833889246815510673) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4f68051a69ee469d700f1330ad9af33') in 0.0747026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_01.prefab
  artifactKey: Guid(abc00000000001624541851004027658) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_01.prefab using Guid(abc00000000001624541851004027658) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78c8bb04cf20ed93bdd8ce903e7af026') in 0.0728538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_A.prefab
  artifactKey: Guid(abc00000000014770711582536660373) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_A.prefab using Guid(abc00000000014770711582536660373) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c87bbee6253fbb686d4d8da35e86a84') in 0.0594646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_mountain.prefab
  artifactKey: Guid(abc00000000008986967732331309788) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_mountain.prefab using Guid(abc00000000008986967732331309788) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00baf830e36a479887fd6818260ae7b2') in 0.0555015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_03_1.prefab
  artifactKey: Guid(abc00000000017012127876838236024) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_03_1.prefab using Guid(abc00000000017012127876838236024) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8448478ae8679cc8f6a31c7466c54b06') in 0.0547211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Market_Stand_01.prefab
  artifactKey: Guid(abc00000000001172960152337823974) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Market_Stand_01.prefab using Guid(abc00000000001172960152337823974) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd34d9ee5dc4d673b2226a04f8a8db943') in 0.0622513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Shield_01.prefab
  artifactKey: Guid(abc00000000015881715998179242804) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Shield_01.prefab using Guid(abc00000000015881715998179242804) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e8e923a2ef66ba8ab4d5e462d2e2cb9') in 0.064534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug.prefab
  artifactKey: Guid(abc00000000007822056554438591189) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug.prefab using Guid(abc00000000007822056554438591189) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd670bd998f2787afa216b0db10980495') in 0.0607115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_C.prefab
  artifactKey: Guid(abc00000000006537966796779105927) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_C.prefab using Guid(abc00000000006537966796779105927) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8cd3ac5c2c16ba9cdfc56fd4c03a3551') in 0.063869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A.prefab
  artifactKey: Guid(abc00000000006362557841762442139) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A.prefab using Guid(abc00000000006362557841762442139) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd87cb17ce290c7c426a0df872378b7c2') in 0.0553704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase.prefab
  artifactKey: Guid(abc00000000018210881637664435060) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase.prefab using Guid(abc00000000018210881637664435060) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe4a1c3a4b4d01c60c9d5d46dc834958') in 0.0587868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x4_01.prefab
  artifactKey: Guid(abc00000000014155675160327830357) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x4_01.prefab using Guid(abc00000000014155675160327830357) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b67c9b747ff681a48fe5e2db1746b2a') in 0.0590866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_DepthFade.shadersubgraph
  artifactKey: Guid(dddf72fe66d7e224bb1d78bedc843a0b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_DepthFade.shadersubgraph using Guid(dddf72fe66d7e224bb1d78bedc843a0b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51d7edbe3d7817afd541d2cab345fef4') in 0.0711549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_A_01.prefab
  artifactKey: Guid(abc00000000012814655491640248950) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_A_01.prefab using Guid(abc00000000012814655491640248950) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'debeaa1a51fa13b32f534b933f14a4df') in 0.078948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_MasterBlend.shadergraph
  artifactKey: Guid(267cda9bc86d86942b23e97359d7eea3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_MasterBlend.shadergraph using Guid(267cda9bc86d86942b23e97359d7eea3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6510e68d5f6bd2162e3342cca6e16932') in 0.0435356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x5.prefab
  artifactKey: Guid(abc00000000009156416213942264455) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x5.prefab using Guid(abc00000000009156416213942264455) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd05cd5dd47ce18020433f13ba9881b83') in 0.0631695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool.prefab
  artifactKey: Guid(abc00000000000461347809624088054) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool.prefab using Guid(abc00000000000461347809624088054) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd31fa1b6052a80fc349d2876e5ba9cf8') in 0.0595526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Edging.prefab
  artifactKey: Guid(abc00000000004095657826792136418) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Edging.prefab using Guid(abc00000000004095657826792136418) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ed50aa34be93df6ea8e2ea432986cf4') in 0.0636613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_03.prefab
  artifactKey: Guid(abc00000000003228867782050657137) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_03.prefab using Guid(abc00000000003228867782050657137) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '28ac74129244c8e629134bc93529f5d8') in 0.1001624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_16x5.prefab
  artifactKey: Guid(abc00000000016557253141712033432) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_16x5.prefab using Guid(abc00000000016557253141712033432) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1a8aeb4a56f62ba151a179f9d128f29') in 0.0718563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(c07e389789c89574caaea1b5213b1e76) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_OcclusionRoughnessMetallic.PNG using Guid(c07e389789c89574caaea1b5213b1e76) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39d96ceff7d3abcd839dfbe6efa41569') in 0.0638363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_01.prefab
  artifactKey: Guid(abc00000000010816312911577100658) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_01.prefab using Guid(abc00000000010816312911577100658) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '155c15f17aacbd9e7b907d331a2fe9ac') in 0.0574277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GroundRock_basecolor.PNG
  artifactKey: Guid(26bebd949dafcbb498fa72bcd7944d3e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GroundRock_basecolor.PNG using Guid(26bebd949dafcbb498fa72bcd7944d3e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffac8e2933e476ed166fb585707c540e') in 0.0466683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_M.PNG
  artifactKey: Guid(f770c42b0c74b1343ba34e604a8de7ab) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_M.PNG using Guid(f770c42b0c74b1343ba34e604a8de7ab) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a64958b08af1537bbe5a021ffb76df53') in 0.063238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_BaseColor.PNG
  artifactKey: Guid(e9423498b512eea44b999edc32dbca0e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_BaseColor.PNG using Guid(e9423498b512eea44b999edc32dbca0e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83fb971b8cc198ab22f35c954417d026') in 0.0455513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_ORMH.PNG
  artifactKey: Guid(cc4f8586fcbc8b84b88c924779dfe7b0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_ORMH.PNG using Guid(cc4f8586fcbc8b84b88c924779dfe7b0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05f4eb6c1e76747da235c058481c846f') in 0.0506584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Gravel.terrainlayer
  artifactKey: Guid(f9d095550a808af46ac020ddb5c17d64) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Gravel.terrainlayer using Guid(f9d095550a808af46ac020ddb5c17d64) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2f5352a347599a58ac69e38a6848974') in 0.0474063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_basecolor.PNG
  artifactKey: Guid(0b8bd9a8e8ba2d246b1fa6b44857b59c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_basecolor.PNG using Guid(0b8bd9a8e8ba2d246b1fa6b44857b59c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cdc9fbd26830b7632059f32e2b622e69') in 0.0504589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Grunge_06.PNG
  artifactKey: Guid(a6f0fb45aea82b44ca69ca802c9fefe6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Grunge_06.PNG using Guid(a6f0fb45aea82b44ca69ca802c9fefe6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71f8327ffb9dd94ebe58064f08892db6') in 0.0744383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_M.PNG
  artifactKey: Guid(92a8784d124a1e64f9cf6a925e614bd0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_M.PNG using Guid(92a8784d124a1e64f9cf6a925e614bd0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b678a023bc657779b00f5e9b18ec3282') in 0.0627596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_M.PNG
  artifactKey: Guid(921c0fc7912e00a46bf42c871c538dbd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_M.PNG using Guid(921c0fc7912e00a46bf42c871c538dbd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc15c156c99cd11a4d5d203a0415fca4') in 0.0640716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_ORMH.PNG
  artifactKey: Guid(3e1be37931dcd7340a10e805eb40cc2f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_ORMH.PNG using Guid(3e1be37931dcd7340a10e805eb40cc2f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb5af1868b3c5664180370d69239cf0d') in 0.0721788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_Normal.PNG
  artifactKey: Guid(26241c0b4eb2e054687e7f0293c8bfc5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cobble_Normal.PNG using Guid(26241c0b4eb2e054687e7f0293c8bfc5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3e034a206f2be3f54b673c30d9d4b63') in 0.0450624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_basecolor.PNG
  artifactKey: Guid(e408464604a92ff46972382d077bfe0d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_basecolor.PNG using Guid(e408464604a92ff46972382d077bfe0d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '247bc7a759ea9a0b6bac3300905c3e74') in 0.0695378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_Snow_M.PNG
  artifactKey: Guid(6587072f6b6d8a84c9a8cdebe2863d6c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_Snow_M.PNG using Guid(6587072f6b6d8a84c9a8cdebe2863d6c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e772b5414354d10ba197a966d02ae92') in 0.0886694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_TilingNoise02_M.PNG
  artifactKey: Guid(ff89dc12bf9a4da409e08dfecd69cbe3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_TilingNoise02_M.PNG using Guid(ff89dc12bf9a4da409e08dfecd69cbe3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8d1e5514f100a7e184834ec7dc63d6c') in 0.1273285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_01_basecolor.PNG
  artifactKey: Guid(c863c5425ca3d7645a3eb0a0762ef1c0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_01_basecolor.PNG using Guid(c863c5425ca3d7645a3eb0a0762ef1c0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e6a1d2d52dafba4c3cc6b4f14122e55') in 0.0840071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_normal.PNG
  artifactKey: Guid(3f73c79ea7b2324498d8bafb1c09bae8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_normal.PNG using Guid(3f73c79ea7b2324498d8bafb1c09bae8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a313e20133a3b3530a8384fa52852d44') in 0.0465063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_N.PNG
  artifactKey: Guid(d0f715e8b0e7aa84f988b0eb44e78bb6) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_N.PNG using Guid(d0f715e8b0e7aa84f988b0eb44e78bb6) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc190062e1556e448518d23165c0b6f6') in 0.0447354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_normal.PNG
  artifactKey: Guid(0fc3b724ac6d00b4c9d0502fd2a3f2dc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_normal.PNG using Guid(0fc3b724ac6d00b4c9d0502fd2a3f2dc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe7958f1ffbf97d3b15ff8cef5ef442b') in 0.0451383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG_1.PNG
  artifactKey: Guid(1cf1d69c017eae5439fc3bddd231befc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG_1.PNG using Guid(1cf1d69c017eae5439fc3bddd231befc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90e9d0e1bcaf362ad317aa44773aa50e') in 0.0524038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_normal - Copy.PNG
  artifactKey: Guid(f7bb1b8e81d31e8499a1cd23214241db) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_normal - Copy.PNG using Guid(f7bb1b8e81d31e8499a1cd23214241db) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86aa986c0741d9e26c3d5b4c2199a898') in 0.05906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL>
  artifactKey: Guid(56fd86b76fc74d24d83522069f5deb9b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL> using Guid(56fd86b76fc74d24d83522069f5deb9b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2172af06c8b8bd57f4d8a6c626c16a0b') in 0.0672857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_Normal.PNG
  artifactKey: Guid(6e5f80257a982be4390c55605635335c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_Normal.PNG using Guid(6e5f80257a982be4390c55605635335c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4570ad1ffaaca4babfca11dc36918e57') in 0.0470251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_BackwardLeft.controller
  artifactKey: Guid(a71b99d518ee970428e8df2c08f61863) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_BackwardLeft.controller using Guid(a71b99d518ee970428e8df2c08f61863) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '296bac294ccb4f06d62c4c806b71216b') in 0.0394468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_D.PNG
  artifactKey: Guid(24f5735da36931143a0ab9135dca143b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_D.PNG using Guid(24f5735da36931143a0ab9135dca143b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '29e99e9b6969c609cf6a41b24418c52e') in 0.067547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown/ReflectionProbe-0.exr
  artifactKey: Guid(f67cf6813d1666842920ea9d91e1c865) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown/ReflectionProbe-0.exr using Guid(f67cf6813d1666842920ea9d91e1c865) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bc61984a5e3b5d416ad92545046c624') in 0.0391968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hands Mask.mask
  artifactKey: Guid(69b447faf5895f1428a1131028d8893c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hands Mask.mask using Guid(69b447faf5895f1428a1131028d8893c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4475496ae769c8f6809321e21cfb31d2') in 0.0486566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Turn01_Left [RM].controller
  artifactKey: Guid(f336e8e5a2d27c34f943defd29bbaee3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Turn01_Left [RM].controller using Guid(f336e8e5a2d27c34f943defd29bbaee3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2db57e213f0f571c2b0d587afae61fa8') in 0.0405976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(f6a7e0442553cad4ca39c8df29411dc7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(f6a7e0442553cad4ca39c8df29411dc7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f1497553a531a06aa985ce3a1337b3f') in 0.0359171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_ForwardRight.controller
  artifactKey: Guid(a805db0a341a54241bb00c040762aa1b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_ForwardRight.controller using Guid(a805db0a341a54241bb00c040762aa1b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af343eab368d917413daccd6b67ea0fe') in 0.0335622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_ORMH.PNG
  artifactKey: Guid(655c29dfbc763c74eb3f36800e0a8fa8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_ORMH.PNG using Guid(655c29dfbc763c74eb3f36800e0a8fa8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '402b230754328e05ffc08afdb0773261') in 0.0476729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_01_normal.PNG
  artifactKey: Guid(95448a5bce1fe594b93aa4a67188e2be) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_01_normal.PNG using Guid(95448a5bce1fe594b93aa4a67188e2be) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2cb86c40986f21c153bbfb2894c153aa') in 0.0796105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Idle_Turn_R90.FBX
  artifactKey: Guid(cd4709f76a1ade04ab4b44ad9a091efc) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Idle_Turn_R90.FBX using Guid(cd4709f76a1ade04ab4b44ad9a091efc) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67fb5e717e1d2481b27c119c878d3a2d') in 0.0643637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Run_A.FBX
  artifactKey: Guid(b77b7cd6ac54c4c4b93abb9a63f3102c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Run_A.FBX using Guid(b77b7cd6ac54c4c4b93abb9a63f3102c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd204cf3320ab16c5dac40298e23d74c5') in 0.0685673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_to_Idle_ver_A.FBX
  artifactKey: Guid(0d10cf4030bd98b41a71692b5c0268a8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_to_Idle_ver_A.FBX using Guid(0d10cf4030bd98b41a71692b5c0268a8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86454fbb0584d17e39ac9301866ce718') in 0.060786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_2.wav
  artifactKey: Guid(bd2a15c71a8e8e84da13f22f676bf558) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_2.wav using Guid(bd2a15c71a8e8e84da13f22f676bf558) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '866ed4a98e523b5afcdb45c63e8950ba') in 0.1698088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_A_to_Run_B.FBX
  artifactKey: Guid(f659b3f7804a1f84b8e256cfe14b3224) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_A_to_Run_B.FBX using Guid(f659b3f7804a1f84b8e256cfe14b3224) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd36cdd3863320218023fa98f49a0ebf5') in 0.060931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_B.FBX
  artifactKey: Guid(a9783a68e6d7e614b8ced6ef3e5b7531) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_B.FBX using Guid(a9783a68e6d7e614b8ced6ef3e5b7531) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d07a701dcd17adebcc7c5e8a2ffa69a') in 0.1014025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_B_to_Run_A.FBX
  artifactKey: Guid(52ad58cc1cb0d6546928e8acd9c1deae) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Run_B_to_Run_A.FBX using Guid(52ad58cc1cb0d6546928e8acd9c1deae) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71c97e436f8db3f196760874ff953c35') in 0.0617536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_A.FBX
  artifactKey: Guid(54e00315430693e4abfa6f0f148d3684) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_A.FBX using Guid(54e00315430693e4abfa6f0f148d3684) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8855fb5fd721e54c7874af96ffccf10d') in 0.0611737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_To_Idle_Root.FBX
  artifactKey: Guid(d56190a20fa15a443b71433f20be46b2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_To_Idle_Root.FBX using Guid(d56190a20fa15a443b71433f20be46b2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '165f4552437fde8fc9ecac8bf85c5965') in 0.072505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L90.FBX
  artifactKey: Guid(8ef4d133d48f45641b509e8cc1127edb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L90.FBX using Guid(8ef4d133d48f45641b509e8cc1127edb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c2ef2358368a39c964dc63a20512807') in 0.0712918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R45.FBX
  artifactKey: Guid(db1fcf91683d7b54cb0c32df48cb4d83) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R45.FBX using Guid(db1fcf91683d7b54cb0c32df48cb4d83) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e708e35947bc849ce4aeceddfb74cab7') in 0.0610561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_R90.FBX
  artifactKey: Guid(794f842f5ed19d347a06d5488f6c3ac9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_R90.FBX using Guid(794f842f5ed19d347a06d5488f6c3ac9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8575eaa8201cb12a39be6c4815761250') in 0.0667604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back.FBX
  artifactKey: Guid(f300936bdeb2eec488c994fda0ea38b3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back.FBX using Guid(f300936bdeb2eec488c994fda0ea38b3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d3947c6ef2fafef55b81d8cc4b91d3c') in 0.0558673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_Bwd_Root.FBX
  artifactKey: Guid(43374fe9161749f43a7505f47ee88b92) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_Bwd_Root.FBX using Guid(43374fe9161749f43a7505f47ee88b92) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72dab4627c620c23c16854fec078010f') in 0.076116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/VFXManager.cs
  artifactKey: Guid(2651a8a4878636f4fa6a3ef21af74150) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/VFXManager.cs using Guid(2651a8a4878636f4fa6a3ef21af74150) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e263c9c90f560d143279874923fef21d') in 0.0252319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R90.FBX
  artifactKey: Guid(b4df8b53eab5f064a8c1c58d835c66c7) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R90.FBX using Guid(b4df8b53eab5f064a8c1c58d835c66c7) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb9e406c33676bda38c66eea779bd311') in 0.0542973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_B_Root.FBX
  artifactKey: Guid(ceb55f96a3c62484e9d49c16fd68420c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_B_Root.FBX using Guid(ceb55f96a3c62484e9d49c16fd68420c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f9465a9813901a2d644d197e65f8732') in 0.0601038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Walk_A_Turn_L90.FBX
  artifactKey: Guid(4c210f0e3421f7b4ab50885287977a6c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Walk_A_Turn_L90.FBX using Guid(4c210f0e3421f7b4ab50885287977a6c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10e7616704e6bf7788937d63d1bc11d2') in 0.0789071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BR45.FBX
  artifactKey: Guid(de9a50f1eabdbc94f8d07fe586b917f0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BR45.FBX using Guid(de9a50f1eabdbc94f8d07fe586b917f0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bde563b271c392888354ca25da876bdd') in 0.068052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_B.FBX
  artifactKey: Guid(77a1ac60661efeb4596840708882aa2a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_B.FBX using Guid(77a1ac60661efeb4596840708882aa2a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acf7ce3eae93d857c1733576536c9dbe') in 0.0558282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_L90_Root.FBX
  artifactKey: Guid(7cb8fcffb0e30b6408b55ad3d1baf4c2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_L90_Root.FBX using Guid(7cb8fcffb0e30b6408b55ad3d1baf4c2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ef6d4373d837bd2fe55e7f5113f948c') in 0.0662042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_R90.FBX
  artifactKey: Guid(448c759820aa13847bf9309603470161) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_R90.FBX using Guid(448c759820aa13847bf9309603470161) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6d74b5657d0cff57266fb246d71290b') in 0.0792081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_L90.FBX
  artifactKey: Guid(9777a6fee49cf88428aaa6de31aa54bd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Idle_ver_B_Turn_L90.FBX using Guid(9777a6fee49cf88428aaa6de31aa54bd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5de16b5a66724746a771aab9f6a37c9d') in 0.0697831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_A_Root.FBX
  artifactKey: Guid(56dbfcf2133f58643b5fb3aa649957f8) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_A_Root.FBX using Guid(56dbfcf2133f58643b5fb3aa649957f8) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7b8990275e95e52a36a00c2a1827116') in 0.064052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Magical_1.wav
  artifactKey: Guid(0840018ad9d617b4cbd80e7fe52038c2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Magical_1.wav using Guid(0840018ad9d617b4cbd80e7fe52038c2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43c323cfe38f4108178c0299c0ba897e') in 0.1630251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_R90.FBX
  artifactKey: Guid(b7f55d31c8373cf438c45e21a82fde3b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_R90.FBX using Guid(b7f55d31c8373cf438c45e21a82fde3b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc7140ecf6ab4716dc148389350c21fb') in 0.0709098 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Walk_A_Turn_L90.FBX
  artifactKey: Guid(e7ccdc40ae678b547b324811d3b6b2d9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Walk_A_Turn_L90.FBX using Guid(e7ccdc40ae678b547b324811d3b6b2d9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05e9a34b592ae953b1100871a1375e26') in 0.0708984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_Turn_L90_Root.FBX
  artifactKey: Guid(6de8bb36c816cf641b938220a1fd2fa0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_Turn_L90_Root.FBX using Guid(6de8bb36c816cf641b938220a1fd2fa0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3895ac6c86dfeee86f2b756741d72615') in 0.0823068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Run_B_Turn_L90_Root.FBX
  artifactKey: Guid(c5cc64f4e3db490489693d10b9c023cb) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Run_B_Turn_L90_Root.FBX using Guid(c5cc64f4e3db490489693d10b9c023cb) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87a98763b52ec03da1fc58c537f3893e') in 0.0636585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_A.FBX
  artifactKey: Guid(188975e7d0a66974b91647c8ae6b615f) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_A.FBX using Guid(188975e7d0a66974b91647c8ae6b615f) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3fa493f4b69ed8399f1b0912eb08310d') in 0.083666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_A_Root.FBX
  artifactKey: Guid(c9b60920d25e8ac4fb4f4147dcaa57a9) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_A_Root.FBX using Guid(c9b60920d25e8ac4fb4f4147dcaa57a9) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b35610b55831e639eea75cd6e956f705') in 0.1030886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_BL45_Root.FBX
  artifactKey: Guid(5af1d335d7928a44fa03b67c236511e5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_BL45_Root.FBX using Guid(5af1d335d7928a44fa03b67c236511e5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a784ba7553ad599269338f8cb11f62f') in 0.134075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_Turn.FBX
  artifactKey: Guid(f4e7d3f9e990d2344934f8229e6f1ddd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_Turn.FBX using Guid(f4e7d3f9e990d2344934f8229e6f1ddd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f47321691348f9e0f9553a473980bbb5') in 0.0666568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Fwd.FBX
  artifactKey: Guid(a944b1434038b1b4f85f0e4832561dfd) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Fwd.FBX using Guid(a944b1434038b1b4f85f0e4832561dfd) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '305e1931e8e79ad97aa1b2ba4868781e') in 0.0779768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_L90_Root.FBX
  artifactKey: Guid(19a49b186cff9f94eaebd682b6150bbf) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_L90_Root.FBX using Guid(19a49b186cff9f94eaebd682b6150bbf) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f6965030fb9fb265933527126822584') in 0.1079022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_L90_Root.FBX
  artifactKey: Guid(0133e6689b15748488f22f5977c92c57) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_L90_Root.FBX using Guid(0133e6689b15748488f22f5977c92c57) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df8eae7a7600488ff959a2da3ea57075') in 0.0623617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_BR45_Root.FBX
  artifactKey: Guid(cd9bd6aaab76233428448ee1e5a4becf) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_BR45_Root.FBX using Guid(cd9bd6aaab76233428448ee1e5a4becf) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b860b40866568dda52d85c021731cb0c') in 0.0669396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Walk_A_Turn_L90_Root.FBX
  artifactKey: Guid(caac1456c1df7cd4880db15980a5f89a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Walk_A_Turn_L90_Root.FBX using Guid(caac1456c1df7cd4880db15980a5f89a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc5af9a626a62f60e6f2dc08ab04a123') in 0.0601779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_R90_Root.FBX
  artifactKey: Guid(ef31167975039f94c868c3da532e2262) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_R90_Root.FBX using Guid(ef31167975039f94c868c3da532e2262) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48e5223470fba450c56e5f176ba6aeb2') in 0.071363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_F.FBX
  artifactKey: Guid(deb2b07fd2cefcd4e83fe076c6dbf72d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_F.FBX using Guid(deb2b07fd2cefcd4e83fe076c6dbf72d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff9d8e973ed3812cc58fc5e61c74b9ea') in 0.0641761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BR45_Root.FBX
  artifactKey: Guid(f0e9c517d3819644f925e283cf48ff30) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BR45_Root.FBX using Guid(f0e9c517d3819644f925e283cf48ff30) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21f67fafb110756f75a28f95d0cfa5c3') in 0.0638212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FR45_Root.FBX
  artifactKey: Guid(8a98c4a557f40ad47adb4dfe1302d3a1) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FR45_Root.FBX using Guid(8a98c4a557f40ad47adb4dfe1302d3a1) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7143808fc9d1b413f1ee8d763a93900') in 0.0742768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_F_Root.FBX
  artifactKey: Guid(441d559bb88277b4aac39e463eff9177) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_F_Root.FBX using Guid(441d559bb88277b4aac39e463eff9177) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17afc22065327753cbce69ace450faff') in 0.0724504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front.FBX
  artifactKey: Guid(9b47bd9e3b792054d9f1d8320fe0403b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front.FBX using Guid(9b47bd9e3b792054d9f1d8320fe0403b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83fdeb116a53b7ba0e93505c4457d479') in 0.0570956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000318 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L45_Root.FBX
  artifactKey: Guid(a0868176f04744645ad27574c088e444) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L45_Root.FBX using Guid(a0868176f04744645ad27574c088e444) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7c398ef0fba19a25c59c43a7ff24fe6') in 0.0636409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000124 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_05.mat
  artifactKey: Guid(abc00000000001298615087021295057) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_05.mat using Guid(abc00000000001298615087021295057) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b0c33f9f7d25b52f71144ec656e19bf') in 0.0552256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_A.fbx
  artifactKey: Guid(abc00000000008918113793324368618) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_A.fbx using Guid(abc00000000008918113793324368618) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f6e210eee71e59422384b7fe53e27539') in 0.0650003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_02.fbx
  artifactKey: Guid(abc00000000004981161394019121506) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_02.fbx using Guid(abc00000000004981161394019121506) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9229917190607f0e9621e37cd842ba9') in 0.0584987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleSlim_01.fbx
  artifactKey: Guid(abc00000000000319748867061539903) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleSlim_01.fbx using Guid(abc00000000000319748867061539903) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b3eae06ad9a77fd84d7dbdbcdab8819') in 0.072057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_Metal.mat
  artifactKey: Guid(096e188cf21626d49b97e8b0fdc1a13a) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_Metal.mat using Guid(096e188cf21626d49b97e8b0fdc1a13a) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9501967387a5d303305519c0ea3a267b') in 0.0554129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x4_02.fbx
  artifactKey: Guid(abc00000000005750963864082493602) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x4_02.fbx using Guid(abc00000000005750963864082493602) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '242eefb80b007bb13daad9a8485451ff') in 0.0752082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Bucket_01.fbx
  artifactKey: Guid(abc00000000002738830346925640710) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Bucket_01.fbx using Guid(abc00000000002738830346925640710) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21b3c95ad3ba68566d16e840a880145e') in 0.0670367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_IronChandelier_Base.fbx
  artifactKey: Guid(abc00000000007050948984930125777) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_IronChandelier_Base.fbx using Guid(abc00000000007050948984930125777) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '911e80e7c616b3bac5495c6fee75cec1') in 0.0655097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Pot.fbx
  artifactKey: Guid(abc00000000005646087455747438425) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Pot.fbx using Guid(abc00000000005646087455747438425) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3370d887deaf428b18e0067e9af3b31') in 0.0628507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ConicalCauldron_02.fbx
  artifactKey: Guid(abc00000000002137042297083440624) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ConicalCauldron_02.fbx using Guid(abc00000000002137042297083440624) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce2c049d88f7cc7745175b66f40e3e3a') in 0.0940494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Jar_01.fbx
  artifactKey: Guid(abc00000000012187121386088606939) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Jar_01.fbx using Guid(abc00000000012187121386088606939) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7d8a18c920e5fdb4449cfa935dc3359') in 0.074194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_FireParticle.prefab
  artifactKey: Guid(b06bcf97bece9c64fa9cf91dfac6e1f4) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_FireParticle.prefab using Guid(b06bcf97bece9c64fa9cf91dfac6e1f4) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a466aca54f70b0ef1ed96c08966099f6') in 0.1240788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/HumanF@Sprint01_ForwardLeft.fbx
  artifactKey: Guid(62f242d2f9358a8429c9ea16bfc70c60) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/HumanF@Sprint01_ForwardLeft.fbx using Guid(62f242d2f9358a8429c9ea16bfc70c60) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '922660b565d8f419155260242396c9e0') in 0.0774248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_SmokeParticle.prefab
  artifactKey: Guid(5f824757289c01a4ab36648492fb6aba) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_SmokeParticle.prefab using Guid(5f824757289c01a4ab36648492fb6aba) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62642c9d81e26447d4c4d85f45b110c9') in 0.1275507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Right.fbx
  artifactKey: Guid(e4554e344c29aeb4087271d927e625f2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Right.fbx using Guid(e4554e344c29aeb4087271d927e625f2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3692f61d47191c525bd10accc2ba6c37') in 0.0570664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Select/SFX_UI_Click_Select_2.wav
  artifactKey: Guid(202a69450ad1618479cc4a864b10db06) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Select/SFX_UI_Click_Select_2.wav using Guid(202a69450ad1618479cc4a864b10db06) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d604f7bce0f94add2de36ae06d4ecf2') in 0.1448698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/SM_Fence_02a.fbx
  artifactKey: Guid(abc00000000012816617391429415999) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/SM_Fence_02a.fbx using Guid(abc00000000012816617391429415999) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffa8c50011cb227001668c8b87d4a40d') in 0.0705724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000116 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_ForwardLeft.fbx
  artifactKey: Guid(f1c72df816110fc4394d0e781c72bc31) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_ForwardLeft.fbx using Guid(f1c72df816110fc4394d0e781c72bc31) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d4c42f86bfdb8f308f3e6d957f20133') in 0.0650721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/SM_Spiral_Stairs.fbx
  artifactKey: Guid(abc00000000013447029806234039067) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/SM_Spiral_Stairs.fbx using Guid(abc00000000013447029806234039067) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a65db28ff65bfd361981c599bc3cd174') in 0.0627537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cup_01.fbx
  artifactKey: Guid(abc00000000004210781081598620104) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cup_01.fbx using Guid(abc00000000004210781081598620104) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83e22a97e4ca585f41ce7031afbf7fe2') in 0.0559613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_Metal.mat
  artifactKey: Guid(da875b9e21b785845a9dfc88849266a2) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_Metal.mat using Guid(da875b9e21b785845a9dfc88849266a2) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '367b758ce7bd23893cad5f4f0d936b53') in 0.0642037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Long.fbx
  artifactKey: Guid(abc00000000017928124400499817709) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Long.fbx using Guid(abc00000000017928124400499817709) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b2f55aa29ee7696469618327f26ab67') in 0.0918573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_ArchWall_01.fbx
  artifactKey: Guid(abc00000000001776774391445695633) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_ArchWall_01.fbx using Guid(abc00000000001776774391445695633) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8319c5e1e14587c6d4ba73e0fb170b92') in 0.0788557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Middle_01.fbx
  artifactKey: Guid(abc00000000015251805011421993590) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Middle_01.fbx using Guid(abc00000000015251805011421993590) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '279ea8b965f997d5c8157d2733e0c85a') in 0.0895722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat
  artifactKey: Guid(abc00000000005011678449934084366) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat using Guid(abc00000000005011678449934084366) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74985e7cd0aabb46a17d7c05e0e81a88') in 0.056697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Sidetable_01.fbx
  artifactKey: Guid(abc00000000001531044058807260614) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Sidetable_01.fbx using Guid(abc00000000001531044058807260614) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fc84fc47b2f05438b3b0fd59203a3eb') in 0.078674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_FirePit_A_01.mat
  artifactKey: Guid(ae53c05e5152d2a4c96b54de2959ce37) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_FirePit_A_01.mat using Guid(ae53c05e5152d2a4c96b54de2959ce37) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0bf36be243d3a3a9f1b975d9c7202e19') in 0.0617375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_A_01.fbx
  artifactKey: Guid(abc00000000010107717685978597656) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_A_01.fbx using Guid(abc00000000010107717685978597656) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9079f5bb6f28e2066b048a568805bf44') in 0.080549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Counter_01.fbx
  artifactKey: Guid(abc00000000017828546647096527201) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Counter_01.fbx using Guid(abc00000000017828546647096527201) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f4a15ad1ee8ef727628438d67c5ab30') in 0.0917953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_Right [RM].fbx
  artifactKey: Guid(9d09a7ef30cc1fe41a7f28ee8fe465c0) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_Right [RM].fbx using Guid(9d09a7ef30cc1fe41a7f28ee8fe465c0) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d275f9158c9069a67d04b53941adabf') in 0.0917409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Small_Rock_B.fbx
  artifactKey: Guid(abc00000000009829145550276728498) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Small_Rock_B.fbx using Guid(abc00000000009829145550276728498) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '616c98c95f2485ce31084c507e05f4a9') in 0.0889587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_B_01.fbx
  artifactKey: Guid(abc00000000015710304559959027251) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_B_01.fbx using Guid(abc00000000015710304559959027251) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '614a8b36411b706a4aeec35656538e3c') in 0.0803378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Materials/No Name.mat
  artifactKey: Guid(5df4b6487cc75c54085033715f93a50b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Materials/No Name.mat using Guid(5df4b6487cc75c54085033715f93a50b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37e2bb5b266daae50f9e715f714c327a') in 0.0768825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_Left [RM].fbx
  artifactKey: Guid(9804b1ef8ab27f84f9d98515273f70e3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_Left [RM].fbx using Guid(9804b1ef8ab27f84f9d98515273f70e3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24447977f5df1ec403b6eabae13628fc') in 0.0809718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_ForwardLeft [RM].fbx
  artifactKey: Guid(1c3a6282a5507e9439ab8d4787dd80c5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_ForwardLeft [RM].fbx using Guid(1c3a6282a5507e9439ab8d4787dd80c5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '091c826bb0cd350ae186a6c63236ae54') in 0.0947092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_ForwardRight [RM].fbx
  artifactKey: Guid(13a7a8b93b87c984dbd8e0556bfebd96) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_ForwardRight [RM].fbx using Guid(13a7a8b93b87c984dbd8e0556bfebd96) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3419c9f0b99370d39ecaa9f5dc4a7b06') in 0.1460116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_01.fbx
  artifactKey: Guid(abc00000000016154007810332105749) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_01.fbx using Guid(abc00000000016154007810332105749) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84ee5bfaaf214cd28351b8bcbd79ba16') in 0.0863682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_ForwardRight [RM].fbx
  artifactKey: Guid(f2713866a3b7c484eb322653260adc9c) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_ForwardRight [RM].fbx using Guid(f2713866a3b7c484eb322653260adc9c) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0b9800a665ae573779c7b5525cd3f64') in 0.0743974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_Castle_Door.fbx
  artifactKey: Guid(abc00000000010072320915516276326) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_Castle_Door.fbx using Guid(abc00000000010072320915516276326) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '607b3b1a9524704c3ed5e5b258f078b4') in 0.0884933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_B_02.fbx
  artifactKey: Guid(abc00000000003679495084735481102) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_B_02.fbx using Guid(abc00000000003679495084735481102) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34c853b40f02676879d59c05ebdaa91b') in 0.0638944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/SM_Planks_4M.fbx
  artifactKey: Guid(abc00000000012612762627137544164) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/SM_Planks_4M.fbx using Guid(abc00000000012612762627137544164) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c76d793fa0831fe0f0883e63d3e9548') in 0.0899538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000203 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Left [RM].fbx
  artifactKey: Guid(d566567af8d4548409c7a95dc3995977) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Left [RM].fbx using Guid(d566567af8d4548409c7a95dc3995977) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a98c4d6a148e58dc4d8558fa2da1d965') in 0.0870177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/Materials/No Name.mat
  artifactKey: Guid(4df232a5ed009c644b4994b88b07e01d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Large/Materials/No Name.mat using Guid(4df232a5ed009c644b4994b88b07e01d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7e79b38abd9aa32378221b5a0470149') in 0.0620201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/MI_Cloth_Market_Blue.mat
  artifactKey: Guid(5ca3b7260255ced48a7fba36ca382d28) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/MI_Cloth_Market_Blue.mat using Guid(5ca3b7260255ced48a7fba36ca382d28) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e73fb0b7c43e38a09a05554af5246825') in 0.05808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_VarnishedWood_01.mat
  artifactKey: Guid(7985696ac286ce6409e75f8d26a0b306) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_VarnishedWood_01.mat using Guid(7985696ac286ce6409e75f8d26a0b306) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '900747b2491293548c310dbcbfe495b0') in 0.0568361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_01.fbx
  artifactKey: Guid(abc00000000015171502228229381962) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_01.fbx using Guid(abc00000000015171502228229381962) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d34e13e2a5f15a185d7872f2c591444') in 0.0919714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_08.fbx
  artifactKey: Guid(abc00000000010535682288393544504) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_08.fbx using Guid(abc00000000010535682288393544504) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0a81e7733aece1200312871011ebeff') in 0.0639554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_A.fbx
  artifactKey: Guid(abc00000000008961040502666938614) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_A.fbx using Guid(abc00000000008961040502666938614) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ea348ad5918927713a75449aee64143') in 0.0855983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_01.fbx
  artifactKey: Guid(abc00000000006940539183249306741) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_01.fbx using Guid(abc00000000006940539183249306741) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70cf1d8f978ca73118ad55ccac1404d0') in 0.0878403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_02.mat
  artifactKey: Guid(16a5ada13924ab5409e40940ea451f98) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_02.mat using Guid(16a5ada13924ab5409e40940ea451f98) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '337f7f2133ea530e55284c4aa557a734') in 0.0673162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_01.fbx
  artifactKey: Guid(abc00000000014983111244320886698) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_01.fbx using Guid(abc00000000014983111244320886698) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8251ee372c098713245ab9032ef7caee') in 0.060961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_04.fbx
  artifactKey: Guid(abc00000000012431421504800498157) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_04.fbx using Guid(abc00000000012431421504800498157) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5eb2aefc55b165288d862727f7db8a26') in 0.0730002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_BrickStoneWall_Disp.mat
  artifactKey: Guid(da4d02d853b54414cb64c44da520a99b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_BrickStoneWall_Disp.mat using Guid(da4d02d853b54414cb64c44da520a99b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1d8080fdfbc46f365312ad05e7225d7') in 0.0610037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_01.mat
  artifactKey: Guid(fa97e607a9d2d124fbafc55c9cf6525e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_01.mat using Guid(fa97e607a9d2d124fbafc55c9cf6525e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da4bec758525020f5bc27a23e274adfd') in 0.0772776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.001095 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_11.fbx
  artifactKey: Guid(abc00000000010699448540376424400) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_11.fbx using Guid(abc00000000010699448540376424400) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '505d5a8e364b4cd4348044bc0063b7cd') in 0.1193899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_XL_02.fbx
  artifactKey: Guid(abc00000000001510340005094097014) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_XL_02.fbx using Guid(abc00000000001510340005094097014) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd814808ef8de4594adf8c4f8945a0882') in 0.1165416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(2176bc49684135f408e8fc5240b93d1b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_LighterPlanks.mat using Guid(2176bc49684135f408e8fc5240b93d1b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcb9ea6ad2f2c6e9a53bdb043ed0a046') in 0.0724346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_Metal.mat
  artifactKey: Guid(6bbba3f34718bed4793c39c0be3a067b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_Metal.mat using Guid(6bbba3f34718bed4793c39c0be3a067b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '804d236ec19455d7970e2f25d9f1435a') in 0.0647152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_M_01.fbx
  artifactKey: Guid(abc00000000011528797295328008000) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_M_01.fbx using Guid(abc00000000011528797295328008000) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1ad609d115103f78e43068ae51919f5') in 0.0779531 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_02.fbx
  artifactKey: Guid(abc00000000013394626622954197195) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_02.fbx using Guid(abc00000000013394626622954197195) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '416e71fd675aa98c164babff64898a36') in 0.0894967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Forward [RM].fbx
  artifactKey: Guid(836229ad8a6407d49a58a3b3fab59d53) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Forward [RM].fbx using Guid(836229ad8a6407d49a58a3b3fab59d53) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0c13b029429aab35f0786a40138d318') in 0.0804374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_Metal.mat
  artifactKey: Guid(b3bb1920a868f5240b9e0f070a6468f3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_Metal.mat using Guid(b3bb1920a868f5240b9e0f070a6468f3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5791d518c9df6477a6b78054e18e46cb') in 0.0659457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_07.fbx
  artifactKey: Guid(abc00000000003019616056560728625) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_07.fbx using Guid(abc00000000003019616056560728625) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5412613d8ff83b520f1404a5e883be1') in 0.1306454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_03.fbx
  artifactKey: Guid(abc00000000017484015916278962752) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_03.fbx using Guid(abc00000000017484015916278962752) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87f38e929ee7c9c0a2fd46d0c2860c1e') in 0.1105816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_06.mat
  artifactKey: Guid(3fe738798992c28449af29e6ddde4fae) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_06.mat using Guid(3fe738798992c28449af29e6ddde4fae) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd137dd627507744b8bd1ec94c943300b') in 0.0560022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Roof_Tiles_02.mat
  artifactKey: Guid(2966514adf66b19489a79d7d33029ea5) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Roof_Tiles_02.mat using Guid(2966514adf66b19489a79d7d33029ea5) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54be2620d3262b38cede2e6fecf08f53') in 0.0672017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_Metal.mat
  artifactKey: Guid(ae24e9518699c0442b61504aa7a2ca91) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_Metal.mat using Guid(ae24e9518699c0442b61504aa7a2ca91) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c980d26763e3b61303daf1c6a3f367d') in 0.0671453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_04.mat
  artifactKey: Guid(6226e5d886cf0f1489ec0d6ca8408e48) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_04.mat using Guid(6226e5d886cf0f1489ec0d6ca8408e48) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e6265ef133489e9fef06b943413375c') in 0.0730999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Counter_02.fbx
  artifactKey: Guid(abc00000000007920335993354305232) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Counter_02.fbx using Guid(abc00000000007920335993354305232) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1636c630748c6b51556b62327dabbf9') in 0.0805801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_6x8_01.fbx
  artifactKey: Guid(abc00000000005599721943764675248) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_6x8_01.fbx using Guid(abc00000000005599721943764675248) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f37f223db1ebd437472d0a9a48bcd35d') in 0.0833112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Jog_A_Root.FBX
  artifactKey: Guid(c508e38ac3f922f40aee9672edd335ee) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Jog_A_Root.FBX using Guid(c508e38ac3f922f40aee9672edd335ee) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '512d65cca6729bfe8b50a177826b809f') in 0.0660704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_basecolor.PNG
  artifactKey: Guid(9aa2489c9b48ed64cb9571bf24eb513e) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_basecolor.PNG using Guid(9aa2489c9b48ed64cb9571bf24eb513e) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dcbdca3a76ba306aeaca97aeb0579a99') in 0.0821787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_B.FBX
  artifactKey: Guid(82f5d928afac83747bd5f2e5f02ae5c3) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_B.FBX using Guid(82f5d928afac83747bd5f2e5f02ae5c3) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63ddb9c40ef1ea6aaea9bd83ed8155cf') in 0.0696818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R45_Root.FBX
  artifactKey: Guid(5fec1d035794da6499d2b191b05fc171) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R45_Root.FBX using Guid(5fec1d035794da6499d2b191b05fc171) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1fc93e54e22e999b89dc7297cb61a7d4') in 0.0660849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_B.prefab
  artifactKey: Guid(abc00000000002115521984995160350) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_B.prefab using Guid(abc00000000002115521984995160350) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8235fd5fbe7deab5d75dc8a83592b7d4') in 0.0718819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_MRA.PNG
  artifactKey: Guid(ebfe09be4643c6b43abc527cf664a154) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_MRA.PNG using Guid(ebfe09be4643c6b43abc527cf664a154) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3db54f6928604e0a1fc465d05e4c27d') in 0.0781694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_A.FBX
  artifactKey: Guid(02dcbf777be614046b7546c84bd0573d) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/4__Right/M_katana_Blade@Damage_Right_Small_ver_A.FBX using Guid(02dcbf777be614046b7546c84bd0573d) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b206037a19d58ad188902c7dc90c3b37') in 0.0892026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MiniSplash_2x6_01_Grayscale.png
  artifactKey: Guid(257cf8a9e06c8aa4eb3ca02423e6d226) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MiniSplash_2x6_01_Grayscale.png using Guid(257cf8a9e06c8aa4eb3ca02423e6d226) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '454a3634f1e2f96b89bdc51c22b294b8') in 0.0873369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_14.fbx
  artifactKey: Guid(abc00000000018189881502853921923) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_14.fbx using Guid(abc00000000018189881502853921923) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c11e8bed00585e203bd8602571601e64') in 0.0823838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1A_Inplace.FBX
  artifactKey: Guid(a4a6e7aa7afbb0a4692aba04e5842a1b) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1A_Inplace.FBX using Guid(a4a6e7aa7afbb0a4692aba04e5842a1b) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51903e1271516311768f1a37d7cfb991') in 0.0649003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Meditative_1.wav
  artifactKey: Guid(53cd6ba2c34ccf74a9bfa163150d3cca) Importer(**********,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Meditative_1.wav using Guid(53cd6ba2c34ccf74a9bfa163150d3cca) Importer(**********,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a095cd7922db7cb28538c605debd31c5') in 0.1999805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0