Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:18:57Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker21
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker21.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [27412]  Target information:

Player connection [27412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1488186075 [EditorId] 1488186075 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27412] Host joined multi-casting on [***********:54997]...
Player connection [27412] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.85 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.33 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56696
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002816 seconds.
- Loaded All Assemblies, in  0.741 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.577 seconds
Domain Reload Profiling: 1319ms
	BeginReloadAssembly (240ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (101ms)
	LoadAllAssembliesAndSetupDomain (296ms)
		LoadAssemblies (231ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (289ms)
				TypeCache.ScanAssembly (270ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (578ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (252ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.343 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.83 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.699 seconds
Domain Reload Profiling: 3041ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (914ms)
		LoadAssemblies (524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (541ms)
			TypeCache.Refresh (404ms)
				TypeCache.ScanAssembly (370ms)
			BuildScriptInfoCaches (104ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1700ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1367ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (254ms)
			ProcessInitializeOnLoadAttributes (969ms)
			ProcessInitializeOnLoadMethodAttributes (127ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 6.08 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (8.8 MB). Loaded Objects now: 8335.
Memory consumption went from 211.9 MB to 203.1 MB.
Total: 18.350400 ms (FindLiveObjects: 1.376700 ms CreateObjectMapping: 1.604900 ms MarkObjects: 8.030400 ms  DeleteObjects: 7.335000 ms)

========================================================================
Received Import Request.
  Time since last request: 1812753.500421 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Cyan.mat
  artifactKey: Guid(e5be4bad6d8e29b4b8aa94631cef7eec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Cyan.mat using Guid(e5be4bad6d8e29b4b8aa94631cef7eec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87577073da909c0ad09677cf12546da3') in 6.5974214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyWhite.mat
  artifactKey: Guid(eb7a06e79dea43549bb02ccd53a948f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyWhite.mat using Guid(eb7a06e79dea43549bb02ccd53a948f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a0389d408133d7d096e6bac77886ba0') in 0.0371117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 16.829520 seconds.
  path: Assets/Scenes/Start 01.unity
  artifactKey: Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Start 01.unity using Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'faa1ef46ac069c44751e12ef5b9f707a') in 0.0007912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 21.791342 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_1.mat
  artifactKey: Guid(8f3f66c592950e24d8d76f93f49046fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_1.mat using Guid(8f3f66c592950e24d8d76f93f49046fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '896b7a297612108a778e590fc7c57024') in 0.0357718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.645072 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab
  artifactKey: Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab using Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30a5941381c6e5c4f593e9acfd5428f0') in 0.059148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab
  artifactKey: Guid(f340455adb5398544a6d24914132484f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleSphere.prefab using Guid(f340455adb5398544a6d24914132484f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53e4a949c5f1f89b367b6c3f15b518ae') in 0.0473439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab
  artifactKey: Guid(a8b3630e141365046a63788d12883c2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab using Guid(a8b3630e141365046a63788d12883c2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72abf8d6ec0ea8e16f9ab4edb83bbeab') in 0.0670424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleCone.prefab
  artifactKey: Guid(19a5c636edbcc1440b8ad974d36d9300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleCone.prefab using Guid(19a5c636edbcc1440b8ad974d36d9300) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04c1e846eab1bb9175a9f15ac58bf309') in 0.0704256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab
  artifactKey: Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab using Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20c952c946c7504c9a5dc08949a7364f') in 0.0536265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 2.638537 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions
  artifactKey: Guid(b38dd67b6d810ec4a82a50749b43f4a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions using Guid(b38dd67b6d810ec4a82a50749b43f4a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '956cc1c1385c1a7174873d51259e8f6c') in 0.0008722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.884123 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/CharacterManager.cs
  artifactKey: Guid(d653000bcba62c14ba9b6c5c324a4197) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/CharacterManager.cs using Guid(d653000bcba62c14ba9b6c5c324a4197) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba6161fd15e2b4e21749ad46dce3c70f') in 0.0011943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.014178 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/AnimatedController.cs
  artifactKey: Guid(6401a0300cc05d74a953f65bf864fd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/AnimatedController.cs using Guid(6401a0300cc05d74a953f65bf864fd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'edfdb324fa59eb71a13c538f001ecacf') in 0.0007886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.313952 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.inputactions
  artifactKey: Guid(010bd85fdf203f34d94abf6f5ad3e876) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Input/MovementActions.inputactions using Guid(010bd85fdf203f34d94abf6f5ad3e876) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07cb145fe5a863ebc34edb3b7260f08f') in 0.0056903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 42.986423 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene.unity
  artifactKey: Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene.unity using Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7e1afe36ab812d99f42e48e30f16228') in 0.0009261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.014271 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Documentation - Physics Character Controller.pdf
  artifactKey: Guid(97289747f4777834d8ea20056a0dfc73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Documentation - Physics Character Controller.pdf using Guid(97289747f4777834d8ea20056a0dfc73) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c1905312af94f677c3415badd8684fce') in 0.0017594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 9.853888 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity
  artifactKey: Guid(afd6880a243c0854882e6dc680d01b5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity using Guid(afd6880a243c0854882e6dc680d01b5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7128e415f03c1e906508424651f89735') in 0.0008313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.334605 seconds.
  path: Assets/Scenes/Start 01.unity
  artifactKey: Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/Start 01.unity using Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6ad06b074cc8bd9ec7d6903ba0da38a') in 0.3784522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 7.813051 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid
  artifactKey: Guid(4114f4018b5cbff4a93603c808950665) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid using Guid(4114f4018b5cbff4a93603c808950665) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e59498b82e10b96dd45bcd0372a42b24') in 0.0177262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid/LightingData.asset
  artifactKey: Guid(cb08b3f46f860e14c8292466618af944) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid/LightingData.asset using Guid(cb08b3f46f860e14c8292466618af944) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd73e7a62aad1ecedf70d8b9ef2e588ea') in 0.0504202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.220 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.43 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.655 seconds
Domain Reload Profiling: 2878ms
	BeginReloadAssembly (402ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (694ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (343ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (299ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1656ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1357ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (247ms)
			ProcessInitializeOnLoadAttributes (989ms)
			ProcessInitializeOnLoadMethodAttributes (107ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 5.96 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7636 unused Assets / (9.3 MB). Loaded Objects now: 8416.
Memory consumption went from 191.6 MB to 182.4 MB.
Total: 17.580300 ms (FindLiveObjects: 1.041200 ms CreateObjectMapping: 0.936200 ms MarkObjects: 7.771600 ms  DeleteObjects: 7.829300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.180 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.72 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.602 seconds
Domain Reload Profiling: 2788ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (709ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (342ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (293ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1603ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1324ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (244ms)
			ProcessInitializeOnLoadAttributes (952ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 4.51 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.0 MB). Loaded Objects now: 8418.
Memory consumption went from 189.8 MB to 179.8 MB.
Total: 16.009000 ms (FindLiveObjects: 1.087500 ms CreateObjectMapping: 0.848800 ms MarkObjects: 7.440200 ms  DeleteObjects: 6.630800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.157 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.41 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.573 seconds
Domain Reload Profiling: 2735ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (701ms)
		LoadAssemblies (524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (334ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (288ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1574ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1286ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (232ms)
			ProcessInitializeOnLoadAttributes (924ms)
			ProcessInitializeOnLoadMethodAttributes (113ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 5.91 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.1 MB). Loaded Objects now: 8420.
Memory consumption went from 189.8 MB to 179.7 MB.
Total: 22.379700 ms (FindLiveObjects: 1.341900 ms CreateObjectMapping: 1.700300 ms MarkObjects: 9.758200 ms  DeleteObjects: 9.576600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 438.723389 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity
  artifactKey: Guid(afd6880a243c0854882e6dc680d01b5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity using Guid(afd6880a243c0854882e6dc680d01b5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '280d18960cebba4188920babd7cada82') in 0.0442342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 46.269443 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)Eyes.controller
  artifactKey: Guid(ac054854941e66044b2648ca7a630471) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)Eyes.controller using Guid(ac054854941e66044b2648ca7a630471) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '17d1a44cd568385b9716a348c3a793d8') in 0.0243294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 43.964408 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_01_Dirty.mat
  artifactKey: Guid(e468b13984260334fb47dbf12d5ae8ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_01_Dirty.mat using Guid(e468b13984260334fb47dbf12d5ae8ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc722c6bfb84f6a974957a4e57e46fba') in 6.7014167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_02.mat
  artifactKey: Guid(fc6b014236a0aec47a3d99b1a5a21d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_02.mat using Guid(fc6b014236a0aec47a3d99b1a5a21d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '764ae2706817e11e5588544d91c687e1') in 0.1535236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_05.mat
  artifactKey: Guid(d6649f8ff4d889d42ab6427d8a31ddf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_05.mat using Guid(d6649f8ff4d889d42ab6427d8a31ddf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5505d203e94ffe9c97ed6ef310576505') in 0.1682198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 73.852396 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(4f9800672af7570478f085a3023ace57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(4f9800672af7570478f085a3023ace57) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '436f8498a9fb4f4c6a0e184378250cf4') in 0.0265111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.258 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 8.78 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.620 seconds
Domain Reload Profiling: 2884ms
	BeginReloadAssembly (376ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (745ms)
		LoadAssemblies (511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (381ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (332ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1316ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (234ms)
			ProcessInitializeOnLoadAttributes (946ms)
			ProcessInitializeOnLoadMethodAttributes (118ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 4.56 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (11.0 MB). Loaded Objects now: 8442.
Memory consumption went from 190.0 MB to 179.1 MB.
Total: 18.371600 ms (FindLiveObjects: 1.078900 ms CreateObjectMapping: 1.058800 ms MarkObjects: 7.972000 ms  DeleteObjects: 8.260100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.421 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.65 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.895 seconds
Domain Reload Profiling: 3322ms
	BeginReloadAssembly (387ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (886ms)
		LoadAssemblies (601ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (396ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (1895ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (305ms)
			ProcessInitializeOnLoadAttributes (1085ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 19.33 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.15 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.5 MB). Loaded Objects now: 8444.
Memory consumption went from 189.9 MB to 179.5 MB.
Total: 31.594000 ms (FindLiveObjects: 1.973900 ms CreateObjectMapping: 2.631200 ms MarkObjects: 14.618500 ms  DeleteObjects: 12.367800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.270 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.22 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.616 seconds
Domain Reload Profiling: 2893ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (814ms)
		LoadAssemblies (546ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (426ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (377ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (258ms)
			ProcessInitializeOnLoadAttributes (943ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 5.42 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.3 MB). Loaded Objects now: 8446.
Memory consumption went from 190.0 MB to 180.6 MB.
Total: 19.877000 ms (FindLiveObjects: 1.285600 ms CreateObjectMapping: 1.281600 ms MarkObjects: 9.018600 ms  DeleteObjects: 8.289200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.240 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.80 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.590 seconds
Domain Reload Profiling: 2834ms
	BeginReloadAssembly (343ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (769ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (416ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (373ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1591ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1285ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (237ms)
			ProcessInitializeOnLoadAttributes (924ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 4.69 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.2 MB). Loaded Objects now: 8448.
Memory consumption went from 190.0 MB to 179.8 MB.
Total: 17.349500 ms (FindLiveObjects: 1.082400 ms CreateObjectMapping: 0.837700 ms MarkObjects: 8.035300 ms  DeleteObjects: 7.392300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 900.446055 seconds.
  path: Assets/Scenes/Start 01.unity
  artifactKey: Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Start 01.unity using Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5d7aa5f9eb4868e58b0ed9cf9b4f4bd7') in 0.050643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.398 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.10 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.560 seconds
Domain Reload Profiling: 2963ms
	BeginReloadAssembly (403ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (78ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (59ms)
	LoadAllAssembliesAndSetupDomain (838ms)
		LoadAssemblies (639ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (398ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (355ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1561ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1246ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (219ms)
			ProcessInitializeOnLoadAttributes (912ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 4.71 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.3 MB). Loaded Objects now: 8450.
Memory consumption went from 190.0 MB to 179.7 MB.
Total: 19.549500 ms (FindLiveObjects: 1.430500 ms CreateObjectMapping: 0.871500 ms MarkObjects: 9.408100 ms  DeleteObjects: 7.837100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.229 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.07 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.598 seconds
Domain Reload Profiling: 2832ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (756ms)
		LoadAssemblies (544ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (375ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (330ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1309ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (933ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 5.53 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.7 MB). Loaded Objects now: 8452.
Memory consumption went from 190.1 MB to 179.3 MB.
Total: 21.452200 ms (FindLiveObjects: 1.676600 ms CreateObjectMapping: 1.143300 ms MarkObjects: 9.196100 ms  DeleteObjects: 9.434300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.239 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.43 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.570 seconds
Domain Reload Profiling: 2814ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (764ms)
		LoadAssemblies (546ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (333ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1571ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1303ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (247ms)
			ProcessInitializeOnLoadAttributes (937ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.78 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.0 MB). Loaded Objects now: 8454.
Memory consumption went from 190.1 MB to 180.1 MB.
Total: 18.172000 ms (FindLiveObjects: 1.058000 ms CreateObjectMapping: 0.946700 ms MarkObjects: 8.380200 ms  DeleteObjects: 7.785200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.80 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7608 unused Assets / (9.8 MB). Loaded Objects now: 8454.
Memory consumption went from 190.1 MB to 180.3 MB.
Total: 22.398600 ms (FindLiveObjects: 1.683100 ms CreateObjectMapping: 1.584100 ms MarkObjects: 9.809000 ms  DeleteObjects: 9.320400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.166 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.24 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.567 seconds
Domain Reload Profiling: 2737ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (701ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (350ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (300ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1568ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1266ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (243ms)
			ProcessInitializeOnLoadAttributes (904ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 4.51 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.9 MB). Loaded Objects now: 8456.
Memory consumption went from 190.2 MB to 180.3 MB.
Total: 16.690200 ms (FindLiveObjects: 1.063200 ms CreateObjectMapping: 0.919300 ms MarkObjects: 7.952600 ms  DeleteObjects: 6.753200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.132 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.97 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.624 seconds
Domain Reload Profiling: 2761ms
	BeginReloadAssembly (312ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (702ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (346ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (306ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1336ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (278ms)
			ProcessInitializeOnLoadAttributes (935ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 5.50 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.4 MB). Loaded Objects now: 8458.
Memory consumption went from 190.3 MB to 179.9 MB.
Total: 19.445400 ms (FindLiveObjects: 1.612500 ms CreateObjectMapping: 1.394200 ms MarkObjects: 7.897100 ms  DeleteObjects: 8.540100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.450 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.79 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.531 seconds
Domain Reload Profiling: 2984ms
	BeginReloadAssembly (392ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (917ms)
		LoadAssemblies (732ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (397ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (349ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1532ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1240ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (246ms)
			ProcessInitializeOnLoadAttributes (883ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 4.54 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.4 MB). Loaded Objects now: 8460.
Memory consumption went from 190.3 MB to 180.9 MB.
Total: 18.034300 ms (FindLiveObjects: 1.090200 ms CreateObjectMapping: 1.339600 ms MarkObjects: 8.348300 ms  DeleteObjects: 7.253500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.228 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.94 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.561 seconds
Domain Reload Profiling: 2792ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (781ms)
		LoadAssemblies (550ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (382ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (329ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1561ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1261ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (259ms)
			ProcessInitializeOnLoadAttributes (860ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 4.60 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.0 MB). Loaded Objects now: 8462.
Memory consumption went from 190.4 MB to 181.3 MB.
Total: 16.201800 ms (FindLiveObjects: 1.065400 ms CreateObjectMapping: 0.813900 ms MarkObjects: 7.747100 ms  DeleteObjects: 6.573600 ms)

Prepare: number of updated asset objects reloaded= 0
