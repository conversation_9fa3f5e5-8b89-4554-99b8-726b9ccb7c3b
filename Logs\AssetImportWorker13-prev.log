Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker13
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker13.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [26600]  Target information:

Player connection [26600]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2998490807 [EditorId] 2998490807 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [26600] Host joined multi-casting on [***********:54997]...
Player connection [26600] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.92 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.48 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56476
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.009175 seconds.
- Loaded All Assemblies, in  1.018 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.189 seconds
Domain Reload Profiling: 2206ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (97ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (123ms)
	LoadAllAssembliesAndSetupDomain (445ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (439ms)
			TypeCache.Refresh (431ms)
				TypeCache.ScanAssembly (398ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (1189ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1046ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (68ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (578ms)
			ProcessInitializeOnLoadMethodAttributes (143ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.295 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.90 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.043 seconds
Domain Reload Profiling: 5334ms
	BeginReloadAssembly (431ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (107ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (105ms)
	LoadAllAssembliesAndSetupDomain (1614ms)
		LoadAssemblies (993ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (855ms)
			TypeCache.Refresh (650ms)
				TypeCache.ScanAssembly (608ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (37ms)
	FinalizeReload (3044ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (424ms)
			ProcessInitializeOnLoadAttributes (1798ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 7.22 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (12.1 MB). Loaded Objects now: 8334.
Memory consumption went from 209.9 MB to 197.7 MB.
Total: 120.397000 ms (FindLiveObjects: 2.473300 ms CreateObjectMapping: 3.328900 ms MarkObjects: 97.531300 ms  DeleteObjects: 17.060200 ms)

========================================================================
Received Import Request.
  Time since last request: 1812181.531117 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab
  artifactKey: Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab using Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8068e7a90443b5d911539472e0775d21') in 2.622689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab
  artifactKey: Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab using Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '745bc4eabdbe57374a797b412b29cc1a') in 0.3670795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab
  artifactKey: Guid(2508951d6b5158047b8c53425a720d81) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab using Guid(2508951d6b5158047b8c53425a720d81) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '15c11858890d16cca3bb47bd22ad59f7') in 0.1529136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Scripts/Debug/BlazeoutDebug.cs
  artifactKey: Guid(96eef2008011b314e88800180c5b0e73) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Debug/BlazeoutDebug.cs using Guid(96eef2008011b314e88800180c5b0e73) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58b930d703a263cf5ca64362e987b95b') in 0.3489791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene/LightingData.asset
  artifactKey: Guid(2fe5bc3a26eeb404cb658b4242bbfb6a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene/LightingData.asset using Guid(2fe5bc3a26eeb404cb658b4242bbfb6a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5cd9cce0f9861c75228e0d96849e7387') in 0.0267277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.001009 seconds.
  path: Assets/Settings/PC_Renderer.asset
  artifactKey: Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/PC_Renderer.asset using Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '07f8aa71b148a04e4719676d25f8cf74') in 0.025728 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/MedievalKingdomHighQualityTextures.txt
  artifactKey: Guid(6ac6af4dc77dcb942affcf4df7cb409e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/MedievalKingdomHighQualityTextures.txt using Guid(6ac6af4dc77dcb942affcf4df7cb409e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2869a84cf5bd862e2204a684fc8db8f') in 0.0247349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_01.mat
  artifactKey: Guid(b71b7ffca2aa6094788b1c0310dd9c62) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_01.mat using Guid(b71b7ffca2aa6094788b1c0310dd9c62) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c43a303f455af9a3ff8d631cc0230a18') in 1.814048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab
  artifactKey: Guid(a94136958165ffb4e91f034a7974d498) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab using Guid(a94136958165ffb4e91f034a7974d498) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7c486e78d2427cbf5f48883fb0588d62') in 0.0616699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_T-Pose_Grrrru_Man(recommend).FBX
  artifactKey: Guid(43992d4f16e57574fa5d16ef485c0cbd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_T-Pose_Grrrru_Man(recommend).FBX using Guid(43992d4f16e57574fa5d16ef485c0cbd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '459c19d67195bacf7e8e815e03ae362e') in 0.1913104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 148

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_AroundWater_Head_02.mat
  artifactKey: Guid(a4481306ae79610469e1670b823bf5e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_AroundWater_Head_02.mat using Guid(a4481306ae79610469e1670b823bf5e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a5a691467e773b24234f66902d6a85cb') in 0.0717969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_01.mat
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_01.mat using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed3da57739e343b13af751d25caf7227') in 0.5797668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3f2e28956d0958792173227aafdaf5e7') in 0.0796585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.prefab
  artifactKey: Guid(fb31d62fd729c27488ed5cc8153e2b12) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.prefab using Guid(fb31d62fd729c27488ed5cc8153e2b12) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '862e46202e4f4fd41a12c5120a895ce4') in 0.6893203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Scripts/Controllers/InputBufferSystem.cs
  artifactKey: Guid(1cbffb7a356cb95418fce65ef2e73e94) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/InputBufferSystem.cs using Guid(1cbffb7a356cb95418fce65ef2e73e94) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0da2700152c6679b2c79f56efe941c4b') in 0.078896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Settings/PC_RPAsset.asset
  artifactKey: Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/PC_RPAsset.asset using Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8af936d889c2a77e4ca3e53b8489e2b4') in 0.2397023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Axe_01.prefab
  artifactKey: Guid(abc00000000000346792517797210715) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Axe_01.prefab using Guid(abc00000000000346792517797210715) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '33224d538ab8b129bb571d3e4adcfcff') in 0.0634631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/PP Profile.asset
  artifactKey: Guid(b176d70af7536e8418281ba8ea518d6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/PP Profile.asset using Guid(b176d70af7536e8418281ba8ea518d6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7de0aa30bd620a720a335b272c4eea05') in 0.0284175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_02_4x4.mat
  artifactKey: Guid(b1e1b714e02f37e48953010abdcd79f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_02_4x4.mat using Guid(b1e1b714e02f37e48953010abdcd79f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90f90ee626744c456d4042342dde49fb') in 0.2671003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack.prefab
  artifactKey: Guid(abc00000000012140916782779676615) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack.prefab using Guid(abc00000000012140916782779676615) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '464eb6a6e09bc168b20df30738484efe') in 0.042495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_Add_NoZ.mat
  artifactKey: Guid(4f7941daf56f4604bb1e2e053a723f4c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_Add_NoZ.mat using Guid(4f7941daf56f4604bb1e2e053a723f4c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0a62067eed99799e647e5d0d64009c7') in 0.0653873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_01.prefab
  artifactKey: Guid(abc00000000006851179421744888992) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_01.prefab using Guid(abc00000000006851179421744888992) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ebf81ddaea93c699a44c638b75feb469') in 0.0431206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bowl_01.prefab
  artifactKey: Guid(abc00000000004828717454480502761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bowl_01.prefab using Guid(abc00000000004828717454480502761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9bfbbadf90f9d0d5719a3162372c33da') in 0.0321374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialThin_01.mat
  artifactKey: Guid(a3ef865fc864cba46a405a9c50ce6ec7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialThin_01.mat using Guid(a3ef865fc864cba46a405a9c50ce6ec7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8df5e4800f2afcb55f2a3acd0360ed42') in 0.5128996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_03.prefab
  artifactKey: Guid(abc00000000004347845447509042274) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_03.prefab using Guid(abc00000000004347845447509042274) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4ab58d0e1bd459cbc8064a336fee3fd0') in 0.0714183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Projectile.prefab
  artifactKey: Guid(430b21a5c1529714fb227af7563a36f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Projectile.prefab using Guid(430b21a5c1529714fb227af7563a36f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8774b7f2e27d8dc1ca8e5ec756eb5853') in 0.1025232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 153

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_04.prefab
  artifactKey: Guid(abc00000000003605172642646372016) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_04.prefab using Guid(abc00000000003605172642646372016) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '33768fc9d85842a7cf4b6bd66c77cba5') in 0.0789913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_01.prefab
  artifactKey: Guid(abc00000000006619999490115597816) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_01.prefab using Guid(abc00000000006619999490115597816) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bcb196bbf2c511fa7852afa8d15a7801') in 0.0490163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity
  artifactKey: Guid(4bd76b5e96cc1bf49b1b111e51d37762) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_04_Waterfall.unity using Guid(4bd76b5e96cc1bf49b1b111e51d37762) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26f6e61ae6d80a916ec12b293a3c2b18') in 0.0379199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron_Lid.prefab
  artifactKey: Guid(abc00000000003516320758811333365) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron_Lid.prefab using Guid(abc00000000003516320758811333365) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a3a71e759cd28216bdcee1b21dc68f86') in 0.0806773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_13.prefab
  artifactKey: Guid(abc00000000017219841727514398970) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_13.prefab using Guid(abc00000000017219841727514398970) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '653bab1a3e60b90afde4f7855d85d4f4') in 0.046197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_02.mat
  artifactKey: Guid(52a5b59d37e573142a809a412bb3cf59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_02.mat using Guid(52a5b59d37e573142a809a412bb3cf59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b72d69bcc5a419128af7f8555fdc68b') in 0.0424543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_12.prefab
  artifactKey: Guid(abc00000000011280800201814850625) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_12.prefab using Guid(abc00000000011280800201814850625) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cdf5c191a80709060ecedb7b7c726bdd') in 0.0429805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Small_01.prefab
  artifactKey: Guid(abc00000000002335111547179833352) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Small_01.prefab using Guid(abc00000000002335111547179833352) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'adf7d8170a6124c85e0eb66dfccc91e5') in 0.0657229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_02.mat
  artifactKey: Guid(82d1943f1377fc9429d578d7892d733d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_02.mat using Guid(82d1943f1377fc9429d578d7892d733d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0fc7f04b0600dc98a1b346256206465') in 0.0804131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair_1.prefab
  artifactKey: Guid(abc00000000010102012648969553656) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair_1.prefab using Guid(abc00000000010102012648969553656) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '512c2b4877dc86f7e25146b067cda7ef') in 0.0400891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_corbels.prefab
  artifactKey: Guid(abc00000000001716561692332122495) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_corbels.prefab using Guid(abc00000000001716561692332122495) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2dc95ade8939be20371b4864994e28f9') in 0.069793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Pots.prefab
  artifactKey: Guid(abc00000000005771072365272981121) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Pots.prefab using Guid(abc00000000005771072365272981121) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ac26867e006ab0ccf30a2041e8b715aa') in 0.0851217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_Pillar.prefab
  artifactKey: Guid(abc00000000009946615682477863275) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_Pillar.prefab using Guid(abc00000000009946615682477863275) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '395fd457fd0a1e0b05966e324349dcf4') in 0.0437979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalGlow_01.mat
  artifactKey: Guid(a27674fc23adcb74daa714437efc2b6e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalGlow_01.mat using Guid(a27674fc23adcb74daa714437efc2b6e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '587f704c7d9c02b42afab7b0ba6c03ee') in 0.0425821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4_Lava.mat
  artifactKey: Guid(e76ff0b4bfa40db4a938e0444c971023) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4_Lava.mat using Guid(e76ff0b4bfa40db4a938e0444c971023) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b7d2fbf0b14c566df324a7100cb8c50') in 0.5819582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_A.prefab
  artifactKey: Guid(abc00000000005784984238246329485) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_Patch_A.prefab using Guid(abc00000000005784984238246329485) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dfed2aff0397047448d18fe77814ee0b') in 0.0325656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_B_02.prefab
  artifactKey: Guid(abc00000000000529600483504752456) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_B_02.prefab using Guid(abc00000000000529600483504752456) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f8464de9d0c11f1aeba8353a87e6560b') in 0.0338311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01_1.prefab
  artifactKey: Guid(abc00000000010210916380694647040) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01_1.prefab using Guid(abc00000000010210916380694647040) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8ff6531e6371dfd11b01ae94a25a5c0f') in 0.0514788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_01c.prefab
  artifactKey: Guid(abc00000000007421162951652076853) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_01c.prefab using Guid(abc00000000007421162951652076853) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '77e666ddb029c19156feb851d90f49b9') in 0.0443632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M.prefab
  artifactKey: Guid(abc00000000009676534734759330999) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M.prefab using Guid(abc00000000009676534734759330999) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4b56eb443c0a54b25b09c2a357c1c43f') in 0.038604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_4x4M.prefab
  artifactKey: Guid(abc00000000011717761348093138613) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_4x4M.prefab using Guid(abc00000000011717761348093138613) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e18692661289de9de76663d3155f4c05') in 0.0391869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FirePit_01.prefab
  artifactKey: Guid(abc00000000014341674918090329798) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FirePit_01.prefab using Guid(abc00000000014341674918090329798) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9418d8034dc3b657a3876f3a059882cf') in 0.0731442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_07_4x3.mat
  artifactKey: Guid(ead10169f4dbea64a8be40d29ceda1e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_07_4x3.mat using Guid(ead10169f4dbea64a8be40d29ceda1e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f470129ae52fecde2f14a9678dc8bdde') in 0.0409775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_02_2x6.mat
  artifactKey: Guid(b103159efffda1c4ea11f8fd8347e5ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_02_2x6.mat using Guid(b103159efffda1c4ea11f8fd8347e5ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5b33f0607e638f79a383d278f82e26a') in 0.0611068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.001072 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_RainExample.prefab
  artifactKey: Guid(ca286ace35093d04195fc5e017ba52c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_RainExample.prefab using Guid(ca286ace35093d04195fc5e017ba52c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9f91158c78b676eeee94ecae9ea00fa') in 0.4409044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HallTable_01.prefab
  artifactKey: Guid(abc00000000013875482050015112762) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HallTable_01.prefab using Guid(abc00000000013875482050015112762) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b3b76bbaea156ea3cd7e0788db88b327') in 0.0406262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterBuff_Loop.prefab
  artifactKey: Guid(442fbc2dc8380e744808015ef90c1816) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterBuff_Loop.prefab using Guid(442fbc2dc8380e744808015ef90c1816) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc5f200c01a2525a0d1d3ab88e61ec75') in 0.2101432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 104

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_05_2x5.mat
  artifactKey: Guid(f8292e482cf868243aa0c3900ee81817) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_05_2x5.mat using Guid(f8292e482cf868243aa0c3900ee81817) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81120f5663621e7ca01d2c0a0b73b1cf') in 0.0413432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Meshes/FX_MS_WaterGeneralShapes_01.fbx
  artifactKey: Guid(2a65a18e1fc39a94e91ba80d460dc4b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Meshes/FX_MS_WaterGeneralShapes_01.fbx using Guid(2a65a18e1fc39a94e91ba80d460dc4b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30fcb8aa0082c089fe666b89666cdf1f') in 0.1794934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_04.prefab
  artifactKey: Guid(abc00000000018436674492772112239) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_04.prefab using Guid(abc00000000018436674492772112239) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8a6a7006de8142f18da54c59809c9c13') in 0.0371702 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterVerticalFlipbook_01_3x3.mat
  artifactKey: Guid(cd72d30a9690b2949851995470b43294) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterVerticalFlipbook_01_3x3.mat using Guid(cd72d30a9690b2949851995470b43294) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c836dbb4f7a7003d741b9fcd778079dd') in 0.2716306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_03.prefab
  artifactKey: Guid(abc00000000005231282806985061788) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_03.prefab using Guid(abc00000000005231282806985061788) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f93467b7db084be165a0147f61bbac90') in 0.073535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01.prefab
  artifactKey: Guid(abc00000000006520854952472361067) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01.prefab using Guid(abc00000000006520854952472361067) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '004eaa872789efa823914c4d4d3dac73') in 0.0418953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09.prefab
  artifactKey: Guid(abc00000000013218791321323825800) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09.prefab using Guid(abc00000000013218791321323825800) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5b1942a4dc364f5343c9163fa2fe4229') in 0.0748735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09_1.prefab
  artifactKey: Guid(abc00000000002029463449080447004) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09_1.prefab using Guid(abc00000000002029463449080447004) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b11d05448f1e27e3f17905b9f1f46325') in 0.0469252 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sack_02.prefab
  artifactKey: Guid(abc00000000010478229845810156308) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sack_02.prefab using Guid(abc00000000010478229845810156308) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2afb64eda17595dc354a642a12ba9cf4') in 0.0362832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/TextMesh Pro/Documentation/TextMesh Pro User Guide 2016.pdf
  artifactKey: Guid(1b8d251f9af63b746bf2f7ffe00ebb9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Documentation/TextMesh Pro User Guide 2016.pdf using Guid(1b8d251f9af63b746bf2f7ffe00ebb9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f1755c868a05dd2e1e14c7178f5502e') in 0.029471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_4M.prefab
  artifactKey: Guid(abc00000000002125231476973929255) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_4M.prefab using Guid(abc00000000002125231476973929255) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '57bbf0f937f36a59978fe4a0f2dd769a') in 0.0385946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_5M.prefab
  artifactKey: Guid(abc00000000013902534722377146128) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_5M.prefab using Guid(abc00000000013902534722377146128) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '99602433d32e536dd310c6b63ea60422') in 0.0400758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 54

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_1.prefab
  artifactKey: Guid(abc00000000007856381484955721662) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_1.prefab using Guid(abc00000000007856381484955721662) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5aeac04131843e813be709f26adf60f3') in 0.0328432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_02.prefab
  artifactKey: Guid(67feafff91dbc76449463d379c9b2542) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_SingleFoam_02.prefab using Guid(67feafff91dbc76449463d379c9b2542) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0ee225fb54409b10c20ea0128b83329') in 0.1485581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sledge_Hammer.prefab
  artifactKey: Guid(abc00000000015290214657353769420) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sledge_Hammer.prefab using Guid(abc00000000015290214657353769420) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e6d7687e252c8f4d32307517a3a3ead7') in 0.0342957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/RefractionShader.shadergraph
  artifactKey: Guid(69b6840bf321c2e43bcff85ee845b227) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/RefractionShader.shadergraph using Guid(69b6840bf321c2e43bcff85ee845b227) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c310ca494b8405ba6c82f0d3794d520') in 0.0531096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_02.prefab
  artifactKey: Guid(abc00000000010080006360725859281) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_02.prefab using Guid(abc00000000010080006360725859281) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '69cd71bd2c31690b18f9f19bd0c120ef') in 0.0429818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TavernSign_01.prefab
  artifactKey: Guid(abc00000000003296888411742535538) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TavernSign_01.prefab using Guid(abc00000000003296888411742535538) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'db673669d988dc70614a2f5ef6cf2802') in 0.059651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000325 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Explosion.prefab
  artifactKey: Guid(246470a14b557ee4a9e374413a699082) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Explosion.prefab using Guid(246470a14b557ee4a9e374413a699082) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ba803f4d86acd3e1c09c67d4e2237cd') in 0.2155908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 112

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01.prefab
  artifactKey: Guid(abc00000000017935320353017186305) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01.prefab using Guid(abc00000000017935320353017186305) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6286bc86cdacde9d7530c22a773a5d18') in 0.0370439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_01.prefab
  artifactKey: Guid(abc00000000006091932806558464166) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Door_01.prefab using Guid(abc00000000006091932806558464166) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0b7774fd3d2b8ee910977bb000d49f8f') in 0.0392346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x2.prefab
  artifactKey: Guid(abc00000000006645477426135240256) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x2.prefab using Guid(abc00000000006645477426135240256) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '849c796a85b6fff6bb7a569ff2874082') in 0.0326397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_04.prefab
  artifactKey: Guid(abc00000000017552042681331075390) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_04.prefab using Guid(abc00000000017552042681331075390) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1f9ac789b1ecc4cec12fe03485597915') in 0.043556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_01.prefab
  artifactKey: Guid(abc00000000000047688043517622466) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_01.prefab using Guid(abc00000000000047688043517622466) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '16621eba558595b289eada8a0f4a7db3') in 0.0383807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_04.prefab
  artifactKey: Guid(abc00000000003963479744631661368) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_04.prefab using Guid(abc00000000003963479744631661368) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7769f20bb2c68e312a94c2b4658843aa') in 0.0347114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab
  artifactKey: Guid(048e6d455f851f140bf5df0f43f7747e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab using Guid(048e6d455f851f140bf5df0f43f7747e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e27d8190e8432c8a257a19db6170e2ca') in 0.0289802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_07.prefab
  artifactKey: Guid(3670081c8add95e45aa69c255021dd79) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_07.prefab using Guid(3670081c8add95e45aa69c255021dd79) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd0d6a39ecf79f5f033ff6c306d8af423') in 0.0628699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/Sound List.pdf
  artifactKey: Guid(53eaaa587fb13a74ba2b7aa5644466cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/Sound List.pdf using Guid(53eaaa587fb13a74ba2b7aa5644466cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8707c7ab7847b939980a62a199db22ff') in 0.0364148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Font Bold.ttf
  artifactKey: Guid(63e785e5a9298dc4780c97476bc0070d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Font Bold.ttf using Guid(63e785e5a9298dc4780c97476bc0070d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ba9bf4fd45c866e8290de682c33012f') in 0.1002796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/TutorialInfo/Scripts/Readme.cs
  artifactKey: Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TutorialInfo/Scripts/Readme.cs using Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '604f332de4593e0380780bc8cf138b8c') in 0.0472314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hierarchy Designer/Editor/Documentation/Hierarchy Designer Patch Notes.TXT
  artifactKey: Guid(d4f0a68e6178df547aea94881c9fe65c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Documentation/Hierarchy Designer Patch Notes.TXT using Guid(d4f0a68e6178df547aea94881c9fe65c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b63ef7a14c809353a6accfb441da9c4a') in 0.0521438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-URP Unlit.shadergraph
  artifactKey: Guid(124c112a6e8f1a54e8b0870e881b56d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-URP Unlit.shadergraph using Guid(124c112a6e8f1a54e8b0870e881b56d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2351f8f53e0257bc5d9394bfe30f611') in 0.112287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Bitmap.shader
  artifactKey: Guid(128e987d567d4e2c824d754223b3f3b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Bitmap.shader using Guid(128e987d567d4e2c824d754223b3f3b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e25ffc067a34d1a7b90b111eefd78a37') in 0.0339245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset
  artifactKey: Guid(ac0316ca287ba459492b669ff1317a6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset using Guid(ac0316ca287ba459492b669ff1317a6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c3cac1aab6225c7a7f70151014ae593') in 0.0497561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Neo II.png
  artifactKey: Guid(4639da9b941006a47a6f99999dace0aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Neo II.png using Guid(4639da9b941006a47a6f99999dace0aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1aa8681f1a1fe24e01489df0f29371df') in 0.1486304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_04_2x5.mat
  artifactKey: Guid(ec4732ef0ad692d49904a868c101fdbd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_04_2x5.mat using Guid(ec4732ef0ad692d49904a868c101fdbd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '033df580590311ba2ff3a0e7e62d86a1') in 0.0708428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation.unity
  artifactKey: Guid(4eea59229e01a4644ae2a9b135859269) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation.unity using Guid(4eea59229e01a4644ae2a9b135859269) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3af25bcea252d946565cffcf8a0d94e9') in 0.0391116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BrickA.prefab
  artifactKey: Guid(abc00000000015268156612552762677) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BrickA.prefab using Guid(abc00000000015268156612552762677) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '408124d47b61aff511d48ccc52cd205a') in 0.0417978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Barrel_01.prefab
  artifactKey: Guid(abc00000000000449322208676681965) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Barrel_01.prefab using Guid(abc00000000000449322208676681965) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '71861b804e16ee6fb2b2fe36c63bfb51') in 0.0759609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset.asset
  artifactKey: Guid(b1e8df3a04d888b41bc499cb89a23b6c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset.asset using Guid(b1e8df3a04d888b41bc499cb89a23b6c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b39fda6ad9d7cc20ecab8557a11adbda') in 0.0624799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_08.prefab
  artifactKey: Guid(abc00000000004352778982903466030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_08.prefab using Guid(abc00000000004352778982903466030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4c97f2f64f962ee8ca5d32e58b176ffa') in 0.0347923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/Music Review.wav
  artifactKey: Guid(0b5c8782bcf125f4588f0a0394f5e127) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/Music Review.wav using Guid(0b5c8782bcf125f4588f0a0394f5e127) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58e129a9dc2c1519ecb642b90c864896') in 0.7283515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default I.png
  artifactKey: Guid(b1501032b2cecec43b3e01dd58435333) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default I.png using Guid(b1501032b2cecec43b3e01dd58435333) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33ceba344ec5147a5b52a3c45647d14a') in 0.0621945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_20.mat
  artifactKey: Guid(ba8dc3f4d42c6b347ab36ec79bea5473) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_20.mat using Guid(ba8dc3f4d42c6b347ab36ec79bea5473) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec6ae4b8550e354bbae6f525b94416a4') in 0.6774214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted I.png
  artifactKey: Guid(9fdd583e711479c499fbdc266966a750) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted I.png using Guid(9fdd583e711479c499fbdc266966a750) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '119b4ac129f713d85008bcef6e0b92b3') in 0.0758087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Resources.cs
  artifactKey: Guid(56621a839c1659545928c227afb22cd5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Resources.cs using Guid(56621a839c1659545928c227afb22cd5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f59a507cc2a1c27b939cedfa3496f45f') in 0.0274231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_General.cs
  artifactKey: Guid(37b97ff2aedee6e4db5b28c41a0d2ce8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_General.cs using Guid(37b97ff2aedee6e4db5b28c41a0d2ce8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67a9bf069c55f900139b431920bbae7e') in 0.0328465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility On Dark.png
  artifactKey: Guid(f0bed2f1b3777194782b551c1542377e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility On Dark.png using Guid(f0bed2f1b3777194782b551c1542377e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c40529ebec8aeae088180bdf7dcdbbb') in 0.042152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted L.png
  artifactKey: Guid(781040607c09f7545a7cd6721f3f1fac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted L.png using Guid(781040607c09f7545a7cd6721f3f1fac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '903fee4077b120c467ea1947d059e520') in 0.0684533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Next-Gen II.png
  artifactKey: Guid(d67393ba2ea11e441ac5599ae72b4ae7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Next-Gen II.png using Guid(d67393ba2ea11e441ac5599ae72b4ae7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6558c09e0e947bcdeb52e1ad7b7e6a4c') in 0.070162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Classic I.png
  artifactKey: Guid(7b3529b87732bd74385981a4c3bd5f30) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Classic I.png using Guid(7b3529b87732bd74385981a4c3bd5f30) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27262c9cd3e1635bc3d3b8e79cbe76e3') in 0.083374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Folders.cs
  artifactKey: Guid(d1580b4c5e49c66439bcf2b703e91b38) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Folders.cs using Guid(d1580b4c5e49c66439bcf2b703e91b38) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32f9c11e3c7b2d0d5bc2a62dc9c2a32a') in 0.0337171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.466844 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_31.mat
  artifactKey: Guid(c8db1103110a4d144afb3db6958da1f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_31.mat using Guid(c8db1103110a4d144afb3db6958da1f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8de1fd8d0310e10dbd7889640fa45f49') in 0.1515736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_04_02.mat
  artifactKey: Guid(0c70233fc7961b1448f25618a6887ca2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_04_02.mat using Guid(0c70233fc7961b1448f25618a6887ca2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be1f4c19fdfe69aacb4126b75c9b5b43') in 1.4164762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_Alternative.mat
  artifactKey: Guid(0db6bc2ac5bbb2f41904473feaa28ff4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_Alternative.mat using Guid(0db6bc2ac5bbb2f41904473feaa28ff4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12c7fe9430e8661d29510250f52f967e') in 0.3381097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_29.mat
  artifactKey: Guid(f2c425017dc33054bb6cca57f12ef92e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_29.mat using Guid(f2c425017dc33054bb6cca57f12ef92e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50e9714c603613de392a2968d79eb96d') in 0.96385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_25.png
  artifactKey: Guid(a83da9f05bff9f746b7ed8864b5160cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_25.png using Guid(a83da9f05bff9f746b7ed8864b5160cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '801c7a48835de2a4128f952cb959d397') in 0.0570563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_12.png
  artifactKey: Guid(b0b41c2f2bce7fc4084846340d45800c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_12.png using Guid(b0b41c2f2bce7fc4084846340d45800c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d4096fc0ebf6e5a295a2a9d63ff7e2c') in 0.0691674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_06.mat
  artifactKey: Guid(3af2b44bc015df64c83d1cc1c26773f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_06.mat using Guid(3af2b44bc015df64c83d1cc1c26773f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ebd44b84679ad87a02b3a8484bc666b') in 0.7966929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_AO.png
  artifactKey: Guid(625ebc959374ac443ae3b1303e0dc2b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_AO.png using Guid(625ebc959374ac443ae3b1303e0dc2b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b481d8ce947c873166cebf7e2619d3f') in 0.0892724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/FoliageDiffusionProfile.asset
  artifactKey: Guid(e0df971ea161c4e4c9187e6b7b2f5dc9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/FoliageDiffusionProfile.asset using Guid(e0df971ea161c4e4c9187e6b7b2f5dc9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0808fbc64860791fe83b6973ed707fc3') in 0.0303374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_13.png
  artifactKey: Guid(0fa53a3765299674885559b4a23b9bfd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_13.png using Guid(0fa53a3765299674885559b4a23b9bfd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66f5d808766ec4c3bd51a15c603d0b2d') in 0.0574526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Skybox_03.mat
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Skybox_03.mat using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcdadbde4f15d149dadaa26a67e8f333') in 0.0788023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_02.png
  artifactKey: Guid(e706da7cd53cb8c46aea51a2a382dfb2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_02.png using Guid(e706da7cd53cb8c46aea51a2a382dfb2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc8f86e3b569c92983f29048bedbfd50') in 0.0659819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Mask_Paint.png
  artifactKey: Guid(3ad9bbff9cdbb734fb2edbe48ea83a2e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Mask_Paint.png using Guid(3ad9bbff9cdbb734fb2edbe48ea83a2e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '699a230d4333408a1046d924b982368f') in 0.1183729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_24.mat
  artifactKey: Guid(a181a19d82799604ab3ba877a050d7e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_24.mat using Guid(a181a19d82799604ab3ba877a050d7e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0002f072c0e9d37e5d8b5097b943671') in 0.3243909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_04.png
  artifactKey: Guid(18b424e9428e03f4ebbfdd4877049d07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_04.png using Guid(18b424e9428e03f4ebbfdd4877049d07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e734f9dceadb744e81b1f7334705bbae') in 0.0976656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_31.png
  artifactKey: Guid(858e84fc5ac4e6049b3cbcee54bebee2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_31.png using Guid(858e84fc5ac4e6049b3cbcee54bebee2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c58b3e65072994c7560b40631c5d194') in 0.0897575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_03.png
  artifactKey: Guid(8b18b302104eb074b8841ce9b4c907b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_16_03.png using Guid(8b18b302104eb074b8841ce9b4c907b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d322feef4359815e372c26a58c31947') in 0.0853508 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_04.png
  artifactKey: Guid(b7fcacaf16f9cde448f42abd3203e9a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_04.png using Guid(b7fcacaf16f9cde448f42abd3203e9a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ad17a51e5b755bd5a93f08fccd06b41') in 0.0746595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_02.png
  artifactKey: Guid(084bc3a4c90ed2442b0c70a399408dee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_02.png using Guid(084bc3a4c90ed2442b0c70a399408dee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4e33569ddb91a5f4eb2206c5ff1de4a') in 0.0653995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Variant.prefab
  artifactKey: Guid(4831039ab58ea614ca7b4f076945ccc1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Variant.prefab using Guid(4831039ab58ea614ca7b4f076945ccc1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40bc92cbdaebe5ee134b4c2e19b63f6e') in 0.2014667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_19.png
  artifactKey: Guid(06ddde1aa612923488d4e6621911ce28) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_19.png using Guid(06ddde1aa612923488d4e6621911ce28) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7549a397fe5fe62ff9da5060345deca5') in 0.0959983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_F_BaseColor_Tattoo.png
  artifactKey: Guid(6c95bf4a8fbfc4c4492eae8f63482b4e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_F_BaseColor_Tattoo.png using Guid(6c95bf4a8fbfc4c4492eae8f63482b4e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a528fc5c02b181ddeee020059dd11d7') in 0.1076966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_16.mat
  artifactKey: Guid(4291bf4ad74ef5140a1e51d06b735ed6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_16.mat using Guid(4291bf4ad74ef5140a1e51d06b735ed6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1fca277040dfe9b4255cbf6103e697c') in 0.8596932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_09.png
  artifactKey: Guid(ce40b8adb50b9d44ca9802d47dddabef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_09.png using Guid(ce40b8adb50b9d44ca9802d47dddabef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd9dd643fef0d81de4ba3116731f1745') in 0.086976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Textures/HumanAnimations_ColorPalette.png
  artifactKey: Guid(2c8e7f499d401414b8215275b03a4c66) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Textures/HumanAnimations_ColorPalette.png using Guid(2c8e7f499d401414b8215275b03a4c66) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5a27c92a2f151d215416db392a42e4a') in 0.0845693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Msh)Disks.fbx
  artifactKey: Guid(86ca4af739c56144c9c126e67e42ce01) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Msh)Disks.fbx using Guid(86ca4af739c56144c9c126e67e42ce01) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5fdb743bb41c20b791e3dfeb85d20735') in 0.0974746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BlueClear.mat
  artifactKey: Guid(011adb4644f2d9546a847bcd5820f2d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BlueClear.mat using Guid(011adb4644f2d9546a847bcd5820f2d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a0c0a9798e504952bda8b499ea7e694') in 0.0333223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_05_aegyosal.png
  artifactKey: Guid(7cfe8002687778d4aaf8d8e58b3223d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_05_aegyosal.png using Guid(7cfe8002687778d4aaf8d8e58b3223d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '304e5e879d0f1abf31bc41205ba64e73') in 0.1162001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)JumpingUp.fbx
  artifactKey: Guid(d58d297844693e346ac6a6edad3f4f2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)JumpingUp.fbx using Guid(d58d297844693e346ac6a6edad3f4f2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94004d46fe7e590864515ec5e90a922f') in 0.0965552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchingIdle.fbx
  artifactKey: Guid(91532bfa84527d84bafd6f536e5a614a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchingIdle.fbx using Guid(91532bfa84527d84bafd6f536e5a614a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46e634252c8bf4580d4b36adf0ad749f') in 0.0840676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Blink.anim
  artifactKey: Guid(135df6e678a9ba840af4b8262166de6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Blink.anim using Guid(135df6e678a9ba840af4b8262166de6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe0d6e0940371d78b0df52c03d67b099') in 0.0445526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyWhite.mat
  artifactKey: Guid(eb7a06e79dea43549bb02ccd53a948f7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyWhite.mat using Guid(eb7a06e79dea43549bb02ccd53a948f7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '634a055f29b0e004f9c8c4bebab0813a') in 0.0371316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_07.png
  artifactKey: Guid(6cd5a7bffc0f5f246888ddb692795381) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_07.png using Guid(6cd5a7bffc0f5f246888ddb692795381) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6392769acd32df90e054c3266853b4be') in 0.1386937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_Height.png
  artifactKey: Guid(1d2e903d0c97ab64a8a8e7521e9c725f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_Height.png using Guid(1d2e903d0c97ab64a8a8e7521e9c725f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e1d371a95742ac7a063901be0ac6211') in 0.0775298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Lines.mat
  artifactKey: Guid(95cc11ee576b1364688ab8a034b57069) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Lines.mat using Guid(95cc11ee576b1364688ab8a034b57069) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '82f6335580700fde9f46d11fc1011d3e') in 0.0384461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Clouds.mat
  artifactKey: Guid(e2be606e9870063459c8700747c3b572) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Clouds.mat using Guid(e2be606e9870063459c8700747c3b572) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bdcebd30a52d251a0565419bc64dd127') in 0.0394887 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterAnimated.prefab
  artifactKey: Guid(8714b7f0b38d36540b1b8396757bea0c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)CharacterAnimated.prefab using Guid(8714b7f0b38d36540b1b8396757bea0c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '82c2c83f3b17cac708f7ed0c47c472e9') in 0.0945354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 185

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_36.png
  artifactKey: Guid(bc237ec4e12ed1c43902b03618de33ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_36.png using Guid(bc237ec4e12ed1c43902b03618de33ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76df44053a2b071c3af42de15cdbe812') in 0.0650923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.020052 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab
  artifactKey: Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab using Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '434e32eaf162a66c056b715f8b68867b') in 0.1142775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactShapes_Water_01.png
  artifactKey: Guid(962be15b227f84b449f762d9f11ccedd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactShapes_Water_01.png using Guid(962be15b227f84b449f762d9f11ccedd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b27ebf4c328a46df10aec230dc50d3d') in 0.0645625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_HighlightSlash_2x5_01.psd
  artifactKey: Guid(7d4213cebfe91144ea76d5aeaa753aa5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_HighlightSlash_2x5_01.psd using Guid(7d4213cebfe91144ea76d5aeaa753aa5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0648a364a25344db7fac09606441d372') in 0.0743237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab
  artifactKey: Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab using Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbbd5c59e105fda639712bb8d2ecfb5d') in 0.0710496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterSmallSplash_3x4_01.psd
  artifactKey: Guid(cf4c38ae56f000d458d0f2b2a39e1467) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterSmallSplash_3x4_01.psd using Guid(cf4c38ae56f000d458d0f2b2a39e1467) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ed32eb3cb0998b7595aaacce1561215') in 0.0808146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundFoam_4x8_01.psd
  artifactKey: Guid(a7d637e35a5f2b84095e3c6a1c76826b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundFoam_4x8_01.psd using Guid(a7d637e35a5f2b84095e3c6a1c76826b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd2335c23e871a22c5a775855d3032a9') in 0.0763625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterDots_2x8_01.psd
  artifactKey: Guid(4cd0efa1fe68e364293301a17b356742) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterDots_2x8_01.psd using Guid(4cd0efa1fe68e364293301a17b356742) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f35acecbf9bba25c33446b7f78558150') in 0.0690995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab
  artifactKey: Guid(027f7676d85eaff4db64945788d561c4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab using Guid(027f7676d85eaff4db64945788d561c4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3764a1a80ae8cb0c036e5c0e972d00e') in 0.0672432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_4x5_02.psd
  artifactKey: Guid(9449c3132f6979442b367859c82ff6ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_4x5_02.psd using Guid(9449c3132f6979442b367859c82ff6ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3689e180f04f20409995c52d0e93ea05') in 0.0871928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Circle_01_Dissolve.png
  artifactKey: Guid(fc9f83cfebf2d024d90877954ab5e771) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_Circle_01_Dissolve.png using Guid(fc9f83cfebf2d024d90877954ab5e771) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efd7051ff123fcaa3f614f39ad51efed') in 0.0656907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab
  artifactKey: Guid(3361286819423b84189232b9d797cb59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab using Guid(3361286819423b84189232b9d797cb59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90e5208e7f8152def5f550bdfddcbf75') in 0.0707161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_RainSplash_01.png
  artifactKey: Guid(eeaf392fc62f09b41a9cd3118e22520f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_RainSplash_01.png using Guid(eeaf392fc62f09b41a9cd3118e22520f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e50f427bf7103e01d33c4c5cb57f1d0') in 0.0512365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CausticNoise_01.psd
  artifactKey: Guid(888c04ba97ef18e4d8f38dc837c778ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CausticNoise_01.psd using Guid(888c04ba97ef18e4d8f38dc837c778ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26038950cda5bc8f9fe2328c8168c267') in 0.0502664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CircleImpact_01.png
  artifactKey: Guid(3d2b3395ed8eec5438746b8b1f3045fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CircleImpact_01.png using Guid(3d2b3395ed8eec5438746b8b1f3045fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c50668d20d1a4a695149682d8fc27032') in 0.0506786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.453874 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterTrail_02.png
  artifactKey: Guid(c2d6419b65dc6334ba84932b72929df2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterTrail_02.png using Guid(c2d6419b65dc6334ba84932b72929df2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f917626433c0ec888a0fb53fde2391ea') in 0.083727 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_Katana_Blade@Skill_I.FBX
  artifactKey: Guid(4fd5b458b4d881c4f9370a13a8129ceb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_Katana_Blade@Skill_I.FBX using Guid(4fd5b458b4d881c4f9370a13a8129ceb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bfab528b269ceea780805a4b86d327d6') in 0.0652763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralWaterSlide_5x5_01.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralWaterSlide_5x5_01.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b63130156713777527cdea169783746') in 0.1199931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Crack.png
  artifactKey: Guid(9873cc868bdc1db418ed20ca3febc7d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)Crack.png using Guid(9873cc868bdc1db418ed20ca3febc7d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06b665baf2756e06c173596201c5d6bc') in 0.092264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_01.png
  artifactKey: Guid(fb3c3bbe52081b04dbcca205911ac341) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_01.png using Guid(fb3c3bbe52081b04dbcca205911ac341) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3385ccc2536f70386c49a2056ba300ee') in 0.0575213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Transparent.png
  artifactKey: Guid(9819619c7de5fcc478ec39ec5fbc513e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Transparent.png using Guid(9819619c7de5fcc478ec39ec5fbc513e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ca47685e60f2aca0f7cd749e96e224b') in 0.1279666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_06.mat
  artifactKey: Guid(e3cdfbdcef7cde84b9388a37c2ace8b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_06.mat using Guid(e3cdfbdcef7cde84b9388a37c2ace8b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a71664b1772985c817de31c6eb56fb06') in 0.8530105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blonde.png
  artifactKey: Guid(7e724b9a3e8b31349a432f9f14c329e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blonde.png using Guid(7e724b9a3e8b31349a432f9f14c329e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7548b06e32f47f0a2654729f596248ec') in 0.105814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_PianoAiry_1.wav
  artifactKey: Guid(986c6f1cab7189641bbbbb89d6bb2f88) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_PianoAiry_1.wav using Guid(986c6f1cab7189641bbbbb89d6bb2f88) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f55f0cfee1e6c4d3b5e0361dd40c9856') in 0.2072898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01.png
  artifactKey: Guid(deeae6a5d191c6648be8f3b7daa3b259) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01.png using Guid(deeae6a5d191c6648be8f3b7daa3b259) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4483ab7c02adc4fb2be9d39c11b094ae') in 0.0714202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_F.FBX
  artifactKey: Guid(b3fbf7fea81c6dd40b32a92f18380f5c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_F.FBX using Guid(b3fbf7fea81c6dd40b32a92f18380f5c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '528f7694cca228ac57b376d4437d0ab5') in 0.0554651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Rising_1.wav
  artifactKey: Guid(58fee5e936f814444a199dd19cea208a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Rising_1.wav using Guid(58fee5e936f814444a199dd19cea208a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fa564c105cd0fadf070a71848d19b95e') in 0.1627893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmallWaterGroundSplash_01_4x4.png
  artifactKey: Guid(99bba36b825c384499e68294e55d613b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmallWaterGroundSplash_01_4x4.png using Guid(99bba36b825c384499e68294e55d613b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2da151ebe24c5a5ea320080db1e70357') in 0.0535416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLateralImpact_4x3_01.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLateralImpact_4x3_01.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c792a858d72d8cb375eb7d568a4b9d98') in 0.0634763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/11__Run_To_Fast_Run/M_Big_Sword@Run_to_Fast_Run_ver_A.FBX
  artifactKey: Guid(4a729580bcf9b28419cbf86df9a8078e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/11__Run_To_Fast_Run/M_Big_Sword@Run_to_Fast_Run_ver_A.FBX using Guid(4a729580bcf9b28419cbf86df9a8078e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2034b8dc9cf23af589f29a5a377e1b03') in 0.0792203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterMask_4x6_01_SoftRender.png
  artifactKey: Guid(5823796e88a3ec944af01f6301d473a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterMask_4x6_01_SoftRender.png using Guid(5823796e88a3ec944af01f6301d473a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27da1a1a13152e64d37cfaf8de71f524') in 0.080285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TiledWater_3x4_01_SoftRender.png
  artifactKey: Guid(f1cee8d3d585e4543984817531835070) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TiledWater_3x4_01_SoftRender.png using Guid(f1cee8d3d585e4543984817531835070) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89b7de95badbf7a2ec5a8e0eb46aa020') in 0.0471895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterWhip_4x4_02.png
  artifactKey: Guid(d0a87af86fe6a264e888a72d87ca6199) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterWhip_4x4_02.png using Guid(d0a87af86fe6a264e888a72d87ca6199) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b1de003217abbd9ecc42f74d99b23c81') in 0.0459859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_A_To_Crouch_Root.FBX
  artifactKey: Guid(02dd99492c374d94d8e35dec4b5cd041) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_A_To_Crouch_Root.FBX using Guid(02dd99492c374d94d8e35dec4b5cd041) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3f6a1969fe52f7e30cda0c36dc1f7c2') in 0.0603219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_22.mat
  artifactKey: Guid(b546916bbe1571043ae6ec5c7fd88d99) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_22.mat using Guid(b546916bbe1571043ae6ec5c7fd88d99) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b494619863020ef3ccb2750a90e20e30') in 0.1691564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Red.png
  artifactKey: Guid(f3e76a1a1c07a144298fe38d0f654f05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Red.png using Guid(f3e76a1a1c07a144298fe38d0f654f05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ffd82aa2bdc98f9a7526689080a5844') in 0.0808888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_03.mat
  artifactKey: Guid(86c1147a7d6049c448c5e1b0e75e0b82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_03.mat using Guid(86c1147a7d6049c448c5e1b0e75e0b82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8804a8569d83aa0e24620427d2deccca') in 0.1702102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_A.FBX
  artifactKey: Guid(83dea9c104495b940b4a0f36baaaad57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_A.FBX using Guid(83dea9c104495b940b4a0f36baaaad57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9faba269f62139e85264aa1ad4ef996') in 0.0697431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_3.wav
  artifactKey: Guid(ab3cce28604f4484bb270b5d26ebde37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_3.wav using Guid(ab3cce28604f4484bb270b5d26ebde37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58471dfaa6639330ad5cde96d38ccd51') in 0.1509344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_3.wav
  artifactKey: Guid(05365dd9a76c0cd44932c57b03376654) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_3.wav using Guid(05365dd9a76c0cd44932c57b03376654) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23551ddbe5b79a2f0a75b7ad6866062b') in 0.1696729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_3.wav
  artifactKey: Guid(dec771062b91a5f42b65c0e401049978) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_3.wav using Guid(dec771062b91a5f42b65c0e401049978) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a5f85db5f54e5e03dc6014f3953db91') in 0.1017319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_2_Inplace.FBX
  artifactKey: Guid(2190a69795dd9344aa1c2a0acc351a28) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_2_Inplace.FBX using Guid(2190a69795dd9344aa1c2a0acc351a28) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c7ddaf205b7d3d140948f45e56050c0') in 0.0697171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_End_ZeroHeight.FBX
  artifactKey: Guid(f2fece3d25e7a0641a6f021e2f83487f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_End_ZeroHeight.FBX using Guid(f2fece3d25e7a0641a6f021e2f83487f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c6dd476201694930c58e353fca58135') in 0.0918125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_2.FBX
  artifactKey: Guid(cb4cc4715657b9847870f490d2b2dc0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_2.FBX using Guid(cb4cc4715657b9847870f490d2b2dc0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39180947edce5bf119795be4886e2272') in 0.0627555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000233 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_07.mat
  artifactKey: Guid(25b1b6e3941e79b4eae19b9ba530a795) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_07.mat using Guid(25b1b6e3941e79b4eae19b9ba530a795) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a629b0f535bbbf4fcdd8e87cf062c481') in 0.2141843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_B.FBX
  artifactKey: Guid(42277b136f950b74bac33b49026a25f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_B.FBX using Guid(42277b136f950b74bac33b49026a25f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80969fbb0ca96713bb56176859664cda') in 0.121226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_3.wav
  artifactKey: Guid(8f1b95171bdb23c4685c528182a9e0a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Big_3.wav using Guid(8f1b95171bdb23c4685c528182a9e0a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3faf48c0bcff252a38fec905fd191e7f') in 0.1694441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_Fast_ver_B.FBX
  artifactKey: Guid(6b6825a4236a9da43a07098be1d6d234) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_Fast_ver_B.FBX using Guid(6b6825a4236a9da43a07098be1d6d234) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f14aa27cc7795154d6f84d45197ccf0') in 0.0660696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Specular.png
  artifactKey: Guid(752dbc3b5c10c544c8aff72778b5ea3d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Specular.png using Guid(752dbc3b5c10c544c8aff72778b5ea3d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5177ec1532c983f55185ce837871e80') in 0.0985276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_22.mat
  artifactKey: Guid(cb099159095f57045a50f96f3b73645e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_22.mat using Guid(cb099159095f57045a50f96f3b73645e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '358a058e17ac75adbb4f9dd55fdfe299') in 0.1873365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL45.FBX
  artifactKey: Guid(411788a811ba2e649957582f31a6d1cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL45.FBX using Guid(411788a811ba2e649957582f31a6d1cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9db87413bb970e6143405b03afdcebce') in 0.0670455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/1__Idle/M_Big_Sword@Idle_Turn_Root.FBX
  artifactKey: Guid(adace3d68fe14d2419de028438e06605) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/1__Idle/M_Big_Sword@Idle_Turn_Root.FBX using Guid(adace3d68fe14d2419de028438e06605) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1ae87d10d485af2483065da01761c3b') in 0.0732797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_Damage_ver_B.FBX
  artifactKey: Guid(d1d184b3fccf8cf4695e2bfc669b2a41) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_Damage_ver_B.FBX using Guid(d1d184b3fccf8cf4695e2bfc669b2a41) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e45a973700c1936e71c8e1564c8ecaec') in 0.0634883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_Fast_ver_A.FBX
  artifactKey: Guid(27cc3932df27d584ea5a35b722ebf593) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_Fast_ver_A.FBX using Guid(27cc3932df27d584ea5a35b722ebf593) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '526aedf1a1d3108be0c73c99ef69da2c') in 0.0577953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Root.FBX
  artifactKey: Guid(7f8d6293100b38b468059ab6bbc1250e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Root.FBX using Guid(7f8d6293100b38b468059ab6bbc1250e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b44a6ffdd8e9a1197ec6aabdf11dc1da') in 0.0586255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down.FBX
  artifactKey: Guid(634da706bbda3a846b3a3bb311165fc7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down.FBX using Guid(634da706bbda3a846b3a3bb311165fc7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '980842c3ef55c3310af0573519c4c1ad') in 0.0693372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_4_Inplace.FBX
  artifactKey: Guid(d563f10e8e203744f9f14df93869f6bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_4_Inplace.FBX using Guid(d563f10e8e203744f9f14df93869f6bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0fb70b3a370ac79a6d159841556e4674') in 0.0518281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A_Root.FBX
  artifactKey: Guid(2b39de01d3a13ba45a39361da667673e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A_Root.FBX using Guid(2b39de01d3a13ba45a39361da667673e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85264dac3e7546d4b7a09e30eb1e7703') in 0.0607353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die_Loop.FBX
  artifactKey: Guid(b5ac8ed5fb6cd85408a7de663521ab91) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die_Loop.FBX using Guid(b5ac8ed5fb6cd85408a7de663521ab91) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cbecdaaf7a431bd79c18b7cf2daf5411') in 0.0613154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Root_ver_A.FBX
  artifactKey: Guid(97633c6a10d68c54b840f34c5386c78d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/4__Run/M_katana_Blade@Run_Root_ver_A.FBX using Guid(97633c6a10d68c54b840f34c5386c78d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e0ef7c2ede2cb2675535e7e046f23dc') in 0.0742161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_C.FBX
  artifactKey: Guid(0f720f5927cf4f64ba0c3bef1e143d77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_C.FBX using Guid(0f720f5927cf4f64ba0c3bef1e143d77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf56f1731b63614cb9d75339b6eeba5c') in 0.061553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/3__Dash_Attack/M_Big_Sword@Dash_Attack_ver_B.FBX
  artifactKey: Guid(ea666a0024e95544eb16d6b6e817f80c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/3__Dash_Attack/M_Big_Sword@Dash_Attack_ver_B.FBX using Guid(ea666a0024e95544eb16d6b6e817f80c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cda7ece8a053958c88bf30554202453') in 0.0656405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/3__Dash_Attack/M_Big_Sword@Dash_Attack_ver_A.FBX
  artifactKey: Guid(aeadecb385108a04383a024f6c6b2bab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/3__Dash_Attack/M_Big_Sword@Dash_Attack_ver_A.FBX using Guid(aeadecb385108a04383a024f6c6b2bab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efd46bf2deb804e1b45cbf485f0def45') in 0.0714626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/3__Left/M_Big_Sword@Damage_Left_Small_ver_A.FBX
  artifactKey: Guid(6e47952fbb19e3f46bbf5c515788c6d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/3__Left/M_Big_Sword@Damage_Left_Small_ver_A.FBX using Guid(6e47952fbb19e3f46bbf5c515788c6d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd69ad5fa4fa5a3605c5bc221578571f5') in 0.0603327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_4_Inplace.FBX
  artifactKey: Guid(26ed62de90b9db74aa52cf8dd29d7424) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_4_Inplace.FBX using Guid(26ed62de90b9db74aa52cf8dd29d7424) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99a546bd9fd06353857aaacc27cc0af5') in 0.0527185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Start.FBX
  artifactKey: Guid(146b9e889fbe5994d8d22f6d5e04eaf2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Start.FBX using Guid(146b9e889fbe5994d8d22f6d5e04eaf2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0133e9facbb0a5770335e1fbb06062bf') in 0.0652058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_B.FBX
  artifactKey: Guid(736bbeded6c422b42a82032094a62968) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_B.FBX using Guid(736bbeded6c422b42a82032094a62968) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19fb648b42637f509b8fbd1111d8b517') in 0.0857067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterDots_4x4_01.png
  artifactKey: Guid(80afbf8448f8a4b429a87054863192c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterDots_4x4_01.png using Guid(80afbf8448f8a4b429a87054863192c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ffc93a383c4767e17e2c2ccad267da3') in 0.0573748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_2.FBX
  artifactKey: Guid(883ce4f3a06dcaf46854f40759547a67) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_2.FBX using Guid(883ce4f3a06dcaf46854f40759547a67) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c1e4ab85dd25f296d923d0b1033da69') in 0.0701714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Interact_1.wav
  artifactKey: Guid(2249ed5065e41d4478d1f69e54d7e7a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Interact_1.wav using Guid(2249ed5065e41d4478d1f69e54d7e7a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f09e0f9fba19a0f071d70bc9dac3c0fb') in 0.165788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_D.FBX
  artifactKey: Guid(6424d7d8af5e9dd41bf2bd9525aec3c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_D.FBX using Guid(6424d7d8af5e9dd41bf2bd9525aec3c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2df8ae5c959cccadbd3869a8c8d658dc') in 0.0756673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_High_KnockDown.FBX
  artifactKey: Guid(05cce434701f2b947a415009133b69de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_High_KnockDown.FBX using Guid(05cce434701f2b947a415009133b69de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a871f4b2c150110ab4c19a863933d1c') in 0.0667739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1.FBX
  artifactKey: Guid(115a73c445c1d0947b07fbbcfc56fe2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1.FBX using Guid(115a73c445c1d0947b07fbbcfc56fe2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e81ea6b489acb8a38f658a54826fbaf') in 0.0732645 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_A_Root.FBX
  artifactKey: Guid(c1cd36f7d24e64746b65d65ef794b5d1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_A_Root.FBX using Guid(c1cd36f7d24e64746b65d65ef794b5d1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0698c316813de25470fa9f49d0c134c') in 0.0558689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_High_KnockDown_ZeroHeight.FBX
  artifactKey: Guid(44c2aee8f1a79564a9e628e79e285af9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_High_KnockDown_ZeroHeight.FBX using Guid(44c2aee8f1a79564a9e628e79e285af9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '374397f5ce74efd4f71b9bbd2fb9b014') in 0.057115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5_Attach.FBX
  artifactKey: Guid(d6f1038bb2ad0da479bcfa7b10d537fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5_Attach.FBX using Guid(d6f1038bb2ad0da479bcfa7b10d537fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2cbdffee4a66c22b054eab987b17938d') in 0.0593556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop_ZeroHeight_Z0.FBX
  artifactKey: Guid(75dd46fa8045c98408ff5824b966546a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop_ZeroHeight_Z0.FBX using Guid(75dd46fa8045c98408ff5824b966546a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc6eeb8c4b5c44fd4cd247efcd08a11b') in 0.0953275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_short distance.FBX
  artifactKey: Guid(08718ccd5eb2b874c8c28e7315065bfe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_short distance.FBX using Guid(08718ccd5eb2b874c8c28e7315065bfe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56f37c672915759463b556dd0d084237') in 0.0954989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_ver_A.FBX
  artifactKey: Guid(c204bd79cf696214fb61f3dfbe9df200) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_ver_A.FBX using Guid(c204bd79cf696214fb61f3dfbe9df200) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e596e44d82780c4fedc73be46a697c4b') in 0.056538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_4_Inplace.FBX
  artifactKey: Guid(3474f59a4c3e93a49a81a7c67caf0869) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_4_Inplace.FBX using Guid(3474f59a4c3e93a49a81a7c67caf0869) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ba89d3309005a0f984c3516b7580cd6') in 0.0669022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Block.FBX
  artifactKey: Guid(6ae2cee383a059940bfab487e7e55171) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Block.FBX using Guid(6ae2cee383a059940bfab487e7e55171) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ac6bf92f5c22cabd01bf5930c52fc2a') in 0.2092433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop.FBX
  artifactKey: Guid(72ae247dcf6cf5042bb06cfcba577848) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop.FBX using Guid(72ae247dcf6cf5042bb06cfcba577848) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05151ebf61e8214147352a51310c1fab') in 0.0638327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/3__Dash_Attack/M_katana_Blade@Dash_Attack_ver_B.FBX
  artifactKey: Guid(c9882739488c4a44cb25e41defe55455) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/3__Dash_Attack/M_katana_Blade@Dash_Attack_ver_B.FBX using Guid(c9882739488c4a44cb25e41defe55455) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d54dd1ffb63482d875ad71e7576d59b') in 0.0831918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1B.FBX
  artifactKey: Guid(7453379ea4350304fb1b17077af19160) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_1B.FBX using Guid(7453379ea4350304fb1b17077af19160) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53b55fb4f26c26198cdf9aa03643b845') in 0.0610099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.FBX
  artifactKey: Guid(82fe4646473d3404b85817443ca69584) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.FBX using Guid(82fe4646473d3404b85817443ca69584) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27b7fa68f24cd55fc7808aa456b3e920') in 1.1896945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.FBX
  artifactKey: Guid(927e4516e516c3149a5fbf41b9ea047f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.FBX using Guid(927e4516e516c3149a5fbf41b9ea047f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '424e0c9a80104a9fe16076f64d71d0a9') in 1.7654117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Corner_B.prefab
  artifactKey: Guid(abc00000000018252124555131516279) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Corner_B.prefab using Guid(abc00000000018252124555131516279) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5fbecb7651102f781e18705c03417d7') in 0.0828472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bar_Shelf.prefab
  artifactKey: Guid(abc00000000008531406406469043789) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bar_Shelf.prefab using Guid(abc00000000008531406406469043789) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34df51692032b3628ee9c95dae71fa48') in 0.0760909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_34.prefab
  artifactKey: Guid(abc00000000006079690300159667556) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_34.prefab using Guid(abc00000000006079690300159667556) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc737da402ad857d685676c7c58e22f7') in 0.1241456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.prefab
  artifactKey: Guid(f921404edb79d574e9182f51fa4d3420) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.prefab using Guid(f921404edb79d574e9182f51fa4d3420) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d7514ae98ed6ffb8f6a973e59c416a3') in 0.4550998 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_Flag_03.prefab
  artifactKey: Guid(abc00000000008533068516358505298) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_Flag_03.prefab using Guid(abc00000000008533068516358505298) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbbd2d1bbc19856f72a7265e0ba3a0d4') in 0.0699047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Smash_Double.FBX
  artifactKey: Guid(1d5a0011942d4f045a96eb477ad0486f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Smash_Double.FBX using Guid(1d5a0011942d4f045a96eb477ad0486f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e49edbf106fa5e1464376176b597021') in 0.0668032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_03.prefab
  artifactKey: Guid(abc00000000018289202013337733741) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_03.prefab using Guid(abc00000000018289202013337733741) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f97db8fcf403c01bd8726809dd6021b') in 0.1296964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B.prefab
  artifactKey: Guid(abc00000000009243210316432420041) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_B.prefab using Guid(abc00000000009243210316432420041) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b65ecfd76bdffb73ebcf8012b8dcd1e9') in 0.0659952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_02.prefab
  artifactKey: Guid(abc00000000012753242278391718196) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_02.prefab using Guid(abc00000000012753242278391718196) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2cdadbb9579e2fda9e14da068c4b4d2') in 0.0612541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_05.prefab
  artifactKey: Guid(abc00000000007776285115681869499) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_05.prefab using Guid(abc00000000007776285115681869499) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fed9c1bbdd1a6454fc08cc8d975a78b1') in 0.0711996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.FBX
  artifactKey: Guid(0d15f9a577845174d8607258796c0ff2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.FBX using Guid(0d15f9a577845174d8607258796c0ff2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2ed410ff80c73912ff8d347a110716b') in 0.6514734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bucket_01.prefab
  artifactKey: Guid(abc00000000007708634073316547047) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bucket_01.prefab using Guid(abc00000000007708634073316547047) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25a1857f8121d3978ba86c2470e17852') in 0.0630262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01.prefab
  artifactKey: Guid(abc00000000016380884341167281589) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01.prefab using Guid(abc00000000016380884341167281589) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c6fa41bf4f4af56b1aa92963e035f93') in 0.0678504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04_1.prefab
  artifactKey: Guid(abc00000000017747089960328179570) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04_1.prefab using Guid(abc00000000017747089960328179570) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ceda91678a9d480ab9b45cc9ca0bc4af') in 0.0853041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_15.fbx
  artifactKey: Guid(abc00000000000743389470854836776) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_15.fbx using Guid(abc00000000000743389470854836776) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c529ac43de3ee2774c0b725f495641f') in 0.098142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_01c.prefab
  artifactKey: Guid(abc00000000007421162951652076853) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_01c.prefab using Guid(abc00000000007421162951652076853) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0aebe8e853df6d5a97f7f03f6a3ccbb') in 0.0665397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_16.prefab
  artifactKey: Guid(abc00000000017433115186810166673) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_16.prefab using Guid(abc00000000017433115186810166673) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85364332c42180bdc1895d8884e0ee55') in 0.0674244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_4M.prefab
  artifactKey: Guid(abc00000000003994021736014691917) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_4M.prefab using Guid(abc00000000003994021736014691917) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0867be1bffd11343c6ffd66eff823a3') in 0.0754065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CurtainWall_SeamHider.prefab
  artifactKey: Guid(abc00000000007989197903566043416) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CurtainWall_SeamHider.prefab using Guid(abc00000000007989197903566043416) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '449ed83f069a6402fbcadc42cc86402a') in 0.0643174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 40

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConeRoof_01.prefab
  artifactKey: Guid(abc00000000017983411432195826884) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConeRoof_01.prefab using Guid(abc00000000017983411432195826884) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2841a162ac16d86271cd48d170a88267') in 0.0987417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_InsideCorner_01.prefab
  artifactKey: Guid(abc00000000009466584702714161502) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_InsideCorner_01.prefab using Guid(abc00000000009466584702714161502) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee10964cafcd1569dabb762ead5a6bb9') in 0.0774542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_A_02.prefab
  artifactKey: Guid(abc00000000000338239502182837735) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_A_02.prefab using Guid(abc00000000000338239502182837735) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8106e90da588acb5d7409d58293224c') in 0.0844679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_03.prefab
  artifactKey: Guid(abc00000000015763950805579918012) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_03.prefab using Guid(abc00000000015763950805579918012) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b59f54bcdd0e84a39a28f548eff060be') in 0.0656888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron.prefab
  artifactKey: Guid(abc00000000012840463066036346035) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cauldron.prefab using Guid(abc00000000012840463066036346035) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d366238a8ab66f8c95f026293cd523e') in 0.063668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med.prefab
  artifactKey: Guid(abc00000000001636842391021204683) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Med.prefab using Guid(abc00000000001636842391021204683) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2596e9abd10978e7211738d11c60c687') in 0.0619894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_direction.png
  artifactKey: Guid(15edbbd6c2c6bc949a177b8d006a71bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_direction.png using Guid(15edbbd6c2c6bc949a177b8d006a71bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46f8485d2bcb7e11dd1c0e5cee43cbe3') in 0.1032236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_FirePit_A_01.mat
  artifactKey: Guid(abc00000000014673551330848278965) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_FirePit_A_01.mat using Guid(abc00000000014673551330848278965) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f30e13d28c07cff736ad8116eedc3d1') in 0.0786628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_7.prefab
  artifactKey: Guid(abc00000000015058747550851706431) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_7.prefab using Guid(abc00000000015058747550851706431) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c0cd5f5bf4e0dfe62844b14f5aeeb95') in 0.0689306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_19.fbx
  artifactKey: Guid(abc00000000007329495030990340940) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_19.fbx using Guid(abc00000000007329495030990340940) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7ba257ea39c8a350a4205df4dc53a45') in 0.0980216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_13.prefab
  artifactKey: Guid(abc00000000007970159593764176638) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_13.prefab using Guid(abc00000000007970159593764176638) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5833c3d3232e306a22cb2615ab02255a') in 0.0791266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02.prefab
  artifactKey: Guid(abc00000000006699053618561820555) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02.prefab using Guid(abc00000000006699053618561820555) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3464919eb38aae967ebff8ec2576d22') in 0.0843297 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01_1.prefab
  artifactKey: Guid(abc00000000016855284128515356830) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01_1.prefab using Guid(abc00000000016855284128515356830) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '881a9a0435ce01dd06d6945f3eb617cc') in 0.0551886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Saw.prefab
  artifactKey: Guid(abc00000000012955199857811331708) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Saw.prefab using Guid(abc00000000012955199857811331708) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b49701d592639bc0759a4dda6a7acbab') in 0.0740501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12.prefab
  artifactKey: Guid(abc00000000017704846649272887479) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12.prefab using Guid(abc00000000017704846649272887479) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '478eb00d9d80b7a25cdf7b56bb326ca0') in 0.0688768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_01.prefab
  artifactKey: Guid(abc00000000007014482728816473322) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_01.prefab using Guid(abc00000000007014482728816473322) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d89232ce46aa58f527cad049daf5d22') in 0.0596582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_B.prefab
  artifactKey: Guid(abc00000000006888821354428298396) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_B.prefab using Guid(abc00000000006888821354428298396) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2303edf50348770468a180fe0237ab7f') in 0.0639034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C.prefab
  artifactKey: Guid(abc00000000010483527000914307316) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C.prefab using Guid(abc00000000010483527000914307316) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d54788c0d01033675b5c02d464f8233') in 0.0615945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_E.prefab
  artifactKey: Guid(abc00000000008242963507806250587) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_E.prefab using Guid(abc00000000008242963507806250587) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbeca9672eb6b035a1573000b003c5a0') in 0.0755899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08_1.prefab
  artifactKey: Guid(abc00000000017152206545133094477) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_08_1.prefab using Guid(abc00000000017152206545133094477) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc898413c8a4de6f3b16f496090bd22b') in 0.0893249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01.prefab
  artifactKey: Guid(abc00000000017935320353017186305) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01.prefab using Guid(abc00000000017935320353017186305) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d1b9abdb6d175989962c1e2048e5425') in 0.0749903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x2.prefab
  artifactKey: Guid(abc00000000006645477426135240256) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x2.prefab using Guid(abc00000000006645477426135240256) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d651fec2e94445d757be05d4390fa64') in 0.0722488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bark_01_basecolor.PNG
  artifactKey: Guid(113ddf425a699fd49a700b5ada151c7f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bark_01_basecolor.PNG using Guid(113ddf425a699fd49a700b5ada151c7f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae0fb91f5a38f725173146d489b07583') in 0.075249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_04.prefab
  artifactKey: Guid(abc00000000016516650418989987030) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_04.prefab using Guid(abc00000000016516650418989987030) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67a9b0fbb3150adb4870b7ab74afb1aa') in 0.0723933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_4M.prefab
  artifactKey: Guid(abc00000000002125231476973929255) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_4M.prefab using Guid(abc00000000002125231476973929255) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e5d492c54f6d4ae15dd394790da58e9') in 0.0605816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04.prefab
  artifactKey: Guid(abc00000000011950325879607030052) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04.prefab using Guid(abc00000000011950325879607030052) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35f26f58194ee8088a96f8ff597f2703') in 0.0580201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ancient_greek_stone_wall_ORMH.PNG
  artifactKey: Guid(924b55748ff59d5438344e307a2148da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ancient_greek_stone_wall_ORMH.PNG using Guid(924b55748ff59d5438344e307a2148da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '079aa1fbfb3a9d65abf056d51dc1ab8f') in 0.0417306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_Emissive.PNG
  artifactKey: Guid(3116dda305497c946976760f80d10538) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_Emissive.PNG using Guid(3116dda305497c946976760f80d10538) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31502b33bfb28561b40ff5444789ce9f') in 0.0781948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_01.prefab
  artifactKey: Guid(abc00000000017237621719740605727) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_01.prefab using Guid(abc00000000017237621719740605727) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a11582d02f689883f942a92dbec09fe') in 0.0587365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x6_01.prefab
  artifactKey: Guid(abc00000000015161224096820761855) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x6_01.prefab using Guid(abc00000000015161224096820761855) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5c902a6b4093d24801a25b50b764f57') in 0.0641788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_CandleFire_01.PNG
  artifactKey: Guid(ae093899fbb83dc409a3ac5c86ef76e2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_CandleFire_01.PNG using Guid(ae093899fbb83dc409a3ac5c86ef76e2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f224e792e7ad2b079cdcf5942136e03') in 0.0676325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_MRAO.PNG
  artifactKey: Guid(3fcbd7845432c0b489aaae3aed0a286e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_MRAO.PNG using Guid(3fcbd7845432c0b489aaae3aed0a286e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5dd1225e0f7f4faac3bdaeb780b3c5eb') in 0.0763089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_BaseColor.PNG
  artifactKey: Guid(e495b5c78f2c85d46b380738e5350963) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_BaseColor.PNG using Guid(e495b5c78f2c85d46b380738e5350963) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76ab11690aa1d5215d6eaf9de8816925') in 0.0469909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_Mask_2.PNG
  artifactKey: Guid(a05af7bd556b2574893ffa728cbad646) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_Mask_2.PNG using Guid(a05af7bd556b2574893ffa728cbad646) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7fcca2dc7bc1c14d38cfa6a02cd018e') in 0.0461253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_A.PNG
  artifactKey: Guid(55cf28bcd87e47c4daac446f1e379c1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_A.PNG using Guid(55cf28bcd87e47c4daac446f1e379c1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed64e9e15e769f7cbcdcb78b0b813206') in 0.0442416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wheel.prefab
  artifactKey: Guid(abc00000000000591457375326423934) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wheel.prefab using Guid(abc00000000000591457375326423934) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff7c2f1b0702c48c4ed27c832998c3cb') in 0.0652605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Herringbone_4M.prefab
  artifactKey: Guid(abc00000000006564929730782368232) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Herringbone_4M.prefab using Guid(abc00000000006564929730782368232) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c510adcd40b623642b082bf83db7ff7e') in 0.0686901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_ORMH.PNG
  artifactKey: Guid(f8920925f07a0274cb1798b87c907ef9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_ORMH.PNG using Guid(f8920925f07a0274cb1798b87c907ef9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fa9ec2c0907f598dd2f44327c11d0b0f') in 0.0601182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_N.PNG
  artifactKey: Guid(20fa6f00caa2423449fc51891c5dd8b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_N.PNG using Guid(20fa6f00caa2423449fc51891c5dd8b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b635ccc3f41b297501576e33f01ec35b') in 0.0902456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_normal.PNG
  artifactKey: Guid(fc0d2b1f9ebdf1349af268c988918a5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_normal.PNG using Guid(fc0d2b1f9ebdf1349af268c988918a5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '033c695734edb791f0cdda22200342b5') in 0.060946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_N.PNG
  artifactKey: Guid(2fe1627768477184e9ed473cd0014d9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_N.PNG using Guid(2fe1627768477184e9ed473cd0014d9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef9cd768ba96ec1821dfbc7400226064') in 0.0711535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DefalutMask.PNG
  artifactKey: Guid(fb6bafc76e058ea448ece9755c565a9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DefalutMask.PNG using Guid(fb6bafc76e058ea448ece9755c565a9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09132519aa64e227a1214e7b723968a6') in 0.0737141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_normal.PNG
  artifactKey: Guid(773703a91696cd34a8e6d31703aec43b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_normal.PNG using Guid(773703a91696cd34a8e6d31703aec43b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f99daec37acc6fc9412f8faab4cefbe') in 0.0610798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_04_OCG.PNG
  artifactKey: Guid(e471b094dc956ea4784f2a40056745f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_04_OCG.PNG using Guid(e471b094dc956ea4784f2a40056745f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '282cb59d56a14adfc960f98dbfb548f9') in 0.0639116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneFence_Base_normal.PNG
  artifactKey: Guid(6b6f4f184c2c89b40983a836a5a9b7fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneFence_Base_normal.PNG using Guid(6b6f4f184c2c89b40983a836a5a9b7fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f04929d162db4d6def4b6e2a7ee76af') in 0.0665211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Water_TilingNormal_Waves_02.PNG
  artifactKey: Guid(d37d7930ad26dd14fa44fd8a0b382ff6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Water_TilingNormal_Waves_02.PNG using Guid(d37d7930ad26dd14fa44fd8a0b382ff6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '204e89d6b0b736a0d76e7afb71322127') in 0.0769665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_Sandstone_N.PNG
  artifactKey: Guid(8d544ce2cf48b1b4ab1e8519db2fa468) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_Sandstone_N.PNG using Guid(8d544ce2cf48b1b4ab1e8519db2fa468) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3dbc62237cc0d01c7dbc0d6a8c9e2fae') in 0.0636017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_BaseColor.PNG
  artifactKey: Guid(0a8eddb82cfed7e43af6860da74285d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_BaseColor.PNG using Guid(0a8eddb82cfed7e43af6860da74285d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4b2c7c2a1cdf3107da0da8a87e2ec58') in 0.0422234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Mountain_A_Normal.PNG
  artifactKey: Guid(f2e4472fa17cc5440b5313e35385962c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Mountain_A_Normal.PNG using Guid(f2e4472fa17cc5440b5313e35385962c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '264f4bfeaddc358ad07fd0e4e7de63b0') in 0.0687217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_basecolor.PNG
  artifactKey: Guid(01be841870c233b4c8dc977da1ccc73d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_New_basecolor.PNG using Guid(01be841870c233b4c8dc977da1ccc73d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b7a2265c8d217af82f7f792cb8d8801') in 0.0463612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_normal.PNG
  artifactKey: Guid(953d11c81b7341f4fad82d978b629fb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_normal.PNG using Guid(953d11c81b7341f4fad82d978b629fb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59aeab18e0f206fe9e017e16b2fe8657') in 0.0685963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_Snow_M.PNG
  artifactKey: Guid(6ea3086a17ba731439338a5465b97f56) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_Snow_M.PNG using Guid(6ea3086a17ba731439338a5465b97f56) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca576790c64eb199d31dabbe3c7326d8') in 0.0652682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_ForwardLeft.controller
  artifactKey: Guid(46979143c4bc73f47a6b7942832af2f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_ForwardLeft.controller using Guid(46979143c4bc73f47a6b7942832af2f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98557e8c851de773196e9fd6d8bcff3f') in 0.0350822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Forward.controller
  artifactKey: Guid(7faeb0aa409545f4b8ce6af989c66fbd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Forward.controller using Guid(7faeb0aa409545f4b8ce6af989c66fbd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7e48882ba6b643d5496f08f8bb8599b') in 0.0371071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL>
  artifactKey: Guid(9508ccae6dd32b54cb61bd81eda00380) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL> using Guid(9508ccae6dd32b54cb61bd81eda00380) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c84a56c39b7365b095e4015361bdee1b') in 0.0616055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_ORMH.PNG
  artifactKey: Guid(9ea0b29e93b3ca74091f6ecf7041ae65) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_ORMH.PNG using Guid(9ea0b29e93b3ca74091f6ecf7041ae65) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96c850de3d30e7027765cccfbd96bd6f') in 0.0475966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Forward.controller
  artifactKey: Guid(4af08048e2807ad44a3040f9848963d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Forward.controller using Guid(4af08048e2807ad44a3040f9848963d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9f02db8dd5548c2434b2934a436a686') in 0.0316156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_M.PNG
  artifactKey: Guid(c66adf32ee1d1554fa146c7efb227234) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_M.PNG using Guid(c66adf32ee1d1554fa146c7efb227234) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd19a0c635bdbbf2363dde3753971183') in 0.0693475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_Snow_M.PNG
  artifactKey: Guid(68d3f31f8c47649478effc704c7d3ba2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_Snow_M.PNG using Guid(68d3f31f8c47649478effc704c7d3ba2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9d50e4aa87751864ee544622ae8617f') in 0.0703286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Right.controller
  artifactKey: Guid(96fc0a1d7aa3fd847a88fb565cb8a292) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Right.controller using Guid(96fc0a1d7aa3fd847a88fb565cb8a292) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff62d4c723be0facb8662e3b30a47f61') in 0.034228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_D.PNG
  artifactKey: Guid(02e73c6f3df4feb4a9cd5e1d6e89e64f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_D.PNG using Guid(02e73c6f3df4feb4a9cd5e1d6e89e64f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce1284492b88b59f3fae0a274ebcbd21') in 0.0648951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_B.FBX
  artifactKey: Guid(8d6d14d4c43050242a0c0d12928a0531) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_B.FBX using Guid(8d6d14d4c43050242a0c0d12928a0531) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb9758b22a30248f57a433ccc1276bc6') in 0.0764617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Walk_B_Turn_R90.FBX
  artifactKey: Guid(b4b47680e9ecb51449a43c43950c7c4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Walk_B_Turn_R90.FBX using Guid(b4b47680e9ecb51449a43c43950c7c4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5938bf64af217303c6acbebc27f8f00b') in 0.0578251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_A_Turn_L90.FBX
  artifactKey: Guid(c2afadc95eab063468c35286455fa2bc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_A_Turn_L90.FBX using Guid(c2afadc95eab063468c35286455fa2bc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7519326e8f32c29f14fd6d65b8eb4fcb') in 0.062964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_B_Turn_R90.FBX
  artifactKey: Guid(b642e81b481ffea4cb32b1ca8d75c544) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_B_Turn_R90.FBX using Guid(b642e81b481ffea4cb32b1ca8d75c544) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c1ab97cee5cf9110962ca127fa3700d4') in 0.0649708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_A_Turn_L90_Root.FBX
  artifactKey: Guid(256b3cb535bb50c4a96cf766c8cd1c4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_A_Turn_L90_Root.FBX using Guid(256b3cb535bb50c4a96cf766c8cd1c4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cabefb357e22d46029c8609974c14d93') in 0.0583612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Run_B.FBX
  artifactKey: Guid(e8ea0272a6ed13342bc9f16bfa3d4911) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Run_B.FBX using Guid(e8ea0272a6ed13342bc9f16bfa3d4911) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '854d632f5f82b638e579736cc98aa7af') in 0.058585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Walk_A_Turn_R90.FBX
  artifactKey: Guid(390c3898f2b23cc48a6da3d66fd36d0e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Walk_A_Turn_R90.FBX using Guid(390c3898f2b23cc48a6da3d66fd36d0e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36346b781f75de814284ede2051221c4') in 0.0791696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_R90_Root.FBX
  artifactKey: Guid(8d8d2382880ba064a8b042feeb038765) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_R90_Root.FBX using Guid(8d8d2382880ba064a8b042feeb038765) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed0d3fa1dce128208a25c87e9a6c0ecb') in 0.0658293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_Turn_R90_Root.FBX
  artifactKey: Guid(b3be7614c598eba4b85d60cc87885975) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_Turn_R90_Root.FBX using Guid(b3be7614c598eba4b85d60cc87885975) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81ce3a7f9ddd36c60ed2894a2c0439a3') in 0.0764157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_Root.FBX
  artifactKey: Guid(a800501111a782a49bed40071d6309c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_Root.FBX using Guid(a800501111a782a49bed40071d6309c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc05732866eeeb460aeaf4a46e152dd4') in 0.057319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_Root.FBX
  artifactKey: Guid(f862eec97b2fa9341b3c8472c83df7f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_Root.FBX using Guid(f862eec97b2fa9341b3c8472c83df7f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe0cdc3c57b3bb13744cba596afaeb3d') in 0.053114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Run_B_Root.FBX
  artifactKey: Guid(8c31a2f57e2b5ff409b5c72d65a2af56) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Run_B_Root.FBX using Guid(8c31a2f57e2b5ff409b5c72d65a2af56) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b78752a0e045fc836651622adb7e26a') in 0.0640455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R90_Root.FBX
  artifactKey: Guid(540d16512c39a314bb17b1282ea6994e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_R90_Root.FBX using Guid(540d16512c39a314bb17b1282ea6994e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77268d31d72b0d40689672184bbbe3b1') in 0.0687535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Run_A_Root.FBX
  artifactKey: Guid(98f6782e08919674a8069d7b9db71d74) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Run_A_Root.FBX using Guid(98f6782e08919674a8069d7b9db71d74) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b451098f275d92f4062fe6e18acf9ab') in 0.0586462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_L90.FBX
  artifactKey: Guid(0e914078ffd1958408efda8b05f88357) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_L90.FBX using Guid(0e914078ffd1958408efda8b05f88357) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1355e43e38306f656dc26f2f7c059f4a') in 0.0664607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L45.FBX
  artifactKey: Guid(211ae2d4f0bf8c246b36f75f5eea2f16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L45.FBX using Guid(211ae2d4f0bf8c246b36f75f5eea2f16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de64a6781440c97aba41f1f64e9e136d') in 0.0653914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_B_Root.FBX
  artifactKey: Guid(39711875e88292a47bb53b9940847f47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_B_Root.FBX using Guid(39711875e88292a47bb53b9940847f47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0418a7da7edf8ea9d0ad4d1e35a80735') in 0.0723847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_Turn_L90_Root.FBX
  artifactKey: Guid(e7e92fc7d0bf84648804e835acd7274d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_Turn_L90_Root.FBX using Guid(e7e92fc7d0bf84648804e835acd7274d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5631a6222a5ff58c9c31d276efab92b0') in 0.0698468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_L90.FBX
  artifactKey: Guid(ab4c897277cb71342abf905e56d4df52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_L90.FBX using Guid(ab4c897277cb71342abf905e56d4df52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a3d10edf664c08351934f881221c741') in 0.0597027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_A.FBX
  artifactKey: Guid(2d310342f0cab024185da26fc92af608) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_A.FBX using Guid(2d310342f0cab024185da26fc92af608) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc7d152f056656130fccbe1623c294ad') in 0.0771552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FR45_Root.FBX
  artifactKey: Guid(bead3ac656f4caf438d05eeb5063fead) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FR45_Root.FBX using Guid(bead3ac656f4caf438d05eeb5063fead) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d0b3d7a3abb095d261b8bf585860983') in 0.0636721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_Root.FBX
  artifactKey: Guid(7af32fb35674e854abef693c0c46fbf3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_Root.FBX using Guid(7af32fb35674e854abef693c0c46fbf3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bd05d0fb6b503572d196d4dfb9e6363') in 0.0601933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BR45.FBX
  artifactKey: Guid(e38f8ecba08bfdc419ae27e4dfc0165e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BR45.FBX using Guid(e38f8ecba08bfdc419ae27e4dfc0165e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7a077cd20f22232e8b6b0a517645485') in 0.0745641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_B_Root.FBX
  artifactKey: Guid(5f9a66101c572cd44a2e1da2d05120ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_B_Root.FBX using Guid(5f9a66101c572cd44a2e1da2d05120ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8138f1dce4f70795814e05f364041efd') in 0.0968124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FL45_Root.FBX
  artifactKey: Guid(044c44296e505d04fbbd372878a8337b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FL45_Root.FBX using Guid(044c44296e505d04fbbd372878a8337b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd046d713215a32f7e3952050ffe9866') in 0.0577004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_B_to_Run_B_Root.FBX
  artifactKey: Guid(aa13c754bf256ae4598e8ff351e92884) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_B_to_Run_B_Root.FBX using Guid(aa13c754bf256ae4598e8ff351e92884) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '07f3c63ffd5117e219f2dfbab1b29af9') in 0.0614973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_A.FBX
  artifactKey: Guid(76b21c975dc16e24a907e2d20a09f933) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Jog_A.FBX using Guid(76b21c975dc16e24a907e2d20a09f933) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd084a76876d8bd94d2477742b3d23276') in 0.0738374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_B_Root.FBX
  artifactKey: Guid(604ec040b9cb9fa43952fc971321c712) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_B_Root.FBX using Guid(604ec040b9cb9fa43952fc971321c712) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2759c72ce6d46b7860474894090a9633') in 0.0638208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_L90_Root.FBX
  artifactKey: Guid(90c2c5965954ea64eb18031c0b5f47d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_L90_Root.FBX using Guid(90c2c5965954ea64eb18031c0b5f47d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '42dda3fd885ab300dd5b842b3cf66a9a') in 0.0602542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_A_to_Run_A.FBX
  artifactKey: Guid(3b736e7ed4b141a47874519cd2d6c661) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_A_to_Run_A.FBX using Guid(3b736e7ed4b141a47874519cd2d6c661) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0e016c4045835f6cf60ab17557de4c7') in 0.067525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000132 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Run_A_Root.FBX
  artifactKey: Guid(42ff610494f602a448be88e121eddbce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Run_A_Root.FBX using Guid(42ff610494f602a448be88e121eddbce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3684eb5f9e1bcd9a46a2ed62442b2262') in 0.0737021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Walk_B_Turn_L90_Root.FBX
  artifactKey: Guid(1bc78d0c518294b42b4e45995cbd3098) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Walk_B_Turn_L90_Root.FBX using Guid(1bc78d0c518294b42b4e45995cbd3098) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6389b4dc81f68aa515c365f0f02ae184') in 0.0586063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_A_Root.FBX
  artifactKey: Guid(393aa7f8a5304d846904eb55e37f12dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_A_Root.FBX using Guid(393aa7f8a5304d846904eb55e37f12dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd99a1eec79d8c4c43d4f82c3ae502ef9') in 0.0664138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Bwd.FBX
  artifactKey: Guid(fd178e40dee4eef4fb5b4557ba52194e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Bwd.FBX using Guid(fd178e40dee4eef4fb5b4557ba52194e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50e35fd411e63fc6d07d869c6330648a') in 0.0968753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_L90_Root.FBX
  artifactKey: Guid(5481a36146fbf5d47b6c56df42cc6282) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_L90_Root.FBX using Guid(5481a36146fbf5d47b6c56df42cc6282) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2115ddb141b17e42112c4acc5b63f02') in 0.0629075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Bwd_Root.FBX
  artifactKey: Guid(57ca4c25516c1224383c38cc8988ccde) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Bwd_Root.FBX using Guid(57ca4c25516c1224383c38cc8988ccde) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0acd874bef5903f03b9b34dec7370d9') in 0.0673392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_BL45.FBX
  artifactKey: Guid(9e5011bdd1070fa429b7fbda62b00884) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_BL45.FBX using Guid(9e5011bdd1070fa429b7fbda62b00884) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb86f941a68dabc70cd15ee69976fd5d') in 0.0723473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_BR45.FBX
  artifactKey: Guid(50ea738025e429347ac65abdfc1e35d4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_BR45.FBX using Guid(50ea738025e429347ac65abdfc1e35d4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cf344fa2c52f780d0200d0179d5557b') in 0.0587046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_B.FBX
  artifactKey: Guid(7feb272bc0f9a504e9dda31de3eae4cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_B.FBX using Guid(7feb272bc0f9a504e9dda31de3eae4cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcc0588ac645f8fa76f96b706d9041a7') in 0.0619634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Jog_A_Root.FBX
  artifactKey: Guid(7cff81c9e63e71a4fa62c3def5dba996) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Jog_A_Root.FBX using Guid(7cff81c9e63e71a4fa62c3def5dba996) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bab4d63afb94f3051e4abc21c8ba32f9') in 0.069265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Run_B.FBX
  artifactKey: Guid(2fce733624ccc3a4a92c097b2ad9c03c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_B_to_Run_B.FBX using Guid(2fce733624ccc3a4a92c097b2ad9c03c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a94a369deb82808af103508ac02f0ec') in 0.0692749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L90.FBX
  artifactKey: Guid(2252c73a70ea9cf468658689df4cd800) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L90.FBX using Guid(2252c73a70ea9cf468658689df4cd800) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee603583cc6e15883f0c353f98b24cfa') in 0.0596513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_B.FBX
  artifactKey: Guid(8d68dca516015f647be6acb7b17710a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_B.FBX using Guid(8d68dca516015f647be6acb7b17710a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1dfbcf3c767e0469e167c15408ec701') in 0.0650804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FL45.FBX
  artifactKey: Guid(dc49e9be102c1ac4f856129565e4c94f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FL45.FBX using Guid(dc49e9be102c1ac4f856129565e4c94f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9532fa7943b7ff4962b0456b53003e97') in 0.0651983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_vol2.FBX
  artifactKey: Guid(149648bffa1469a49abb573ce219296e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_vol2.FBX using Guid(149648bffa1469a49abb573ce219296e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f879dcbde7c30e0e4980172220491c7f') in 0.0622485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000357 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BR45.FBX
  artifactKey: Guid(72add4135fe4c224d8165371857618c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BR45.FBX using Guid(72add4135fe4c224d8165371857618c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06d48bb4d0a715bb43e181e70815047a') in 0.0643011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R45.FBX
  artifactKey: Guid(84d6b6c6184140747b543bde6179f0c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R45.FBX using Guid(84d6b6c6184140747b543bde6179f0c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5021258392005d47e79a1c454e8f5703') in 0.0786786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Walk_A_Turn_R90_Root.FBX
  artifactKey: Guid(cedd458b87eabb949b2ef63c5bbf8a2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Walk_A_Turn_R90_Root.FBX using Guid(cedd458b87eabb949b2ef63c5bbf8a2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99aed7130dacf3c9d427d6e7ec63ad6f') in 0.0669613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/SM_Mountain_04.fbx
  artifactKey: Guid(abc00000000018249771007402632772) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/SM_Mountain_04.fbx using Guid(abc00000000018249771007402632772) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '533ba973d99cc09b18d485d5a2babb18') in 0.0751539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_05.fbx
  artifactKey: Guid(abc00000000005413866043802843474) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_05.fbx using Guid(abc00000000005413866043802843474) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06ecc0ba38d0dcde0312ec86108b76b5') in 0.0738232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_04.fbx
  artifactKey: Guid(abc00000000001481058468344514872) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_04.fbx using Guid(abc00000000001481058468344514872) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62af774100617938aff139fad60a42df') in 0.0704181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/Fbx Default Material.mat
  artifactKey: Guid(3549694357077354fb16cccab1f14aa2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/Fbx Default Material.mat using Guid(3549694357077354fb16cccab1f14aa2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f744abd1a60cbe245b74c028b83721f') in 0.0579538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_02.mat
  artifactKey: Guid(abc00000000001516021530692751608) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_02.mat using Guid(abc00000000001516021530692751608) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7918f0282d31ea5e37008a90649aa0ff') in 0.0649514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(a95577a6e14161e4480c77932f02e328) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_WoodPlanks.mat using Guid(a95577a6e14161e4480c77932f02e328) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e87b1c57902afe021a35b423a172547b') in 0.0715582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000142 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_02.fbx
  artifactKey: Guid(abc00000000003806059242369235088) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_02.fbx using Guid(abc00000000003806059242369235088) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '28e9cec2ea14d5c51cebcf497780dd0e') in 0.0694614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cauldron_Lid.fbx
  artifactKey: Guid(abc00000000008521771495765179458) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cauldron_Lid.fbx using Guid(abc00000000008521771495765179458) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87c4e08a4dac44f4c8192bcde93af9af') in 0.0541556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_IronChandelier.fbx
  artifactKey: Guid(abc00000000008206208664770366215) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_IronChandelier.fbx using Guid(abc00000000008206208664770366215) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1d267289e8f5de77983abcf62d09004') in 0.086679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Plate_01.fbx
  artifactKey: Guid(abc00000000002394450527697686584) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Plate_01.fbx using Guid(abc00000000002394450527697686584) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bb98ef761b8bbfac0f40fcfe0d80620') in 0.0596862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Materials/M_Smoke.mat
  artifactKey: Guid(251f329e9f65944409f96791dedb09b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Materials/M_Smoke.mat using Guid(251f329e9f65944409f96791dedb09b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ee2b7f95bb8367ad20c9029e7edeec7') in 0.052277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Forward.fbx
  artifactKey: Guid(2d87094962c8b48478651fa8fe1f7a5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Forward.fbx using Guid(2d87094962c8b48478651fa8fe1f7a5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bef788671755f11e8744c183f017a0ce') in 0.0544197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Textures/T_Fire01_Tiled_D_Yellow.png
  artifactKey: Guid(63e52429d4613d745abcc7f56b2d149e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Textures/T_Fire01_Tiled_D_Yellow.png using Guid(63e52429d4613d745abcc7f56b2d149e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dac0e7773bde2720f03cd8e4fef7ea49') in 0.0775914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM] - Begin.fbx
  artifactKey: Guid(c8115ddbaf422d84bba45d0cc5d9fc70) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM] - Begin.fbx using Guid(c8115ddbaf422d84bba45d0cc5d9fc70) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c27d9f8e8c52907289a0b4475e9803b8') in 0.0943361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_03.fbx
  artifactKey: Guid(abc00000000002738622310250322585) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_03.fbx using Guid(abc00000000002738622310250322585) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5696db1c186c1d95e1627387fbdebe2') in 0.0680741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Right [RM].fbx
  artifactKey: Guid(8296dab082eb77948ab45344c6a09bcd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Right [RM].fbx using Guid(8296dab082eb77948ab45344c6a09bcd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6133899c079a6d370fdbe22aace90b2') in 0.0555061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Locked_Error_1.wav
  artifactKey: Guid(b5441ef24a06c4a488aaee7f10c99aa5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Locked_Error_1.wav using Guid(b5441ef24a06c4a488aaee7f10c99aa5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd25f6eebc69260f4e3753f9df90ef31c') in 0.1838014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/SM_corbels.fbx
  artifactKey: Guid(abc00000000010945915585901555114) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/SM_corbels.fbx using Guid(abc00000000010945915585901555114) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3d6b48cc579effb60c833ef633e5f01') in 0.0606694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Pop_Generic_2.wav
  artifactKey: Guid(c78d89a0c00685e4abd3bc2b84970d8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Pop_Generic_2.wav using Guid(c78d89a0c00685e4abd3bc2b84970d8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9c8cb424dac6157796d255e63cf1ed6') in 0.1173156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000141 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/Materials/MI_Mountain_Grass.mat
  artifactKey: Guid(fd4c2ed21e7dbb44d8be8ab40585f2c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/Materials/MI_Mountain_Grass.mat using Guid(fd4c2ed21e7dbb44d8be8ab40585f2c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5566f4544a3fb95c2fcf299acd962e4e') in 0.058117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/No Name.mat
  artifactKey: Guid(068fb484e44eaed4e83c39db86d84605) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/No Name.mat using Guid(068fb484e44eaed4e83c39db86d84605) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98e990976464e4f4966e4c1314d8a53c') in 0.0604236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_A.fbx
  artifactKey: Guid(abc00000000010609703288329463798) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_A.fbx using Guid(abc00000000010609703288329463798) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f748ce8e796bfec3acbffb60819c300a') in 0.0820145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/MI_Wood_A.mat
  artifactKey: Guid(1d03f4d6f7159074c8bacacecba8ad53) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/MI_Wood_A.mat using Guid(1d03f4d6f7159074c8bacacecba8ad53) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e6a41675f6f337dbe8b72f2e184cc135') in 0.0682803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/SM_Spiral_Stairs_Cap.fbx
  artifactKey: Guid(abc00000000010110704276742301356) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/SM_Spiral_Stairs_Cap.fbx using Guid(abc00000000010110704276742301356) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '075d025fd08b93f21bf38ad55d401f44') in 0.0840296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Stool.fbx
  artifactKey: Guid(abc00000000017099141804546668910) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Stool.fbx using Guid(abc00000000017099141804546668910) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '932bf2be55650a4357b018f91ed5c412') in 0.0815231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/SM_Market_Stand_01.fbx
  artifactKey: Guid(abc00000000011724976883372618194) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/SM_Market_Stand_01.fbx using Guid(abc00000000011724976883372618194) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac55b31fa881a9e9fc2be068c3dec03a') in 0.0697785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Turn/HumanF@Turn01_Right.fbx
  artifactKey: Guid(b4ae722a24b2a8d4b98cbe5ddda51e41) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Turn/HumanF@Turn01_Right.fbx using Guid(b4ae722a24b2a8d4b98cbe5ddda51e41) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8232876723509b6863924ab0f3e59928') in 0.0787871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Stool_Short.fbx
  artifactKey: Guid(abc00000000016737581405060225071) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Stool_Short.fbx using Guid(abc00000000016737581405060225071) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10031a76e05fe0ad558617c3e6c0fc1e') in 0.0924554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Small_Rock_A.fbx
  artifactKey: Guid(abc00000000010925786312951696495) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Small_Rock_A.fbx using Guid(abc00000000010925786312951696495) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '845de4657c1fc5a8b367d8ef1c5d6079') in 0.0707178 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Bundle_01.fbx
  artifactKey: Guid(abc00000000015693719092267018545) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Bundle_01.fbx using Guid(abc00000000015693719092267018545) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6dd88acbb244ee51e26150f58a3b4c07') in 0.0950894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_ForwardLeft [RM].fbx
  artifactKey: Guid(51df1b7fbbe387f48a57bce2e93100b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_ForwardLeft [RM].fbx using Guid(51df1b7fbbe387f48a57bce2e93100b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ad00313d8447568601ead8c917257aa') in 0.0785204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_TableAndChairs.fbx
  artifactKey: Guid(abc00000000002389031002860230080) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_TableAndChairs.fbx using Guid(abc00000000002389031002860230080) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '720cbb53cbef637f8e89a77eb8f75632') in 0.1048464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(834aabbef4891634787d16992c4ff00d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/Details/Materials/MI_StoneFence_02.mat using Guid(834aabbef4891634787d16992c4ff00d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f2e3e2b6924d6873e6ad6fc825d79e7') in 0.0653421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_Left [RM].fbx
  artifactKey: Guid(17eb721b405a2864b84ea7c088d6bf02) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_Left [RM].fbx using Guid(17eb721b405a2864b84ea7c088d6bf02) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf2931206d81308b05a5705e03fbfb59') in 0.0821111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Right [RM].fbx
  artifactKey: Guid(c94ea603add67b745a5c6198446707fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Right [RM].fbx using Guid(c94ea603add67b745a5c6198446707fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7c69a13fcabc16b0b37ae5abd5c348f') in 0.0871951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_01.fbx
  artifactKey: Guid(abc00000000001037613591319247348) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_01.fbx using Guid(abc00000000001037613591319247348) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93eed665493e9c9d71a08e365c777624') in 0.1066717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Wall_Edging.fbx
  artifactKey: Guid(abc00000000000267956099476822947) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Wall_Edging.fbx using Guid(abc00000000000267956099476822947) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2fe3ac93eac183fab7b4314e2ac62610') in 0.0594427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Front_Arch.fbx
  artifactKey: Guid(abc00000000009578453455450062412) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Front_Arch.fbx using Guid(abc00000000009578453455450062412) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab2bf7ca53b27f0367b8b94fea46a961') in 0.0787198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_ConeRoof_01.fbx
  artifactKey: Guid(abc00000000010956649111554039250) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_ConeRoof_01.fbx using Guid(abc00000000010956649111554039250) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ccbe5cea81880084e3c18e9c17de9e5') in 0.1141902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Wall_1x5.fbx
  artifactKey: Guid(abc00000000012048873173125559334) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Wall_1x5.fbx using Guid(abc00000000012048873173125559334) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7e9570044fd054864f8a6564cf7e9d1') in 0.0703204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_04.fbx
  artifactKey: Guid(abc00000000011535874283724199782) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_04.fbx using Guid(abc00000000011535874283724199782) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efcd2a54e1f530a3aee28b32b3b4161f') in 0.1302655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Cloth_Blue.mat
  artifactKey: Guid(c67e43a8655cae84db267b1ff48915f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Cloth_Blue.mat using Guid(c67e43a8655cae84db267b1ff48915f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98ffec3da751cab5763d96a4c317fa79') in 0.0554326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(5e8abe9a3fdd9984382ee23b3deb345f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_LighterPlanks.mat using Guid(5e8abe9a3fdd9984382ee23b3deb345f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56584b013278048bccea4fdb93b84534') in 0.0549191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(1850ba299076e614fbe4c09b485b2b79) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_WoodPlanks.mat using Guid(1850ba299076e614fbe4c09b485b2b79) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5a924ec6e5c44b0dd80d0b4de7a2d30') in 0.0582933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Chimney_Segment.fbx
  artifactKey: Guid(abc00000000008984867136370588259) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Chimney_Segment.fbx using Guid(abc00000000008984867136370588259) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22ecfdd4293e064948e235f7a66b2c61') in 0.0895942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_02.fbx
  artifactKey: Guid(abc00000000017495007312605558734) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_02.fbx using Guid(abc00000000017495007312605558734) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9301dc54951740000178264e3f00a7d') in 0.0695729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/Materials/No Name.mat
  artifactKey: Guid(baeab8af1df56ba4bb2b07a739606499) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/Materials/No Name.mat using Guid(baeab8af1df56ba4bb2b07a739606499) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6d63241dc4cfe234295b67950bb5448') in 0.0597803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_D.fbx
  artifactKey: Guid(abc00000000017710884736111928521) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_D.fbx using Guid(abc00000000017710884736111928521) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12dce70b695604502ab1a9bdf071470e') in 0.0791718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_03.fbx
  artifactKey: Guid(abc00000000003886274153257158775) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_03.fbx using Guid(abc00000000003886274153257158775) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ec195db0192d39a7806b8b90e123589') in 0.1015516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_A_01.fbx
  artifactKey: Guid(abc00000000016766400474658221508) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_A_01.fbx using Guid(abc00000000016766400474658221508) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcdc43241f18f04e215ae582e3ac490e') in 0.0641246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_03.fbx
  artifactKey: Guid(abc00000000011196255972012378847) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_03.fbx using Guid(abc00000000011196255972012378847) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5774a245d2f3cf776d96bd79b743cbde') in 0.0876748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(e85ad0cbb6da4f249a609560aa04a09f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_StoneFence_02.mat using Guid(e85ad0cbb6da4f249a609560aa04a09f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad44605c32d3d756d58849cd5a9fd14e') in 0.0587656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/No Name.mat
  artifactKey: Guid(ad3ca1bb8374cbf47b5a818260039b0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/No Name.mat using Guid(ad3ca1bb8374cbf47b5a818260039b0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '617902e71a7221bc2a8a961f8ac41248') in 0.0793121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000951 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_05.fbx
  artifactKey: Guid(abc00000000017899801503217457501) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_05.fbx using Guid(abc00000000017899801503217457501) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e0d0951e5dc980e136865685d4b4840') in 0.129154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_02.mat
  artifactKey: Guid(8392cf297876de04091850982afa8563) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_02.mat using Guid(8392cf297876de04091850982afa8563) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2363bbc2f04f3a081667c969261269b') in 0.0766395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_BrickKit_07.mat
  artifactKey: Guid(752a53e4e33a90549ae71f62e2e78a6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_BrickKit_07.mat using Guid(752a53e4e33a90549ae71f62e2e78a6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '502e6c488a56554f3f94a079c35a0d4e') in 0.0695586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_StoneFence_03.mat
  artifactKey: Guid(f1e3d31c3ba92df4bbaeaef75eb3e1ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_StoneFence_03.mat using Guid(f1e3d31c3ba92df4bbaeaef75eb3e1ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62da6321abbfd3dc3ac27739f91cfc6e') in 0.0620085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_StoneFence_05.mat
  artifactKey: Guid(b94ff3e3d6303d74aa5899d641295bf2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_StoneFence_05.mat using Guid(b94ff3e3d6303d74aa5899d641295bf2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de9d4a98e373a53da61409f8987e2532') in 0.0523234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_M_01.fbx
  artifactKey: Guid(abc00000000008285934249366986512) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_M_01.fbx using Guid(abc00000000008285934249366986512) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f58ed138f6f8fbb6c697d9471f19c79') in 0.0796469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Materials/M_SilverFir_Atlas_01.mat
  artifactKey: Guid(abc00000000013947084889266466769) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Materials/M_SilverFir_Atlas_01.mat using Guid(abc00000000013947084889266466769) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69ce2a42db6e9b4cf009a7b424d80b48') in 0.0489379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_09.fbx
  artifactKey: Guid(abc00000000008008838813775312319) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_09.fbx using Guid(abc00000000008008838813775312319) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '743954ac18c7cbd7e8cabaed73b05f4d') in 0.0944943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/Materials/No Name.mat
  artifactKey: Guid(7ad64dfd961985341a3bc118b3c3f932) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/Materials/No Name.mat using Guid(7ad64dfd961985341a3bc118b3c3f932) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '769317f2dccad1cba4600222e29d8db0') in 0.059977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/Materials/MI_Individual_Bricks.mat
  artifactKey: Guid(f33745da079543e4fb2c3962ba637fc6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/Materials/MI_Individual_Bricks.mat using Guid(f33745da079543e4fb2c3962ba637fc6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2160a145643dbf191a6fa6033d003060') in 0.0523978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_03.fbx
  artifactKey: Guid(abc00000000010088337759829012804) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_03.fbx using Guid(abc00000000010088337759829012804) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8247d5625ab3da9776cf096d83446373') in 0.0787358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/No Name.mat
  artifactKey: Guid(40892864f26c026428e739baeae39b0f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/No Name.mat using Guid(40892864f26c026428e739baeae39b0f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9bcb4b011962485ccb39189c0eb8151c') in 0.0577514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_VarnishedWood_01.mat
  artifactKey: Guid(80c3eef84db35c4419d8a572e7d095b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_VarnishedWood_01.mat using Guid(80c3eef84db35c4419d8a572e7d095b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff310888153dfeb5739bb406b6a407f0') in 0.0626486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_XL_02.fbx
  artifactKey: Guid(abc00000000011046732550376685046) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_XL_02.fbx using Guid(abc00000000011046732550376685046) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2874f17aeb9ca75807c26a54d838d988') in 0.0826364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Backward [RM].fbx
  artifactKey: Guid(31116f689ea4d374593385937d990c7c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Backward [RM].fbx using Guid(31116f689ea4d374593385937d990c7c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6257d9b0c69c42aeb9365982f271cf7c') in 0.0754857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000171 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/MI_CobblePath_01.mat
  artifactKey: Guid(89cc66871c87ad4458b269d81ce0b609) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/MI_CobblePath_01.mat using Guid(89cc66871c87ad4458b269d81ce0b609) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abff25c02c571b42c9c5316edfe57e61') in 0.0634092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sack_02.fbx
  artifactKey: Guid(abc00000000013568073051946711379) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sack_02.fbx using Guid(abc00000000013568073051946711379) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b20f32c5522b6bd4381216b559d16e5a') in 0.0612864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_Root.FBX
  artifactKey: Guid(849f29ec2d5ff6542a9937d0d4ffacb9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_Root.FBX using Guid(849f29ec2d5ff6542a9937d0d4ffacb9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '445515f073b21f9cb1da5224ced2cf8c') in 0.0646428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_02.prefab
  artifactKey: Guid(abc00000000016399473995144652937) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_02.prefab using Guid(abc00000000016399473995144652937) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d1b6ad1ed3d4c933266275fa6dad861') in 0.076582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_ForwardRight.controller
  artifactKey: Guid(13b286d706343b644b44225d5a9a561b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_ForwardRight.controller using Guid(13b286d706343b644b44225d5a9a561b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a71f61fb9c5310b7e006fb2b438944e3') in 0.0421953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Dirt_basecolor.PNG
  artifactKey: Guid(7e1b2a00a0173a74da0ee68b6c263319) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Dirt_basecolor.PNG using Guid(7e1b2a00a0173a74da0ee68b6c263319) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '465e71d63d2d65e0188e55cd6a8c8b19') in 0.0908223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_ALL_ZeroHeight.FBX
  artifactKey: Guid(cbf049557a573b4498eae831f7d50b8a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_ALL_ZeroHeight.FBX using Guid(cbf049557a573b4498eae831f7d50b8a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70f308cd80886cbc0730d6eac25bff1e') in 0.0813776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_A_Root.FBX
  artifactKey: Guid(ae7d9d587280f4c4fb490262b1997194) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_A_Root.FBX using Guid(ae7d9d587280f4c4fb490262b1997194) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '801f68a9d3b521e4e8372afbfe8d7992') in 0.0893732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Attack.FBX
  artifactKey: Guid(98b40a0f8d28b2541854c6fa4941f7bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Attack.FBX using Guid(98b40a0f8d28b2541854c6fa4941f7bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2dea7e8449525dce43f7cb4eb80c624') in 0.0734777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Loop.FBX
  artifactKey: Guid(5e5a4644beb17764ba30a1d7fce81d0e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Loop.FBX using Guid(5e5a4644beb17764ba30a1d7fce81d0e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5786b8dde117db6f2531a24533564397') in 0.0879089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_N.PNG
  artifactKey: Guid(1227acf039d41fb4ba055d613ac55c4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_decal_dirt_leak_01_N.PNG using Guid(1227acf039d41fb4ba055d613ac55c4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3856ef2a6bc202864b317e5646e5cf7e') in 0.0733415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_D.PNG
  artifactKey: Guid(b6f9ec0e27d0ab3449403767b3d441b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_D.PNG using Guid(b6f9ec0e27d0ab3449403767b3d441b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9daa0e27734d71933a654d3b5f581ed4') in 0.0744741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_6_Attach_ZeroHeight.FBX
  artifactKey: Guid(dd1228c3bd140a9438ef66b854850e84) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_6_Attach_ZeroHeight.FBX using Guid(dd1228c3bd140a9438ef66b854850e84) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9ba856ebef1b9954f7c245742191286') in 0.065857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0