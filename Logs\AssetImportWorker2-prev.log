Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:04:47Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker2.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19724]  Target information:

Player connection [19724]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1879606843 [EditorId] 1879606843 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19724] Host joined multi-casting on [***********:54997]...
Player connection [19724] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 6.70 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.12 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56356
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003036 seconds.
- Loaded All Assemblies, in  0.637 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.573 seconds
Domain Reload Profiling: 1209ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (304ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (298ms)
				TypeCache.ScanAssembly (278ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (573ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (513ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (269ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.167 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.26 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.549 seconds
Domain Reload Profiling: 2715ms
	BeginReloadAssembly (262ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (777ms)
		LoadAssemblies (481ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (442ms)
			TypeCache.Refresh (318ms)
				TypeCache.ScanAssembly (296ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1550ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1229ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (224ms)
			ProcessInitializeOnLoadAttributes (896ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 6.66 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.2 MB). Loaded Objects now: 8334.
Memory consumption went from 210.5 MB to 200.3 MB.
Total: 24.936700 ms (FindLiveObjects: 1.692100 ms CreateObjectMapping: 2.384800 ms MarkObjects: 12.261200 ms  DeleteObjects: 8.596500 ms)

========================================================================
Received Import Request.
  Time since last request: 1811912.061385 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset
  artifactKey: Guid(28733647ae58e1245891229aca418864) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset using Guid(28733647ae58e1245891229aca418864) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '40409726037f913e1982586cfa2bcd9c') in 0.0410947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 28.043498 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab
  artifactKey: Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab using Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0ae7b18286ef4fcb1627f82008b99766') in 0.888443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 291

========================================================================
Received Import Request.
  Time since last request: 6.636338 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/0__Intro/<EMAIL>
  artifactKey: Guid(af5e97a7b3911fe4d93337bf22a947a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/0__Intro/<EMAIL> using Guid(af5e97a7b3911fe4d93337bf22a947a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb83945c37daf9e7b4ec3e5cf6b2da63') in 0.0398126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.699577 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured.FBX
  artifactKey: Guid(d3762b16d75e4a5468e22a58002973aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured.FBX using Guid(d3762b16d75e4a5468e22a58002973aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a22266c3b7665cab030c5c73b10a9912') in 0.0351873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 141

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Root.FBX
  artifactKey: Guid(7f8d6293100b38b468059ab6bbc1250e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Root.FBX using Guid(7f8d6293100b38b468059ab6bbc1250e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5572ced3dcacdbc8425fcfeb0f45b4bf') in 0.0346547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 141

========================================================================
Received Import Request.
  Time since last request: 6.881292 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/0_Controller/Controller_M_Big_Sword.controller
  artifactKey: Guid(ae99634d6f668614fb1283404fa32a93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/0_Controller/Controller_M_Big_Sword.controller using Guid(ae99634d6f668614fb1283404fa32a93) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50c1f6a9dd2531358bb6f2c51a6aed1a') in 0.4255878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 875

========================================================================
Received Import Request.
  Time since last request: 17.197591 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A
  artifactKey: Guid(8bc5591f061e2264ca7358f6a9623a36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A using Guid(8bc5591f061e2264ca7358f6a9623a36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8aee39c0dd747f0f75c389e3981f3719') in 0.0308801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back.FBX
  artifactKey: Guid(a35e7696d2fd31f4b82415c8c9d5dce0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back.FBX using Guid(a35e7696d2fd31f4b82415c8c9d5dce0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae9c22c059e9be494097b6c709de5903') in 0.0095454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90_Root.FBX
  artifactKey: Guid(9226a7482ad359040810d9f050baa6a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90_Root.FBX using Guid(9226a7482ad359040810d9f050baa6a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b8e1097c43cc95708ea5c7c48d106d0') in 0.0324625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45_Root.FBX
  artifactKey: Guid(6e3a361d41abf8e488923bc9558c0556) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45_Root.FBX using Guid(6e3a361d41abf8e488923bc9558c0556) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f39ed9e9d1a29d0176d609b8a1f93d1') in 0.0099694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90.FBX
  artifactKey: Guid(dd1802c7bc061054d9364020d0ffee22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90.FBX using Guid(dd1802c7bc061054d9364020d0ffee22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66067966ad1ad1d158e4a066f23b1e3b') in 0.007374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90_Root.FBX
  artifactKey: Guid(6a628fa1413909748ad2144f6fc8aa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90_Root.FBX using Guid(6a628fa1413909748ad2144f6fc8aa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3eb9337c2a09da6efd82bfdd052f3913') in 0.0109515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45_Root.FBX
  artifactKey: Guid(94be1451cf5c0ae4e93ffb72c0ab093a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45_Root.FBX using Guid(94be1451cf5c0ae4e93ffb72c0ab093a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7d78fff4b5df812df701d28a1892c40') in 0.0087723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0