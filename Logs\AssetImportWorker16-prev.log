Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker16
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker16.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11764]  Target information:

Player connection [11764]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1831551647 [EditorId] 1831551647 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11764] Host joined multi-casting on [***********:54997]...
Player connection [11764] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 43.72 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 8.18 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56404
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005370 seconds.
- Loaded All Assemblies, in  1.120 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.165 seconds
Domain Reload Profiling: 2284ms
	BeginReloadAssembly (322ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (114ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (136ms)
	LoadAllAssembliesAndSetupDomain (507ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (500ms)
			TypeCache.Refresh (497ms)
				TypeCache.ScanAssembly (464ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1166ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (898ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (133ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (250ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.378 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.14 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.142 seconds
Domain Reload Profiling: 5511ms
	BeginReloadAssembly (483ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (130ms)
	RebuildNativeTypeToScriptingClass (43ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (1598ms)
		LoadAssemblies (1014ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (864ms)
			TypeCache.Refresh (643ms)
				TypeCache.ScanAssembly (598ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (38ms)
	FinalizeReload (3143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (455ms)
			ProcessInitializeOnLoadAttributes (1648ms)
			ProcessInitializeOnLoadMethodAttributes (203ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 1.41 seconds
Refreshing native plugins compatible for Editor in 90.49 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.8 MB). Loaded Objects now: 8334.
Memory consumption went from 210.1 MB to 199.3 MB.
Total: 142.393600 ms (FindLiveObjects: 2.317000 ms CreateObjectMapping: 2.864300 ms MarkObjects: 13.687100 ms  DeleteObjects: 123.522800 ms)

========================================================================
Received Import Request.
  Time since last request: 1812183.774108 seconds.
  path: Assets/Scripts/Editor/PlayerController3DEditor.cs
  artifactKey: Guid(63d2bbec5bb8e494f905df7462e9d581) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Editor/PlayerController3DEditor.cs using Guid(63d2bbec5bb8e494f905df7462e9d581) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6456cf3f5a1cbd32e5b3f0c658365c9') in 2.1723286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Cast.prefab
  artifactKey: Guid(5cb8c3b6b12f4fd41ba43c9206c62496) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Cast.prefab using Guid(5cb8c3b6b12f4fd41ba43c9206c62496) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e62b416c4f0be8deaa2869a399872f7') in 1.0194903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 68

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_3.prefab
  artifactKey: Guid(c5f6af8ade86ca5449b9775f1d70fa8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_3.prefab using Guid(c5f6af8ade86ca5449b9775f1d70fa8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2552757cc7d2488c98747f9cbe534969') in 0.0312903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_CircleGround_Buff_01.mat
  artifactKey: Guid(c5b0b73de21bef94f8c7d32573a7a213) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_CircleGround_Buff_01.mat using Guid(c5b0b73de21bef94f8c7d32573a7a213) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0bcbe333ca1435d84835fd408c3da21') in 1.5299489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_MainFoam.prefab
  artifactKey: Guid(f58c1fb0643d4f048ba646dacce6a63d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_MainFoam.prefab using Guid(f58c1fb0643d4f048ba646dacce6a63d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f94a8355c55f5689c57b9e188ac2ecd9') in 0.0509561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene.unity
  artifactKey: Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene.unity using Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d3275ebeaf60669e07ade1d481d4f10') in 0.0727588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Demo/TC_Demo_02_2k.HDR
  artifactKey: Guid(359b92a952f17ee438b358340a275cfc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Demo/TC_Demo_02_2k.HDR using Guid(359b92a952f17ee438b358340a275cfc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56fd9045e9927981051e38428bffd373') in 0.2637325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.003112 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_02.mat
  artifactKey: Guid(18f78fae82f146a46983815e8e9c559d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_02.mat using Guid(18f78fae82f146a46983815e8e9c559d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c4dd0b01f93f01b1f35692190fd9ca8') in 0.2770773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.prefab
  artifactKey: Guid(4243487e5ba110147988dda3e63b9b8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.prefab using Guid(4243487e5ba110147988dda3e63b9b8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4bc732a0b7dbcdc918be1c2e1af83437') in 0.0696487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01_Back.mat
  artifactKey: Guid(9272da95f2a5ae24ba4922ed07f1fe4e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01_Back.mat using Guid(9272da95f2a5ae24ba4922ed07f1fe4e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62baac0a5f015476c9f1f68a0065fa46') in 0.6202746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.prefab
  artifactKey: Guid(f921404edb79d574e9182f51fa4d3420) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_11.prefab using Guid(f921404edb79d574e9182f51fa4d3420) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0d64f365488cbf1424007242e6acdfe8') in 0.6980094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset
  artifactKey: Guid(28733647ae58e1245891229aca418864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/LightingData.asset using Guid(28733647ae58e1245891229aca418864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '12fe426ccef73bd5c7241d8be371104b') in 0.030387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch_1.prefab
  artifactKey: Guid(abc00000000006201299956295673687) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch_1.prefab using Guid(abc00000000006201299956295673687) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '315cc030c42a79e5d1ed408050c0697a') in 0.0508145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_02_4x4.mat
  artifactKey: Guid(35ccc2992a4f0f443a23d2cf45f1532e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FallingWater_02_4x4.mat using Guid(35ccc2992a4f0f443a23d2cf45f1532e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '172c5749eda6bb1a47b762b82b4850de') in 0.6340881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleSplash_01_4x4.mat
  artifactKey: Guid(c26a3e0211834d74ba24199167198dcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleSplash_01_4x4.mat using Guid(c26a3e0211834d74ba24199167198dcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7efa16f69e1b9253b59383ccddcbc9f7') in 0.5565063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_05_4x5.mat
  artifactKey: Guid(fed2280a071951744843ded32bbcdfa9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_05_4x5.mat using Guid(fed2280a071951744843ded32bbcdfa9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8dbebefdab572b8d6c2ddb859fda6086') in 0.0814779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_01.prefab
  artifactKey: Guid(abc00000000010743531460957361430) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_01.prefab using Guid(abc00000000010743531460957361430) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a3c66d2827506f98a7b343260de93661') in 0.0344041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000097 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CargoCrate_01.prefab
  artifactKey: Guid(abc00000000012252198206148877258) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CargoCrate_01.prefab using Guid(abc00000000012252198206148877258) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1f62a3052cb9540955032cfeb034f91b') in 0.0725554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity
  artifactKey: Guid(cc778ff8e7d7b874d9ef5cf06279e322) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity using Guid(cc778ff8e7d7b874d9ef5cf06279e322) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity additively'
Loaded scene 'Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity'
	Deserialize:            39.879 ms
	Integration:            3567.030 ms
	Integration of assets:  0.012 ms
	Thread Wait Time:       0.040 ms
	Total Operation Time:   3606.960 ms
 -> (artifact id: '3b45ca54a90169ed5cf0177e0df833f6') in 6.0614474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 775

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_D.prefab
  artifactKey: Guid(abc00000000004027028008972081728) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_D.prefab using Guid(abc00000000004027028008972081728) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ca9c90e00d3f621e0fe977d8ad2006a1') in 0.0398125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02.prefab
  artifactKey: Guid(abc00000000010829330222347857713) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02.prefab using Guid(abc00000000010829330222347857713) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b889ead674411f780ac7b68bf15cc2d9') in 0.0476366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mug.prefab
  artifactKey: Guid(abc00000000007373755861264708906) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mug.prefab using Guid(abc00000000007373755861264708906) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '70d71248e9843322ca12df7d3ac43375') in 0.0314757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_01.prefab
  artifactKey: Guid(abc00000000007357929331176521613) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_01.prefab using Guid(abc00000000007357929331176521613) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0a3e04312abce17faad93e8225ab757d') in 0.0303244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_03.prefab
  artifactKey: Guid(abc00000000006725378370871592864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_03.prefab using Guid(abc00000000006725378370871592864) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '374c2fb0c0b3dbc2222a0aa888427568') in 0.0423537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000222 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01_1.prefab
  artifactKey: Guid(abc00000000003804069267254648650) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01_1.prefab using Guid(abc00000000003804069267254648650) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '84f793ec6bfc383782030f1c44dc56d0') in 0.0606911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_01.prefab
  artifactKey: Guid(abc00000000008651461517677355870) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_01.prefab using Guid(abc00000000008651461517677355870) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '049b7ff3b8d3a69c06e4c9aa27f685a0') in 0.0683415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000266 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve_Beam.prefab
  artifactKey: Guid(abc00000000012588386773910390050) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve_Beam.prefab using Guid(abc00000000012588386773910390050) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '61f180bac2ac052a970a1f40c7b9008a') in 0.0742433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_1.prefab
  artifactKey: Guid(abc00000000015828755011961175657) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rug_1.prefab using Guid(abc00000000015828755011961175657) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '385e74d5b059a936bf3075a3ee5942cd') in 0.0531414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_02.prefab
  artifactKey: Guid(abc00000000005439670819512234065) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_02.prefab using Guid(abc00000000005439670819512234065) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1c0ccddacc52fd5abe2bf7eb1b910fd5') in 0.0423731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_UnderwaterFoam.prefab
  artifactKey: Guid(dc2259ef953c36249b7ee875f2ab9bd7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Waterfall_UnderwaterFoam.prefab using Guid(dc2259ef953c36249b7ee875f2ab9bd7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4051023982fec1c5a4e2520f7a42c00') in 0.3304446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Impact.prefab
  artifactKey: Guid(3a5c33b0b845583419675161aff09dc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Impact.prefab using Guid(3a5c33b0b845583419675161aff09dc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee6c051791b7187834e6539237dd9d78') in 0.2737609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 113

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Loop.prefab
  artifactKey: Guid(c12ac9e839f59454e8a71d940042bcb3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Loop.prefab using Guid(c12ac9e839f59454e8a71d940042bcb3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91df66e9ef7a2b31021248873eca0135') in 0.2438707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 153

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Throne.prefab
  artifactKey: Guid(abc00000000000467259060501473599) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Throne.prefab using Guid(abc00000000000467259060501473599) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2aa1ea5499fc5ffbc51e496f42bf02e4') in 0.079586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Projectile_Cast.prefab
  artifactKey: Guid(3664140494b88e3468dcca84b6dbde23) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Projectile_Cast.prefab using Guid(3664140494b88e3468dcca84b6dbde23) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7aeea4223bebcf51dd045aeff2982f21') in 0.1643263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 53

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_1x5.prefab
  artifactKey: Guid(abc00000000004357895327718654900) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_1x5.prefab using Guid(abc00000000004357895327718654900) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'afec0795fb9b3168f35a7586c25273f9') in 0.0471026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_16x5.prefab
  artifactKey: Guid(abc00000000016557253141712033432) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_16x5.prefab using Guid(abc00000000016557253141712033432) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'de9b077ea8d128bdcb8ac33a465a4c44') in 0.0437379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_03.prefab
  artifactKey: Guid(abc00000000018284835566804858819) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_03.prefab using Guid(abc00000000018284835566804858819) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e3cb2f49c00788e505e9e4df904bf1c') in 0.0308093 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt
  artifactKey: Guid(d82c1b31c7e74239bff1220585707d2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt using Guid(d82c1b31c7e74239bff1220585707d2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dee8f6b2f7de588b4d9e14668b1477b6') in 0.0323682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_B_01.prefab
  artifactKey: Guid(abc00000000008459799861029584559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_B_01.prefab using Guid(abc00000000008459799861029584559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dd0376517100cabee0448a91445aaf08') in 0.033783 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_End_01.prefab
  artifactKey: Guid(abc00000000002276231062997728989) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_End_01.prefab using Guid(abc00000000002276231062997728989) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd94ee81a7faa0e7ce70f61756ea76409') in 0.0417269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_05_1.prefab
  artifactKey: Guid(abc00000000013573692171270119839) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_05_1.prefab using Guid(abc00000000013573692171270119839) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '83b65acd3e8e3b7af636ebdfad281412') in 0.057818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_04.prefab
  artifactKey: Guid(1778f2db3b314e246b228ab725a91179) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_04.prefab using Guid(1778f2db3b314e246b228ab725a91179) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a888a157977fae187a7d518c6c0e69aa') in 0.0616743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene.unity
  artifactKey: Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene.unity using Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Nappin/PhysicsCharacterController/ExampleScene.unity additively'
Loaded scene 'Assets/Nappin/PhysicsCharacterController/ExampleScene.unity'
	Deserialize:            149.107 ms
	Integration:            799.538 ms
	Integration of assets:  3.873 ms
	Thread Wait Time:       0.110 ms
	Total Operation Time:   952.628 ms
 -> (artifact id: 'd97ce445749fed6e71ee34aeb4fda47f') in 1.2321795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 821

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.prefab
  artifactKey: Guid(a5d09d5371936d24984839e062302b43) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.prefab using Guid(a5d09d5371936d24984839e062302b43) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dc27a1cef90db75f2d838d337762c7c2') in 0.3389303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterBuff_Loop.prefab
  artifactKey: Guid(442fbc2dc8380e744808015ef90c1816) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterBuff_Loop.prefab using Guid(442fbc2dc8380e744808015ef90c1816) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eb038d62bd6642d1084138f16571988b') in 0.0894644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 122

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment_1.prefab
  artifactKey: Guid(abc00000000002353935698868763680) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment_1.prefab using Guid(abc00000000002353935698868763680) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e190c97a2a020950529121439abd2e30') in 0.0787244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_02_4x4.mat
  artifactKey: Guid(f5539723bd0f8b94196cc056a49f9ff0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_02_4x4.mat using Guid(f5539723bd0f8b94196cc056a49f9ff0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '326f778860eda64c87d3339412cf892b') in 0.058258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashWaterSecondary_01_3x4.mat
  artifactKey: Guid(3730af93325ee5f498ce870d1373b900) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashWaterSecondary_01_3x4.mat using Guid(3730af93325ee5f498ce870d1373b900) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73af078355b46edd9db3fd1b20870cf8') in 0.2463617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSimpleImpact_ADD_02.mat
  artifactKey: Guid(23ff87ce410cfc84e9bd7a51846cb7b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSimpleImpact_ADD_02.mat using Guid(23ff87ce410cfc84e9bd7a51846cb7b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de25115cfae8138b9e619211ba2e5ca8') in 0.0498884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_07.prefab
  artifactKey: Guid(abc00000000015176355037887582961) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_07.prefab using Guid(abc00000000015176355037887582961) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '076592dc48802e4991a687271b108cce') in 0.042029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.prefab
  artifactKey: Guid(5248f34b570b29c4d9cd428d786579f8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.prefab using Guid(5248f34b570b29c4d9cd428d786579f8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '158498ade36c0f14b992e0cf16b6e33b') in 0.1964172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 35

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default Terminal Bud.png
  artifactKey: Guid(7e9a925f80a4ea44d83a8420f17c68a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default Terminal Bud.png using Guid(7e9a925f80a4ea44d83a8420f17c68a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d87560cd3ad148e29e6bb35c6c35bc6') in 0.1019386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern III.png
  artifactKey: Guid(af47f7eb261016c47ba72ca9dc93fb34) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern III.png using Guid(af47f7eb261016c47ba72ca9dc93fb34) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c91ce5631927525c4ab6b1933c8325a6') in 0.0834313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_05.mat
  artifactKey: Guid(00f7f3505b4bfda46adaa485986ce898) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_05.mat using Guid(00f7f3505b4bfda46adaa485986ce898) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd48067fd920375725547e4002f58336d') in 0.6679269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Design.cs
  artifactKey: Guid(7a3a5503e4c9b1f42a310d4687b8c774) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Design.cs using Guid(7a3a5503e4c9b1f42a310d4687b8c774) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37952caeb219a07222f42a5a17f0f45b') in 0.0350275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Next-Gen I.png
  artifactKey: Guid(93a5159659f0d744abcc5824654d9d3e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Next-Gen I.png using Guid(93a5159659f0d744abcc5824654d9d3e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89feb3008c90533fa8db239b672ed014') in 0.1040383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Presets.cs
  artifactKey: Guid(2033e14b87c93ed4b90c260f6ef31729) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Presets.cs using Guid(2033e14b87c93ed4b90c260f6ef31729) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd55346c18644992974de757b6aa4938b') in 0.0357433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted Terminal Bud.png
  artifactKey: Guid(4e771bc57ffe974479d9e48af95caffc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted Terminal Bud.png using Guid(4e771bc57ffe974479d9e48af95caffc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e0875c00b155990e8451d3912c74c11') in 0.0844915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.001021 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern II.png
  artifactKey: Guid(282af483548380f4ab3921f9179daa71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern II.png using Guid(282af483548380f4ab3921f9179daa71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47f85aeeaad6a7ae97089e7428b3e3df') in 0.0597741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_24.mat
  artifactKey: Guid(2836d6d0398de6e40bb3aaf0995bbd35) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_24.mat using Guid(2836d6d0398de6e40bb3aaf0995bbd35) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1618574af2766b63d097be5e841fab6d') in 0.1303766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.435166 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_34.mat
  artifactKey: Guid(d6b557cad640f88459f665edeb6927e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_34.mat using Guid(d6b557cad640f88459f665edeb6927e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f8dffa59f25aa135f3852f085de725a') in 0.105893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_05.mat
  artifactKey: Guid(46cc966b3e6a9514eb4b30227c7cefc9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_05.mat using Guid(46cc966b3e6a9514eb4b30227c7cefc9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85047cdee87c3152567273a24a1adc3e') in 0.7532321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_10.mat
  artifactKey: Guid(d1dacc1a38068f148b1a82f681e3e9b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_10.mat using Guid(d1dacc1a38068f148b1a82f681e3e9b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5194fb249e3ea955a8f32d675699130d') in 1.1079433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_02.mat
  artifactKey: Guid(a5dc4e4c792e20347bf009ad23bfab54) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_02.mat using Guid(a5dc4e4c792e20347bf009ad23bfab54) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea8a1b3b15548e1376e74b5db7d06aaf') in 0.2493981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_19.mat
  artifactKey: Guid(e97c78a4e2dd70447a3d5c67f9f80168) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_19.mat using Guid(e97c78a4e2dd70447a3d5c67f9f80168) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2ae363cfcc29956acb0ed22f28909ad') in 0.1596732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/IDA_Hair.shadergraph
  artifactKey: Guid(21427971a435e1049841ff5cdb9ac474) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/IDA_Hair.shadergraph using Guid(21427971a435e1049841ff5cdb9ac474) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78d8c60d11edcf8eb064cc9671084d2e') in 0.9179363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21_Dirty.mat
  artifactKey: Guid(ef7853677ac90f24f82df20e01c52642) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21_Dirty.mat using Guid(ef7853677ac90f24f82df20e01c52642) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1db1662ecaa18d3004b045e0b9215c9') in 0.9711272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_17.mat
  artifactKey: Guid(a0279d95bace79a448e2d6cb12c6784c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_17.mat using Guid(a0279d95bace79a448e2d6cb12c6784c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '29e058697d95c595a0d744e721340d4f') in 0.1510028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_02.mat
  artifactKey: Guid(02f6554d287701f49b4f61bd6eb1bb72) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_02.mat using Guid(02f6554d287701f49b4f61bd6eb1bb72) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8713e1d13b2b97767d8d532258734d02') in 0.2740061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_WITH_FUR Variant.prefab
  artifactKey: Guid(4ab5b2ea6d4daca4abe025f3239e3b80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_WITH_FUR Variant.prefab using Guid(4ab5b2ea6d4daca4abe025f3239e3b80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0bbc615b4391fb02cc91224a3d14f4a8') in 0.2254924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 781

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_Roughness.png
  artifactKey: Guid(20b33505116d1cd48bac8e218d7c92a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_Roughness.png using Guid(20b33505116d1cd48bac8e218d7c92a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a891cd2eaaba4295bb03ad446edb461f') in 0.0837307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_03.png
  artifactKey: Guid(853145fd99dfb6c40a8b6415948d7a9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_03.png using Guid(853145fd99dfb6c40a8b6415948d7a9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4a24ab1950aba7e964b79a1846003e0') in 0.0897027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_30.png
  artifactKey: Guid(9db5ea18a4780694785a47809188c026) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_30.png using Guid(9db5ea18a4780694785a47809188c026) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '822eb7e7f7ab21e0035fbcadef4de444') in 0.0740709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_DetailWeight.png
  artifactKey: Guid(29f46698a2b57a14ea01769f05237850) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_DetailWeight.png using Guid(29f46698a2b57a14ea01769f05237850) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6630c4784b33ebd0c9e1f0d2ee9630e0') in 0.1080503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_10.png
  artifactKey: Guid(a10c4b09d28991c4aa42b4f876717b67) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_10.png using Guid(a10c4b09d28991c4aa42b4f876717b67) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0d536623f7271d0b0ec437d08efc392') in 0.0803143 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Normal_02.png
  artifactKey: Guid(4f0916fb0d3d5ac45936dcad0896aa12) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Normal_02.png using Guid(4f0916fb0d3d5ac45936dcad0896aa12) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b7396a33edbd12a6f53dccea25a42e9') in 0.1005144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_08.mat
  artifactKey: Guid(7f70399ff1c5e694c8e6f2f2b3f1d662) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_08.mat using Guid(7f70399ff1c5e694c8e6f2f2b3f1d662) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e431c26b9d7e73bc3ff9a3b469915054') in 0.117592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_09.png
  artifactKey: Guid(eae0dd72aeb9d774f8e0b8f7e1d7e478) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_09.png using Guid(eae0dd72aeb9d774f8e0b8f7e1d7e478) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef9d1ba337be45d01716093dd6f67a89') in 0.0803443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_36.mat
  artifactKey: Guid(dba1b25dcb501654c93a156b348d6e0f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_36.mat using Guid(dba1b25dcb501654c93a156b348d6e0f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '607cba36b2afb2d7d922a8b5f1809516') in 0.1596977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_01_Dirty.mat
  artifactKey: Guid(2e32a9422949e5749b04e49e9154fd5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_01_Dirty.mat using Guid(2e32a9422949e5749b04e49e9154fd5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7267b1d4595b388fb92956d267d00fc2') in 0.2622491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_08.png
  artifactKey: Guid(5c8d6268f0c286f4896b5883505ce148) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_08.png using Guid(5c8d6268f0c286f4896b5883505ce148) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '469c59b16b6f7f5292b777d4bba8535a') in 0.1159965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_22.png
  artifactKey: Guid(954f7b451d08f9340972df57dc2d73cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_22.png using Guid(954f7b451d08f9340972df57dc2d73cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0cb82021737d3861c57f081cf70bc7ba') in 0.1001788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Meshes/Girl/SK_ROCA_HUMAN.fbx
  artifactKey: Guid(2462c565c16c7b7418250aab2bd4cd2a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Meshes/Girl/SK_ROCA_HUMAN.fbx using Guid(2462c565c16c7b7418250aab2bd4cd2a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10351479e6f38369e2015a061aa4850a') in 0.3151322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 380

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Orange.mat
  artifactKey: Guid(9503aa2300e0bf34fb32ef8d63124d55) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Orange.mat using Guid(9503aa2300e0bf34fb32ef8d63124d55) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d22ba2bd3441f1704c79e8fe0f2068e') in 0.0961569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_02.png
  artifactKey: Guid(03e10e021e0bf5847ad15f8e08286115) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_02.png using Guid(03e10e021e0bf5847ad15f8e08286115) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8422cf8ca8fc0042a4a9a7e7c658124e') in 0.1391357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Scripts/UpperBodyAnimations-SpineProxy.url
  artifactKey: Guid(600f7cf1c6250e84687d018d42666742) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Scripts/UpperBodyAnimations-SpineProxy.url using Guid(600f7cf1c6250e84687d018d42666742) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd674af673941e87639e00f1b96d91c27') in 0.0346949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000108 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Walking.fbx
  artifactKey: Guid(b1140fd1818841e47892a909aeb6ca9c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Walking.fbx using Guid(b1140fd1818841e47892a909aeb6ca9c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8fff04fd48dc5dd0467dc12244a102f') in 0.0702512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Msh)Ladder.fbx
  artifactKey: Guid(0c1072a814aa1cd469c0d19095682873) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Msh)Ladder.fbx using Guid(0c1072a814aa1cd469c0d19095682873) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e3697c5f03b5119722f55b1233a72a7') in 0.0584977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_04.png
  artifactKey: Guid(93af1900dff22f24eb3a4ec982a88b8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_04.png using Guid(93af1900dff22f24eb3a4ec982a88b8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9183673b6bd33ee20393a1a752f22f4') in 0.0565258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Normal.png
  artifactKey: Guid(a4cb367a5c2cd6e4f850b8c7d5f0d59e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Normal.png using Guid(a4cb367a5c2cd6e4f850b8c7d5f0d59e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa233a766f5fd56c8b3b6c6089dd5c97') in 0.1049857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Green.mat
  artifactKey: Guid(41cdb632b6e13624c86ce356489d6e6d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Green.mat using Guid(41cdb632b6e13624c86ce356489d6e6d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '114b79a43740c3a638a0f1586713f53d') in 0.0500951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)FallingIdle.fbx
  artifactKey: Guid(35e3e1f632c392241ba3328c02b89693) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)FallingIdle.fbx using Guid(35e3e1f632c392241ba3328c02b89693) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba85fa954f2bc46f096272e1b2713f22') in 0.0520401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_05.png
  artifactKey: Guid(893df5c2610a197459305833015b05fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_05.png using Guid(893df5c2610a197459305833015b05fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d6f75e457cbeefafb329b8fb209fe56') in 0.0582815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Blue.mat
  artifactKey: Guid(d8b06aed0f8442a4693303bcffe3c7c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Blue.mat using Guid(d8b06aed0f8442a4693303bcffe3c7c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69f476e6448f415c95b86bf8da0f1913') in 0.0434701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_18.png
  artifactKey: Guid(2c388e26fab804f4faf56fe017699531) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_18.png using Guid(2c388e26fab804f4faf56fe017699531) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2334b0d0dc5a1ed509979ee7b5dc0ec6') in 0.0876634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset
  artifactKey: Guid(a9375f78e085f844d88ad1bda4308bd8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset using Guid(a9375f78e085f844d88ad1bda4308bd8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '838706bd73cd24e48e5bba770050f757') in 0.0354598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.056456 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab
  artifactKey: Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab using Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06bf23d271318b8cef8032bad6be311d') in 0.0726895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_2.prefab
  artifactKey: Guid(84b2e7fd01ad126468f437666801b5b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_2.prefab using Guid(84b2e7fd01ad126468f437666801b5b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32bf8b897189739a480b9b780c2df127') in 0.0812058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Terrain.prefab
  artifactKey: Guid(9acf48a55782c5b4da0118d166df5563) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Terrain.prefab using Guid(9acf48a55782c5b4da0118d166df5563) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69750f2f076f3976281c357a528892bd') in 0.0735522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundWaterSplash_4x5_03.psd
  artifactKey: Guid(37748109d5da5e3438c95a8c256a3fd2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundWaterSplash_4x5_03.psd using Guid(37748109d5da5e3438c95a8c256a3fd2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a978377db8273b7854a82ca5d3f19462') in 0.1025268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab
  artifactKey: Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab using Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd0144c7c7a6baa99d55b298d1e19d51') in 0.2517599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_VerticalImpact_01.png
  artifactKey: Guid(278c96278e68ac24da20c483f5902657) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_VerticalImpact_01.png using Guid(278c96278e68ac24da20c483f5902657) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9b6dffc0e2e68d012a2bf51251eb2b5') in 0.061667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamLoop_4x6_01.psd
  artifactKey: Guid(96ddf442242721f4ebb7f795ff5083e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamLoop_4x6_01.psd using Guid(96ddf442242721f4ebb7f795ff5083e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59cd6703470ffed5b67e136b20521a77') in 0.0933055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Gradient/FX_TX_Gradients_Blue_03.psd
  artifactKey: Guid(faa4dcc48952abd45ba057538ed3f5e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Gradient/FX_TX_Gradients_Blue_03.psd using Guid(faa4dcc48952abd45ba057538ed3f5e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c23e2b4e7e957686f0a867ccde68a19c') in 0.0798398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralSplash_3x4_02.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralSplash_3x4_02.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef278203e0678364dec22fd2c9cbdedf') in 0.1209787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_03.psd
  artifactKey: Guid(dcafdbbe4768c67449be25eee92d08f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_03.psd using Guid(dcafdbbe4768c67449be25eee92d08f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '386e7c96a3f4cab965f75e62cb704822') in 0.0550331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_04.psd
  artifactKey: Guid(1802ca92ffb162442bf5623c848b248e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_04.psd using Guid(1802ca92ffb162442bf5623c848b248e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f69f8f14abe122864518af991e766ea') in 0.0717965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWaterSlide_5x5_01.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWaterSlide_5x5_01.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45f9b29033dc20ab7928b2f11aa6195c') in 0.0743504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_SlashBasic.playable
  artifactKey: Guid(28c3506b660c6e842834b924f1046a73) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_SlashBasic.playable using Guid(28c3506b660c6e842834b924f1046a73) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81640b2be25392d73a410d9d6f90d7fd') in 0.0568964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.353088 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat
  artifactKey: Guid(79459efec17a4d00a321bdcc27bbc385) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat using Guid(79459efec17a4d00a321bdcc27bbc385) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bf88810a381fb1739c4ef881ee92936') in 0.1537518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_02.mat
  artifactKey: Guid(ba098071d5cb82c479c33ce3a8cfd69d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_02.mat using Guid(ba098071d5cb82c479c33ce3a8cfd69d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4c7bbbee5261c23d1e3b5fee8974887') in 0.720044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Normal.png
  artifactKey: Guid(5686f427bd7b7f14a97237fabf5c9ce8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Normal.png using Guid(5686f427bd7b7f14a97237fabf5c9ce8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76a674ff835664aae5b18f172297efd2') in 0.0887645 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_12.mat
  artifactKey: Guid(b82015d7413ba4443bf637284a41bb7c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_12.mat using Guid(b82015d7413ba4443bf637284a41bb7c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd551ccd8deedb42a0ac00c6bfe794c8') in 0.1888939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplashSlides_4x6_02.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplashSlides_4x6_02.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1679ac44875478b3315ffb7086f40f0') in 0.0799696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity
  artifactKey: Guid(cc778ff8e7d7b874d9ef5cf06279e322) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Human Basic Motions - Unity Demo Scene.unity using Guid(cc778ff8e7d7b874d9ef5cf06279e322) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cbdee24a1488f91fe97b1383c2669c7') in 0.079602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_09.mat
  artifactKey: Guid(21068f61f426ade47bf992a7e756b06a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_09.mat using Guid(21068f61f426ade47bf992a7e756b06a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1cdb6ae7a1b770960ea883e3088a444') in 0.1913017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_ID.png
  artifactKey: Guid(6f938d013d38b0848af6326bf5235420) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_ID.png using Guid(6f938d013d38b0848af6326bf5235420) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c15908102a1e44b464a70df59fbc317') in 0.0832615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Materials/Env_MT_Ground.mat
  artifactKey: Guid(3d868ad19e0922843b55854c277ffa7b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Materials/Env_MT_Ground.mat using Guid(3d868ad19e0922843b55854c277ffa7b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e73ceda9148d722b95785fbd3013aa4') in 0.0727885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_ImpactSplash_4x4_01.png
  artifactKey: Guid(4e564f84065d81043913c36ae84a49f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_ImpactSplash_4x4_01.png using Guid(4e564f84065d81043913c36ae84a49f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce2068f4b110e9d071e4e11d9948c342') in 0.0594478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_3x4_02.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_3x4_02.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9af162ff40f9e24eda156b8501e2d23') in 0.0684486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Human Body Full Mask.mask
  artifactKey: Guid(89527f5525238ee44b3182458d85143a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Human Body Full Mask.mask using Guid(89527f5525238ee44b3182458d85143a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d5c4da8e6ef40bdfe5395a291c4790f') in 0.0498228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_3x3_01.png
  artifactKey: Guid(2e09065ce24256a40b351bd000dccf32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_3x3_01.png using Guid(2e09065ce24256a40b351bd000dccf32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e4424c9d1044fc988428ed1165f74ef') in 0.0463806 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Shiny_2.wav
  artifactKey: Guid(0b0828ecc02f9044c9e92738395f9ca3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Shiny_2.wav using Guid(0b0828ecc02f9044c9e92738395f9ca3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d3879e3bb5429b7fe0dea01c15719ea') in 0.2061208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_2.wav
  artifactKey: Guid(95776e67b327c8a44b041e9cfcc6c714) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_2.wav using Guid(95776e67b327c8a44b041e9cfcc6c714) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f6b53e3a5a30993eabbdb7aeb6b34c22') in 0.156523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_2.wav
  artifactKey: Guid(11a786d60a3923f4e93818678a9471b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_2.wav using Guid(11a786d60a3923f4e93818678a9471b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '507ba1b4d77722e5b75e0d935ddd2ee5') in 0.1141477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/0__Intro/<EMAIL>
  artifactKey: Guid(8ed1f4ca0f13d6f459c9ceb935df630e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/0__Intro/<EMAIL> using Guid(8ed1f4ca0f13d6f459c9ceb935df630e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5179fbe2fcd2ac7bf41731a90b639a43') in 0.0626018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_08.mat
  artifactKey: Guid(b0f22c81a762df14c8131ebeb5e37db1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_08.mat using Guid(b0f22c81a762df14c8131ebeb5e37db1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8cb47d4983e0c886595155763c400e81') in 0.1579309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_AiryString_1.wav
  artifactKey: Guid(71439feb59d0db94d85252ce3f083721) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_AiryString_1.wav using Guid(71439feb59d0db94d85252ce3f083721) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7924968d177ea8cf355525a70e6a03a1') in 0.1724086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_C.FBX
  artifactKey: Guid(a85a5d9ac17217e4995311a9c79ab5e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_C.FBX using Guid(a85a5d9ac17217e4995311a9c79ab5e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '139105632ae9c7eeadeabb3b0dbaf4b9') in 0.0657636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_09.mat
  artifactKey: Guid(3ac5cb3c9c46652438c4ed34faec6511) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_09.mat using Guid(3ac5cb3c9c46652438c4ed34faec6511) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7c89b74eb097a2f38381143caa68c42') in 0.2085614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_16.mat
  artifactKey: Guid(e182b5761b89f904b9ca99ff4bd39c03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_16.mat using Guid(e182b5761b89f904b9ca99ff4bd39c03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '845832935c4fd9fde4086e58b0918496') in 0.1513109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmpleBubble_4x4_01.png
  artifactKey: Guid(690937ba5caf43442b6370654da4c8c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SmpleBubble_4x4_01.png using Guid(690937ba5caf43442b6370654da4c8c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2989b4f2e466a2702e35f9d241294d65') in 0.0801631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundSplash_5x5_01.png
  artifactKey: Guid(ef84c18cb1b95804dbecbef4809187c1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundSplash_5x5_01.png using Guid(ef84c18cb1b95804dbecbef4809187c1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f79b46575be4564ed73ebe9685420cf') in 0.0505364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_19.mat
  artifactKey: Guid(1704f5ba9e526dd4f837cb8369620582) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_19.mat using Guid(1704f5ba9e526dd4f837cb8369620582) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2036f362dcf870f48af07e11dea755e') in 0.1901398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/1__Idle/<EMAIL>
  artifactKey: Guid(a6807a6471eddc440aadf941d9d3bdec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/1__Idle/<EMAIL> using Guid(a6807a6471eddc440aadf941d9d3bdec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04efd4ebaba3bf47067fa7892ab1e8b7') in 0.0932622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_B.FBX
  artifactKey: Guid(55a90b1cb535bb64db2eb735415ace73) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_B.FBX using Guid(55a90b1cb535bb64db2eb735415ace73) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78b4f5932211192081926227ef2152f0') in 0.0586453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TwirlingWater_2x8_01.png
  artifactKey: Guid(bbc317af38799ee44aed7032052fda22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TwirlingWater_2x8_01.png using Guid(bbc317af38799ee44aed7032052fda22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f12577a683bd19ce5561a1012d1098d3') in 0.0578424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SlashNoisy_3x5_01.png
  artifactKey: Guid(ac124becbc9cb154e952e202889be043) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SlashNoisy_3x5_01.png using Guid(ac124becbc9cb154e952e202889be043) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c71887058d330c5e473268a121919c97') in 0.0492381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SimpleCenterSplash_4x4_01.png
  artifactKey: Guid(0dcbb9332dde99b4e94991a184e96fe1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_SimpleCenterSplash_4x4_01.png using Guid(0dcbb9332dde99b4e94991a184e96fe1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ebedfc9d4db8a9e1d23264a753a7238') in 0.1139867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000261 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL45.FBX
  artifactKey: Guid(f98fa90cdf410d04e9d23b123179dfe2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL45.FBX using Guid(f98fa90cdf410d04e9d23b123179dfe2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56e5cbdfbf4fdfed77fe146ac1e2b98a') in 0.0657685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x5_05.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x5_05.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2fb8b0a346f3e257459b56c87a5f732c') in 0.0564367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_05.mat
  artifactKey: Guid(3a815766bdf218e4382bead159c354ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_05.mat using Guid(3a815766bdf218e4382bead159c354ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fb5eceb44a1da3ad0a15b3516856aca') in 0.1950412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_1.wav
  artifactKey: Guid(17af8bebf81b84f429a5473979251de4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Small_1.wav using Guid(17af8bebf81b84f429a5473979251de4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5ed32c8bc2b28f2939462ecf9abd330') in 0.157061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_1.wav
  artifactKey: Guid(09abce736482db14b9580640218ac95f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_1.wav using Guid(09abce736482db14b9580640218ac95f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e3e2231b233f847fb13b116535f1109') in 0.1571155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR45.FBX
  artifactKey: Guid(842a66ec081769d458ffa2a9b5e10976) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BR45.FBX using Guid(842a66ec081769d458ffa2a9b5e10976) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b14f1d6a11c735e832335634f5ccda1e') in 0.0729413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FL90.FBX
  artifactKey: Guid(00cc6eff4eeef95478de9b75df6e10f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FL90.FBX using Guid(00cc6eff4eeef95478de9b75df6e10f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2139e58f5b9cf4679074a0a7b7d2f3ae') in 0.068986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Right.FBX
  artifactKey: Guid(358f10a7d9d12184a98552ffaccefbcc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Right.FBX using Guid(358f10a7d9d12184a98552ffaccefbcc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '390ae5f550fae2677361ba714e13dba9') in 0.0705403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_2_ZeroHeight.FBX
  artifactKey: Guid(c6682b74c650f464abf152cff106cad8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_2_ZeroHeight.FBX using Guid(c6682b74c650f464abf152cff106cad8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc4c5392e383c2bb5bf1753685081a68') in 0.072254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_B_Idle_Root.FBX
  artifactKey: Guid(916d101409d7f1c48afba6fa0847d302) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_B_Idle_Root.FBX using Guid(916d101409d7f1c48afba6fa0847d302) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a197b0658e6a30f22aedb9391873afc') in 0.0817677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_4.FBX
  artifactKey: Guid(9b8482507c77b8a4ca0b9df3fcd1369e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_4.FBX using Guid(9b8482507c77b8a4ca0b9df3fcd1369e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70b391dd6b86619cfc7f1c9a10e64b1b') in 0.0594463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Block_Root.FBX
  artifactKey: Guid(74e7f23d5dab6774986b51c5a5cb371c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Block_Root.FBX using Guid(74e7f23d5dab6774986b51c5a5cb371c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5de3b7c2caa27dae45b050138a71c498') in 0.0912248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 317

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_A_Idle_Root.FBX
  artifactKey: Guid(84e5455e577ab9644acdfee37f6bccdb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_A_Idle_Root.FBX using Guid(84e5455e577ab9644acdfee37f6bccdb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86f6921c03000e24a518b6d4cebf0d3c') in 0.0721303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Small_ver_A.FBX
  artifactKey: Guid(e029583522ec7a443826f69b40828de9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Small_ver_A.FBX using Guid(e029583522ec7a443826f69b40828de9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3684cdb3d877c3634d1eff1efd2339b') in 0.0699834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_B_Idle.FBX
  artifactKey: Guid(1bfa748a78838d54e9d5e2ddbee9924f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_B_Idle.FBX using Guid(1bfa748a78838d54e9d5e2ddbee9924f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '760a6f47befcd79c4af0454e6cc5f164') in 0.0634443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_B_Idle_Root.FBX
  artifactKey: Guid(6f91342f432c88e43bbf45ca97202c31) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_B_Idle_Root.FBX using Guid(6f91342f432c88e43bbf45ca97202c31) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b19632fe34bbfac9c8de2a56ccc0bb77') in 0.1202038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down_Loop.FBX
  artifactKey: Guid(d4054121478fbdd4ea1073f62a124f8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down_Loop.FBX using Guid(d4054121478fbdd4ea1073f62a124f8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbbf643c2c79f402ab54072755bffbb9') in 0.0780625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Damage.FBX
  artifactKey: Guid(2b8456fd9f6740b47bf215fec83517f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Damage.FBX using Guid(2b8456fd9f6740b47bf215fec83517f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27106366e9f15d73d201ee2b2cbc35ab') in 0.0608719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down_StandUp.FBX
  artifactKey: Guid(6c8c45bf3f9a1d145bd669c07c2a1891) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/2__Back/M_Big_Sword@Damage_Back_Down_StandUp.FBX using Guid(6c8c45bf3f9a1d145bd669c07c2a1891) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9c15656f9fadd91a9b37b50c8c2a0c9') in 0.0688217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_A_ALL_Inplace.FBX
  artifactKey: Guid(bbb0f43d3889d1f46a5f486382635934) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_A_ALL_Inplace.FBX using Guid(bbb0f43d3889d1f46a5f486382635934) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fea03eeb614a7cd1ab4d93254c19c9e') in 0.0748306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_A.FBX
  artifactKey: Guid(2091ebb37d4500b4c995e8ac413092dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Small_ver_A.FBX using Guid(2091ebb37d4500b4c995e8ac413092dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd1004ce7defd89d6138c81c12a2d71d') in 0.071879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Bird_Loop_1.wav
  artifactKey: Guid(7038fadafac57cc4a8e68732a3fb342d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Bird_Loop_1.wav using Guid(7038fadafac57cc4a8e68732a3fb342d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9045195aed058e40ef35f2b465820df6') in 0.2848599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_A.FBX
  artifactKey: Guid(133f831095087dd40815d6f4ce7c25a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_A.FBX using Guid(133f831095087dd40815d6f4ce7c25a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cf7d942a5bb172e6a520a3501e7946a') in 0.0723164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_1_Inplace.FBX
  artifactKey: Guid(1b1fb5d9e17dcfd4784c63b6cfb851ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_1_Inplace.FBX using Guid(1b1fb5d9e17dcfd4784c63b6cfb851ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aaab54ec647c3403e1298a4014e5bb17') in 0.0832257 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_ver_B.FBX
  artifactKey: Guid(234d21fcceba1f54abbc9b0fa5bfef50) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_ver_B.FBX using Guid(234d21fcceba1f54abbc9b0fa5bfef50) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ee57b26473c6628f29acb39237c6819') in 0.063294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start_ZeroHeight.FBX
  artifactKey: Guid(08940ae74eb4cf445bc0c086f238af20) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start_ZeroHeight.FBX using Guid(08940ae74eb4cf445bc0c086f238af20) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f864f5eecb5793fef976558b9970423') in 0.0733424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_3.FBX
  artifactKey: Guid(aede023101ef33d44a03f509344232d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_3.FBX using Guid(aede023101ef33d44a03f509344232d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a49b5e40dde6585f7e2f0ac670bd6b8') in 0.053254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_2_Attach.FBX
  artifactKey: Guid(a5ed3a8a51f54474c964b85f228e14db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_2_Attach.FBX using Guid(a5ed3a8a51f54474c964b85f228e14db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '970b20b251181697ecebab4495cb7216') in 0.0660934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90_Root.FBX
  artifactKey: Guid(339f038baa8dc9842a24aef07f8db69f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90_Root.FBX using Guid(339f038baa8dc9842a24aef07f8db69f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac75f281f56f8dd08fa08754f3fda0e5') in 0.0813684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_ZeroHeight.FBX
  artifactKey: Guid(623645921eee85042ac05b8e2887634c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_ZeroHeight.FBX using Guid(623645921eee85042ac05b8e2887634c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e64e44716d5150b2258f2df6a9ab5fb') in 0.0725882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop_ZeroHeight_Z0.FBX
  artifactKey: Guid(60c7ddb511241c545ba3341bea1202a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop_ZeroHeight_Z0.FBX using Guid(60c7ddb511241c545ba3341bea1202a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '926a0ce5b7667957f8a4a9903586831b') in 0.0561625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Big.FBX
  artifactKey: Guid(20f8efa7b8f510c4285df9055a3a1133) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Big.FBX using Guid(20f8efa7b8f510c4285df9055a3a1133) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e136f51992f98fe0ee098899ee3fed4b') in 0.0591287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Run_Fast_Dodge_Right.FBX
  artifactKey: Guid(e280a15393d8b264e9884f687c4ee775) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Run_Fast_Dodge_Right.FBX using Guid(e280a15393d8b264e9884f687c4ee775) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '318b69e6562978b56550b4a8992ead46') in 0.2520077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop_ZeroHeight.FBX
  artifactKey: Guid(89e03bb5a5462fb42862d23ed752b202) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Loop_ZeroHeight.FBX using Guid(89e03bb5a5462fb42862d23ed752b202) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '921ce12b9df643d7a07dc870fa5fa660') in 0.0877573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_A.FBX
  artifactKey: Guid(375775a403b7f3d4c8a8c1ad5f14ba45) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_A.FBX using Guid(375775a403b7f3d4c8a8c1ad5f14ba45) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '095c5fbe090225ff6f029b3586859e83') in 0.0679368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamIndividual_4x4_01.png
  artifactKey: Guid(46c0416a5a1b2f942a673cd678a96f32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamIndividual_4x4_01.png using Guid(46c0416a5a1b2f942a673cd678a96f32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8f7a4d1ebf75056d3e59f1868845424') in 0.0486653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_InteriorCups.mat
  artifactKey: Guid(abc00000000002684236441138652053) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_InteriorCups.mat using Guid(abc00000000002684236441138652053) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dffe920c7e7c25978e90f4ff89cc5bae') in 0.0640425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000211 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Attack_ver_A.FBX
  artifactKey: Guid(7725a65f99d5ae849a941554cd371e72) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Attack_ver_A.FBX using Guid(7725a65f99d5ae849a941554cd371e72) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aeb718ca3b4b4573b145e46dbafc9488') in 0.1138929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_01.mat
  artifactKey: Guid(abc00000000009361911379653530679) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_01.mat using Guid(abc00000000009361911379653530679) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24c281d0235db14f7e06693887adce90') in 0.1156039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Plain.mat
  artifactKey: Guid(abc00000000007593937091663335591) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Plain.mat using Guid(abc00000000007593937091663335591) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4040975592681e4778e0b8baa1fccdaf') in 0.0672568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.FBX
  artifactKey: Guid(06a1e7d4717534d4b84cf0a79073ca12) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.FBX using Guid(06a1e7d4717534d4b84cf0a79073ca12) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f24d8d552773f137d867ea26580eac1') in 1.5331706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_09.prefab
  artifactKey: Guid(abc00000000004464500011015390631) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_09.prefab using Guid(abc00000000004464500011015390631) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df8fad837aacf1e8e152bb787cd378f3') in 0.1243276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000712 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_30.fbx
  artifactKey: Guid(abc00000000008799890617518498350) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_30.fbx using Guid(abc00000000008799890617518498350) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a8f7005c1a900c9e04739c3d9c03329') in 0.4811697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_70.fbx
  artifactKey: Guid(abc00000000001100707113863616597) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_70.fbx using Guid(abc00000000001100707113863616597) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3285d714bb075faea2234b1a2a0aae22') in 0.07518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Red.mat
  artifactKey: Guid(abc00000000009153909646925066780) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Red.mat using Guid(abc00000000009153909646925066780) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3bfab002264fee2d12c6f204edaa07f4') in 0.0739737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.prefab
  artifactKey: Guid(4243487e5ba110147988dda3e63b9b8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_4.prefab using Guid(4243487e5ba110147988dda3e63b9b8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3edc4da3dec5738874dc568233b8179c') in 0.0761952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_WoodPlanks.mat
  artifactKey: Guid(abc00000000004986009580943129396) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_WoodPlanks.mat using Guid(abc00000000004986009580943129396) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '274cc71c233915c85288956de5df9dd9') in 0.0511958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Stab_Algae.mat
  artifactKey: Guid(abc00000000006230293555905555623) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Stab_Algae.mat using Guid(abc00000000006230293555905555623) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0326f13b4a036713cc1b1242a0d7fce') in 0.2557453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_03.prefab
  artifactKey: Guid(abc00000000002799692809114770013) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_03.prefab using Guid(abc00000000002799692809114770013) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '945b888b7e715ca2e91a5b8891319e67') in 0.1073609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_A.prefab
  artifactKey: Guid(abc00000000010709643981833476098) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_A.prefab using Guid(abc00000000010709643981833476098) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cdab466715ee614f2b3564a7e9701763') in 0.0683009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack.prefab
  artifactKey: Guid(abc00000000012140916782779676615) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack.prefab using Guid(abc00000000012140916782779676615) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ae65a0af1e7c6976126ceca8c69cf61') in 0.0618559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000166 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CartWheel_01.prefab
  artifactKey: Guid(abc00000000003085888915241965482) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CartWheel_01.prefab using Guid(abc00000000003085888915241965482) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8092a3d5f28aeac7236e4a5823814419') in 0.0667318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BasicTextured.mat
  artifactKey: Guid(abc00000000000403124660741396823) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BasicTextured.mat using Guid(abc00000000000403124660741396823) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8c971cb1a83b5a495b32a723d941ebf') in 0.0963682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04.prefab
  artifactKey: Guid(abc00000000017741063982725564442) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04.prefab using Guid(abc00000000017741063982725564442) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '845e12aec7b43255c8826e65d83768ee') in 0.1530993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.prefab
  artifactKey: Guid(a5d09d5371936d24984839e062302b43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_13.prefab using Guid(a5d09d5371936d24984839e062302b43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8aca82538a8aa59d63a24e4ae749b425') in 0.2768999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_01.prefab
  artifactKey: Guid(abc00000000016444828941082041913) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_01.prefab using Guid(abc00000000016444828941082041913) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '941601dc0efd60c5b3b6d26e542d1909') in 0.0713264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_12.fbx
  artifactKey: Guid(abc00000000001978877052994093273) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_12.fbx using Guid(abc00000000001978877052994093273) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e26efd4f4e56914e337fe88d2236c2a') in 0.0838936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Barrel_01.prefab
  artifactKey: Guid(abc00000000000449322208676681965) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Barrel_01.prefab using Guid(abc00000000000449322208676681965) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e0d2c12b109941777398ef60400b6a5') in 0.0974764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Pots.prefab
  artifactKey: Guid(abc00000000005771072365272981121) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Pots.prefab using Guid(abc00000000005771072365272981121) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eaa4301a9919174b38e47ed0458a93f3') in 0.0631595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockBeam_02.prefab
  artifactKey: Guid(abc00000000005234286136877360854) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockBeam_02.prefab using Guid(abc00000000005234286136877360854) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd095460b2e914d6b1853b0edd3e6fb9') in 0.0687275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_XL_02.prefab
  artifactKey: Guid(abc00000000012231957801027281667) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_XL_02.prefab using Guid(abc00000000012231957801027281667) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c28103e571762c6fbf7ccd71d472f91') in 0.0705934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_17.prefab
  artifactKey: Guid(abc00000000004371273187608930184) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_17.prefab using Guid(abc00000000004371273187608930184) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93c54b5b88d68f85e24bcc19528b5c1a') in 0.0707761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_1.prefab
  artifactKey: Guid(abc00000000015334481626655366618) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_1.prefab using Guid(abc00000000015334481626655366618) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3ba532aad3115c3ea281b6cb93c249c') in 0.0731733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chest_Open_01.prefab
  artifactKey: Guid(abc00000000006417150632656342615) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chest_Open_01.prefab using Guid(abc00000000006417150632656342615) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e71128729e5ac731221e50c1138aec55') in 0.0638451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_OutsideCorner_01.prefab
  artifactKey: Guid(abc00000000011143252428546293327) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_OutsideCorner_01.prefab using Guid(abc00000000011143252428546293327) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f3e213d38408443a7ee61678c49b02b') in 0.0816726 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_70.prefab
  artifactKey: Guid(abc00000000011878893216896833501) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_70.prefab using Guid(abc00000000011878893216896833501) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b51c86e08f4445d71b8cbce23976efcf') in 0.0597101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_6x8_01.prefab
  artifactKey: Guid(abc00000000005649026582625509863) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_6x8_01.prefab using Guid(abc00000000005649026582625509863) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa479801cb90e28db6b2902eaecc5503') in 0.0916733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_Door.prefab
  artifactKey: Guid(abc00000000013475826865379448896) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_Door.prefab using Guid(abc00000000013475826865379448896) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47bea8596f5e1d8b0a00558fff1e7a3e') in 0.0672368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.prefab
  artifactKey: Guid(98e11877557dba94da7b865dbfcbdf82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.prefab using Guid(98e11877557dba94da7b865dbfcbdf82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6998b2fd8e9a4b0c9431df74a62ce200') in 0.460822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07_1.prefab
  artifactKey: Guid(abc00000000002568350946993410206) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_07_1.prefab using Guid(abc00000000002568350946993410206) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'db3fa6cef7e2d443e6e66bd00c7dfa1a') in 0.0743005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01.prefab
  artifactKey: Guid(abc00000000004222044396635746146) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01.prefab using Guid(abc00000000004222044396635746146) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17f1ec59fdec6adf5e23713261d4e95f') in 0.0707867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_04.prefab
  artifactKey: Guid(abc00000000006585424960363013979) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_RockCliff_04.prefab using Guid(abc00000000006585424960363013979) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b5f0e9d42bcdd6dcdd0085ea3f07ed1') in 0.0712996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lsarge_Rock_B.prefab
  artifactKey: Guid(abc00000000005713074273989172594) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lsarge_Rock_B.prefab using Guid(abc00000000005713074273989172594) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fd8266a9a622affaf66ca9cb6682ee4') in 0.0587665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rock_Large_A.prefab
  artifactKey: Guid(abc00000000012409974318942294950) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rock_Large_A.prefab using Guid(abc00000000012409974318942294950) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b7d73a5c69ed50ed7fd7c019c0ffc1b') in 0.0715893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis.prefab
  artifactKey: Guid(abc00000000015397822618480844408) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis.prefab using Guid(abc00000000015397822618480844408) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91bfaf6f77a95478e336fb2155db2791') in 0.063886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_04.prefab
  artifactKey: Guid(abc00000000007611508329726168971) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_04.prefab using Guid(abc00000000007611508329726168971) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e275a8c0cfa8ef66b6026cb151c8940') in 0.0578698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlagPole.prefab
  artifactKey: Guid(abc00000000007993684537440899761) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlagPole.prefab using Guid(abc00000000007993684537440899761) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd85d253f23147597e9a58d0196298496') in 0.059885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Main_Stairs.prefab
  artifactKey: Guid(abc00000000009185449643404600751) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Main_Stairs.prefab using Guid(abc00000000009185449643404600751) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d7f1062ac83e6bc8b6999636648029b') in 0.0818735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02.prefab
  artifactKey: Guid(abc00000000010829330222347857713) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02.prefab using Guid(abc00000000010829330222347857713) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cb01eb6943f03f4a81e9f005b902c59') in 0.0669429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_9.prefab
  artifactKey: Guid(abc00000000002923321966201374329) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_9.prefab using Guid(abc00000000002923321966201374329) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d92c34dc2c5aee2623fda5138ccd670') in 0.069187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_1.prefab
  artifactKey: Guid(abc00000000007856381484955721662) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_1.prefab using Guid(abc00000000007856381484955721662) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb30098cc7acc270bae8cd5189272b1a') in 0.0606656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Water.shadergraph
  artifactKey: Guid(0eaae29f371926a4fb41ee2ce13d0bf3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Water.shadergraph using Guid(0eaae29f371926a4fb41ee2ce13d0bf3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43fe909d4e81699548d1630134573276') in 0.0461472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_BaseColor.PNG
  artifactKey: Guid(88b5c6ad1bfd98f44a994e8e1b85abe2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_BaseColor.PNG using Guid(88b5c6ad1bfd98f44a994e8e1b85abe2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a94e1c089cb81189c6e2fe80a859c506') in 0.0505832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_01.prefab
  artifactKey: Guid(abc00000000004070176755419816583) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_01.prefab using Guid(abc00000000004070176755419816583) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0d8288c5be28eda11332e73223e489d') in 0.0748792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_BaseColor.PNG
  artifactKey: Guid(40cd11d39764dea4da97f31ef8a98fad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_BaseColor.PNG using Guid(40cd11d39764dea4da97f31ef8a98fad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8ea7f020085b42f8e2887a1eebdb9ae') in 0.1088352 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_C_01.prefab
  artifactKey: Guid(abc00000000004928823807164677240) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WoodFloor_C_01.prefab using Guid(abc00000000004928823807164677240) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd83bb70838307323898838cd6fa43327') in 0.0634543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sidetable_01.prefab
  artifactKey: Guid(abc00000000014240842798744314698) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sidetable_01.prefab using Guid(abc00000000014240842798744314698) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0297e0dbf0131d8fd8a8947204ba6c23') in 0.0676863 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/SubG_HeightLerp.shadersubgraph
  artifactKey: Guid(9f032a316b848134a8ed0d24e4f5db04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/SubG_HeightLerp.shadersubgraph using Guid(9f032a316b848134a8ed0d24e4f5db04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f98427f0795ef6f29a76401a1fcc7ed1') in 0.0731735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Mountain.shadergraph
  artifactKey: Guid(d945d256d85c66740a1dd6a92a079288) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Mountain.shadergraph using Guid(d945d256d85c66740a1dd6a92a079288) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf49f14d1a79877d3b4063a4097ab1dc') in 0.068101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_E.prefab
  artifactKey: Guid(abc00000000002381047540023078438) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_E.prefab using Guid(abc00000000002381047540023078438) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1906e6d0e540337b0d0e4625d9ec56a9') in 0.0592083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle_1.prefab
  artifactKey: Guid(abc00000000002300554496726977217) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle_1.prefab using Guid(abc00000000002300554496726977217) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34fcced8b0bc4bab800d4673e8bfa9e7') in 0.0607103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_BnWStripes_M.PNG
  artifactKey: Guid(37c702ed8621ee841a491e44c7c370fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_BnWStripes_M.PNG using Guid(37c702ed8621ee841a491e44c7c370fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f026ef31fb9d0f0b5b9a31bb66dbd28') in 0.0477863 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cups_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(0a9376061213cf84b82fe0094d391068) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cups_OcclusionRoughnessMetallic.PNG using Guid(0a9376061213cf84b82fe0094d391068) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7274d4a81925b9b3511874f975ef71e') in 0.0604444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ground_Moss_D.PNG
  artifactKey: Guid(7ae7e96815378944894857125e716a33) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ground_Moss_D.PNG using Guid(7ae7e96815378944894857125e716a33) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bd05ca8e5ff8ff4a12152b197694f21') in 0.0463875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(cbab2e964545e344f9817f9cfc78cf4c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_candles_standardSurface1_OcclusionRoughnessMetallic.PNG using Guid(cbab2e964545e344f9817f9cfc78cf4c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eabb11bf2b4b908d5a7be694a581ea53') in 0.0497428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cloud_Tile.PNG
  artifactKey: Guid(c96a45c0a658f7346a5ce33bb3072493) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cloud_Tile.PNG using Guid(c96a45c0a658f7346a5ce33bb3072493) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f2370d72f02992310294a94260c24d8') in 0.0635477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_M.PNG
  artifactKey: Guid(0927ae6004cd69b4ea51e29c29613797) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_M.PNG using Guid(0927ae6004cd69b4ea51e29c29613797) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70fd60d1f9a8d26b43ac99b569983d65') in 0.071425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_normal.PNG
  artifactKey: Guid(0519dceba432c09479d347bc918410df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Gravel_normal.PNG using Guid(0519dceba432c09479d347bc918410df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e154a3851f5fac6443eee81ba7914dab') in 0.0503818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_kloofendal_48d_partly_cloudy_puresky_4k.EXR
  artifactKey: Guid(d7b9e06142a908940b67c82d3bda5cb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_kloofendal_48d_partly_cloudy_puresky_4k.EXR using Guid(d7b9e06142a908940b67c82d3bda5cb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af2f8132c3e6053ac8177d33c527bcb7') in 0.07199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lanter_Colour.PNG
  artifactKey: Guid(1cc06debe04b6d347a8b5e2924af8283) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lanter_Colour.PNG using Guid(1cc06debe04b6d347a8b5e2924af8283) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f0b6d9dbde068f8c14b8d3206b5c5a6') in 0.0609835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GroundRock_ARM.PNG
  artifactKey: Guid(9ff33a666f606f44784f4c1cf29dc280) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GroundRock_ARM.PNG using Guid(9ff33a666f606f44784f4c1cf29dc280) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '331d5431b97d8f2d94e637cc6cbb0cb6') in 0.0704284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_normal.PNG
  artifactKey: Guid(aea770b63d8d8694c873448e5c90c437) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_normal.PNG using Guid(aea770b63d8d8694c873448e5c90c437) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc59a333f43927d3b8375c1d0204c5b7') in 0.0532696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_N.PNG
  artifactKey: Guid(901f84322a201a34782767ba167c1fa1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Bark_01_N.PNG using Guid(901f84322a201a34782767ba167c1fa1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c59e64b08ee942b8ad0a01a48adb5a37') in 0.0528624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_01_N.PNG
  artifactKey: Guid(f229b4a01c701cd4486f49a0d315e15c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_01_N.PNG using Guid(f229b4a01c701cd4486f49a0d315e15c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f616ef2d53ac84f8a67f81e6a790a132') in 0.06125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_normal.PNG
  artifactKey: Guid(aa0991dfd6590e44f8eee7e3b21a18d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_normal.PNG using Guid(aa0991dfd6590e44f8eee7e3b21a18d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3f6575a8198aa008f51c5305df3c8e0') in 0.1174844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_RMA.PNG
  artifactKey: Guid(ce1304f91a7d7d34d8f63fba5586f0ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_RMA.PNG using Guid(ce1304f91a7d7d34d8f63fba5586f0ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2223b0098ed05ad9c93e005e83a3ad6d') in 0.0541153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000124 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_M.PNG
  artifactKey: Guid(47e4148e38f4100468e6269838d70a1d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_M.PNG using Guid(47e4148e38f4100468e6269838d70a1d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '924d36a5dbd6ab993a1a77b85a2658cd') in 0.0744445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SwordShield_Mask01.TGA
  artifactKey: Guid(5f60de88d49a12f44995003a37ed7516) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SwordShield_Mask01.TGA using Guid(5f60de88d49a12f44995003a37ed7516) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '793ad021557fb8a9a9b3b92a3aead1cc') in 0.0603606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterFlow_01_Foam_Tiled_N.PNG
  artifactKey: Guid(a0722a989b937fb4485282823cbe6af2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterFlow_01_Foam_Tiled_N.PNG using Guid(a0722a989b937fb4485282823cbe6af2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '474f070506fd0e02bd2c4e9776e5a756') in 0.0611908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_basecolor - Copy.PNG
  artifactKey: Guid(01c8d2f55d8c02c498561334dfa7d992) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_basecolor - Copy.PNG using Guid(01c8d2f55d8c02c498561334dfa7d992) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd52454fa3f9ef9702ba5d9cb12d062a') in 0.0494911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDamage_ARM.PNG
  artifactKey: Guid(a886ac67bcfd97048bd1fcb5b6f971c9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDamage_ARM.PNG using Guid(a886ac67bcfd97048bd1fcb5b6f971c9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccf38504d4327080337502eb5e34a542') in 0.0490211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_Slate_N.PNG
  artifactKey: Guid(b2e7b535ada8701419316a07889b958d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rock_Slate_N.PNG using Guid(b2e7b535ada8701419316a07889b958d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c495bda060b882b111db3c60059a0b2') in 0.0452849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arm Left Mask.mask
  artifactKey: Guid(3c01d1ed15a1c0f49b77151cfdaa3e8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arm Left Mask.mask using Guid(3c01d1ed15a1c0f49b77151cfdaa3e8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3e3a8a7026ae86869640f80a2d5d306') in 0.0538683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Left.controller
  artifactKey: Guid(547eb51a8a16d124eb430a2be2e67518) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Left.controller using Guid(547eb51a8a16d124eb430a2be2e67518) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b4abf5c32e91ada22ad1f9c1e442ad6') in 0.0298631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Right.controller
  artifactKey: Guid(9a1c467f49c89fb40a3d7a3048d6c412) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Right.controller using Guid(9a1c467f49c89fb40a3d7a3048d6c412) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acae825e6b01139327d4ed7344e85ed0') in 0.0313953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000189 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_BackwardLeft.controller
  artifactKey: Guid(06c49bcaab16ee64286ab7119dc8b645) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_BackwardLeft.controller using Guid(06c49bcaab16ee64286ab7119dc8b645) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2c4166513883887315d9a452b05538a') in 0.0387385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/FirstPersonCameraController.cs
  artifactKey: Guid(d20ea1623cbcedd4aa3575d62034ff08) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/FirstPersonCameraController.cs using Guid(d20ea1623cbcedd4aa3575d62034ff08) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7dcc9b4d17dc9c2bdbeb9451c5d184e4') in 0.038069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Right.controller
  artifactKey: Guid(28ff3835120968e4096d1b55ced1d49e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Right.controller using Guid(28ff3835120968e4096d1b55ced1d49e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e82142831cf9620b1f316ee7dabf9e6e') in 0.0426814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_ForwardRight.controller
  artifactKey: Guid(54441a28445fa614e9a3f00552da1014) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_ForwardRight.controller using Guid(54441a28445fa614e9a3f00552da1014) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '989f976b500c16ed9dee5adc4ebbb3f7') in 0.0395456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(013e11dc1ceff254282379cbb8cb530f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(013e11dc1ceff254282379cbb8cb530f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ae24a88b27b4377a3156ee0e5011502') in 0.0325278 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/MovingPlatform.cs
  artifactKey: Guid(9e6e42095cd5f6941a76244515f2a735) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/MovingPlatform.cs using Guid(9e6e42095cd5f6941a76244515f2a735) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b1b29d417cf522e781e8531b0abac736') in 0.025704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_basecolor.PNG
  artifactKey: Guid(23b187aef7cb8514a9de14f9cf93c1df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_basecolor.PNG using Guid(23b187aef7cb8514a9de14f9cf93c1df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf7b6193205c8c74eb7c9f4b356412af') in 0.0572711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_basecolor.PNG
  artifactKey: Guid(eb25465056a06d247a208fac64ce24e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Moss_basecolor.PNG using Guid(eb25465056a06d247a208fac64ce24e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd860042d99ce856c2f4c0210fa2b539d') in 0.044258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Run_A.FBX
  artifactKey: Guid(5f2dea4f744809c41b8d966b9ac90d52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Run_A.FBX using Guid(5f2dea4f744809c41b8d966b9ac90d52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c843367e81128c64503bb6c5b9a001e7') in 0.0571372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_A.FBX
  artifactKey: Guid(f9834ccebb896dd448d25612ca0060cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_A.FBX using Guid(f9834ccebb896dd448d25612ca0060cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8279bb9d1aaffaec9a5c87773a11914e') in 0.0696409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Walk_B_Turn_L90.FBX
  artifactKey: Guid(32a1cc8d8811feb49ac1a91d9fc873c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Walk_B_Turn_L90.FBX using Guid(32a1cc8d8811feb49ac1a91d9fc873c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df3b585a0a29e6e470fb5e13bf35946a') in 0.0572534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/TrampolineSensor.cs
  artifactKey: Guid(179777db0dc87894fa5a9ac316dfe1ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/TrampolineSensor.cs using Guid(179777db0dc87894fa5a9ac316dfe1ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c4f220e1d7bdc4e117245f47f089467') in 0.0290925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000308 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_B.FBX
  artifactKey: Guid(c1cccb03e87832b4caf742768f5d303e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_B.FBX using Guid(c1cccb03e87832b4caf742768f5d303e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5223143210804320a6b7e4f1155f16be') in 0.0625434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_B_Turn_R90_Root.FBX
  artifactKey: Guid(12d828ee981a3bd4da409dd8d13110e8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Run_B_Turn_R90_Root.FBX using Guid(12d828ee981a3bd4da409dd8d13110e8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '160536ef7cc274ef71b16f29ebb721c7') in 0.0741193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_1.wav
  artifactKey: Guid(0abc4012654de704bbe77b02ac7931e2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_1.wav using Guid(0abc4012654de704bbe77b02ac7931e2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7802bc4c50f5001a72586cfeb28ca3a2') in 0.1873965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_3.wav
  artifactKey: Guid(158804fdfb7c3c4459fbbab360fcd5c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Star_3.wav using Guid(158804fdfb7c3c4459fbbab360fcd5c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c22a1c45e70a55f34327c6bc0159f21') in 0.1740818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Walk_B_Turn_L90_Root.FBX
  artifactKey: Guid(af89eb085890eb045b7bc85fdc4b3114) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Walk_B_Turn_L90_Root.FBX using Guid(af89eb085890eb045b7bc85fdc4b3114) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bdae09a8164a3559063e34497715459') in 0.0627882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_A_Root.FBX
  artifactKey: Guid(1fbbb4e2eb31ff540a462876737c7016) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_A_Root.FBX using Guid(1fbbb4e2eb31ff540a462876737c7016) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f83e393fcf7d89eb2d4c65dd766d8023') in 0.0660571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L45_Root.FBX
  artifactKey: Guid(f5826d323dde70b49b173bb360c7b888) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L45_Root.FBX using Guid(f5826d323dde70b49b173bb360c7b888) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d960a120c034731416ebef5cdff5f8a') in 0.0654327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Run_B_Root.FBX
  artifactKey: Guid(39768e71b414eca479c38e4e5ee5114e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Run_B_Root.FBX using Guid(39768e71b414eca479c38e4e5ee5114e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47d83b0ad3f4eba98cfc83b4f17773d9') in 0.0706268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R90.FBX
  artifactKey: Guid(241cbabc22f74b2408a153a2d46902a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R90.FBX using Guid(241cbabc22f74b2408a153a2d46902a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25a7dbf99671a4dace305b576092f62e') in 0.0572246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Walk_B_Root.FBX
  artifactKey: Guid(8dac3d8f128285846b067321e51223d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Walk_B_Root.FBX using Guid(8dac3d8f128285846b067321e51223d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed1dc335e385629aae8556821907a40c') in 0.0771639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Run_A.FBX
  artifactKey: Guid(cd0e045b40b882f4d98eea78133f50ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Run_A.FBX using Guid(cd0e045b40b882f4d98eea78133f50ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6d61ab4436aeca7072ed2f8d114b99f') in 0.0576599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_B_Root.FBX
  artifactKey: Guid(e621a5da2fc208f49b8a94af22873429) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_B_Root.FBX using Guid(e621a5da2fc208f49b8a94af22873429) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9c8aab8285c6b089b1686cf24acf70d') in 0.0662995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Walk_B_Turn_R90.FBX
  artifactKey: Guid(f78121a9358bc584dab50427f34d6d1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Walk_B_Turn_R90.FBX using Guid(f78121a9358bc584dab50427f34d6d1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e0827975d434e7917582733e41bf244') in 0.0665489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BL45_Root.FBX
  artifactKey: Guid(9de28eff99905c143b067e53d9f83ae3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BL45_Root.FBX using Guid(9de28eff99905c143b067e53d9f83ae3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10eb3977e08c921f37c38a1e61d76bba') in 0.0658052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_L90.FBX
  artifactKey: Guid(da213e24f6c3a6f43a1855725cd0cdb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_L90.FBX using Guid(da213e24f6c3a6f43a1855725cd0cdb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '15a6e7c0138427169992041711d48cd2') in 0.0577049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FL45_Root.FBX
  artifactKey: Guid(bbc4d6f806dd1f14eb392685214892c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FL45_Root.FBX using Guid(bbc4d6f806dd1f14eb392685214892c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80ef0f147f57f9d4021177a346cb448a') in 0.0628795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90.FBX
  artifactKey: Guid(f0c1c5a8ef201e645ba4bf6db7f8410e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90.FBX using Guid(f0c1c5a8ef201e645ba4bf6db7f8410e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '623ae102b8d952b276a51e6d1acceb6d') in 0.0786471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_Root_vol2.FBX
  artifactKey: Guid(09d48dd1e54cf4a4e8d2aeaaedb1a43d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_Root_vol2.FBX using Guid(09d48dd1e54cf4a4e8d2aeaaedb1a43d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b29814ebd0651b31433bb168125be2da') in 0.0554947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Run_B_Turn_L90.FBX
  artifactKey: Guid(51aea2bab9821374582873ff32d8eb14) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Run_B_Turn_L90.FBX using Guid(51aea2bab9821374582873ff32d8eb14) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98df7d95304c98931c588cd2f0fd4fc1') in 0.0594324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R45_Root.FBX
  artifactKey: Guid(6849a94b6a89fc14f9d3e12de500aa42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R45_Root.FBX using Guid(6849a94b6a89fc14f9d3e12de500aa42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7694b91136aa4adf9adb9253e9801a20') in 0.0603974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_D.FBX
  artifactKey: Guid(6b0de88146763bd4cb7f570320d6165b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_D.FBX using Guid(6b0de88146763bd4cb7f570320d6165b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a443f27fa15f491f499610673f07c3bf') in 0.058496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Run_B_Root.FBX
  artifactKey: Guid(e1c811c09ff4cdc488647dd333b5777c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Run_B_Root.FBX using Guid(e1c811c09ff4cdc488647dd333b5777c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6abec70e81e2cab16038c81701f090ab') in 0.0622751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90_Root.FBX
  artifactKey: Guid(a739d20aa46835f42a70082a6a4e4b3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90_Root.FBX using Guid(a739d20aa46835f42a70082a6a4e4b3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f08cba132e63e15680b8b8d9ee79dab') in 0.0709057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Run_A.FBX
  artifactKey: Guid(0d3fbe32b51821e458e2ccb2aac5d766) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Run_A.FBX using Guid(0d3fbe32b51821e458e2ccb2aac5d766) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c9661917d0bc9b0431aa1c3bb72e87f') in 0.1134173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_A_to_Run_B.FBX
  artifactKey: Guid(dcede2aeecb535649b7f480b196a1f92) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Run_A_to_Run_B.FBX using Guid(dcede2aeecb535649b7f480b196a1f92) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2eb3e68828b32c27adc400a559275655') in 0.0782025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Run_A.FBX
  artifactKey: Guid(fd1f1a1ccf704484d841556f9c5b8c2c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Run_A.FBX using Guid(fd1f1a1ccf704484d841556f9c5b8c2c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8e3cd447adfdad733f4fb2051991879') in 0.0732542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Run_A_Turn_L90_Root.FBX
  artifactKey: Guid(4b5f3bfa41de2ba4496461a6aa6d8a36) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Run_A_Turn_L90_Root.FBX using Guid(4b5f3bfa41de2ba4496461a6aa6d8a36) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9ff3990a365646cd87497d6befa9ec8') in 0.0737325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Run_B_Root.FBX
  artifactKey: Guid(a0ffad106bfb04d41bbbae40886d5544) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_A_to_Run_B_Root.FBX using Guid(a0ffad106bfb04d41bbbae40886d5544) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e2f9dd8f7a33bb7c338754f2d490090') in 0.1248314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back.FBX
  artifactKey: Guid(a35e7696d2fd31f4b82415c8c9d5dce0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back.FBX using Guid(a35e7696d2fd31f4b82415c8c9d5dce0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb6620f27ce801e5b694f8f22b496192') in 0.0627087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_B_Root.FBX
  artifactKey: Guid(f5bc8668c53bc3f4fb84941479f5f66f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Jog_B_Root.FBX using Guid(f5bc8668c53bc3f4fb84941479f5f66f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6fea5f211d365415d9fb0265ea4edf2') in 0.079312 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Walk_A_Root.FBX
  artifactKey: Guid(b75e0bd54007c514fb05823912f4bc68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Walk_A_Root.FBX using Guid(b75e0bd54007c514fb05823912f4bc68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3eac2be3a9dbd8b14da64c184e36dbf1') in 0.0622158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_Root.FBX
  artifactKey: Guid(312eb9706a8f53a49986315e5b262f77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_Root.FBX using Guid(312eb9706a8f53a49986315e5b262f77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fdaf595e344e42b4309d00bf20baed37') in 0.0558952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_FL45.FBX
  artifactKey: Guid(64b9e927bc8737744a995ce6540e2886) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_FL45.FBX using Guid(64b9e927bc8737744a995ce6540e2886) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a127e0df6f8434e2d0b6f88b00c4c983') in 0.0708018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90_Root.FBX
  artifactKey: Guid(1e7ca535a36f41341ab9aaae9c118995) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90_Root.FBX using Guid(1e7ca535a36f41341ab9aaae9c118995) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6866b3251e2eefe201e78f3d0a1fbf20') in 0.0627708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_R90_Root.FBX
  artifactKey: Guid(69b7a85799775d3419eef081c435ff3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_R90_Root.FBX using Guid(69b7a85799775d3419eef081c435ff3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5641644a0184d2fcaf9ad6cd24d652e') in 0.0646936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_Root.FBX
  artifactKey: Guid(718e184bc04433a4b8cec99d40b193e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_Root.FBX using Guid(718e184bc04433a4b8cec99d40b193e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ccaa7284aac066ef969a8b678a171a5') in 0.0600298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90.FBX
  artifactKey: Guid(8ac72df8122b3be4d97ce294373d77fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90.FBX using Guid(8ac72df8122b3be4d97ce294373d77fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de0087ba33085b40d594a8fb655034c6') in 0.0702546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_B_Root.FBX
  artifactKey: Guid(347699a89a7c92d468e8f2fcb66547bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_B_Root.FBX using Guid(347699a89a7c92d468e8f2fcb66547bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6732a7e17e02158848b9d23735dad9f') in 0.0593594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BL45_Root.FBX
  artifactKey: Guid(ec2fb83002dfea44eb46a109bb2af6c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_BL45_Root.FBX using Guid(ec2fb83002dfea44eb46a109bb2af6c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3953e520b4ac6bc264d2f66c1a86841') in 0.0593762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_R90.FBX
  artifactKey: Guid(7c7eec7aaf53338489f71f3d155d60d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_R90.FBX using Guid(7c7eec7aaf53338489f71f3d155d60d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75a1bdbeb9525e8f515cd85604ebb7d9') in 0.0783004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90.FBX
  artifactKey: Guid(301be80f247a2894eb94606aabf7442b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90.FBX using Guid(301be80f247a2894eb94606aabf7442b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5567562e5395c71e88c063f3b273de92') in 0.0603843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R45_Root.FBX
  artifactKey: Guid(f9fbe5bff252853439474ddd0e2e7d49) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_R45_Root.FBX using Guid(f9fbe5bff252853439474ddd0e2e7d49) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '151b501927fd73aee7c758f4be131673') in 0.0642546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_03.prefab
  artifactKey: Guid(a38ed6d658baaa247a77a93d62bcf332) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_03.prefab using Guid(a38ed6d658baaa247a77a93d62bcf332) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ceca14d438cbb5dbc40e50847b48668c') in 0.0530983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_1x4_01.fbx
  artifactKey: Guid(abc00000000001630980729956285578) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_1x4_01.fbx using Guid(abc00000000001630980729956285578) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '151c420d36018612cee56ea0aa39c538') in 0.0783719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockBeam_01.fbx
  artifactKey: Guid(abc00000000001364700478640881830) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockBeam_01.fbx using Guid(abc00000000001364700478640881830) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e8f8c7adf47ce7199b2091a817f7caa') in 0.0642623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/SM_FlagPole.fbx
  artifactKey: Guid(abc00000000017347628356943590139) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/SM_FlagPole.fbx using Guid(abc00000000017347628356943590139) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a09a3638f536e2d007e46a4312f00859') in 0.0559638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x6_01.fbx
  artifactKey: Guid(abc00000000003249029173681622685) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_4x6_01.fbx using Guid(abc00000000003249029173681622685) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed89eef58550c1164113dbbcfccdd98f') in 0.0806095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_OutsideCorner_01.fbx
  artifactKey: Guid(abc00000000000177868835488292372) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_DockWall_OutsideCorner_01.fbx using Guid(abc00000000000177868835488292372) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48b80920cc929a5bb68213e8c339cc5e') in 0.085657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Mug.fbx
  artifactKey: Guid(abc00000000018063660120263193169) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Mug.fbx using Guid(abc00000000018063660120263193169) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0fe79f935673093aa72136f20753590') in 0.0715261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Plate_02.fbx
  artifactKey: Guid(abc00000000003265977407108679817) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Plate_02.fbx using Guid(abc00000000003265977407108679817) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f984d3fc46cc92f73a546b8d8349fcf9') in 0.062006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Anvil.fbx
  artifactKey: Guid(abc00000000015596078835172257808) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Anvil.fbx using Guid(abc00000000015596078835172257808) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55965049dfb89f2b90d5b65e6e36a378') in 0.05449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneFence_04.mat
  artifactKey: Guid(ae55da748f27a174babbb858c308d801) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneFence_04.mat using Guid(ae55da748f27a174babbb858c308d801) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '289814b4b170d3cf3eb1f1b333ea6f10') in 0.0597269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Barrel_01.fbx
  artifactKey: Guid(abc00000000009725425756996688144) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Barrel_01.fbx using Guid(abc00000000009725425756996688144) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6436886144cc97a4ef38b5684ca4c22c') in 0.0926172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Materials/M_FireSheet_01.mat
  artifactKey: Guid(d10c1dc24453f8c4eb26d8ed7223d745) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Materials/M_FireSheet_01.mat using Guid(d10c1dc24453f8c4eb26d8ed7223d745) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '576d62ba873fcb3457c1cb5552a9f5f9') in 0.0390653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_ForwardLeft.fbx
  artifactKey: Guid(2d491dc6ab8cc5045919954fe2601203) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_ForwardLeft.fbx using Guid(2d491dc6ab8cc5045919954fe2601203) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34d6bb6175b4d2c76c8ebb153ccae7fb') in 0.0570679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Backward.fbx
  artifactKey: Guid(a8e6c7cf678a13541a726c2ae9ec00e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Backward.fbx using Guid(a8e6c7cf678a13541a726c2ae9ec00e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb67b316152ea69b880bbb25acd310fb') in 0.0530949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 - Land.fbx
  artifactKey: Guid(1dce3ce39fd4c0f4d9d2dbd5929a328c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 - Land.fbx using Guid(1dce3ce39fd4c0f4d9d2dbd5929a328c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '60c92ebe01faf284a3919512a0da7606') in 0.0834509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 [RM] - Land.fbx
  artifactKey: Guid(fa72f21fa809c0542b384cb2889e901e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 [RM] - Land.fbx using Guid(fa72f21fa809c0542b384cb2889e901e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '588cf6c79e4c0725fd0e5143fc9fa6d5') in 0.1012354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Forward.fbx
  artifactKey: Guid(c133e3c197c12e04a9dd23bd0966910f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Forward.fbx using Guid(c133e3c197c12e04a9dd23bd0966910f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2376561b64873dff2eb50a362e463974') in 0.0817306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Pop_Generic_1.wav
  artifactKey: Guid(dc1e3f34761143b4894c2f2ef509db94) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Pop_Generic_1.wav using Guid(dc1e3f34761143b4894c2f2ef509db94) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '104624eb15c935c793a239c74532d806') in 0.1127353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Left.fbx
  artifactKey: Guid(b702e254d5e77904da0429cfcbc77709) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Left.fbx using Guid(b702e254d5e77904da0429cfcbc77709) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c9d8eb26c2a9feecb95c2f9f743624c') in 0.0697291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/MI_Slabs_02.mat
  artifactKey: Guid(3f229507f5981614a8e0b90eeb42d25f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/MI_Slabs_02.mat using Guid(3f229507f5981614a8e0b90eeb42d25f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acc3ba8a20b321b5ac159897f28b202b') in 0.058155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_ForwardRight.fbx
  artifactKey: Guid(5088dccb2ba6bc8478f1dc0919d3e8cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_ForwardRight.fbx using Guid(5088dccb2ba6bc8478f1dc0919d3e8cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5a4c8fe737f79d51388e73042f22274') in 0.0553183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/No Name.mat
  artifactKey: Guid(27f086f6e89a0d7488bdbda1f537d1a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/No Name.mat using Guid(27f086f6e89a0d7488bdbda1f537d1a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6945772e1e106daee8bdb50f8a9e4176') in 0.0598057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Small_01.fbx
  artifactKey: Guid(abc00000000016869624174712020602) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Small_01.fbx using Guid(abc00000000016869624174712020602) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9a0a3276e8e984fba8654844d208299') in 0.1041703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_01.fbx
  artifactKey: Guid(abc00000000016373812325260659237) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_01.fbx using Guid(abc00000000016373812325260659237) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd16181eec0296193458918befc6555a1') in 0.0821944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_C.fbx
  artifactKey: Guid(abc00000000002890348646889239720) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_C.fbx using Guid(abc00000000002890348646889239720) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad727c86bbdad37c310f509b9f2da2fe') in 0.0877335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(a601a108bdbc9124394893332b712c39) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_OcclusionRoughnessMetallic.PNG using Guid(a601a108bdbc9124394893332b712c39) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b1eff4fc5337e84f27aa0ba4d411f65') in 0.0567452 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/SKM_MarketCloth_01.fbx
  artifactKey: Guid(abc00000000011112206424264823986) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/SKM_MarketCloth_01.fbx using Guid(abc00000000011112206424264823986) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92b76915c027b63441df247664e6d9d5') in 0.0525899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_3.wav
  artifactKey: Guid(c9b27638ff5fc97498e01704577aaa11) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_3.wav using Guid(c9b27638ff5fc97498e01704577aaa11) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5cb27957a7c21d34212fff63bd4cc10') in 0.144526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bed.fbx
  artifactKey: Guid(abc00000000004180880479104063966) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bed.fbx using Guid(abc00000000004180880479104063966) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31451d280420e3d0fb1fc6b197a524fc') in 0.067087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Right [RM].fbx
  artifactKey: Guid(67b3854dfc8f4ee4485ed3c744ac0cca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Right [RM].fbx using Guid(67b3854dfc8f4ee4485ed3c744ac0cca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4d236019f7577c10c407e670d0e14d1') in 0.0901146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(3c6d3c882975d31459dde939a82b1e82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/Materials/MI_StoneFence_02.mat using Guid(3c6d3c882975d31459dde939a82b1e82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8839c0a5fd89d127d8b7ef2d12063a18') in 0.0662723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Backward [RM].fbx
  artifactKey: Guid(2e0c1b2ccd3eeeb43b00e035f4fe7841) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_Backward [RM].fbx using Guid(2e0c1b2ccd3eeeb43b00e035f4fe7841) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a40f0c4084bc494be93e12950f619aea') in 0.0754413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Apple.fbx
  artifactKey: Guid(abc00000000017955121476181734109) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Apple.fbx using Guid(abc00000000017955121476181734109) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a778a725c0784786a32bc0d54f0431d5') in 0.0607283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_ForwardRight [RM].fbx
  artifactKey: Guid(7c23c3776f1ce0248aaa1fa0043581cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_ForwardRight [RM].fbx using Guid(7c23c3776f1ce0248aaa1fa0043581cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78dc73cc55a650431c96e1edfd9fffa9') in 0.0965445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_02.fbx
  artifactKey: Guid(abc00000000004203285846091470706) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_02.fbx using Guid(abc00000000004203285846091470706) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab21cf3dfd03ccc1ea55806c117c3946') in 0.0724009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/Materials/MI_Wood_A1.mat
  artifactKey: Guid(c37ee8237b3308343a78a1aa15f619b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/Materials/MI_Wood_A1.mat using Guid(c37ee8237b3308343a78a1aa15f619b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '483cd6c41d1882fbcdbdd50e4d3d28bd') in 0.0638532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_Pillar.fbx
  artifactKey: Guid(abc00000000009976548036629916451) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_Pillar.fbx using Guid(abc00000000009976548036629916451) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8009f2cea80894ea175e1fd6fae9cd68') in 0.0849982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stairs_4M.fbx
  artifactKey: Guid(abc00000000005584772429544552383) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stairs_4M.fbx using Guid(abc00000000005584772429544552383) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57fff2f083881b1074ab665b1dab49a2') in 0.0929937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_04.fbx
  artifactKey: Guid(abc00000000007615524536535298675) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Crenel_A_04.fbx using Guid(abc00000000007615524536535298675) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05ec608afeafe6292b0598ca9a7084b0') in 0.0724598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_BackwardRight [RM].fbx
  artifactKey: Guid(55f7d9665d0be68498e73fb21fcf3267) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_BackwardRight [RM].fbx using Guid(55f7d9665d0be68498e73fb21fcf3267) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0ff9b50fdca81819c7366be0c5b27d3') in 0.0914329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_02.mat
  artifactKey: Guid(b7b9e3aaac033234497ac18f1197cf75) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_02.mat using Guid(b7b9e3aaac033234497ac18f1197cf75) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7d7bcf5a9f01caea76f4ca990bf487e') in 0.0672852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Wall_16x5.fbx
  artifactKey: Guid(abc00000000006201211861379925098) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Wall_16x5.fbx using Guid(abc00000000006201211861379925098) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a3da2bc3290e7adfb59a9a7c966877f') in 0.0764458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Left [RM].fbx
  artifactKey: Guid(dd0055e79f0f8414f986f0dce8f01186) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Left [RM].fbx using Guid(dd0055e79f0f8414f986f0dce8f01186) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a13a030354c2bc7924669ad198fddd1') in 0.0738988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(a3f3d8a4c434ffd4cb8ce368f03aa248) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_LighterPlanks.mat using Guid(a3f3d8a4c434ffd4cb8ce368f03aa248) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df1a6b6740fc3338514f9ba6b2399f2d') in 0.0637904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000106 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/No Name.mat
  artifactKey: Guid(eae37a88700a4d34085447864563c4dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/No Name.mat using Guid(eae37a88700a4d34085447864563c4dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '712f33951920b1ebe7a8cbd1f9d55377') in 0.0883187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_Metal.mat
  artifactKey: Guid(beba1dd82adbb3743a7b230246ae69bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/MI_Metal.mat using Guid(beba1dd82adbb3743a7b230246ae69bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b479b198ae9858656c525cd06600bde4') in 0.0584834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_02.fbx
  artifactKey: Guid(abc00000000009466016926765675473) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_A_02.fbx using Guid(abc00000000009466016926765675473) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc94cac6cd170b46c4e154964a490936') in 0.0667657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_04.fbx
  artifactKey: Guid(abc00000000017305591008357493916) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_04.fbx using Guid(abc00000000017305591008357493916) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69fe47f920e66a94c4680f16a8545a0f') in 0.0644881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_B.fbx
  artifactKey: Guid(abc00000000000241433819516248067) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_B.fbx using Guid(abc00000000000241433819516248067) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4822142ac1bee580af061997400ddb72') in 0.0816922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Med_Loose.fbx
  artifactKey: Guid(abc00000000005466961541210229174) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Med_Loose.fbx using Guid(abc00000000005466961541210229174) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf4d115b957e36ac065a996b9bbaf0e3') in 0.0750066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_01.fbx
  artifactKey: Guid(abc00000000002644025033004430445) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/SM_CastleTower_Wall_Door_A_01.fbx using Guid(abc00000000002644025033004430445) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0b62c44621880a81b4160589607175e') in 0.0935648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_04.mat
  artifactKey: Guid(f66a3d35f7296c64ab88ad9ef8eeac97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_04.mat using Guid(f66a3d35f7296c64ab88ad9ef8eeac97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '867f7345215f7735552b6160d923d83c') in 0.0653177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000111 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_C_01.fbx
  artifactKey: Guid(abc00000000009847705880496500582) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/SM_WoodFloor_C_01.fbx using Guid(abc00000000009847705880496500582) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '424777c30e2b4bc1e21eb355c859bebe') in 0.06353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_06.mat
  artifactKey: Guid(502fbfa4b82de6a47ae24fc0c4623127) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_06.mat using Guid(502fbfa4b82de6a47ae24fc0c4623127) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1858af820d1ca5b0a278970af13a98e1') in 0.0569362 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_02.fbx
  artifactKey: Guid(abc00000000017473425305926037504) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_02.fbx using Guid(abc00000000017473425305926037504) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4d0e69d3fd2d876a88d71a34c5bc5c6') in 0.1905513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_BackwardRight [RM].fbx
  artifactKey: Guid(9a4f86a2229f88c4092c9a51a307198e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_BackwardRight [RM].fbx using Guid(9a4f86a2229f88c4092c9a51a307198e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '962028abab3ae55d22b79625c22f65ae') in 0.097603 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/No Name.mat
  artifactKey: Guid(e496ba1c25cb8f4428b97d2778272181) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/No Name.mat using Guid(e496ba1c25cb8f4428b97d2778272181) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d546f84bb5a6feb652543f9478b2982') in 0.0660089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_M_02.fbx
  artifactKey: Guid(abc00000000009851186666640817645) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_M_02.fbx using Guid(abc00000000009851186666640817645) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba75a01d7a9a0f40d2db81b39ad4d234') in 0.0678977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_StoneFence_04.mat
  artifactKey: Guid(2cb60bb4699de304598d12d62ec0a97d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_StoneFence_04.mat using Guid(2cb60bb4699de304598d12d62ec0a97d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c302aa00b66d4ad95b1733b31cb0744e') in 0.0725643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_BrickStoneWall_Disp.mat
  artifactKey: Guid(1e39cb1a9779d5347a7cdfb6f7a613e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/MI_BrickStoneWall_Disp.mat using Guid(1e39cb1a9779d5347a7cdfb6f7a613e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec8714e896b935b99bc61d634319153d') in 0.0542263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_04.fbx
  artifactKey: Guid(abc00000000014581373542030554925) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_04.fbx using Guid(abc00000000014581373542030554925) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a30ae1b441bece59fd733a4317a3d108') in 0.1185807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_02.fbx
  artifactKey: Guid(abc00000000015207725900925794285) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_02.fbx using Guid(abc00000000015207725900925794285) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fed234eabe3b9214c5cda82e9acbbef') in 0.0608288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_03.fbx
  artifactKey: Guid(abc00000000015001694161101525555) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_A_03.fbx using Guid(abc00000000015001694161101525555) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0566a963f36b21d71df89783a02fda7f') in 0.0818777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_04.fbx
  artifactKey: Guid(abc00000000017665508883783670429) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_04.fbx using Guid(abc00000000017665508883783670429) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ea5025e5e5cddfed3daa495da591d01') in 0.1031625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/Materials/MI_BrickStoneWall_Disp.mat
  artifactKey: Guid(588dc1b0496638941b5f3ee01cc7950d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/DoorWays/Materials/MI_BrickStoneWall_Disp.mat using Guid(588dc1b0496638941b5f3ee01cc7950d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa9dee2e0b49844dae15e149bed3bbb7') in 0.0604103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_08.fbx
  artifactKey: Guid(abc00000000001966429758244935284) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_08.fbx using Guid(abc00000000001966429758244935284) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dffcfaf21b2e869bb4433e1283c84eb') in 0.1047947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/Materials/MI_EuropeanBeech_Bark_01.mat
  artifactKey: Guid(a2d6300e30bda2143832fc45caf1c7f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/Materials/MI_EuropeanBeech_Bark_01.mat using Guid(a2d6300e30bda2143832fc45caf1c7f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1eb4d7b44c6716fd7674d549f24a294') in 0.0340512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Metal.mat
  artifactKey: Guid(103c617ac3ceaba4f8e3c6cff8db6390) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Metal.mat using Guid(103c617ac3ceaba4f8e3c6cff8db6390) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f24468fb9b12f87d431b135ca4e882e') in 0.0597845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/No Name.mat
  artifactKey: Guid(cd41bbf2e6bfbeb41a6b603f84c76ce7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/Materials/No Name.mat using Guid(cd41bbf2e6bfbeb41a6b603f84c76ce7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f2a036255908628cb8073a56ab3c884') in 0.0708689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_VarnishedWood_01.mat
  artifactKey: Guid(1e36353e2ce58c541b76bed65c2f189a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_VarnishedWood_01.mat using Guid(1e36353e2ce58c541b76bed65c2f189a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '035795f2c9ee689069708adff16005f4') in 0.0647171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Run_B_to_Run_A_Root.FBX
  artifactKey: Guid(562d905bdcfba5d428228290656cd28d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Run_B_to_Run_A_Root.FBX using Guid(562d905bdcfba5d428228290656cd28d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1d79c5339b34f6c72113506a4a46cce') in 0.0643571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_F_Root.FBX
  artifactKey: Guid(14bed6ef8bacfc34ab56d08d0b06c5cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_F_Root.FBX using Guid(14bed6ef8bacfc34ab56d08d0b06c5cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '42e36b8a395f90a0a3e45edc74d026c8') in 0.0851863 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Bundle_02.fbx
  artifactKey: Guid(abc00000000008689694593110309920) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Bundle_02.fbx using Guid(abc00000000008689694593110309920) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b060026fe8a795822c09c163c25d9fd') in 0.0714976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_B_Turn_R90_Root.FBX
  artifactKey: Guid(7ac16dea6f743004ba60b1b3aac618b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_B_Turn_R90_Root.FBX using Guid(7ac16dea6f743004ba60b1b3aac618b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b08a3098611472c0460d63088aa8411') in 0.0641677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_02.prefab
  artifactKey: Guid(abc00000000010080006360725859281) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_02.prefab using Guid(abc00000000010080006360725859281) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd06f220ee385ebf461d8c62c3743ef6c') in 0.0720142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR45.FBX
  artifactKey: Guid(7daa311d2c1f42b438f111d30c8d15b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR45.FBX using Guid(7daa311d2c1f42b438f111d30c8d15b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c928dd32b498f2aeaf6db958f4cff232') in 0.0874208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.prefab
  artifactKey: Guid(6c5597952c90b064aad5bac1d985fd5b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.prefab using Guid(6c5597952c90b064aad5bac1d985fd5b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f02239983adc90b51de9820795d92cf4') in 0.2298014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockA.prefab
  artifactKey: Guid(abc00000000004486004173860205213) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockA.prefab using Guid(abc00000000004486004173860205213) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ea70fd71f85a036214b04cc29ddb526') in 0.0645979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Run_B_Root.FBX
  artifactKey: Guid(6903615164772864ba4c19e689c3eb68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Run_B_Root.FBX using Guid(6903615164772864ba4c19e689c3eb68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccc1b92125237c396a2870a534bd90cf') in 0.0836524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.FBX
  artifactKey: Guid(0499e806a83f3d546827b636dec7fbab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.FBX using Guid(0499e806a83f3d546827b636dec7fbab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa0a54d96c5def831e5a5b4fd39f457f') in 0.4808779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0