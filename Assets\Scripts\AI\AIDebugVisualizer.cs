/*
 * Script: AIDebugVisualizer
 * Purpose: Advanced debug visualization and testing tools for AI systems
 * 
 * Setup Instructions:
 * 1. Attach this script to any GameObject in the scene (preferably a manager object)
 * 2. Configure debug settings in the inspector
 * 3. Use keyboard shortcuts to toggle different debug modes during play
 * 4. Enable/disable specific visualization features as needed
 * 
 * CRITICAL: Default Values Must Work Out-of-Box
 * - All debug features disabled by default for performance
 * - Keyboard shortcuts use common debug keys (F1-F12)
 * - Visual elements use clear, contrasting colors
 * - Performance impact minimized with smart update intervals
 * 
 * Dependencies:
 * - NPCEnemyAI scripts in scene for visualization
 * - Unity GUI system for on-screen debug info
 * 
 * Usage:
 * Attach to a manager GameObject and configure debug options.
 * Use during development to visualize AI behavior and tune parameters.
 * 
 * Keyboard Shortcuts:
 * - F1: Toggle AI state display
 * - F2: Toggle detection range visualization
 * - F3: Toggle pathfinding visualization
 * - F4: Toggle performance metrics
 * - F5: Toggle all debug features
 * 
 * Public Methods:
 * - ToggleDebugMode(): Enable/disable all debug features
 * - ShowAIStates(): Display current AI states
 * - HighlightAI(NPCEnemyAI ai): Highlight specific AI entity
 * 
 * Author: <PERSON> AI Assistant
 * Created: 2025-08-01
 * Last Modified: 2025-08-01
 */

using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace Blazeout.AI
{
    /// <summary>
    /// Advanced debug visualization for AI systems
    /// Attach this script to: Manager GameObject or Main Camera
    /// </summary>
    public class AIDebugVisualizer : MonoBehaviour
    {
        #region Inspector Settings
        [Header("Debug Controls")]
        [Tooltip("Master toggle for all debug features")]
        [SerializeField] private bool enableDebug = false;
        
        [Tooltip("Show AI states above each NPC")]
        [SerializeField] private bool showAIStates = true;
        
        [Tooltip("Show detection ranges and sight cones")]
        [SerializeField] private bool showDetectionRanges = true;
        
        [Tooltip("Show NavMesh paths and patrol routes")]
        [SerializeField] private bool showPathfinding = true;
        
        [Tooltip("Show performance metrics")]
        [SerializeField] private bool showPerformanceMetrics = false;
        
        [Tooltip("Show player detection status")]
        [SerializeField] private bool showPlayerDetection = true;

        [Header("Visual Settings")]
        [Tooltip("Size of debug text")]
        [SerializeField] private int debugTextSize = 12;
        
        [Tooltip("Color for AI state text")]
        [SerializeField] private Color stateTextColor = Color.white;
        
        [Tooltip("Color for detection indicators")]
        [SerializeField] private Color detectionColor = Color.red;
        
        [Tooltip("Color for patrol paths")]
        [SerializeField] private Color patrolPathColor = Color.green;

        [Header("Performance")]
        [Tooltip("How often to update debug info (seconds)")]
        [SerializeField] private float updateInterval = 0.1f;
        
        [Tooltip("Maximum number of AI to show debug info for")]
        [SerializeField] private int maxDebugAI = 10;
        #endregion

        #region Private Variables
        private NPCEnemyAI[] allAI;
        private Transform playerTransform;
        private float lastUpdateTime;
        private GUIStyle debugTextStyle;
        private bool guiStyleInitialized = false;
        
        // Performance tracking
        private int frameCount = 0;
        private float deltaTime = 0.0f;
        private float fps = 0.0f;
        
        // Debug state tracking
        private Dictionary<NPCEnemyAI, Vector3> lastKnownPositions = new Dictionary<NPCEnemyAI, Vector3>();
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize debug system
        /// </summary>
        private void Start()
        {
            InitializeDebugSystem();
        }

        /// <summary>
        /// Update debug visualization
        /// </summary>
        private void Update()
        {
            if (!enableDebug) return;
            
            HandleKeyboardInput();
            
            // Update at specified interval for performance
            if (Time.time - lastUpdateTime >= updateInterval)
            {
                UpdateDebugData();
                lastUpdateTime = Time.time;
            }
            
            UpdatePerformanceMetrics();
        }

        /// <summary>
        /// Draw debug GUI
        /// </summary>
        private void OnGUI()
        {
            if (!enableDebug) return;
            
            InitializeGUIStyle();
            
            if (showAIStates) DrawAIStates();
            if (showPerformanceMetrics) DrawPerformanceMetrics();
            if (showPlayerDetection) DrawPlayerDetectionStatus();
            
            DrawDebugControls();
        }

        /// <summary>
        /// Draw debug gizmos
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!enableDebug) return;
            
            if (showDetectionRanges) DrawDetectionRanges();
            if (showPathfinding) DrawPathfindingInfo();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the debug system
        /// </summary>
        private void InitializeDebugSystem()
        {
            // Find all AI in scene
            RefreshAIList();
            
            // Find player
            GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
            if (playerObject != null)
            {
                playerTransform = playerObject.transform;
            }
            
            Debug.Log("[AIDebugVisualizer] Debug system initialized");
        }

        /// <summary>
        /// Refresh the list of AI entities
        /// </summary>
        private void RefreshAIList()
        {
            allAI = FindObjectsOfType<NPCEnemyAI>();
            Debug.Log($"[AIDebugVisualizer] Found {allAI.Length} AI entities");
        }

        /// <summary>
        /// Initialize GUI style
        /// </summary>
        private void InitializeGUIStyle()
        {
            if (guiStyleInitialized) return;
            
            debugTextStyle = new GUIStyle(GUI.skin.label);
            debugTextStyle.fontSize = debugTextSize;
            debugTextStyle.normal.textColor = stateTextColor;
            debugTextStyle.alignment = TextAnchor.MiddleCenter;
            
            guiStyleInitialized = true;
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle keyboard shortcuts for debug controls
        /// </summary>
        private void HandleKeyboardInput()
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                showAIStates = !showAIStates;
                Debug.Log($"[AIDebugVisualizer] AI States: {(showAIStates ? "ON" : "OFF")}");
            }

            if (Input.GetKeyDown(KeyCode.F2))
            {
                showDetectionRanges = !showDetectionRanges;
                Debug.Log($"[AIDebugVisualizer] Detection Ranges: {(showDetectionRanges ? "ON" : "OFF")}");
            }

            if (Input.GetKeyDown(KeyCode.F3))
            {
                showPathfinding = !showPathfinding;
                Debug.Log($"[AIDebugVisualizer] Pathfinding: {(showPathfinding ? "ON" : "OFF")}");
            }

            if (Input.GetKeyDown(KeyCode.F4))
            {
                showPerformanceMetrics = !showPerformanceMetrics;
                Debug.Log($"[AIDebugVisualizer] Performance Metrics: {(showPerformanceMetrics ? "ON" : "OFF")}");
            }
            
            if (Input.GetKeyDown(KeyCode.F5))
            {
                ToggleDebugMode();
            }
            
            if (Input.GetKeyDown(KeyCode.F6))
            {
                RefreshAIList();
            }
        }
        #endregion

        #region Debug Data Updates
        /// <summary>
        /// Update debug data for all AI
        /// </summary>
        private void UpdateDebugData()
        {
            if (allAI == null || allAI.Length == 0) return;
            
            // Update position tracking
            foreach (NPCEnemyAI ai in allAI)
            {
                if (ai != null)
                {
                    lastKnownPositions[ai] = ai.transform.position;
                }
            }
        }

        /// <summary>
        /// Update performance metrics
        /// </summary>
        private void UpdatePerformanceMetrics()
        {
            frameCount++;
            deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
            
            if (frameCount % 30 == 0) // Update FPS every 30 frames
            {
                fps = 1.0f / deltaTime;
            }
        }
        #endregion

        #region GUI Drawing
        /// <summary>
        /// Draw AI state information
        /// </summary>
        private void DrawAIStates()
        {
            if (allAI == null) return;
            
            int aiCount = 0;
            foreach (NPCEnemyAI ai in allAI)
            {
                if (ai == null || aiCount >= maxDebugAI) break;
                
                Vector3 screenPos = Camera.main.WorldToScreenPoint(ai.transform.position + Vector3.up * 2.5f);
                
                if (screenPos.z > 0) // Only draw if in front of camera
                {
                    string stateInfo = $"{ai.name}\n" +
                                     $"State: {ai.CurrentState}\n" +
                                     $"Alert: {ai.AlertLevel:F2}\n" +
                                     $"Dist: {ai.DistanceToPlayer:F1}m\n" +
                                     $"See: {(ai.CanSeePlayer ? "YES" : "NO")}";
                    
                    GUI.color = ai.CanSeePlayer ? detectionColor : stateTextColor;
                    GUI.Label(new Rect(screenPos.x - 60, Screen.height - screenPos.y - 50, 120, 100), stateInfo, debugTextStyle);
                }
                
                aiCount++;
            }
            
            GUI.color = Color.white; // Reset color
        }

        /// <summary>
        /// Draw performance metrics
        /// </summary>
        private void DrawPerformanceMetrics()
        {
            string performanceInfo = $"FPS: {fps:F1}\n" +
                                   $"AI Count: {(allAI != null ? allAI.Length : 0)}\n" +
                                   $"Frame Time: {deltaTime * 1000:F1}ms";
            
            GUI.color = Color.yellow;
            GUI.Label(new Rect(10, 10, 200, 60), performanceInfo, debugTextStyle);
            GUI.color = Color.white;
        }

        /// <summary>
        /// Draw player detection status
        /// </summary>
        private void DrawPlayerDetectionStatus()
        {
            if (allAI == null || playerTransform == null) return;
            
            int detectingAI = allAI.Count(ai => ai != null && ai.CanSeePlayer);
            bool isDetected = detectingAI > 0;
            
            string detectionStatus = $"Player Status: {(isDetected ? "DETECTED" : "HIDDEN")}\n" +
                                   $"Detecting AI: {detectingAI}/{allAI.Length}";
            
            GUI.color = isDetected ? Color.red : Color.green;
            GUI.Label(new Rect(Screen.width - 200, 10, 190, 40), detectionStatus, debugTextStyle);
            GUI.color = Color.white;
        }

        /// <summary>
        /// Draw debug control instructions
        /// </summary>
        private void DrawDebugControls()
        {
            string controls = "AI Debug Controls:\n" +
                            "F1: Toggle AI States\n" +
                            "F2: Toggle Detection Ranges\n" +
                            "F3: Toggle Pathfinding\n" +
                            "F4: Toggle Performance\n" +
                            "F5: Toggle All Debug\n" +
                            "F6: Refresh AI List";
            
            GUI.color = Color.cyan;
            GUI.Label(new Rect(10, Screen.height - 140, 200, 130), controls, new GUIStyle(GUI.skin.label) { fontSize = 10 });
            GUI.color = Color.white;
        }
        #endregion

        #region Gizmo Drawing
        /// <summary>
        /// Draw detection ranges and sight cones
        /// </summary>
        private void DrawDetectionRanges()
        {
            if (allAI == null) return;
            
            foreach (NPCEnemyAI ai in allAI)
            {
                if (ai == null) continue;
                
                // This would require exposing some properties from NPCEnemyAI
                // For now, we'll draw basic ranges
                Gizmos.color = ai.CanSeePlayer ? Color.red : Color.yellow;
                Gizmos.DrawWireSphere(ai.transform.position, 10f); // Approximate sight range
                
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireSphere(ai.transform.position, 5f); // Approximate hearing range
            }
        }

        /// <summary>
        /// Draw pathfinding information
        /// </summary>
        private void DrawPathfindingInfo()
        {
            if (allAI == null) return;
            
            foreach (NPCEnemyAI ai in allAI)
            {
                if (ai == null) continue;
                
                // Draw line to last known player position
                if (ai.LastKnownPlayerPosition != Vector3.zero)
                {
                    Gizmos.color = new Color(1f, 0.5f, 0f); // Orange color
                    Gizmos.DrawLine(ai.transform.position, ai.LastKnownPlayerPosition);
                    Gizmos.DrawWireSphere(ai.LastKnownPlayerPosition, 0.5f);
                }
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Toggle all debug features
        /// </summary>
        public void ToggleDebugMode()
        {
            enableDebug = !enableDebug;
            Debug.Log($"[AIDebugVisualizer] Debug Mode: {(enableDebug ? "ENABLED" : "DISABLED")}");
        }

        /// <summary>
        /// Enable/disable specific debug feature
        /// </summary>
        public void SetDebugFeature(string feature, bool enabled)
        {
            switch (feature.ToLower())
            {
                case "states":
                    showAIStates = enabled;
                    break;
                case "detection":
                    showDetectionRanges = enabled;
                    break;
                case "pathfinding":
                    showPathfinding = enabled;
                    break;
                case "performance":
                    showPerformanceMetrics = enabled;
                    break;
            }
        }

        /// <summary>
        /// Highlight specific AI entity
        /// </summary>
        public void HighlightAI(NPCEnemyAI ai)
        {
            if (ai != null)
            {
                // Could implement special highlighting here
                Debug.Log($"[AIDebugVisualizer] Highlighting AI: {ai.name}");
            }
        }
        #endregion
    }
}
