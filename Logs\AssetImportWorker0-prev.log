Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:04:02Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker0.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14252]  Target information:

Player connection [14252]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 595229157 [EditorId] 595229157 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14252] Host joined multi-casting on [***********:54997]...
Player connection [14252] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.28 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 27.53 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56368
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004289 seconds.
- Loaded All Assemblies, in  0.671 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 1286ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (295ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (289ms)
			TypeCache.Refresh (286ms)
				TypeCache.ScanAssembly (264ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.571 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.85 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.778 seconds
Domain Reload Profiling: 3348ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (1054ms)
		LoadAssemblies (658ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (597ms)
			TypeCache.Refresh (439ms)
				TypeCache.ScanAssembly (409ms)
			BuildScriptInfoCaches (121ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1381ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (271ms)
			ProcessInitializeOnLoadAttributes (984ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 6.79 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.0 MB). Loaded Objects now: 8334.
Memory consumption went from 210.3 MB to 200.3 MB.
Total: 18.341800 ms (FindLiveObjects: 1.205500 ms CreateObjectMapping: 1.445200 ms MarkObjects: 8.399400 ms  DeleteObjects: 7.289400 ms)

========================================================================
Received Import Request.
  Time since last request: 1811858.783268 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab
  artifactKey: Guid(86804816d3c9e214c9a526e82ee95703) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab using Guid(86804816d3c9e214c9a526e82ee95703) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50988fb781e9cafa88a04e0dda26291f') in 0.9091297 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 36.804045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_T-Pose_Grrrru_Man(recommend).FBX
  artifactKey: Guid(43992d4f16e57574fa5d16ef485c0cbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_T-Pose_Grrrru_Man(recommend).FBX using Guid(43992d4f16e57574fa5d16ef485c0cbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '826f89851a85b09f2fff09f6ff648cee') in 0.1786827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 148

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Weapon_Avatar Mask.mask
  artifactKey: Guid(b038f001f1b41254e8e6cdb37c9fab85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Weapon_Avatar Mask.mask using Guid(b038f001f1b41254e8e6cdb37c9fab85) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2649236c2ae47371c7e5b94177ba4d9f') in 0.0018428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_Include_Katana.FBX
  artifactKey: Guid(78619ea0e9e2ea84f9d02ae4880f9382) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_Include_Katana.FBX using Guid(78619ea0e9e2ea84f9d02ae4880f9382) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '854e8aa26e0ac83a3c0166c270f1fc60') in 0.0958705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 154

========================================================================
Received Import Request.
  Time since last request: 14.893780 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/ReflectionProbe-0.exr
  artifactKey: Guid(a5afe8b22695ed048be4e011d9b0aad4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/ReflectionProbe-0.exr using Guid(a5afe8b22695ed048be4e011d9b0aad4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a92ab592f5623d082c6c291ca6f345e') in 0.0538193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 28.038261 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab
  artifactKey: Guid(958cf4748c84fb948bc3400edc8005e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab using Guid(958cf4748c84fb948bc3400edc8005e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79e230aa49c3d95940336d164f289af3') in 0.0345561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab
  artifactKey: Guid(acc85f946427de74fa6e3c695337c836) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_no_weapon.prefab using Guid(acc85f946427de74fa6e3c695337c836) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd58db227ff5f36b2305e51c7c1206dbf') in 0.0503212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 290

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab
  artifactKey: Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab using Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ee1af4f7aacccecd4fdb4b828bce193') in 7.0254729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1257

========================================================================
Received Import Request.
  Time since last request: 1.005075 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A.FBX
  artifactKey: Guid(41a6295a81dcdfd4ab1970b57b92b0d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A.FBX using Guid(41a6295a81dcdfd4ab1970b57b92b0d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec2897556823c3808173e1e7c618f0ce') in 0.0363696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 141

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B.FBX
  artifactKey: Guid(8a5e92b128725e547b79fb11a0564e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B.FBX using Guid(8a5e92b128725e547b79fb11a0564e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa89659ca5ca0b57969d0a8b3b50a7ce') in 0.0310688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 24.684805 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45.FBX
  artifactKey: Guid(4bbe169dcfd603d48b5964e57f1cdacf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45.FBX using Guid(4bbe169dcfd603d48b5964e57f1cdacf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aee1b8c22669352dd74f804811ee2938') in 0.01161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 143

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90.FBX
  artifactKey: Guid(1a6e625f375f97544a3b21f1fde2e29f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90.FBX using Guid(1a6e625f375f97544a3b21f1fde2e29f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9f129f1e180832448671fc847ba2779') in 0.0088083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_Root.FBX
  artifactKey: Guid(60e1d0a3d59a3134d8da259d8c15933a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_Root.FBX using Guid(60e1d0a3d59a3134d8da259d8c15933a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5892a357b9cff2603f8d4b20285f69d9') in 0.0092914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90_Root.FBX
  artifactKey: Guid(1e7ca535a36f41341ab9aaae9c118995) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L90_Root.FBX using Guid(1e7ca535a36f41341ab9aaae9c118995) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8993d9e81ef9d2bfda7a985f190e1b0') in 0.0098641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45_Root.FBX
  artifactKey: Guid(b57a4c083e371a242bc687deee287ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45_Root.FBX using Guid(b57a4c083e371a242bc687deee287ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '666b1c39eaa8452b1684b0146f4c6207') in 0.007396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90.FBX
  artifactKey: Guid(419ae4620afd8ed4e90c0bbc096cef7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90.FBX using Guid(419ae4620afd8ed4e90c0bbc096cef7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba30809c7a07b2e7b6edbae2c9c0dc2d') in 0.0105107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90_Root.FBX
  artifactKey: Guid(a04ecdf98b0103e41b39158c59a1d153) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90_Root.FBX using Guid(a04ecdf98b0103e41b39158c59a1d153) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdf28ee85a213f50e6dc57fb290ef8e1') in 0.0078375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45.FBX
  artifactKey: Guid(6cae46fabedc7a543a0b3fa22418260f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45.FBX using Guid(6cae46fabedc7a543a0b3fa22418260f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '052c640a1a6afa14976093c95a090b8f') in 0.0102835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 61.561334 seconds.
  path: Assets/SrRubfish_VFX_02/Materials
  artifactKey: Guid(9abfd3f1ce6e03d4f94c2e2195fe1274) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials using Guid(9abfd3f1ce6e03d4f94c2e2195fe1274) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f6e15e13caa091d87de104db68dc440') in 0.0063274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.172900 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_02.mat
  artifactKey: Guid(4c99ecd0c2ea1a1458821662217b3609) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_02.mat using Guid(4c99ecd0c2ea1a1458821662217b3609) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5c2c5da62f91debcffc3b65fabdc6b3') in 0.0935437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_4x8.mat
  artifactKey: Guid(91f50f270f7b31744a8b19e7d7cabed9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_4x8.mat using Guid(91f50f270f7b31744a8b19e7d7cabed9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30c9febe4bfeb1c1af22500f398d16e4') in 1.1871634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.055413 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_02.mat
  artifactKey: Guid(18f78fae82f146a46983815e8e9c559d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_02.mat using Guid(18f78fae82f146a46983815e8e9c559d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ebbe34c1551f783c656021e3b1469d7a') in 0.045885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Water_01.mat
  artifactKey: Guid(5f8d04154cf0bee4d9be1a93c770193a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Water_01.mat using Guid(5f8d04154cf0bee4d9be1a93c770193a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f46c7ed6059a6a445f4329cee9b2b4c3') in 0.0673379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_01_4x4.mat
  artifactKey: Guid(b0845708ba6709849bcd5952e020716a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_01_4x4.mat using Guid(b0845708ba6709849bcd5952e020716a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad76ad78e05f3f31974ee2b35a72b394') in 0.0459644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_02.mat
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_02.mat using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9083e8a15b04a9a50105078915c082e4') in 0.3855857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_MainSlash_01_4x4.mat
  artifactKey: Guid(5c0bdf43d47c35d4f924a26257009cc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_MainSlash_01_4x4.mat using Guid(5c0bdf43d47c35d4f924a26257009cc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '543c1207e38fe3364045276723fa4ea9') in 0.1245061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_01_2x5.mat
  artifactKey: Guid(2ee7f5f46194fd642b91e014a56bb2a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_01_2x5.mat using Guid(2ee7f5f46194fd642b91e014a56bb2a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef1c10ee968b00695e47dcb2e19661c2') in 0.0294167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_NoiseWater_01.mat
  artifactKey: Guid(7d4c1045919823d4fad991c3b569889b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_NoiseWater_01.mat using Guid(7d4c1045919823d4fad991c3b569889b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8e417ea6ec01d48950a4cc017231a48') in 0.0568131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_03_3x4.mat
  artifactKey: Guid(10c1dfdaee409364388911503797be3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_03_3x4.mat using Guid(10c1dfdaee409364388911503797be3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a18d64ccc3bcdef3e5e9419dd1fa5e0f') in 0.0469197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_02.mat
  artifactKey: Guid(52a5b59d37e573142a809a412bb3cf59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_02.mat using Guid(52a5b59d37e573142a809a412bb3cf59) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f346446fb520620b15ae619380deb3b2') in 0.0368331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.587776 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01.png
  artifactKey: Guid(deeae6a5d191c6648be8f3b7daa3b259) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_NormalSplash_4x4_01.png using Guid(deeae6a5d191c6648be8f3b7daa3b259) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7139842037e5ee093a00bf2ff5ebe64') in 0.0523103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 14.703008 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BedSheet.mat
  artifactKey: Guid(abc00000000009288061145056192450) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BedSheet.mat using Guid(abc00000000009288061145056192450) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6660d6eda3c16f3adc29bc43c47a88e4') in 0.096769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_ChimneyPots.mat
  artifactKey: Guid(abc00000000018252559287460586247) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_ChimneyPots.mat using Guid(abc00000000018252559287460586247) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '016257e51b65aff187a721718555122e') in 0.0619801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_FirePit_A_01.mat
  artifactKey: Guid(abc00000000014673551330848278965) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_FirePit_A_01.mat using Guid(abc00000000014673551330848278965) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e800cfad79458fbf7bbeb59980f4fc0f') in 0.0775709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Flag_02.mat
  artifactKey: Guid(abc00000000011493624177128157933) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Flag_02.mat using Guid(abc00000000011493624177128157933) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50519bfc36522db51c66b1f60ff9f037') in 0.1019293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cobblestone_A.mat
  artifactKey: Guid(abc00000000013236273414641559398) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cobblestone_A.mat using Guid(abc00000000013236273414641559398) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f45f7544f05395a63b93896f9ae180aa') in 0.066985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 3.786092 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Glass.mat
  artifactKey: Guid(abc00000000010553704239738727099) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Glass.mat using Guid(abc00000000010553704239738727099) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '12ccbdebfd1261576071fa23d7c3f51a') in 0.0387182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_IndividualRoofTiles.mat
  artifactKey: Guid(abc00000000010410350431632913622) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_IndividualRoofTiles.mat using Guid(abc00000000010410350431632913622) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '798c21138fe5536f8702bb70140dd5f6') in 0.0181705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Metal.mat
  artifactKey: Guid(abc00000000015081879039839770399) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Metal.mat using Guid(abc00000000015081879039839770399) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff326899a9cee800749592be7f322830') in 0.0678994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_A.mat
  artifactKey: Guid(abc00000000015730279596845295603) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_A.mat using Guid(abc00000000015730279596845295603) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7a1895d8cc5b49408232793db3b4f6d') in 0.1832975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rock.mat
  artifactKey: Guid(abc00000000017111455146933407426) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rock.mat using Guid(abc00000000017111455146933407426) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0bd35adfc96172b934703abb806ec93') in 0.0262708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Slabs.mat
  artifactKey: Guid(abc00000000006393320318721073033) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Slabs.mat using Guid(abc00000000006393320318721073033) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0345b8a30a0dfb9ab0f46540bda1119c') in 0.0788509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_RockMossy_02.mat
  artifactKey: Guid(abc00000000017644046680987046221) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_RockMossy_02.mat using Guid(abc00000000017644046680987046221) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '876dee08b881907fcdbdd2319fa9bb89') in 0.0204721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 1.531660 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_02.mat
  artifactKey: Guid(abc00000000001516021530692751608) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_02.mat using Guid(abc00000000001516021530692751608) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '006efeeb41df088ffc227573ed3e98b4') in 0.0412373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_06.mat
  artifactKey: Guid(abc00000000013376651248173773372) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_06.mat using Guid(abc00000000013376651248173773372) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4a677ab13da7d2de4da7c9ba2c227dcc') in 0.0534703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0