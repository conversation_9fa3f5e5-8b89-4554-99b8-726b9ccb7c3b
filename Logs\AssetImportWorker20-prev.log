Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:18:57Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker20
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker20.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [38876]  Target information:

Player connection [38876]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1727364029 [EditorId] 1727364029 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [38876] Host joined multi-casting on [***********:54997]...
Player connection [38876] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.13 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.34 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56732
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005765 seconds.
- Loaded All Assemblies, in  0.710 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.563 seconds
Domain Reload Profiling: 1273ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (285ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (279ms)
			TypeCache.Refresh (277ms)
				TypeCache.ScanAssembly (258ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (564ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (250ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.347 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.72 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.741 seconds
Domain Reload Profiling: 3087ms
	BeginReloadAssembly (280ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (908ms)
		LoadAssemblies (528ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (535ms)
			TypeCache.Refresh (405ms)
				TypeCache.ScanAssembly (371ms)
			BuildScriptInfoCaches (105ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1742ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (262ms)
			ProcessInitializeOnLoadAttributes (967ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 5.82 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.0 MB). Loaded Objects now: 8335.
Memory consumption went from 210.1 MB to 201.1 MB.
Total: 19.387600 ms (FindLiveObjects: 1.242800 ms CreateObjectMapping: 1.383100 ms MarkObjects: 8.360500 ms  DeleteObjects: 8.399200 ms)

========================================================================
Received Import Request.
  Time since last request: 1812753.507081 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)PinkClear.mat
  artifactKey: Guid(7119f4676a7006b4f84f592f1f218002) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)PinkClear.mat using Guid(7119f4676a7006b4f84f592f1f218002) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f77f97772af3266e51ba23359c90ad94') in 6.5009401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)LightBlue.mat
  artifactKey: Guid(810f2207d0c19a94980377788bfa507c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)LightBlue.mat using Guid(810f2207d0c19a94980377788bfa507c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65d709b43e1b40ae983ce453f8b83580') in 0.0744689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 41.504314 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab
  artifactKey: Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Plank.prefab using Guid(9433a9dacb6dfee438b9eecbbd6f56b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '505fc12cc3244c1b78813935412e42c9') in 0.0589018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab
  artifactKey: Guid(2508951d6b5158047b8c53425a720d81) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Small.prefab using Guid(2508951d6b5158047b8c53425a720d81) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c681b4e711c25dea1c493b3178f0599') in 0.0412611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab
  artifactKey: Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeSphere.prefab using Guid(56bd2ff22176c424b8ae06281e1401eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f73f63900edfdd821509f40f88fff7fd') in 0.0670382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab
  artifactKey: Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ramp.prefab using Guid(2872327bc55b85f40a6f09a39f4625f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27ceb89ff8e3bd592a03cf8238fa4c7e') in 0.0689213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab
  artifactKey: Guid(048e6d455f851f140bf5df0f43f7747e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab using Guid(048e6d455f851f140bf5df0f43f7747e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c27ba074cc33c9f0fcbc31a98fd8d30d') in 0.0532188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0