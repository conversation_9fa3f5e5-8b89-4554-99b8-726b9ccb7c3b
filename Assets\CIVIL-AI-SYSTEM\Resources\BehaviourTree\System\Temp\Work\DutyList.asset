%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e2e157e58268604faab405e70084c01, type: 3}
  m_Name: DutyList
  m_EditorClassIdentifier: 
  nodes:
    _keys:
    - b768798a-93de-44b8-993c-a40b86040fff
    _values:
    - id: b768798a-93de-44b8-993c-a40b86040fff
      nodeConnection:
      - guid: 0e142c56-dc84-43a7-8d26-45fd4e5e4049
        requirementData:
          rid: -2
      iterator: 0
      globalWeighting: 0.75
      localWeighting: 0.75
      name: collect wood
      desc: 
      weighting: 0
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
