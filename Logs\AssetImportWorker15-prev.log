Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker15
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker15.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [38716]  Target information:

Player connection [38716]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1689097789 [EditorId] 1689097789 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [38716] Host joined multi-casting on [***********:54997]...
Player connection [38716] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 22.43 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 3.01 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56840
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005879 seconds.
- Loaded All Assemblies, in  1.104 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.130 seconds
Domain Reload Profiling: 2233ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (108ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (124ms)
	LoadAllAssembliesAndSetupDomain (511ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (504ms)
			TypeCache.Refresh (500ms)
				TypeCache.ScanAssembly (439ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1131ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (927ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (85ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (307ms)
			ProcessInitializeOnLoadAttributes (396ms)
			ProcessInitializeOnLoadMethodAttributes (127ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.334 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.97 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.362 seconds
Domain Reload Profiling: 5692ms
	BeginReloadAssembly (453ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (133ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (130ms)
	LoadAllAssembliesAndSetupDomain (1577ms)
		LoadAssemblies (1000ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (827ms)
			TypeCache.Refresh (610ms)
				TypeCache.ScanAssembly (568ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (3363ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2328ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (20ms)
			BeforeProcessingInitializeOnLoad (437ms)
			ProcessInitializeOnLoadAttributes (1670ms)
			ProcessInitializeOnLoadMethodAttributes (188ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.47 seconds
Refreshing native plugins compatible for Editor in 10.21 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 1.91 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (4.9 MB). Loaded Objects now: 8334.
Memory consumption went from 208.2 MB to 203.3 MB.
Total: 31.203900 ms (FindLiveObjects: 3.986600 ms CreateObjectMapping: 3.446300 ms MarkObjects: 14.029600 ms  DeleteObjects: 9.738700 ms)

========================================================================
Received Import Request.
  Time since last request: 1812182.752614 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_ImpactGround.prefab
  artifactKey: Guid(512ba2be3c335144fa1a6eaaaeb2dafa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_ImpactGround.prefab using Guid(512ba2be3c335144fa1a6eaaaeb2dafa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fc2c53682a68163de6c01002f1180415') in 3.5501082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 105

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Scripts/Controllers/PlayerController3D.cs
  artifactKey: Guid(6cfb8360923fbe0489489cd496d084d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/PlayerController3D.cs using Guid(6cfb8360923fbe0489489cd496d084d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '881ff11b1a2caf1353a3e334a830566a') in 0.5021772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_03.mat
  artifactKey: Guid(9f605f1b8a260c34c9b34af79741b0fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_03.mat using Guid(9f605f1b8a260c34c9b34af79741b0fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7c8ba894b05271deb1180ef02a46c4f0') in 1.9564202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Settings/Mobile_RPAsset.asset
  artifactKey: Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/Mobile_RPAsset.asset using Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d05326380d7e7bf2aa98db97f9d9525') in 0.2069063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_11.prefab
  artifactKey: Guid(9f8b589841bea3c45991cca3098751a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_11.prefab using Guid(9f8b589841bea3c45991cca3098751a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e8bfdeff5a604e9c1886a473a4759f23') in 0.0937019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_01_Dirty.mat
  artifactKey: Guid(e468b13984260334fb47dbf12d5ae8ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_01_Dirty.mat using Guid(e468b13984260334fb47dbf12d5ae8ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3db03dccc16e0bbdfdca4808af1c4a7') in 0.8108496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/SkinDiffusionProfile.asset
  artifactKey: Guid(48e911a1e337b44e2b85dbc65b47a594) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/SkinDiffusionProfile.asset using Guid(48e911a1e337b44e2b85dbc65b47a594) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8a1000352f0b5e801d45d510a54e9a67') in 0.0773943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[PlayerAnimated].prefab
  artifactKey: Guid(cf4b3ff444aac3d479825f1fa43b6237) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[PlayerAnimated].prefab using Guid(cf4b3ff444aac3d479825f1fa43b6237) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3138eacdebea5b6c0b3cd87980695c0c') in 0.1385134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 296

========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_05_2x5.mat
  artifactKey: Guid(52cc90312a9eacc4dbdcae00c6bb7007) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_05_2x5.mat using Guid(52cc90312a9eacc4dbdcae00c6bb7007) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83abe09ef5d5181c7430e47a6d42f3a5') in 0.6873181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_4.prefab
  artifactKey: Guid(455682f92b929434fbd87009ed5ae269) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_4.prefab using Guid(455682f92b929434fbd87009ed5ae269) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '55a82a17dccd48761199d101857b0c9d') in 0.0460159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity
  artifactKey: Guid(f56951a420d7af648a3ae7527f83bc25) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity using Guid(f56951a420d7af648a3ae7527f83bc25) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '07ca8bdbb02e0e14dc4c77f919bdd22c') in 0.0569586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_02.prefab
  artifactKey: Guid(abc00000000003958368600663530461) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_02.prefab using Guid(abc00000000003958368600663530461) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e16c735d8e7aa4aa7f733fc5ff31ca6') in 0.0344355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Trampoline.prefab
  artifactKey: Guid(e7a352f5f8207f641ad56e6c3831468f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Trampoline.prefab using Guid(e7a352f5f8207f641ad56e6c3831468f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a75be7ea5b51495a2188a54e16cb7ff0') in 0.0339631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_FoamImpact.prefab
  artifactKey: Guid(c4d499a9e2b74fb41ad940b085b28e33) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_FoamImpact.prefab using Guid(c4d499a9e2b74fb41ad940b085b28e33) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'df9e9b4d1c21177656b0da1f793e6312') in 0.0707809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01_2.prefab
  artifactKey: Guid(abc00000000005316435892695745325) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01_2.prefab using Guid(abc00000000005316435892695745325) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b5984f413942a00713bec0280b101cb1') in 0.0618949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashNoise_01_3x5.mat
  artifactKey: Guid(f0f36c10aa777b2439c14619ce1f7a14) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashNoise_01_3x5.mat using Guid(f0f36c10aa777b2439c14619ce1f7a14) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1074efb01901275e6daf347cb9840280') in 0.5938779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_09.prefab
  artifactKey: Guid(abc00000000004464500011015390631) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_09.prefab using Guid(abc00000000004464500011015390631) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '018d79cda2c9fed14da02c100c445493') in 0.04036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_01.mat
  artifactKey: Guid(bd39735466490684bb478d9f05046312) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_StandardGlow_01.mat using Guid(bd39735466490684bb478d9f05046312) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f713ebee40649b2b699d23e57059ddb') in 0.0564933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_03.prefab
  artifactKey: Guid(abc00000000008938765446196406868) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_03.prefab using Guid(abc00000000008938765446196406868) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '71b44a612b64d54bc9c4cb973e386144') in 0.0369482 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialGroundCircle_01_4x4.mat
  artifactKey: Guid(200339bd447757548b59a52289ff368d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialGroundCircle_01_4x4.mat using Guid(200339bd447757548b59a52289ff368d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26282a754c635169e38d49c948747d16') in 0.0529478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Empty/Scene.unity
  artifactKey: Guid(8124e5870f4fd4c779e7a5f994e84ad1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Scene.unity using Guid(8124e5870f4fd4c779e7a5f994e84ad1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Empty/Scene.unity additively'
Loaded scene 'Assets/Empty/Scene.unity'
	Deserialize:            22.817 ms
	Integration:            8.375 ms
	Integration of assets:  0.118 ms
	Thread Wait Time:       0.026 ms
	Total Operation Time:   31.337 ms
 -> (artifact id: '27bfde3bde301c20ea1cb7ea407358e1') in 0.0820845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_03.prefab
  artifactKey: Guid(abc00000000004819125062046549367) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_03.prefab using Guid(abc00000000004819125062046549367) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22d45002cd3ae0efa54ec99c946c91d9') in 0.0759999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_01.prefab
  artifactKey: Guid(abc00000000013837959523274180471) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_01.prefab using Guid(abc00000000013837959523274180471) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '75c629a8430b9cc5c14c18bd844f2afd') in 0.0456693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_03.prefab
  artifactKey: Guid(abc00000000002718345017765155898) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_03.prefab using Guid(abc00000000002718345017765155898) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '888e062f3ee626da9dba935845ca706d') in 0.0381848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneSettings.lighting
  artifactKey: Guid(57c1f638a8e915e448ad3796862ad893) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneSettings.lighting using Guid(57c1f638a8e915e448ad3796862ad893) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12b4f9dc47330fbfa8340aa4844a2c2c') in 0.0628121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_04.prefab
  artifactKey: Guid(abc00000000014686189744945388786) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_04.prefab using Guid(abc00000000014686189744945388786) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ea754a0d4b6a507b2c9dadfaea4ac2c') in 0.0801309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_15.prefab
  artifactKey: Guid(abc00000000002259975432115874212) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_15.prefab using Guid(abc00000000002259975432115874212) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '91e5d195f7b590502910948a2c05509b') in 0.1112235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long.prefab
  artifactKey: Guid(abc00000000016262846598353024921) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long.prefab using Guid(abc00000000016262846598353024921) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2536220df11f6834b78d98feb0cc4450') in 0.0390227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong.prefab
  artifactKey: Guid(abc00000000017602426385492134554) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong.prefab using Guid(abc00000000017602426385492134554) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2a7b7e1d03479f23efff6bb0e518f71b') in 0.0770962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_03.mat
  artifactKey: Guid(06ca4bd894a7a7f41bbf7a183e5f446c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_03.mat using Guid(06ca4bd894a7a7f41bbf7a183e5f446c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '451770f31a718e212d6181b1c7727b96') in 0.0432121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8.mat
  artifactKey: Guid(ca41aa2fde6d9304d8a576da32b2ff9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_01_6x8.mat using Guid(ca41aa2fde6d9304d8a576da32b2ff9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a77b90ba8f9b9f799ebae488c3afab4d') in 0.6019326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_03_Fix.prefab
  artifactKey: Guid(abc00000000001582411784641340072) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_03_Fix.prefab using Guid(abc00000000001582411784641340072) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '85466b42625f7b7d24566c1cf102080b') in 0.1053132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment.prefab
  artifactKey: Guid(abc00000000001546907486632341860) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment.prefab using Guid(abc00000000001546907486632341860) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '49f2708986de92457a67a1e9bb319910') in 0.0546092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_02.prefab
  artifactKey: Guid(abc00000000009808437911761296293) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_02.prefab using Guid(abc00000000009808437911761296293) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2d8af42e16cf54ca3f22cbf5ec6657c6') in 0.0421571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01.prefab
  artifactKey: Guid(abc00000000004535829560276980957) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01.prefab using Guid(abc00000000004535829560276980957) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '74fcc8aa8daca5c46b017d5dafbdbfe3') in 0.082029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_01.prefab
  artifactKey: Guid(abc00000000003709479468941872714) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_01.prefab using Guid(abc00000000003709479468941872714) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '34600b6134971cd4d61238551833e54b') in 0.0840788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_02.prefab
  artifactKey: Guid(abc00000000008384018515174714024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Counter_02.prefab using Guid(abc00000000008384018515174714024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ff625cbfea6537e42253fd68c6d229fc') in 0.0404635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_15.prefab
  artifactKey: Guid(abc00000000013862869448088672586) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_15.prefab using Guid(abc00000000013862869448088672586) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2bb16040d486906f9ea9d03732a73eec') in 0.0565469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a.prefab
  artifactKey: Guid(abc00000000016747308749705262770) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a.prefab using Guid(abc00000000016747308749705262770) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5e50dbad0e90bbbfc4bec2ed71a3dcae') in 0.0711744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.prefab
  artifactKey: Guid(70c482706ba2a504ab47e4bfd710ac9e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.prefab using Guid(70c482706ba2a504ab47e4bfd710ac9e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '66437c7a5c531af8ae23e5cd3027b8fe') in 0.3544049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A_1.prefab
  artifactKey: Guid(abc00000000016488092838365708715) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A_1.prefab using Guid(abc00000000016488092838365708715) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f8256c1fa1955abf962a9975af721440') in 0.0361564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_02.prefab
  artifactKey: Guid(abc00000000014351051305097994192) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_02.prefab using Guid(abc00000000014351051305097994192) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '664d1f0dfdde5453fb582b4c9cd605ad') in 0.0473548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_03_2x5.mat
  artifactKey: Guid(332052334afc47a44872ae5ee0d9cfdf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Water_LinearSplash_03_2x5.mat using Guid(332052334afc47a44872ae5ee0d9cfdf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '770a12cba96e50fc68f2a2c41c71d03e') in 0.5773046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_02.prefab
  artifactKey: Guid(abc00000000013741201486591237417) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Jar_02.prefab using Guid(abc00000000013741201486591237417) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '161e7a92c62e7928e9bc39cc0188a516') in 0.0323316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_03_1.prefab
  artifactKey: Guid(abc00000000017012127876838236024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_03_1.prefab using Guid(abc00000000017012127876838236024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ff74c547cb91f57f4ef4f8119aaec127') in 0.0624313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_02.prefab
  artifactKey: Guid(abc00000000001904259696300257621) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_02.prefab using Guid(abc00000000001904259696300257621) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2c8e247724317464f5041062a654d554') in 0.0677637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Poison_SimpleSplash_01.prefab
  artifactKey: Guid(578f4dc6bf2d25d42b056dfcdd6b2628) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Poison_SimpleSplash_01.prefab using Guid(578f4dc6bf2d25d42b056dfcdd6b2628) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c9d050aadb3c63fe8cb3534421cf164') in 0.385078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_F.prefab
  artifactKey: Guid(abc00000000000895846825472282064) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_F.prefab using Guid(abc00000000000895846825472282064) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1c3817f8247877ed72cd81710c302af7') in 0.0628961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_A.prefab
  artifactKey: Guid(abc00000000001841544801729642007) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_A.prefab using Guid(abc00000000001841544801729642007) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '20f6ed799c79bc904537491443e29cc6') in 0.0702538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08.prefab
  artifactKey: Guid(abc00000000016303654725886764330) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08.prefab using Guid(abc00000000016303654725886764330) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '02d959a55826bf2f92b1c4555e583ba9') in 0.0488617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_04.prefab
  artifactKey: Guid(abc00000000003060818401318630062) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_04.prefab using Guid(abc00000000003060818401318630062) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '35f6042b9c425a5134e8f8f8eaa8ff56') in 0.0603812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterSimpleProjectile_Traveling.prefab
  artifactKey: Guid(6d4c2bd141c202242a96c86f3edee707) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterSimpleProjectile_Traveling.prefab using Guid(6d4c2bd141c202242a96c86f3edee707) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '34781564bb0a2b974376799cd19d9303') in 0.0459405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Shield_01.prefab
  artifactKey: Guid(abc00000000015881715998179242804) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Shield_01.prefab using Guid(abc00000000015881715998179242804) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '42d3f6b5b391fc93d72392934bf701db') in 0.0381122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_03.prefab
  artifactKey: Guid(abc00000000010392827481872026946) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_03.prefab using Guid(abc00000000010392827481872026946) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7b3dd9162895edd9e901fdf0fd266b91') in 0.0561488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Carrot.prefab
  artifactKey: Guid(abc00000000005070647827898157856) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Carrot.prefab using Guid(abc00000000005070647827898157856) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd6a7013e5d8dbb92cadf961f935a301a') in 0.0315302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/LerpTransparencies_WithNoise.shadergraph
  artifactKey: Guid(d9cc33a123b5b4b4580a46ea8d110e76) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/LerpTransparencies_WithNoise.shadergraph using Guid(d9cc33a123b5b4b4580a46ea8d110e76) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd472a4e0583f7064d984e52e50e8315') in 0.0520439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_1.prefab
  artifactKey: Guid(abc00000000011812170210108673851) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_1.prefab using Guid(abc00000000011812170210108673851) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cfd617edb02c7ae64c74db4e50e42dcb') in 0.0330284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Cast.prefab
  artifactKey: Guid(3a42d8d03a34231428341ac591cfde3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Cast.prefab using Guid(3a42d8d03a34231428341ac591cfde3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe5c87dec975072ebfcbb0df5e3c5dc8') in 0.1649317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/WaterShader_Sphere.shadergraph
  artifactKey: Guid(cb0b2b013d559b94abc05f51029d49dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/WaterShader_Sphere.shadergraph using Guid(cb0b2b013d559b94abc05f51029d49dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8270587eafee1581eab95dcad98882d') in 0.0481416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_01.prefab
  artifactKey: Guid(abc00000000006264877392217652874) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_01.prefab using Guid(abc00000000006264877392217652874) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22bed8fc10a0ae0df318c52a818503d0') in 0.0391141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TableAndChairs.prefab
  artifactKey: Guid(abc00000000011690563593163698220) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TableAndChairs.prefab using Guid(abc00000000011690563593163698220) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e9fd905cdafdbec5c4130f3e4a6770b') in 0.0403527 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Square_Planks_4M.prefab
  artifactKey: Guid(abc00000000002059608317990159834) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Square_Planks_4M.prefab using Guid(abc00000000002059608317990159834) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'caf930df71b4a0a9a2d99318556227b1') in 0.0525755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x4_01.prefab
  artifactKey: Guid(abc00000000014155675160327830357) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_3x4_01.prefab using Guid(abc00000000014155675160327830357) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '56f17699724702406e4f7175a8c2fa05') in 0.0371391 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_A.prefab
  artifactKey: Guid(abc00000000015566476325916018874) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_A.prefab using Guid(abc00000000015566476325916018874) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f273ed0dcf2062d0be65ede3f5eb2ca6') in 0.0363472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_04.prefab
  artifactKey: Guid(abc00000000016516650418989987030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_04.prefab using Guid(abc00000000016516650418989987030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c166a6417e327d1babcbdb516b892800') in 0.0371043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04.prefab
  artifactKey: Guid(abc00000000011950325879607030052) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04.prefab using Guid(abc00000000011950325879607030052) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '10c2c49bed69f5b60991d06ab459a7fa') in 0.0413001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x6_01.prefab
  artifactKey: Guid(abc00000000015944167753137022154) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x6_01.prefab using Guid(abc00000000015944167753137022154) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '47594be3c7f838c78aba4fd7830e867f') in 0.0463829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_InnerCorner_01.prefab
  artifactKey: Guid(abc00000000015450678356241267505) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_InnerCorner_01.prefab using Guid(abc00000000015450678356241267505) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ee5a5900257b881d12f6e07312d296d5') in 0.0399605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabB.prefab
  artifactKey: Guid(abc00000000009222023049361081909) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabB.prefab using Guid(abc00000000009222023049361081909) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0233f2c296463cc6aa44ede97f7a00be') in 0.0400831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown/Sky and Fog Settings Profile.asset
  artifactKey: Guid(15903b6d22d20b2489e20ad183b10773) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown/Sky and Fog Settings Profile.asset using Guid(15903b6d22d20b2489e20ad183b10773) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'be739140ae585547c1d7d0157d73a2d2') in 0.0311541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_03.prefab
  artifactKey: Guid(abc00000000011239063437705940543) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_03.prefab using Guid(abc00000000011239063437705940543) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b20283f6f991e0d511b957af6511a3ca') in 0.0320008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/Sound Effects, Stinger and Ambience Review.wav
  artifactKey: Guid(4244c4ec6471339418588e23e52aed92) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/Sound Effects, Stinger and Ambience Review.wav using Guid(4244c4ec6471339418588e23e52aed92) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac93072921d28f2ffd91fa511d0ca4f2') in 0.6149021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Default Outline.png
  artifactKey: Guid(3074f8ca5b373ab46a7333ddbe58100b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Default Outline.png using Guid(3074f8ca5b373ab46a7333ddbe58100b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5da176c3a3f39b9bdc58e72896b0bd6c') in 0.2542642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader
  artifactKey: Guid(0178fcb869bafef4690d177d31d17db8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader using Guid(0178fcb869bafef4690d177d31d17db8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f13dd607460d05340742336d1899d659') in 0.0375062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Scene.png
  artifactKey: Guid(4445790c252970d45ae069bd9011b484) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Scene.png using Guid(4445790c252970d45ae069bd9011b484) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d029dcbd31813aa9de733ae4f2c8bec') in 0.070114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/ReflectionProbe-0.exr
  artifactKey: Guid(a5afe8b22695ed048be4e011d9b0aad4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation/ReflectionProbe-0.exr using Guid(a5afe8b22695ed048be4e011d9b0aad4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a32698b2d5a1262e2cee517447da7584') in 0.0502558 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Surface-Mobile.shader
  artifactKey: Guid(85187c2149c549c5b33f0cdb02836b17) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Surface-Mobile.shader using Guid(85187c2149c549c5b33f0cdb02836b17) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '518c2768677e9e30a8dcd47cab053734') in 0.0275677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_02.prefab
  artifactKey: Guid(abc00000000017957118347635269566) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_02.prefab using Guid(abc00000000017957118347635269566) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ffcd7664ddb68960603041ddd4c844bf') in 0.034522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_03.prefab
  artifactKey: Guid(abc00000000002799692809114770013) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_03.prefab using Guid(abc00000000002799692809114770013) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c136c55cc81ff7471c085c129de4729') in 0.0854205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Explosion.prefab
  artifactKey: Guid(246470a14b557ee4a9e374413a699082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Explosion.prefab using Guid(246470a14b557ee4a9e374413a699082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a64f027f565681b50388fdea0ffe98a0') in 0.1140371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 133

========================================================================
Received Import Request.
  Time since last request: 0.000212 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Water_01.mat
  artifactKey: Guid(5f8d04154cf0bee4d9be1a93c770193a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Water_01.mat using Guid(5f8d04154cf0bee4d9be1a93c770193a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1136561b13401d458d8718a7e96e97b5') in 0.6099801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Side_B.prefab
  artifactKey: Guid(abc00000000015347540358612700065) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Side_B.prefab using Guid(abc00000000015347540358612700065) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ddc6dc61d4b3e35f531b89bee5c5de60') in 0.0383094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_MagicWater_OrbLoop.prefab
  artifactKey: Guid(3978f9137b47bba40a4ea58f2f46d448) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_MagicWater_OrbLoop.prefab using Guid(3978f9137b47bba40a4ea58f2f46d448) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd565150221057c3d890ebfaf914c0c16') in 0.0774473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 118

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Empty/Settings/HDRP Balanced.asset
  artifactKey: Guid(3e2e6bfc59709614ab90c0cd7d755e48) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRP Balanced.asset using Guid(3e2e6bfc59709614ab90c0cd7d755e48) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4fdc4316117dc0cc5805bc67b24de0ba') in 0.0249161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern I.png
  artifactKey: Guid(66f5d8c4b3f3fab41be3847ff69428ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Modern I.png using Guid(66f5d8c4b3f3fab41be3847ff69428ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '598ada3a2ff19598809a9c241dedb898') in 0.0717823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Unlock Light.png
  artifactKey: Guid(ac41dfdff3dbc5347a80e3e9d4a8a4a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Unlock Light.png using Guid(ac41dfdff3dbc5347a80e3e9d4a8a4a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de7c350fdbcd89b668e20f81a984dcb2') in 0.0663871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility Off Light.png
  artifactKey: Guid(a77eda282343f8f4eac895c675b1d0d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility Off Light.png using Guid(a77eda282343f8f4eac895c675b1d0d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85a9e9e7d701e8a1441e7b56986d73e9') in 0.0740764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility Off Dark.png
  artifactKey: Guid(2dbfd3cc8273bfd41a4dba007693ba2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility Off Dark.png using Guid(2dbfd3cc8273bfd41a4dba007693ba2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58343414d691d3955c033d3bf13b677d') in 0.0453815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Postmodern I.png
  artifactKey: Guid(ae40793beaf811a4a91be43db0933924) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Postmodern I.png using Guid(ae40793beaf811a4a91be43db0933924) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40713b0ba90f2ad0d1118f6e78c7427b') in 0.077713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility On Light.png
  artifactKey: Guid(fb2e42b3a34b8bb489535d3d084de17c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Visibility On Light.png using Guid(fb2e42b3a34b8bb489535d3d084de17c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '017a3682fc1606e082d7e57b96626661') in 0.0642281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_BODY_F_01_Dirty.mat
  artifactKey: Guid(2d9140dcc126def44bcbf74eca9a401d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_BODY_F_01_Dirty.mat using Guid(2d9140dcc126def44bcbf74eca9a401d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5cf8ab967759b62cd1448eb716e312bd') in 0.7572426 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Advanced.cs
  artifactKey: Guid(38d6d2ce3d31ede45beec24e3473c066) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Advanced.cs using Guid(38d6d2ce3d31ede45beec24e3473c066) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3ac9c0640b792f22d27f9a5bb8edd3e') in 0.029688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_08.mat
  artifactKey: Guid(ae1954c65457c1c4796a2857f30a605c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_08.mat using Guid(ae1954c65457c1c4796a2857f30a605c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9bb4330c275ce7f6e8c3fd187c34bb37') in 0.4986066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.192669 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_26.mat
  artifactKey: Guid(408a437b645e1074baeaeca4466ff58e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_26.mat using Guid(408a437b645e1074baeaeca4466ff58e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14f3d0ac61d35bc72ba577b6df4a6c9f') in 0.1537698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_02_Dirty.mat
  artifactKey: Guid(b767a152526d6c94aa199b7b26c44b00) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_02_Dirty.mat using Guid(b767a152526d6c94aa199b7b26c44b00) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5d30a6db60ee2ffd0cf9edceeacf846') in 0.7123882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_14_01.mat
  artifactKey: Guid(9127a010c37c73546b43018651d31206) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_14_01.mat using Guid(9127a010c37c73546b43018651d31206) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '874cd2603e405ed58021896e281ab2a5') in 1.0000398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_03.mat
  artifactKey: Guid(95b9ac170d6be4d4d9114ab9cc214177) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_03.mat using Guid(95b9ac170d6be4d4d9114ab9cc214177) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e47d7f3b4e2863fb6c5bd930f2d4521') in 0.3315695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_01.mat
  artifactKey: Guid(8e909ab962c50e34b9da863cc5bee21c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_01.mat using Guid(8e909ab962c50e34b9da863cc5bee21c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed45e93d6319a7fedef7e8fcbc0b1713') in 0.2746665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_17.png
  artifactKey: Guid(8096ab10d00d540468fef24f065c7bf9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_17.png using Guid(8096ab10d00d540468fef24f065c7bf9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94322bf669235fc0d2bcfc193ea1b800') in 0.1052243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000601 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Normal_03.png
  artifactKey: Guid(aab497a0b7d270f409345d6a5628b945) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Normal_03.png using Guid(aab497a0b7d270f409345d6a5628b945) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3da987845271a326c52c1c73a7a976d9') in 0.1115455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset.asset
  artifactKey: Guid(b1e8df3a04d888b41bc499cb89a23b6c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset.asset using Guid(b1e8df3a04d888b41bc499cb89a23b6c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f5175850862a7568c660953936c4f4c') in 0.0988983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(cdeafcc103c3e3d4990c581ed587b2fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineGlobalSettings.asset using Guid(cdeafcc103c3e3d4990c581ed587b2fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05a59f776d24b4f5b92ef77bbed7c89c') in 0.0671106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_30.mat
  artifactKey: Guid(f47266506b7a31e43bffe0904ea2b7cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_30.mat using Guid(f47266506b7a31e43bffe0904ea2b7cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4bf01d82b3312a423378af8718726f8') in 0.3135971 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Black.mat
  artifactKey: Guid(bc17866a726121c4f96c2448dc04f4cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Black.mat using Guid(bc17866a726121c4f96c2448dc04f4cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1556753d320e6454195ddacdb5e5249f') in 0.7615227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_16.png
  artifactKey: Guid(c14c7700f9fd9e0488e29a38a1729d33) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_16.png using Guid(c14c7700f9fd9e0488e29a38a1729d33) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c05d35010184d41bc57807d62df0d33') in 0.0690637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_23_Alternative.mat
  artifactKey: Guid(aa2df0d4ea33d8447ba65028cb2dfadc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_23_Alternative.mat using Guid(aa2df0d4ea33d8447ba65028cb2dfadc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5931d85ef98cde14978cbe38a41f705e') in 0.3148797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000125 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_07.png
  artifactKey: Guid(fb2244e877de10c4981be0dd5c062f06) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_07.png using Guid(fb2244e877de10c4981be0dd5c062f06) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af372e032be0993408fa1037dd1c9d88') in 0.0656454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_DETAIL_Normal_MASK.png
  artifactKey: Guid(d172ad1654f831948b933c1c72d6edf6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_DETAIL_Normal_MASK.png using Guid(d172ad1654f831948b933c1c72d6edf6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da841a9149e4d8997cae4b9081410048') in 0.0750827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_03.png
  artifactKey: Guid(b98878c414e1a3b4680f15f6549bb61a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_13_03.png using Guid(b98878c414e1a3b4680f15f6549bb61a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b335ff4f39d958cc7d9abcbc0e407ee7') in 0.0827705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_24.png
  artifactKey: Guid(74e10e1f70e4b1b4c9b7705a600a0d96) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_24.png using Guid(74e10e1f70e4b1b4c9b7705a600a0d96) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0e1be802be8354749c53d476c7277a7') in 0.0732458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Normal_01.png
  artifactKey: Guid(85efc7731471ebf4ebb037bcf99597ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Normal_01.png using Guid(85efc7731471ebf4ebb037bcf99597ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be02ad91d7d055795a2c5fbe254c120c') in 0.0845308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_MaskMap.png
  artifactKey: Guid(bfee40aac91bf8f41921a9783ac940f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_MaskMap.png using Guid(bfee40aac91bf8f41921a9783ac940f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '388c00b54895dee4cfd89eb5eed1c20a') in 0.0550092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Ivory.mat
  artifactKey: Guid(c1bb4b6f8be8d1a4aa000d81ade8a56f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Ivory.mat using Guid(c1bb4b6f8be8d1a4aa000d81ade8a56f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3605e3f287b9a1b19eedc2149eca90a5') in 0.1590421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Sclera.png
  artifactKey: Guid(3144b7a3ee38f984196a3d296bed7ada) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Sclera.png using Guid(3144b7a3ee38f984196a3d296bed7ada) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd2032cdd27a248daa502f690b15f2cb') in 0.0823362 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_02.png
  artifactKey: Guid(b95d7302ff83797499a837aa20b92fc9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_11_02.png using Guid(b95d7302ff83797499a837aa20b92fc9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '935c76582fd7f426c0b2c834ace007db') in 0.061894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000710 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_20.png
  artifactKey: Guid(51a418b1eead4484f9df4f68154f4950) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_20.png using Guid(51a418b1eead4484f9df4f68154f4950) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d01dbfd146740bb3f71083d19668e9b') in 0.1073343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_10.png
  artifactKey: Guid(fb1d0662937d15940963660ab3b87fab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_10.png using Guid(fb1d0662937d15940963660ab3b87fab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58ee9ae30ab5a0a435358481b7326108') in 0.0727083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_21.png
  artifactKey: Guid(43d23bea124048148a7deaa6d9f9e3c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_21.png using Guid(43d23bea124048148a7deaa6d9f9e3c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8614d87634a1202c1f3e370591c5b33') in 0.0754094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset_Renderer.asset
  artifactKey: Guid(b7cb793f0e2198349833d4c0b91499b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/URPDefaultResources/UniversalRenderPipelineAsset_Renderer.asset using Guid(b7cb793f0e2198349833d4c0b91499b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce74e1cc46e69c9fd367ef9b553a4b71') in 0.1763549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_05.png
  artifactKey: Guid(14b9c788866ef9b4a80aac5eb1001f6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_05.png using Guid(14b9c788866ef9b4a80aac5eb1001f6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd84a53f3c02ad1f9dde6945bfdf63152') in 0.1033259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_19_Alternative.mat
  artifactKey: Guid(0d0899692ef5ce045aef1dde61c9ae16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_19_Alternative.mat using Guid(0d0899692ef5ce045aef1dde61c9ae16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99ec96d665acdaa5c99f4b7d74c3781e') in 0.4579659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_BaseColor.png
  artifactKey: Guid(9e772482ea9a26e439bb3009ed97357a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_BaseColor.png using Guid(9e772482ea9a26e439bb3009ed97357a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abe7a476c78a888542d488f78554ccbe') in 0.092156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_Normal.png
  artifactKey: Guid(526cde2442e336e419a32987e1ab74c4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_Normal.png using Guid(526cde2442e336e419a32987e1ab74c4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '210319f9c58fb454d5fd930d750f5bce') in 0.1477239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_03.png
  artifactKey: Guid(23d36bc5f1efc054383837f193730786) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_03.png using Guid(23d36bc5f1efc054383837f193730786) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fa218e143743a5e0b6888209e7adc99') in 0.1496193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)RedClear.mat
  artifactKey: Guid(379b9e3ae55cbc24e8b774854c72f561) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)RedClear.mat using Guid(379b9e3ae55cbc24e8b774854c72f561) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de5dac3e1668ea42e21e6981daa33e47') in 0.0729136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Idle.anim
  artifactKey: Guid(2c949caf50da74243a1a1730b7ed4894) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Idle.anim using Guid(2c949caf50da74243a1a1730b7ed4894) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb7c5900f58419d5f3b9d4de8d9d296f') in 0.0594407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/IdaFaber/Textures/Pattern/T_Pattern_02.png
  artifactKey: Guid(391fdbaeffd8f6545a37b427b93f8011) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Pattern/T_Pattern_02.png using Guid(391fdbaeffd8f6545a37b427b93f8011) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ce0f689efa8db83719299ae5bd6a7ef') in 0.0671903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Dirt_02.png
  artifactKey: Guid(464d51ebe3ed80c49b0936b2bd997239) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Dirt_02.png using Guid(464d51ebe3ed80c49b0936b2bd997239) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e55baf387cefbb020f62c129b71dd46') in 0.0732542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab
  artifactKey: Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab using Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ab5788109eb54046c4cf1b32cf9d6ba') in 0.0631473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_MaskMap.png
  artifactKey: Guid(8cc3e5d29e108d046a53514435f93c9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_MaskMap.png using Guid(8cc3e5d29e108d046a53514435f93c9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6c8d4ffc6cd772c08d2ee0f874b74cf') in 0.1188591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Textures/Pattern/T_Pattern_01.png
  artifactKey: Guid(b0f46d6764b02c348b7f5cf3fd9b67c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Pattern/T_Pattern_01.png using Guid(b0f46d6764b02c348b7f5cf3fd9b67c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ddc0dc5c03f82bff2abd9e0733211d83') in 0.0536604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_ROCA_BODY_BaseColor_Markings.png
  artifactKey: Guid(f41b3a92a97bfa54a9cdaf0ad4b0e885) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_ROCA_BODY_BaseColor_Markings.png using Guid(f41b3a92a97bfa54a9cdaf0ad4b0e885) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9f1e0b2a7291a0112daefc5a8b76556') in 0.0629204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.100269 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Climb.prefab
  artifactKey: Guid(5852e38a76009af4a9930c5b14fca2f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Climb.prefab using Guid(5852e38a76009af4a9930c5b14fca2f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f163331b0915967e9c608b5e1745bfb2') in 0.0800904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_Magicorb.playable
  artifactKey: Guid(48fe222c7da7d2241ab5c9c43c7ece10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_Magicorb.playable using Guid(48fe222c7da7d2241ab5c9c43c7ece10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '537b767cd31d3ad8197be1170efc66f8') in 0.0658297 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterfallFoam_02.png
  artifactKey: Guid(b5badb1c1af7f95409ce7da60ec0ebc0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterfallFoam_02.png using Guid(b5badb1c1af7f95409ce7da60ec0ebc0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '967c38df0d5faa0eced2e5c96d0d3d0b') in 0.0527588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_OffsetSphereMask_01 1.png
  artifactKey: Guid(8bea54f0b4b1b7146bcc54069363062d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_OffsetSphereMask_01 1.png using Guid(8bea54f0b4b1b7146bcc54069363062d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47af6d601913ffa228d8a4dd336b527b') in 0.0451978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_01.psd
  artifactKey: Guid(b6b812f3c47a8ef47b1fadd7d0191773) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_01.psd using Guid(b6b812f3c47a8ef47b1fadd7d0191773) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e85e6fb64db1dc948ad1048c1bbb802') in 0.0617188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FallingWater_4x4_02.psd
  artifactKey: Guid(2c960d1808fa5e048ad9b9d3f3cdea5f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FallingWater_4x4_02.psd using Guid(2c960d1808fa5e048ad9b9d3f3cdea5f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '941aa17a70f0f65ec30fd5e3db11be2f') in 0.0690979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlipbookDots_01.png
  artifactKey: Guid(7f9f980ddbad7914b9948951b4d1b5ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlipbookDots_01.png using Guid(7f9f980ddbad7914b9948951b4d1b5ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98d48c985d7dbd38ac92d6799d9ce71a') in 0.0570451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_02.psd
  artifactKey: Guid(a90c418aa757f544ab58246df1f7ef10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MainSlash_4x4_02.psd using Guid(a90c418aa757f544ab58246df1f7ef10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd87bddf4e32c1bf0fedcee5b62a61e5') in 0.0676207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FallingWater_4x3_01.psd
  artifactKey: Guid(7f05efca875ec76429b0b82272b2877e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FallingWater_4x3_01.psd using Guid(7f05efca875ec76429b0b82272b2877e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a24bac47aec10b68eade30d47ed1c211') in 0.0888477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_RadialBurst_01.psd
  artifactKey: Guid(ebde58515f079544287da77d518a6497) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_RadialBurst_01.psd using Guid(ebde58515f079544287da77d518a6497) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab77d8a63fd6cf0fdd443f6da9bb85b6') in 0.0494606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_3.prefab
  artifactKey: Guid(c5f6af8ade86ca5449b9775f1d70fa8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_3.prefab using Guid(c5f6af8ade86ca5449b9775f1d70fa8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb649e413486eec60a10cc18debf0bc0') in 0.0619282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterLoop_01_5x5.psd
  artifactKey: Guid(56700453adc30e442b21738698dd7208) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterLoop_01_5x5.psd using Guid(56700453adc30e442b21738698dd7208) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ed54d22e30b3eb642e61f0d58af212e') in 0.0869832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterfallFoam_01.png
  artifactKey: Guid(b63b5cfd7ac2fc14983180bd27ad0f48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterfallFoam_01.png using Guid(b63b5cfd7ac2fc14983180bd27ad0f48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbf4dd4401c94caff45bc7975f62bc30') in 0.0534253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_NormalSplash_4x4_01.psd
  artifactKey: Guid(01981984d2dbade4b852b1d222f396b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_NormalSplash_4x4_01.psd using Guid(01981984d2dbade4b852b1d222f396b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c60cdec7fff9758a9916a2eea9767ec6') in 0.0933783 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.432196 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterMask.png
  artifactKey: Guid(adbaaf5376ea73a47b07c5bfb5560943) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterMask.png using Guid(adbaaf5376ea73a47b07c5bfb5560943) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1834d6b882b54a0fe87e0847c6701243') in 0.0931525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Input/LockCursor.cs
  artifactKey: Guid(79c5bfd0a915d9443ac472e81374d0c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Input/LockCursor.cs using Guid(79c5bfd0a915d9443ac472e81374d0c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d40021bfdf58eea9b4b493915f9c0e2') in 0.0341697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_18.mat
  artifactKey: Guid(9d534f7f0622e0a49b15cc5fbc680d6a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_18.mat using Guid(9d534f7f0622e0a49b15cc5fbc680d6a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5909094be44615230ab6634d7b9d8252') in 1.3177762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000153 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_BubbleMagic_5x5_01.png
  artifactKey: Guid(534ee4defadabe245ad1533ca387f444) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_BubbleMagic_5x5_01.png using Guid(534ee4defadabe245ad1533ca387f444) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b04e9d2fe0633de5286eafe536924895') in 0.0708711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Orange_Ombre_alpha.png
  artifactKey: Guid(d076c635d6224894188454ccfe938d6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Orange_Ombre_alpha.png using Guid(d076c635d6224894188454ccfe938d6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a535c73d43afa01c0c90397a387773e4') in 0.1301528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/TRNS_MUSC_E_Intro_1.wav
  artifactKey: Guid(ae90f4159a75be04ea1fcb508a1230d1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/TRNS_MUSC_E_Intro_1.wav using Guid(ae90f4159a75be04ea1fcb508a1230d1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65ab6c9827b6ccbb5d4abff65ecad9c2') in 0.250186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)X.png
  artifactKey: Guid(7fcc2a60475702b43acbdbb2432d7d59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/Textures/(Txt)X.png using Guid(7fcc2a60475702b43acbdbb2432d7d59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc822dc506d447c3fb22bbb782b100e1') in 0.0940214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamIndividual_4x4_02.png
  artifactKey: Guid(fad497a570f985e498ebafd5025baf4e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamIndividual_4x4_02.png using Guid(fad497a570f985e498ebafd5025baf4e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5aecfe4eb3ae0e23f195a10b61f24ff6') in 0.0862211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_07.mat
  artifactKey: Guid(640b3d8d629a2e54b91e0dafdabea68f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_07.mat using Guid(640b3d8d629a2e54b91e0dafdabea68f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1be9ab37e4ab312427beae6209b5b8b7') in 0.1820828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TiledWater_3x4_01.png
  artifactKey: Guid(45db14a54fc7a294f9a76ed2ac04ee57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TiledWater_3x4_01.png using Guid(45db14a54fc7a294f9a76ed2ac04ee57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7926d643c38046295b3c33fe57d5aba') in 0.080753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/SFX_UI_Hint_Short_1.wav
  artifactKey: Guid(1e8eb0c56b0af4646b4d8f272c180afc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/SFX_UI_Hint_Short_1.wav using Guid(1e8eb0c56b0af4646b4d8f272c180afc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '860b7836d1bed27a978bdbc2fdd2da7e') in 0.1445687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR45_Root.FBX
  artifactKey: Guid(232d28d2a6aff1544acabbb49f4e89bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BR45_Root.FBX using Guid(232d28d2a6aff1544acabbb49f4e89bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1dd7e696de91ba6f833b2386138cdcdc') in 0.1160581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLoop_01.png
  artifactKey: Guid(3e4182363cebf8d429e59d68f8dad103) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLoop_01.png using Guid(3e4182363cebf8d429e59d68f8dad103) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7e28787c026f2b10e8df4c1452ea107') in 0.0524991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TwirlTiled_4x5_01.png
  artifactKey: Guid(693e1e7d22496a8498d172efdbac3eb0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_TwirlTiled_4x5_01.png using Guid(693e1e7d22496a8498d172efdbac3eb0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34556857e6614f28a28e3e75ddf80e30') in 0.0486134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_A_To_Crouch.FBX
  artifactKey: Guid(27be5c8df560f734fb42f7dd7a47b8c9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_A_To_Crouch.FBX using Guid(27be5c8df560f734fb42f7dd7a47b8c9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '476a1dcf4d3131ceeeb338e8cb2dddf5') in 0.0536573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_B_To_Crouch_Root.FBX
  artifactKey: Guid(3d64b0a11ef443d43850f2291e4a69fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/15__Walk_To_Crouch/M_Big_Sword@Walk_ver_B_To_Crouch_Root.FBX using Guid(3d64b0a11ef443d43850f2291e4a69fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f31f8a5d8fbbaea3046384ffdc80a774') in 0.0672694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_root.png
  artifactKey: Guid(baff62cd5d733ec41a65fb672cd084d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_root.png using Guid(baff62cd5d733ec41a65fb672cd084d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c434e6250a3f6cc3d4e90ecb975a52fa') in 0.0621251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterSimpleSplash_4x4_01.png
  artifactKey: Guid(2077fa38349073e4690c18e9a60221cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterSimpleSplash_4x4_01.png using Guid(2077fa38349073e4690c18e9a60221cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14becd1bc12bedbf5a524aa92e2565bc') in 0.0668625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_E_Loop_1.wav
  artifactKey: Guid(e459321a43e3202469b354534d71b093) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_E_Loop_1.wav using Guid(e459321a43e3202469b354534d71b093) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4845c186e2c0286dea3ea135ad8aac8') in 0.7501681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_16.png
  artifactKey: Guid(6a14373a1ecafc745a5721980b9baa56) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_16.png using Guid(6a14373a1ecafc745a5721980b9baa56) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '815cfda6e449fcc086a097e509205137') in 0.0592395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_04.mat
  artifactKey: Guid(f6ea2a4d655657241842fbac1a0db82a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_04.mat using Guid(f6ea2a4d655657241842fbac1a0db82a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0fdb4905c104e64efcedcd0d2f15d8f4') in 0.1765509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Start_ZeroHeight.FBX
  artifactKey: Guid(bb904196078a3464cb028154a1e38df3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Start_ZeroHeight.FBX using Guid(bb904196078a3464cb028154a1e38df3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ca9aca156909f4c9fd45b5260ecbab8') in 0.0753546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FR45_Root.FBX
  artifactKey: Guid(469130c8e7d86f541841d6e099858470) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FR45_Root.FBX using Guid(469130c8e7d86f541841d6e099858470) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f79948cdde1d81f5955dbe8ce5ade097') in 0.0835633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_A.FBX
  artifactKey: Guid(b841ccdce6f504e47bd94b6ebce5cd37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/5__Run_Injured/M_Big_Sword@Run_Injured_ver_A.FBX using Guid(b841ccdce6f504e47bd94b6ebce5cd37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84301d95940e31da97b363fa7eb8da09') in 0.0619559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_ALL_ZeroHeight.FBX
  artifactKey: Guid(3aaf11c3aeda72549ac676fb2ba3d0fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_ALL_ZeroHeight.FBX using Guid(3aaf11c3aeda72549ac676fb2ba3d0fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1278a8573bbf1d6325568f092e295fbc') in 0.0800494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_1.wav
  artifactKey: Guid(414c54dd335919e4283af6bd372a0e51) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_1.wav using Guid(414c54dd335919e4283af6bd372a0e51) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c1215d8683809bfa84987dd652ed784') in 0.1259144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterMask_4x6_01_FoamRender.png
  artifactKey: Guid(bed4723ae58e30841a0986585d910592) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterMask_4x6_01_FoamRender.png using Guid(bed4723ae58e30841a0986585d910592) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56e21ac6f7459b0440b78f66a1575073') in 0.1013425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_13.mat
  artifactKey: Guid(addf3674f149b7145a85268d045e9e94) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_13.mat using Guid(addf3674f149b7145a85268d045e9e94) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6658f79a209e35729aa7004846ffb938') in 0.1805546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_3_Inplace.FBX
  artifactKey: Guid(1c2a95287b4f93b44bc3a4a3270c0c16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_3_Inplace.FBX using Guid(1c2a95287b4f93b44bc3a4a3270c0c16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4caca1de07dc6239260a98e024ba7852') in 0.085532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Natural.png
  artifactKey: Guid(110d6bc39a3df934384b94698501397a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Natural.png using Guid(110d6bc39a3df934384b94698501397a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e34774d21638da742083946138712223') in 0.0697636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLateralImpact_4x3_01_Grayscale.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterLateralImpact_4x3_01_Grayscale.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8774ae3de788f8e5b13d9c9e28ba91c8') in 0.0869598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_C.FBX
  artifactKey: Guid(7cac73c2336bb004683c714fb771b962) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_C.FBX using Guid(7cac73c2336bb004683c714fb771b962) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33a18ed5389eef08d9be88ad8adb1d53') in 0.0659816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_2.wav
  artifactKey: Guid(0531f551eca9d7042b04335dcaeca1bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_2.wav using Guid(0531f551eca9d7042b04335dcaeca1bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '619b77095fb9df310d0056ced4ffdabe') in 0.1713687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_A.FBX
  artifactKey: Guid(a48d20d799d44904d962b86c008bed5e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_A.FBX using Guid(a48d20d799d44904d962b86c008bed5e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '865622979f998d3156a95a15aa5cce5e') in 0.0784187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_2.FBX
  artifactKey: Guid(1a436aa52e89b8049bdafcd98b98ca6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_2.FBX using Guid(1a436aa52e89b8049bdafcd98b98ca6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84ea43786eb2b218e9ac04582bc2c02b') in 0.0600616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_High_KnockDown_ZeroHeight.FBX
  artifactKey: Guid(b007ee90dc2ef2745aa6296bd05c8ac4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_High_KnockDown_ZeroHeight.FBX using Guid(b007ee90dc2ef2745aa6296bd05c8ac4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6e6a75e1af2b02644250432d04f1077') in 0.0628627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_High_KnockDown.FBX
  artifactKey: Guid(fb9b70bead7913f4995825ea31e1ab09) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_High_KnockDown.FBX using Guid(fb9b70bead7913f4995825ea31e1ab09) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63c886e8d2abeddbdfe6fab5730b5dc1') in 0.0791761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_1_Inplace.FBX
  artifactKey: Guid(c45b7027d69f28b4eaefb95ed65bcc67) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_1_Inplace.FBX using Guid(c45b7027d69f28b4eaefb95ed65bcc67) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7569aab48d771dc04d16712578a7aa98') in 0.0518444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die.FBX
  artifactKey: Guid(2fab5d1d69cf30e4ab3b5370eb65ab6e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die.FBX using Guid(2fab5d1d69cf30e4ab3b5370eb65ab6e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f367322f77eb1e68f56f13e846bda359') in 0.0675983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Loop.FBX
  artifactKey: Guid(3fa5704c6cebc174da2ffca73404fc80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_Loop.FBX using Guid(3fa5704c6cebc174da2ffca73404fc80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b3ff19e04bee25326ffdb0004c7b6c7') in 0.0685215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_Turn_Root.FBX
  artifactKey: Guid(735a0dfdb573d5146b5fe6d9fa105808) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_B_Turn_Root.FBX using Guid(735a0dfdb573d5146b5fe6d9fa105808) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0cdc0260c0a3452949409e0fac7db2e8') in 0.0726397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_ALL_Inplace.FBX
  artifactKey: Guid(a5220ea1dd3ebb04e99d7a1dd21c8953) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_ALL_Inplace.FBX using Guid(a5220ea1dd3ebb04e99d7a1dd21c8953) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80f4194f45c1123009e3999a49cac77f') in 0.0579605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_ALL_ZeroHeight.FBX
  artifactKey: Guid(b9c8ea5beab0d914b811cb3ba86edf72) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_ALL_ZeroHeight.FBX using Guid(b9c8ea5beab0d914b811cb3ba86edf72) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f0c8f48068d8fc60b8b3fb4e6a285f2') in 0.064981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Smash_Double.FBX
  artifactKey: Guid(91c25a9996f5b0240b051c7c73cb0043) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_Smash_Double.FBX using Guid(91c25a9996f5b0240b051c7c73cb0043) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e16bfa5a1dedfc8902ad14834e1eea3') in 0.1052752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_A_Idle.FBX
  artifactKey: Guid(3d2910c01b0eb7e46a48f7246f1df60e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_A_To_Crouch_ver_A_Idle.FBX using Guid(3d2910c01b0eb7e46a48f7246f1df60e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99c8fd37711814c7ce5d19d4c40961ca') in 0.0751341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Loop.FBX
  artifactKey: Guid(5c208891f34d16044bb3412ea7f8f887) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Loop.FBX using Guid(5c208891f34d16044bb3412ea7f8f887) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8090e40e6bc7c3c91c0625db0fcc459') in 0.0718029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_10.mat
  artifactKey: Guid(a5db4e97c8bf7224fbe609da9127631d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_10.mat using Guid(a5db4e97c8bf7224fbe609da9127631d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cfabeb3abef79837540b8d5edab9d56') in 0.2195057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Textures/Env_TX_GroundGrid.png
  artifactKey: Guid(99f29f05f6b4bf247a700ddd1750f0df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Textures/Env_TX_GroundGrid.png using Guid(99f29f05f6b4bf247a700ddd1750f0df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2503d2f17a01f61f686e868767651abf') in 0.0607328 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Back.FBX
  artifactKey: Guid(4c7515e0629f41141a2fef19f0724dcf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Back.FBX using Guid(4c7515e0629f41141a2fef19f0724dcf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '152527d209323ace0306475eb9486d9e') in 0.0696735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B_Root.FBX
  artifactKey: Guid(a4a6f1a96f11c4643bd3bd011f4fb2e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B_Root.FBX using Guid(a4a6f1a96f11c4643bd3bd011f4fb2e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6e7c863f03acb0509fd97d3a5e65aa1') in 0.0680748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_Attach_ZeroHeight.FBX
  artifactKey: Guid(35f06e39787836d45a38635c21cf981b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4_Attach_ZeroHeight.FBX using Guid(35f06e39787836d45a38635c21cf981b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '081df8d897bc93213beb61120603193f') in 0.0684495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_B_Root.FBX
  artifactKey: Guid(23bea49deff8e3c488864df759a596c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_To_Walk_ver_B_Root.FBX using Guid(23bea49deff8e3c488864df759a596c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4eaed65a5e22fa1deef10211cc85cb51') in 0.0749631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start.FBX
  artifactKey: Guid(7783200db1acb884ab1928b0094472aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start.FBX using Guid(7783200db1acb884ab1928b0094472aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51b5e5db3e08005a2592ccf9a816b96b') in 0.06371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_ALL.FBX
  artifactKey: Guid(c1efb4ad0ee896743b07c8057b54cbdb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_ALL.FBX using Guid(c1efb4ad0ee896743b07c8057b54cbdb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c376ad11dea7b384a17a2ec90218953') in 0.0549337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start_ZeroHeight_Z0.FBX
  artifactKey: Guid(dba3b6ab68d721046a869acb27620b3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Start_ZeroHeight_Z0.FBX using Guid(dba3b6ab68d721046a869acb27620b3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '42971eac3ca4fdd5f4811c636598e5fc') in 0.0687538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Smash.FBX
  artifactKey: Guid(039d3f2c288655645886ee5134b00ee7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Smash.FBX using Guid(039d3f2c288655645886ee5134b00ee7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '360be995ede78ec4ba2ec39adcdb6df7') in 0.0941535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_A_ZeroHeight.FBX
  artifactKey: Guid(a21b186c7c4031a45ae8bce77fdd600b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_A_ZeroHeight.FBX using Guid(a21b186c7c4031a45ae8bce77fdd600b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0601a47fe2441bab0400b9cecba2150e') in 0.0560846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2_Move.FBX
  artifactKey: Guid(61860a5209e042c4f83615f07279b876) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_2_Move.FBX using Guid(61860a5209e042c4f83615f07279b876) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f93da246d2d6df963ba557e84483d7fe') in 0.1146327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 139

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4.FBX
  artifactKey: Guid(f053195916b44a148a0ca09d59de55c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_4.FBX using Guid(f053195916b44a148a0ca09d59de55c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b446e97a3cd475bb1d4e44383b91907') in 0.0742355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_StandUp.FBX
  artifactKey: Guid(1af1127288c0f4b4fad18f086c21b34a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_StandUp.FBX using Guid(1af1127288c0f4b4fad18f086c21b34a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '61bee10242535429f00775a11cb8a146') in 0.0558808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_Attach_ZeroHeight.FBX
  artifactKey: Guid(71652a895af10244f959e17d6b198b25) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_Attach_ZeroHeight.FBX using Guid(71652a895af10244f959e17d6b198b25) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '483c8b9660f6b84da095d00e9bebc5c3') in 0.2702716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 322

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90.FBX
  artifactKey: Guid(5eb9672e9c783f74ebcb3d2d23bd9eaf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90.FBX using Guid(5eb9672e9c783f74ebcb3d2d23bd9eaf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa77f69fc8ceaf468c71ca72140139d1') in 0.0785231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_Attach.FBX
  artifactKey: Guid(bc48905dae8519a46ae0a98e19f53f62) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_Attach.FBX using Guid(bc48905dae8519a46ae0a98e19f53f62) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52bbfcf9a89028bd50dcf983569d85c3') in 0.0720783 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Rythmic_1.wav
  artifactKey: Guid(3889e8cd94f340a46a5bbe3aae397108) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Rythmic_1.wav using Guid(3889e8cd94f340a46a5bbe3aae397108) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fd60a128a50fba6280e568e4e3fc9bb') in 0.2073409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Accept.FBX
  artifactKey: Guid(64a30163b219bcc4ab07047d16dd44b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Accept.FBX using Guid(64a30163b219bcc4ab07047d16dd44b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75e4fe1550ec4daf34977fcd3f4f3f5b') in 0.0639354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Wood_A1.mat
  artifactKey: Guid(abc00000000003024444187039167912) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Wood_A1.mat using Guid(abc00000000003024444187039167912) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a604002bd19b7cd09f74b76ccb313f92') in 0.1056666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000121 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_02.mat
  artifactKey: Guid(abc00000000015246686425851332343) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_02.mat using Guid(abc00000000015246686425851332343) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e1b94ca67da386344974ed6cbd51b5c') in 0.0969764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die.FBX
  artifactKey: Guid(31086bd557dcb6249b5668bd0b8a5d1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die.FBX using Guid(31086bd557dcb6249b5668bd0b8a5d1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '575636be3f467fe2a7f21af07f39c910') in 0.0811858 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_65.FBX
  artifactKey: Guid(6519542efcd4be14cb2a1e7682be993e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_65.FBX using Guid(6519542efcd4be14cb2a1e7682be993e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a6f1b1de9e84929d8658b0ca5a294d2') in 2.0860774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.001012 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_01.prefab
  artifactKey: Guid(abc00000000004726281824012566575) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_01.prefab using Guid(abc00000000004726281824012566575) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2413a86e4c10f04a4533930a792f6abc') in 0.0967466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_16.fbx
  artifactKey: Guid(abc00000000007563892285159922169) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Fence_02a_Splines_16.fbx using Guid(abc00000000007563892285159922169) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7983307f0e4a921eb20f4ea56d210f9a') in 0.1694927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_9.fbx
  artifactKey: Guid(abc00000000006188934554544756145) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_9.fbx using Guid(abc00000000006188934554544756145) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9820323d8a05711cafc734dc78f9fe7') in 0.0795367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bed.prefab
  artifactKey: Guid(abc00000000018241557152127990318) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bed.prefab using Guid(abc00000000018241557152127990318) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b210e0b611ed82417908736e62e1f90') in 0.0870251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die_Root.FBX
  artifactKey: Guid(517009d75b98d1749870a4ea95310741) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/6__Die/M_Katana_Blade@Damage_Die_Root.FBX using Guid(517009d75b98d1749870a4ea95310741) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '501a4dcf0a2f991ffb67408885e80ede') in 0.1115998 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.prefab
  artifactKey: Guid(fb31d62fd729c27488ed5cc8153e2b12) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_33.prefab using Guid(fb31d62fd729c27488ed5cc8153e2b12) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b73cb4fcc3e6d034e9b75f8c8972f80a') in 0.5383783 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_16.prefab
  artifactKey: Guid(abc00000000015187561951193175487) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_16.prefab using Guid(abc00000000015187561951193175487) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a41f75af463df00aaed40f7c5f3392c') in 0.096243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_01.prefab
  artifactKey: Guid(abc00000000004626776900735612895) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_01.prefab using Guid(abc00000000004626776900735612895) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '300707be67db434df1d72af9dcd242ed') in 0.0898241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cabinet_02.prefab
  artifactKey: Guid(abc00000000016902396061287896548) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cabinet_02.prefab using Guid(abc00000000016902396061287896548) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48d60959c243a7324dcbe794dfd6eb43') in 0.0785398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_01.prefab
  artifactKey: Guid(abc00000000013837959523274180471) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_01.prefab using Guid(abc00000000013837959523274180471) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '213502b464de9b3bd5bffa1d2842928c') in 0.0884438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Flag_02.mat
  artifactKey: Guid(abc00000000011493624177128157933) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Flag_02.mat using Guid(abc00000000011493624177128157933) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3fd6ab95db78446a5d4ee32219b88f4') in 0.1014109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_ChimneyPots.mat
  artifactKey: Guid(abc00000000018252559287460586247) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_ChimneyPots.mat using Guid(abc00000000018252559287460586247) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5caad163b24151f721f7c98088a81d4c') in 0.0539439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_11.prefab
  artifactKey: Guid(9f8b589841bea3c45991cca3098751a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_11.prefab using Guid(9f8b589841bea3c45991cca3098751a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e88129112157055199813036ca8ebcb0') in 0.0863732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Wood_Algae.mat
  artifactKey: Guid(abc00000000007403405953551258654) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Wood_Algae.mat using Guid(abc00000000007403405953551258654) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '881dd8dde86b635407126d470166a1d0') in 0.1286815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_04.prefab
  artifactKey: Guid(abc00000000005248435499528781811) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_04.prefab using Guid(abc00000000005248435499528781811) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f94e3e3207d36c5e13eafafcdd8507a4') in 0.0665425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_69.prefab
  artifactKey: Guid(abc00000000000417408960686482107) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_69.prefab using Guid(abc00000000000417408960686482107) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81f73387355bfc73a2838a86135d297d') in 0.0984355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000109 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_01.prefab
  artifactKey: Guid(abc00000000001864290288166493324) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_01.prefab using Guid(abc00000000001864290288166493324) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ded007824dcdfcc170d511777bbffae') in 0.0766795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_02.prefab
  artifactKey: Guid(abc00000000017518264023766144327) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_02.prefab using Guid(abc00000000017518264023766144327) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa64d055b0c415ea37f917de290bb159') in 0.0784174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fish_2.prefab
  artifactKey: Guid(abc00000000002571688947622677864) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fish_2.prefab using Guid(abc00000000002571688947622677864) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc089d82ddf96d3d86bc1840f0bf3ce8') in 0.0529854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_02.prefab
  artifactKey: Guid(abc00000000007279725207663366801) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_A_02.prefab using Guid(abc00000000007279725207663366801) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d943091be58efa5d4497f943d45f02b') in 0.0525543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_01.prefab
  artifactKey: Guid(abc00000000006989009370769287934) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_01.prefab using Guid(abc00000000006989009370769287934) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57c1fb198321caa72e608a489f4968d7') in 0.0706334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_B_01.prefab
  artifactKey: Guid(abc00000000009960594456047277785) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_B_01.prefab using Guid(abc00000000009960594456047277785) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'daf7403c7d919956dcc0709bcdf1c2aa') in 0.0607948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment.prefab
  artifactKey: Guid(abc00000000001546907486632341860) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment.prefab using Guid(abc00000000001546907486632341860) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6365233999f83feabbd61ec9030633a3') in 0.093041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cup_01.prefab
  artifactKey: Guid(abc00000000006862668080870461067) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cup_01.prefab using Guid(abc00000000006862668080870461067) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cff40ec02691fedecd3a733643ac13f8') in 0.0656645 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_02.prefab
  artifactKey: Guid(abc00000000003590945754320045061) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_02.prefab using Guid(abc00000000003590945754320045061) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '60e457c2af6c1d238552db267ab907fd') in 0.0776523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_40.fbx
  artifactKey: Guid(abc00000000012660856231273186397) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_40.fbx using Guid(abc00000000012660856231273186397) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '743bcf6139c5b4550dccdeb13e5cb930') in 0.1751193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_13.prefab
  artifactKey: Guid(abc00000000017219841727514398970) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_13.prefab using Guid(abc00000000017219841727514398970) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b54f2a43b3fd0a421dc823c2379f600') in 0.0952236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_B_Root.FBX
  artifactKey: Guid(fb06b81c8b107db4ebc52b9cbc9366c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_B_Root.FBX using Guid(fb06b81c8b107db4ebc52b9cbc9366c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df28c9c60da1ca9d140979f6ae747240') in 0.0667848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12_1.prefab
  artifactKey: Guid(abc00000000012089362061524675311) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_12_1.prefab using Guid(abc00000000012089362061524675311) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa68bfacef503fd2632088572932fcb7') in 0.0964633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug.prefab
  artifactKey: Guid(abc00000000000690484132462057524) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug.prefab using Guid(abc00000000000690484132462057524) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d5f7f9c4449e28c4dd188ae9ca418a6') in 0.0811347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve.prefab
  artifactKey: Guid(abc00000000008699132442441646196) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Sleeve.prefab using Guid(abc00000000008699132442441646196) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7afc07930b35d46d6aa1d545c5d913c6') in 0.0793342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09_1.prefab
  artifactKey: Guid(abc00000000002029463449080447004) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_09_1.prefab using Guid(abc00000000002029463449080447004) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ded1f6f8047c112def45ff284f58883') in 0.071655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_02.prefab
  artifactKey: Guid(abc00000000004411290568376933152) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_02.prefab using Guid(abc00000000004411290568376933152) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86f1f117b4d9e3fbb68baa49e711c268') in 0.0592851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01.prefab
  artifactKey: Guid(abc00000000015005830544568857422) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01.prefab using Guid(abc00000000015005830544568857422) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f915be401a4a976e1d278f19a84d1787') in 0.0591286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_D.prefab
  artifactKey: Guid(abc00000000000558807816154458515) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_D.prefab using Guid(abc00000000000558807816154458515) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ebdd024ad79a4fd063a58e3369a0114') in 0.0623678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_B.prefab
  artifactKey: Guid(abc00000000005067968224069450894) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_B.prefab using Guid(abc00000000005067968224069450894) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a0624419761f64ab1345c2d64c86f12') in 0.0555042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Podium.prefab
  artifactKey: Guid(abc00000000010819368574505770761) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Podium.prefab using Guid(abc00000000010819368574505770761) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbff8e77af28c0bfb85f4f38011f29b2') in 0.07276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Small.prefab
  artifactKey: Guid(abc00000000016608912069669358665) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Small.prefab using Guid(abc00000000016608912069669358665) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1bf09222a574c19e31d15f40739b375') in 0.0579072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_02.prefab
  artifactKey: Guid(abc00000000005439670819512234065) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Bundle_02.prefab using Guid(abc00000000005439670819512234065) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8f3db0eb4aa85498d6c26b754fa3eb9') in 0.054276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Waterfall_Loop_1.wav
  artifactKey: Guid(e01c74597ada1d94d9e1a3332b950453) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Waterfall_Loop_1.wav using Guid(e01c74597ada1d94d9e1a3332b950453) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1cb0df694decc2198097c2762dfe902') in 0.286578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_A_01.prefab
  artifactKey: Guid(abc00000000001314945204152803406) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_A_01.prefab using Guid(abc00000000001314945204152803406) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ec91e2377c9af09549452f5c3d29210') in 0.0766318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000266 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_01.prefab
  artifactKey: Guid(abc00000000016185023768972930501) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_01.prefab using Guid(abc00000000016185023768972930501) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '720064905e70b11964880e2b933ca2d2') in 0.0465478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_2.prefab
  artifactKey: Guid(abc00000000010042851532632003929) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_2.prefab using Guid(abc00000000010042851532632003929) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77573187d635ce6e94bfbcb71591a8ec') in 0.0577153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_02.prefab
  artifactKey: Guid(abc00000000003736532129170593977) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_02.prefab using Guid(abc00000000003736532129170593977) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cfb5fec4be7b1ad8d88a7d535c28d7d2') in 0.0691965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs.prefab
  artifactKey: Guid(abc00000000011763392490455100587) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs.prefab using Guid(abc00000000011763392490455100587) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3538af75835660510a6e00c317e346d0') in 0.0580329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_5M.prefab
  artifactKey: Guid(abc00000000013902534722377146128) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stairs_5M.prefab using Guid(abc00000000013902534722377146128) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e39015cde0f5c19401ecf271fa857aa3') in 0.110304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x6_01.prefab
  artifactKey: Guid(abc00000000015944167753137022154) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x6_01.prefab using Guid(abc00000000015944167753137022154) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ecd26a0b4f5a45fa11d92d854c768de') in 0.0690956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_InnerCorner_01.prefab
  artifactKey: Guid(abc00000000015450678356241267505) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_InnerCorner_01.prefab using Guid(abc00000000015450678356241267505) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '275878f2328a39e39d0129dcefb8d584') in 0.0621285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_03.prefab
  artifactKey: Guid(abc00000000016551305415872494976) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_03.prefab using Guid(abc00000000016551305415872494976) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72c48f1347952eb046ed6512099064bd') in 0.0628602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_01.prefab
  artifactKey: Guid(abc00000000010148249731873018540) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_01.prefab using Guid(abc00000000010148249731873018540) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '996a98e9d132ac2570c5d4c116260745') in 0.0623122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_M.PNG
  artifactKey: Guid(ecf7c86dd1c31744c8a3dce5cab61e00) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_M.PNG using Guid(ecf7c86dd1c31744c8a3dce5cab61e00) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59fa4a9653797242b1b477b837c4b4f0') in 0.0621205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cloud_RadientMask.PNG
  artifactKey: Guid(1d892c7a794ff0a4f8724ae87502f6d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cloud_RadientMask.PNG using Guid(1d892c7a794ff0a4f8724ae87502f6d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebcc9fcb15a44fdbb015c255227d6ee0') in 0.062496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_Basecolour.PNG
  artifactKey: Guid(edb301034140c2c47b283bbfbb0fd1aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_Basecolour.PNG using Guid(edb301034140c2c47b283bbfbb0fd1aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4eae698ec29fc43a1b3ff8143e29f6a') in 0.0461414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_Corner_01.prefab
  artifactKey: Guid(abc00000000006704188601032085561) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_Corner_01.prefab using Guid(abc00000000006704188601032085561) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9fd3569c40cf2e90daeb20c775c277a') in 0.0695484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_N.PNG
  artifactKey: Guid(9045bbbd94837d944a321112335ff546) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_N.PNG using Guid(9045bbbd94837d944a321112335ff546) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb6a10b44279e4cddf7cf8850fe27e27') in 0.0639101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Dirt_normal.PNG
  artifactKey: Guid(a4acb2e3e2d11054aad700a3bf4251ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Dirt_normal.PNG using Guid(a4acb2e3e2d11054aad700a3bf4251ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '818610e0aae7db37b5ad755f7f0712ac') in 0.078387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cups_BaseColor.PNG
  artifactKey: Guid(2aea83929acf5d04590b1ba4b0e75ff1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Cups_BaseColor.PNG using Guid(2aea83929acf5d04590b1ba4b0e75ff1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb52918e9eeed4606e225adb06de15fd') in 0.0494145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_ARM.PNG
  artifactKey: Guid(f0cedb1bab5c87747b3889686c884f8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_ARM.PNG using Guid(f0cedb1bab5c87747b3889686c884f8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '20f4b592b958fb2cb9275610ffff2d80') in 0.046775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_LeavesFX_Leaf1C.PNG
  artifactKey: Guid(bc2d257be692be64995e6aa433a1b290) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_LeavesFX_Leaf1C.PNG using Guid(bc2d257be692be64995e6aa433a1b290) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4cc2de276dd782918806eb27a6f93f5b') in 0.0732306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_N.PNG
  artifactKey: Guid(c252f2349a8f38845b316b50d3d69f69) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_N.PNG using Guid(c252f2349a8f38845b316b50d3d69f69) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b53d3be0ec73e6557f416930483b9d0') in 0.0670691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_basecolor.PNG
  artifactKey: Guid(b91935b27edcaf847a5c1e2f321fb5e8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_basecolor.PNG using Guid(b91935b27edcaf847a5c1e2f321fb5e8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '593b55fe130096a27a0206bf35dcea55') in 0.084217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_basecolor - Copy.PNG
  artifactKey: Guid(9c90c61824c6a9a4c8709de93527d820) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_basecolor - Copy.PNG using Guid(9c90c61824c6a9a4c8709de93527d820) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '372f9189d948c31b14f47f037a244f0e') in 0.0638439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_BaseColor.PNG
  artifactKey: Guid(dbdd18f1df42fa545b5484311cf7339c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_BaseColor.PNG using Guid(dbdd18f1df42fa545b5484311cf7339c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '432c420dd6a8830625b8d6750d6979d4') in 0.1323273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_02_normal.PNG
  artifactKey: Guid(36b7deeadf038834cba757be2de82aa6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_02_normal.PNG using Guid(36b7deeadf038834cba757be2de82aa6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5e25e8777176f18c0e8b2edd18507a2') in 0.0449678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_BC.PNG
  artifactKey: Guid(5971342aa25ebf5428ffe4f000792342) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_BC.PNG using Guid(5971342aa25ebf5428ffe4f000792342) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e1fd76ebd6171840f3f102446fa4d1b') in 0.0439773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_Moss_ORMH.PNG
  artifactKey: Guid(07c7ae7e583081746a2d0e18d2bb3d64) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_Moss_ORMH.PNG using Guid(07c7ae7e583081746a2d0e18d2bb3d64) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35db2a420793d46d1f502577387727e7') in 0.0567425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_normal - Copy.PNG
  artifactKey: Guid(fdca9f547a45b7d43b3f266fa94bb8ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_normal - Copy.PNG using Guid(fdca9f547a45b7d43b3f266fa94bb8ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9fe5068ebaacd07bf88e9073ebd82e87') in 0.0397501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_Normal.PNG
  artifactKey: Guid(a3c4aaa924e982141a60d165723ea15d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mud_Normal.PNG using Guid(a3c4aaa924e982141a60d165723ea15d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe05aed18a7b76de5296bd4d72a53cca') in 0.0549948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_N.PNG
  artifactKey: Guid(64debe33e77d355498a0c623b51c8d0d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFirBark_01_N.PNG using Guid(64debe33e77d355498a0c623b51c8d0d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72b0dcc4daa9b7d003e32017a9640721') in 0.0452648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_normal.PNG
  artifactKey: Guid(dc033165ef9c5b641b56c1004e6e1ad2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_normal.PNG using Guid(dc033165ef9c5b641b56c1004e6e1ad2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8cc32cb87ee7d29432b25b3a9b51b311') in 0.0439665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/CameraManager.cs
  artifactKey: Guid(41504c86d9c4ebd41a7896c0fcc071e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/CameraManager.cs using Guid(41504c86d9c4ebd41a7896c0fcc071e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c17940b35b1fb6af9db5749897a5f823') in 0.0296796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_ForwardLeft.controller
  artifactKey: Guid(3d66248d859d4ca47885f6945c59da9f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_ForwardLeft.controller using Guid(3d66248d859d4ca47885f6945c59da9f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb8dd2dff99a7563e3123b0400614899') in 0.0366045 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Prefabs/Human_BasicMotionsDummy_M.prefab
  artifactKey: Guid(bb96394e9629f20408ea23d3760ba123) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Prefabs/Human_BasicMotionsDummy_M.prefab using Guid(bb96394e9629f20408ea23d3760ba123) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5dd75fe4b36737ca2de48efbf94b33c') in 0.0696458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 247

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arm Right Mask.mask
  artifactKey: Guid(a1efc4df860a2fe4a8c3e44b28fd963e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arm Right Mask.mask using Guid(a1efc4df860a2fe4a8c3e44b28fd963e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffd133c7e90af430c3c65db0fbb6cd4f') in 0.0572152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/PlatformSensor.cs
  artifactKey: Guid(61a3037e0014c5345be9034239b44458) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/PlatformSensor.cs using Guid(61a3037e0014c5345be9034239b44458) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4775a7238797da6cd678c33402c8bc62') in 0.0311608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_basecolor.PNG
  artifactKey: Guid(71b9c50cc95bf8e4996d979f9ad09cb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_basecolor.PNG using Guid(71b9c50cc95bf8e4996d979f9ad09cb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ee870f8fd1613a3a8142403bd432bc8') in 0.0499995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arms Mask.mask
  artifactKey: Guid(a6373b7536cc6fd4485ebc59e60dc4cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Arms/Human Arms Mask.mask using Guid(a6373b7536cc6fd4485ebc59e60dc4cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce424b1b047825f01faea2e2f701f48c') in 0.048378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(bfa5422f1f4836f4e8693d7ca039c09c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(bfa5422f1f4836f4e8693d7ca039c09c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b2de5352038e4c99f4ac42f0508ca8ec') in 0.0304215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_ForwardRight.controller
  artifactKey: Guid(634ba9c616bbff2429fa6d79d25390e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_ForwardRight.controller using Guid(634ba9c616bbff2429fa6d79d25390e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8313c50c61fc34cd8e62547c50477653') in 0.0333514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Left.controller
  artifactKey: Guid(b6c0465f8e296d746874de35399bf579) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_Left.controller using Guid(b6c0465f8e296d746874de35399bf579) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '133a1dd8114689d6003ab8f337f3bfcc') in 0.0322815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_Normal.PNG
  artifactKey: Guid(92236693490872d47aa4e306930cd8b2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fruit_Normal.PNG using Guid(92236693490872d47aa4e306930cd8b2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9adde678d9fa4b55c1e00f3f3293d48d') in 0.0594509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_A_Turn_R90.FBX
  artifactKey: Guid(a4165689506c41d408c4930a98bf9ece) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_A_Turn_R90.FBX using Guid(a4165689506c41d408c4930a98bf9ece) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b9c79861326905b6ba6287f63fadbb02') in 0.0723376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_A_Turn_R90_Root.FBX
  artifactKey: Guid(27edcbc4d77673a4b8420f4aa3116f85) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_A_Turn_R90_Root.FBX using Guid(27edcbc4d77673a4b8420f4aa3116f85) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47500d27babc134afcfc6ff5154e4751') in 0.0635577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Run_B.FBX
  artifactKey: Guid(fd1afd8dc771ef849aa4f2bc37c8e0fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Run_B.FBX using Guid(fd1afd8dc771ef849aa4f2bc37c8e0fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc6b96f9e10d6657ebeb548d32cc827e') in 0.0648765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_A_Root.FBX
  artifactKey: Guid(2156e32be4229c346ba78269c147606d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_A_Root.FBX using Guid(2156e32be4229c346ba78269c147606d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6a5b7c23b8817e2ba0bd8ce63bff2d5') in 0.0638149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_A_Turn_L90.FBX
  artifactKey: Guid(1e8f03c8941dc4343a18018c9139cb22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Run_A_Turn_L90.FBX using Guid(1e8f03c8941dc4343a18018c9139cb22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '192590040ca1388fff595ce9a1ce51f3') in 0.0642866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Coin_1.wav
  artifactKey: Guid(df967d21de5b26842bc5da80bba9f572) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Coin_1.wav using Guid(df967d21de5b26842bc5da80bba9f572) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef82396c6e669bf5278d8c5313b8d054') in 0.1594163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Walk_A_Turn_L90.FBX
  artifactKey: Guid(4e06465aab4dd2948939161dc4d8c284) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Walk_A_Turn_L90.FBX using Guid(4e06465aab4dd2948939161dc4d8c284) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '129c0da964557191401647613fbeb9d0') in 0.0674056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_1.wav
  artifactKey: Guid(c3fbae6e7b0750046a8e18da615f08a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_1.wav using Guid(c3fbae6e7b0750046a8e18da615f08a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd02a0c1559a2e6ee3e45f70457dae05') in 0.0995687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_R90_Root.FBX
  artifactKey: Guid(9535b0ac86e81714b83fcf12c170686e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_R90_Root.FBX using Guid(9535b0ac86e81714b83fcf12c170686e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '722efa1e6044691cd7c65e05e793cbfb') in 0.0746507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Idle_Turn_L90_Root.FBX
  artifactKey: Guid(422b4ceb6255da7439863ca8ec150073) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_B_To_Idle_Turn_L90_Root.FBX using Guid(422b4ceb6255da7439863ca8ec150073) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcee6e5864ff3378716eb58873ed4b1f') in 0.0623717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_BR45_Root.FBX
  artifactKey: Guid(ff99a73c585b029408187d78741e09e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_BR45_Root.FBX using Guid(ff99a73c585b029408187d78741e09e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c0a4d5c38698e573695f57f9540a661') in 0.067336 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_FL45.FBX
  artifactKey: Guid(694d4ade98c44844c920503ec91fb5de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_FL45.FBX using Guid(694d4ade98c44844c920503ec91fb5de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44e854dbb4249fb1b3614a3d28702ef4') in 0.0802037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_Idle_Turn.FBX
  artifactKey: Guid(8dd7f73a90468ba47b51a1de7ab8e3ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_Idle_Turn.FBX using Guid(8dd7f73a90468ba47b51a1de7ab8e3ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a340a18c3660ef525805096e9503a72') in 0.0679895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L45_Root.FBX
  artifactKey: Guid(358dc588ef0166a48ba80b72a68bd6b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L45_Root.FBX using Guid(358dc588ef0166a48ba80b72a68bd6b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe24e68eb98fe8cbd225892361c4e8e9') in 0.0596028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L90_Root.FBX
  artifactKey: Guid(dcf24900e4aa2ac478f9c97e67d1f27d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_L90_Root.FBX using Guid(dcf24900e4aa2ac478f9c97e67d1f27d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b8ebd0c19d326643f80803bcb9be9d7') in 0.0590342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_B_Turn_R90_Root.FBX
  artifactKey: Guid(a2ed597ce6f55f9468fbe79885a0e79c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_B_Turn_R90_Root.FBX using Guid(a2ed597ce6f55f9468fbe79885a0e79c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48de26c7bb1ab06a378d1c16bbdfb03b') in 0.072233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_A.FBX
  artifactKey: Guid(d0d2272df558f954a9b01e428591b631) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Run_To_Idle/M_katana_Blade@Run_Fast_To_Idle_ver_A.FBX using Guid(d0d2272df558f954a9b01e428591b631) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22290f0d412ca10992a81926865df4f5') in 0.0572162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BR45_Root.FBX
  artifactKey: Guid(da33448e251872e42b50d0ff0ac506ea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BR45_Root.FBX using Guid(da33448e251872e42b50d0ff0ac506ea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f41388df43f7843eed2264a69eadce1') in 0.0765289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R90_Root.FBX
  artifactKey: Guid(04138e4053976ca46bd409ec5281dca2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_R90_Root.FBX using Guid(04138e4053976ca46bd409ec5281dca2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a8d1f57d09b0cda5edef7c8aade30fc') in 0.0630219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L45_Root.FBX
  artifactKey: Guid(f2dfae0e9e61cd74fbfe6000792d08a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L45_Root.FBX using Guid(f2dfae0e9e61cd74fbfe6000792d08a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6ab2e8d1705ba71bd84e3b75d838879') in 0.0549953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_B.FBX
  artifactKey: Guid(298adde5029697a47b20a68c8921e783) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_B.FBX using Guid(298adde5029697a47b20a68c8921e783) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2b6c43bbaef63a83ccfa9e4211ff530') in 0.0620394 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_Root.FBX
  artifactKey: Guid(2d8e8d73398e05d4b93bad3f5425ef5f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_Root.FBX using Guid(2d8e8d73398e05d4b93bad3f5425ef5f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3fe4ff9a0787fd00cd526f120498ce76') in 0.0814353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_Root.FBX
  artifactKey: Guid(621cda4d339f10e43a79fa6c5150ece1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_R90_Root.FBX using Guid(621cda4d339f10e43a79fa6c5150ece1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0914db08dd3a7c7fd28c7122453946cc') in 0.0591159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BL45_Root.FBX
  artifactKey: Guid(0596d49f88de8324aa273e169f662516) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_BL45_Root.FBX using Guid(0596d49f88de8324aa273e169f662516) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd4342d87d8b648b99703fa696aa267e') in 0.0590389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_B_Turn_R90.FBX
  artifactKey: Guid(e7f4592d75a02be46858474c364fd4f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Jogging_B_Turn_R90.FBX using Guid(e7f4592d75a02be46858474c364fd4f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79079d89be10dda842529b5602e652ec') in 0.0658272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Run_A_Root.FBX
  artifactKey: Guid(b709218ff58ed60469d33633ae86d467) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Run_A_Root.FBX using Guid(b709218ff58ed60469d33633ae86d467) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fafbf6e0aab9d203a1a1e26049952a36') in 0.1153631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_L90.FBX
  artifactKey: Guid(1a878afeee8908540975d929e011ed3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_L90.FBX using Guid(1a878afeee8908540975d929e011ed3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da58515c3d2dbd5d96c2c5823da70113') in 0.0740914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Run_B.FBX
  artifactKey: Guid(425997fccf1362b46924e342ed82faed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Run_B.FBX using Guid(425997fccf1362b46924e342ed82faed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '913fce8553089fe09032b0359e4ce74a') in 0.0634613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_L90_Root.FBX
  artifactKey: Guid(d4a1b8c158b1db64ea4404a8a86c9c25) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_L90_Root.FBX using Guid(d4a1b8c158b1db64ea4404a8a86c9c25) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57597fc905f3c6d864647e6196365d97') in 0.0673461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_R90_Root.FBX
  artifactKey: Guid(b4e867f4bbc0bad4099544762e89adab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Jogging_A_Turn_R90_Root.FBX using Guid(b4e867f4bbc0bad4099544762e89adab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bffb7ed0baf1127e874401e655f417b0') in 0.0807268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Run_B_Root.FBX
  artifactKey: Guid(ba8508c807850bf49b7e5193951ec133) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Run_B_Root.FBX using Guid(ba8508c807850bf49b7e5193951ec133) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25f14e41fa3fb444ff4ff967b7c06ccc') in 0.0833922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_Turn_R90_Root.FBX
  artifactKey: Guid(af096bc27968f2b4eab84951c199d92a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_Turn_R90_Root.FBX using Guid(af096bc27968f2b4eab84951c199d92a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a84c193334af18e8036b595d199f426') in 0.1760848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_R90.FBX
  artifactKey: Guid(905ca533233e9104f83943422115d59c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_R90.FBX using Guid(905ca533233e9104f83943422115d59c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bee91346d4b9d3882e0d5daa339c089f') in 0.0759458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_FL45.FBX
  artifactKey: Guid(8a726a8d26d546343ba5d03983152b49) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_FL45.FBX using Guid(8a726a8d26d546343ba5d03983152b49) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '319c955a25a0fca6d134fca6bb98259b') in 0.0681553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle.FBX
  artifactKey: Guid(3a321c0c0b6e2c74587fa2a3bae1a5b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Idle.FBX using Guid(3a321c0c0b6e2c74587fa2a3bae1a5b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5437ad9ee36ed494ffef6ab5d7c6f00') in 0.0659954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle.FBX
  artifactKey: Guid(eb8cc302bb3204846bbca5f3f3210449) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle.FBX using Guid(eb8cc302bb3204846bbca5f3f3210449) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd720d933c8281723d52f1006bd7b9989') in 0.0684203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_FR45_Root.FBX
  artifactKey: Guid(3cc3b541d8aa1f94faf8031f3ed82bed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_FR45_Root.FBX using Guid(3cc3b541d8aa1f94faf8031f3ed82bed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97912f2f4f2177b6b7279808e642dec8') in 0.0608479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90.FBX
  artifactKey: Guid(dd1802c7bc061054d9364020d0ffee22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R90.FBX using Guid(dd1802c7bc061054d9364020d0ffee22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62e8ba0812bffe958aed81bf888106c2') in 0.0576537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Jog_B.FBX
  artifactKey: Guid(efb3f694d25dcb7479e12a693248b262) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_B_to_Jog_B.FBX using Guid(efb3f694d25dcb7479e12a693248b262) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '974275d5ed0b388715a61a47f577533f') in 0.070594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_vol2.FBX
  artifactKey: Guid(4909d82bfee552544bbb0969e2bc1451) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_vol2.FBX using Guid(4909d82bfee552544bbb0969e2bc1451) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b48eb638025d7f1b68b0c4e2e687e1a') in 0.0642834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FL45_Root.FBX
  artifactKey: Guid(c31731d2834cc0048996465142db39f7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FL45_Root.FBX using Guid(c31731d2834cc0048996465142db39f7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55df8a0fc254d33432123a41993429f2') in 0.0611521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L45.FBX
  artifactKey: Guid(4d0fe52f58c7be742aad2766032caab3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L45.FBX using Guid(4d0fe52f58c7be742aad2766032caab3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ad458b096be8eae514ba295bce2cf8d') in 0.0609087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front.FBX
  artifactKey: Guid(5e04ec40bbc3ac84796a85672bfea74b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front.FBX using Guid(5e04ec40bbc3ac84796a85672bfea74b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24ad11e7d06a0a557896094d898147ed') in 0.0815386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_L90.FBX
  artifactKey: Guid(c06c7e147db38e84eb4699bb8a9e8d3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_L90.FBX using Guid(c06c7e147db38e84eb4699bb8a9e8d3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6500f299c2a2a9ac66ff09ecbad0f8f') in 0.0650928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90_Root.FBX
  artifactKey: Guid(1f0d6330fa31ef04eaef586e6b3973f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90_Root.FBX using Guid(1f0d6330fa31ef04eaef586e6b3973f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e80942bf2ec8840672965a51afe24f6') in 0.0672131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_R90_Root.FBX
  artifactKey: Guid(a2fe49881c978b84c8eda3cff05bc21b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_R90_Root.FBX using Guid(a2fe49881c978b84c8eda3cff05bc21b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7060fafad9b4bcdff3b62cfa3fb6f1a9') in 0.0798689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_03.fbx
  artifactKey: Guid(abc00000000014035766746179463656) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_03.fbx using Guid(abc00000000014035766746179463656) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1390dd86fe0e479b6e1244ce05fe27a') in 0.0835591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Fish_Splash_02.mat
  artifactKey: Guid(20492c95d4599c045b9e73ead39d559a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Fish_Splash_02.mat using Guid(20492c95d4599c045b9e73ead39d559a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6691e45c9b4b4f18242e33d3a25c3dc') in 0.0599385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_01.mat
  artifactKey: Guid(abc00000000004818322303702504829) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_01.mat using Guid(abc00000000004818322303702504829) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2303cdff220d900a56ffb84923c67602') in 0.0561233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobbles_4M.fbx
  artifactKey: Guid(abc00000000012125003666775119289) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobbles_4M.fbx using Guid(abc00000000012125003666775119289) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c9f3946a7e7da1ff9e9853b042dc38f') in 0.1150359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_OuterCorner_01.fbx
  artifactKey: Guid(abc00000000006249826499881208713) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_OuterCorner_01.fbx using Guid(abc00000000006249826499881208713) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7a1dca9775a94fb2f3cf3b4b2e4a177') in 0.0758873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Jar_02.fbx
  artifactKey: Guid(abc00000000014936433842269337454) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Jar_02.fbx using Guid(abc00000000014936433842269337454) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0920f638717275828972b30d4b753b3f') in 0.0792274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Torch_A.fbx
  artifactKey: Guid(abc00000000008526831063014590324) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Torch_A.fbx using Guid(abc00000000008526831063014590324) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8e8199b6769ddea8efea2dd4994cfe6') in 0.0683234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ChainLink_01.fbx
  artifactKey: Guid(abc00000000001794651582929028619) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_ChainLink_01.fbx using Guid(abc00000000001794651582929028619) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab948c3654977e56405eb19ce9f58957') in 0.0556361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneFence_05.mat
  artifactKey: Guid(d031d5d402e593d48a9f7a2c4fbc5cff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Materials/MI_StoneFence_05.mat using Guid(d031d5d402e593d48a9f7a2c4fbc5cff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81b94dc44db235215170866310dccd40') in 0.0573251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_A.fbx
  artifactKey: Guid(abc00000000017006358635735633138) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_A.fbx using Guid(abc00000000017006358635735633138) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b2c3b54f2e6c5b16be0c29f9adad8bf0') in 0.0657376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/HumanF@Sprint01_Right.fbx
  artifactKey: Guid(31b250fe2fe55ed4c8daa545b042f886) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/HumanF@Sprint01_Right.fbx using Guid(31b250fe2fe55ed4c8daa545b042f886) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '926ce6b51f1a68e6a31445f513f25dae') in 0.0502203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_04.fbx
  artifactKey: Guid(abc00000000013652502380754856410) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_04.fbx using Guid(abc00000000013652502380754856410) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8086cee1662ebed42e77831c2eecb890') in 0.0641637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_BackwardLeft.fbx
  artifactKey: Guid(1f15e0650d22c9d48befae3bb44bc43d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_BackwardLeft.fbx using Guid(1f15e0650d22c9d48befae3bb44bc43d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0b30d0d4b37dab7f075bb7d9ca9de4b') in 0.0628516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/<EMAIL>
  artifactKey: Guid(2035f8d45874b4e47a2c15ea2fa026fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/<EMAIL> using Guid(2035f8d45874b4e47a2c15ea2fa026fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00f31910d715dbea03166dae92ed2776') in 0.0722512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_ForwardLeft.fbx
  artifactKey: Guid(77673eecac9076048b4b09f89e0c3d02) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_ForwardLeft.fbx using Guid(77673eecac9076048b4b09f89e0c3d02) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6165134163baee552adb1582ae21dbd') in 0.0544472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Right.fbx
  artifactKey: Guid(846fff649819bcf49b933d00ef6f703f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Right.fbx using Guid(846fff649819bcf49b933d00ef6f703f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac0578bdf6da453fa0ed111c064f05d4') in 0.0595149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_2.wav
  artifactKey: Guid(0993a2bcc914f9c4db6d7f12b78d9c85) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_2.wav using Guid(0993a2bcc914f9c4db6d7f12b78d9c85) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8dbf8793125b929e3b8ad3c4e4987cb0') in 0.1392869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/MI_Slabs_03.mat
  artifactKey: Guid(b1f99e7c122d67f4b8296cba8351cb8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/MI_Slabs_03.mat using Guid(b1f99e7c122d67f4b8296cba8351cb8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c3103dd9bc08bf463518d721c6c8aac') in 0.0617569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Backward.fbx
  artifactKey: Guid(f1f1135ca9cfa8c47bf81718bb0d6873) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Backward.fbx using Guid(f1f1135ca9cfa8c47bf81718bb0d6873) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ecc15ab0c8e2b71873a1672da202818') in 0.059981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Watery_Generic_1.wav
  artifactKey: Guid(33226d151f771c94d87ab7878a227aae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Watery_Generic_1.wav using Guid(33226d151f771c94d87ab7878a227aae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47fd16af158a0423c2da5cfb7d7f7a4a') in 0.1546637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Book_B.fbx
  artifactKey: Guid(abc00000000003700605946542412155) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Book_B.fbx using Guid(abc00000000003700605946542412155) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fc4bf91d02b6b525e1cc0e467094a27') in 0.0674514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_CobblePath_01.mat
  artifactKey: Guid(f2b6d801e3a51ba41949fac55996a486) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_CobblePath_01.mat using Guid(f2b6d801e3a51ba41949fac55996a486) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d1396c2abedcabc8da1df0b9f9b6a55') in 0.0700118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Connect_01.fbx
  artifactKey: Guid(abc00000000003626406365941670329) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/SM_Bridge_Connect_01.fbx using Guid(abc00000000003626406365941670329) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f91d6292b9c0e98ecb6ccfe7715f888') in 0.0834799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_StoneFence_05.mat
  artifactKey: Guid(ad0d451be9544bb43b27495c2ba58d8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_StoneFence_05.mat using Guid(ad0d451be9544bb43b27495c2ba58d8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec0bbd5ec628dc7db7e84454c369d4ec') in 0.07381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_InteriorCups.mat
  artifactKey: Guid(011b77911b2cf9048a326c3c4aeb904c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_InteriorCups.mat using Guid(011b77911b2cf9048a326c3c4aeb904c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9aa4eedeb3f52d38f10c631aed40da79') in 0.0760596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Cabinet_01.fbx
  artifactKey: Guid(abc00000000000815193679455690177) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Cabinet_01.fbx using Guid(abc00000000000815193679455690177) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb7143bec38486ac498252d1dfab4955') in 0.0686859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_1.wav
  artifactKey: Guid(ba3dcf7fcb9fe3744811442a6b2b99d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Earth_Generic_1.wav using Guid(ba3dcf7fcb9fe3744811442a6b2b99d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b1d8805400ce712a3eca1797cd4cee0') in 0.172976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Cabinet_02.fbx
  artifactKey: Guid(abc00000000009353445624446197808) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Cabinet_02.fbx using Guid(abc00000000009353445624446197808) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7b9c1f6316b479d6112fcfefe618fc5') in 0.1032843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_TavernSign.mat
  artifactKey: Guid(38d2fdd6463b59d4db981cf6ca90676c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_TavernSign.mat using Guid(38d2fdd6463b59d4db981cf6ca90676c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a0a09a6cc2fc3f6a43dd815454a0b98') in 0.0644164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_BackwardRight [RM].fbx
  artifactKey: Guid(5399e89e69e4ef2468a17b7975d02bd9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_BackwardRight [RM].fbx using Guid(5399e89e69e4ef2468a17b7975d02bd9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f30d32beae78488ca5e00d58f0757054') in 0.0817522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_BackwardLeft [RM].fbx
  artifactKey: Guid(ab147e0813d61eb439be75037830be27) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_BackwardLeft [RM].fbx using Guid(ab147e0813d61eb439be75037830be27) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13adbf693c60a04a4a4c44a19c769f1e') in 0.0761969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_VarnishedWood_01.mat
  artifactKey: Guid(18647ab57289dbf4b9bfef01b168f066) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_VarnishedWood_01.mat using Guid(18647ab57289dbf4b9bfef01b168f066) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5e337f5f73352bce04d65fdabe3ba2c') in 0.0834062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_Forward [RM].fbx
  artifactKey: Guid(5a0e32c255e96fa4294a09f20c564818) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/RootMotion/HumanM@Sprint01_Forward [RM].fbx using Guid(5a0e32c255e96fa4294a09f20c564818) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a58381b9eb8771b7cd0b9a478072d15') in 0.0907019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_04.fbx
  artifactKey: Guid(abc00000000006835680540162579464) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_04.fbx using Guid(abc00000000006835680540162579464) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71e096687fe915889e6e09bcceb6b3a8') in 0.0624751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_02.fbx
  artifactKey: Guid(abc00000000001688851420104508474) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_02.fbx using Guid(abc00000000001688851420104508474) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b6e456381545fa41cc6319708d2e365') in 0.0933773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/SM_Arch_Walkway_Corner_B.fbx
  artifactKey: Guid(abc00000000002275564871569384191) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/SM_Arch_Walkway_Corner_B.fbx using Guid(abc00000000002275564871569384191) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '058bd9ed1f9602138dd1204ab88700f8') in 0.0704691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Wall_A_01.fbx
  artifactKey: Guid(abc00000000015256025665241579623) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Wall_A_01.fbx using Guid(abc00000000015256025665241579623) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8636586626145ad7f14f37a8916c89a') in 0.0934009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_A_02.fbx
  artifactKey: Guid(abc00000000004745136802274189161) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Crate_A_02.fbx using Guid(abc00000000004745136802274189161) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25d13dd70d011d7dc5c6a3545fb2ba01') in 0.0668444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/No Name.mat
  artifactKey: Guid(e3b856a9764a2e94ba63de971cb22b5e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/No Name.mat using Guid(e3b856a9764a2e94ba63de971cb22b5e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dda79fa3386178edb769e99c9342adbf') in 0.0566883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_05.mat
  artifactKey: Guid(23c87285978227247bd8fd613d791393) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_BrickKit_05.mat using Guid(23c87285978227247bd8fd613d791393) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ac137f275b2f8df7a89f465e4951785') in 0.0599571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_ForwardRight [RM].fbx
  artifactKey: Guid(82bf8a2b215c9d7408d4dcf081f8f622) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_ForwardRight [RM].fbx using Guid(82bf8a2b215c9d7408d4dcf081f8f622) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b81199f7d32c8ba70b23b948b3c7e84b') in 0.079821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/No Name.mat
  artifactKey: Guid(2f66197aac3bfc64db567c9e52d6d04f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/No Name.mat using Guid(2f66197aac3bfc64db567c9e52d6d04f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2edd854999ae33d101e84660fce0face') in 0.0562658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/MI_Cloth_Plain.mat
  artifactKey: Guid(1cd69c391c065d641ba89e4643a0113c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/MI_Cloth_Plain.mat using Guid(1cd69c391c065d641ba89e4643a0113c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '463d9def21289260802ddda0e142e2af') in 0.0632286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/Materials/No Name.mat
  artifactKey: Guid(8b8cb6beaf8e26047be2142f0eb3468e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/Materials/No Name.mat using Guid(8b8cb6beaf8e26047be2142f0eb3468e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93e44b73f806b4e7942aecd57bb4cbbe') in 0.0525533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_ForwardRight [RM].fbx
  artifactKey: Guid(abf537a4b09f6af4ebaa70cbfbbb3353) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/RootMotion/HumanF@Walk01_ForwardRight [RM].fbx using Guid(abf537a4b09f6af4ebaa70cbfbbb3353) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fec0f7fe88bc185419f9035e64eb0257') in 0.0846752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000484 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_C.fbx
  artifactKey: Guid(abc00000000001241685861226714536) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/SM_Ivy_C.fbx using Guid(abc00000000001241685861226714536) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e626903aca7967ead532b7c8d6af268') in 0.0925262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_03.fbx
  artifactKey: Guid(abc00000000001018407102182824668) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_03.fbx using Guid(abc00000000001018407102182824668) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '475ba3871eaa9280b62dd5920fb8a3a7') in 0.0780501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Door_01.fbx
  artifactKey: Guid(abc00000000009522030005696062946) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Door_01.fbx using Guid(abc00000000009522030005696062946) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '314205876fbcca9b648edb847e6325e9') in 0.1036345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Door_02.fbx
  artifactKey: Guid(abc00000000014733086947414398668) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Door_02.fbx using Guid(abc00000000014733086947414398668) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a438ca942bcff1ca8c675ec41bcaea8a') in 0.0657404 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000148 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/SM_Fence_02a.fbx
  artifactKey: Guid(abc00000000003664044893035161282) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/SM_Fence_02a.fbx using Guid(abc00000000003664044893035161282) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f169b9282954c6c52a33fe13133c0cfc') in 0.0876504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_03.fbx
  artifactKey: Guid(abc00000000013042370953891657276) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Top_03.fbx using Guid(abc00000000013042370953891657276) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '749ae07c55e857e8fc30a5c3e3906ad8') in 0.0613846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_StoneFence_04.mat
  artifactKey: Guid(46df78c9c69ab504282410dac0121dcf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/Materials/MI_StoneFence_04.mat using Guid(46df78c9c69ab504282410dac0121dcf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a9577ab1e8bfc30aeec20ce9467b814') in 0.0687925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_08.fbx
  artifactKey: Guid(abc00000000002081897358562289926) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_08.fbx using Guid(abc00000000002081897358562289926) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cba423bd379cecb0dab3daf9d6819126') in 0.1346031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_02.mat
  artifactKey: Guid(38f22707379bcc549aa86195b33a56ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickKit_02.mat using Guid(38f22707379bcc549aa86195b33a56ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cdb77093bd5074a16ec9504921bb53ba') in 0.1225924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_03.mat
  artifactKey: Guid(b2a3f16756257804c830e43b560cdb90) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_03.mat using Guid(b2a3f16756257804c830e43b560cdb90) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cbacffe144a1e62f1fd09d74b92a2d19') in 0.0864296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_07.mat
  artifactKey: Guid(6e3ef314cd3f792429abf1c7aae3f2e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/Materials/MI_BrickKit_07.mat using Guid(6e3ef314cd3f792429abf1c7aae3f2e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efee8e7807db2470ff12caf5d4a6b513') in 0.0809285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_VarnishedWood_01.mat
  artifactKey: Guid(fa762bba9697b4941a262786ed454ca8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/Materials/MI_VarnishedWood_01.mat using Guid(fa762bba9697b4941a262786ed454ca8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a64c699cd6ff4a69b538042badcb67ac') in 0.0708937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Materials/M_WildGrass_Atlas_01.mat
  artifactKey: Guid(abc00000000013474369230937995009) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Materials/M_WildGrass_Atlas_01.mat using Guid(abc00000000013474369230937995009) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b10937ee78f11232acc783862673fb8e') in 0.0307177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/No Name.mat
  artifactKey: Guid(429d209c251afc64eb4a46a17a7578cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/Materials/No Name.mat using Guid(429d209c251afc64eb4a46a17a7578cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '207c4fc090d84d62c0e374d5bb447f9b') in 0.0545179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_S_02.fbx
  artifactKey: Guid(abc00000000009562540249038495423) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_S_02.fbx using Guid(abc00000000009562540249038495423) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '661bfa8f64b92a58b106e57f97c7dea2') in 0.0600599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_StoneFence_05.mat
  artifactKey: Guid(af8f688d282a4c64a9e58a6e3075d659) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_StoneFence_05.mat using Guid(af8f688d282a4c64a9e58a6e3075d659) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f3d87ba178f8bb8b5ed4df73f5243b6') in 0.0709448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_04.fbx
  artifactKey: Guid(abc00000000014462335631113571616) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_04.fbx using Guid(abc00000000014462335631113571616) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69b6cb54583223dda4d8369971fdc56a') in 0.0935886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_Slabs.mat
  artifactKey: Guid(57400618c3c56554b9d7d07fb137ade3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_Slabs.mat using Guid(57400618c3c56554b9d7d07fb137ade3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63410b5d21acea8452754ca94af6ab6e') in 0.0649088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000119 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_01.fbx
  artifactKey: Guid(abc00000000006875714146476830343) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_01.fbx using Guid(abc00000000006875714146476830343) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb2d467c61822c571bc9ec8efeec38b2') in 0.0902087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_05.mat
  artifactKey: Guid(3727b5731d897d645982fc7992c33b11) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/MI_BrickKit_05.mat using Guid(3727b5731d897d645982fc7992c33b11) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '610c7e31fd595cfc177dc0169a18e2fd') in 0.0680172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_plaster_01.mat
  artifactKey: Guid(df7396abd8b278f4eb195ced145a2874) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_plaster_01.mat using Guid(df7396abd8b278f4eb195ced145a2874) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c487f4ddadb3868d63b68b2b0fbe49e') in 0.0589933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleFat_02.fbx
  artifactKey: Guid(abc00000000010402276059598391524) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleFat_02.fbx using Guid(abc00000000010402276059598391524) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f20ccfb2ed6ee8664dac172dc5a68bc') in 0.0799881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_03.fbx
  artifactKey: Guid(abc00000000011198170786279518672) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_03.fbx using Guid(abc00000000011198170786279518672) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68dd6bedc1b30dfa3b1aba5f1a5386da') in 0.0723002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_A_Root.FBX
  artifactKey: Guid(3ac390cc6271df64f9b2ea3ba4039ccb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_A_Root.FBX using Guid(3ac390cc6271df64f9b2ea3ba4039ccb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6771958db52c9d2344e630b33474119a') in 0.0729676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Middle_Arch.fbx
  artifactKey: Guid(abc00000000001992189474303827802) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Middle_Arch.fbx using Guid(abc00000000001992189474303827802) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efd9250c1b39480bed68340b48c29f32') in 0.0850381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R45_Root.FBX
  artifactKey: Guid(f62381c450492044cb59a10f015a9cb0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R45_Root.FBX using Guid(f62381c450492044cb59a10f015a9cb0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ece40b4a75a06fce34bcc5f19c8fb6d3') in 0.0546711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Right.controller
  artifactKey: Guid(bfc8f7217a9c59e4498d073095ecc661) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Right.controller using Guid(bfc8f7217a9c59e4498d073095ecc661) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd706d006e3f56b8e9ac1b3ae12448de7') in 0.0392556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Run_A_Root.FBX
  artifactKey: Guid(254d38837d0e729479f775b00217dd57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Jog_B_to_Run_A_Root.FBX using Guid(254d38837d0e729479f775b00217dd57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '468db1085afde4f52d3b2f742c959e68') in 0.0739682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SM_TavernSign_01_BC.PNG
  artifactKey: Guid(371adc8bfd10b0743b358037c516a435) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SM_TavernSign_01_BC.PNG using Guid(371adc8bfd10b0743b358037c516a435) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3be466989800b71385da3241af118b27') in 0.0514099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_01.prefab
  artifactKey: Guid(abc00000000005861316417145525211) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_01.prefab using Guid(abc00000000005861316417145525211) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '542df6564f27f503b9aa3313e2b1bf41') in 0.0657223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bench_01.fbx
  artifactKey: Guid(abc00000000003537958322319831003) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bench_01.fbx using Guid(abc00000000003537958322319831003) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17c854c21c9d0d17b89f3c76496c2cb4') in 0.0904448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_CargoCrate_01.fbx
  artifactKey: Guid(abc00000000016402393084393872382) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_CargoCrate_01.fbx using Guid(abc00000000016402393084393872382) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6290c90ff42d9199c73544b68f79ffe4') in 0.0853769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_C.FBX
  artifactKey: Guid(8775895829e01044ea01c7b2cac33610) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_C.FBX using Guid(8775895829e01044ea01c7b2cac33610) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d801979529ce476db228c16c95e5d2e') in 0.0812602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/M_katana_Blade@UpperAttack_ZeroHeight_Z0.FBX
  artifactKey: Guid(0fddbe6463b4d724183dddb2552fd597) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/5__Upper_Attack/M_katana_Blade@UpperAttack_ZeroHeight_Z0.FBX using Guid(0fddbe6463b4d724183dddb2552fd597) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a07d1f3b8fb926410afa19f329fa9db3') in 0.0752294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_01.mat
  artifactKey: Guid(03071d6c2903167498fc95aa52b191a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_01.mat using Guid(03071d6c2903167498fc95aa52b191a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '364e3116964946cbe057a42bd329bd01') in 0.2916739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0