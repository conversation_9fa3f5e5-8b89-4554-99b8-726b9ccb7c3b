Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:04:02Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker1.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [37896]  Target information:

Player connection [37896]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2934157221 [EditorId] 2934157221 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [37896] Host joined multi-casting on [***********:54997]...
Player connection [37896] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 10.00 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 42.49 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56644
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002732 seconds.
- Loaded All Assemblies, in  0.692 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.608 seconds
Domain Reload Profiling: 1299ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (298ms)
		LoadAssemblies (210ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (291ms)
				TypeCache.ScanAssembly (267ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (543ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (43ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (271ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.556 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.11 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.764 seconds
Domain Reload Profiling: 3317ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1043ms)
		LoadAssemblies (615ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (605ms)
			TypeCache.Refresh (431ms)
				TypeCache.ScanAssembly (400ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (1765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (275ms)
			ProcessInitializeOnLoadAttributes (976ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 5.07 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.0 MB). Loaded Objects now: 8334.
Memory consumption went from 212.2 MB to 203.2 MB.
Total: 21.606200 ms (FindLiveObjects: 1.148100 ms CreateObjectMapping: 1.357800 ms MarkObjects: 12.462600 ms  DeleteObjects: 6.635800 ms)

========================================================================
Received Import Request.
  Time since last request: 1811858.793612 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Speed.prefab
  artifactKey: Guid(e263a650d9724a04fb16a49a08c8db3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Speed.prefab using Guid(e263a650d9724a04fb16a49a08c8db3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fef411fd0f878f8cc13d4f9fe4301e9e') in 0.894848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab
  artifactKey: Guid(027f7676d85eaff4db64945788d561c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Jump.prefab using Guid(027f7676d85eaff4db64945788d561c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b7c1af1ecb1ad8d1d3fb67ad4d4766d') in 0.0251107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 36.722135 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_Big_Sword.FBX
  artifactKey: Guid(0466dd790c0cfd34db4afe171eadd2a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_Big_Sword.FBX using Guid(0466dd790c0cfd34db4afe171eadd2a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc4455642259eca05d0430ffc1556bbe') in 0.1289673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_katana_Blade.FBX
  artifactKey: Guid(bf3739b4d53347c4ab88bad4e567aafa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Modeling_Weapon_katana_Blade.FBX using Guid(bf3739b4d53347c4ab88bad4e567aafa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '52d2ec7164958295d62e98dc95979cff') in 0.0758822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_no_weapon.FBX
  artifactKey: Guid(d3ffbb53d382790469e6c8f926efe5f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Modeling/Unity_Grruzam_BaseModeling_no_weapon.FBX using Guid(d3ffbb53d382790469e6c8f926efe5f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c51258ca41790ae8ac4c7defc200bef0') in 0.1043707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 148

========================================================================
Received Import Request.
  Time since last request: 43.022405 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab
  artifactKey: Guid(a20a162ac11a35643bed0f3823718abb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_katana_Blade.prefab using Guid(a20a162ac11a35643bed0f3823718abb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '10cfd6b2b1ba2f323f642971914b9017') in 0.0501255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab
  artifactKey: Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_GreatSword.prefab using Guid(b70b99e5a8a474b438e08193e49ee63d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b5d6940f1e6112ca66f53893c0e499d6') in 6.6488281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1182

========================================================================
Received Import Request.
  Time since last request: 1.484441 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A_Root.FBX
  artifactKey: Guid(2b39de01d3a13ba45a39361da667673e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A_Root.FBX using Guid(2b39de01d3a13ba45a39361da667673e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c07f0d8e7ba0424e4d050fa0ccb1a551') in 0.0310976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 141

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B_Root.FBX
  artifactKey: Guid(a4a6f1a96f11c4643bd3bd011f4fb2e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_B_Root.FBX using Guid(a4a6f1a96f11c4643bd3bd011f4fb2e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6456199b1263eb443b2bb49b36d43106') in 0.0355994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 141

========================================================================
Received Import Request.
  Time since last request: 24.687173 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45_Root.FBX
  artifactKey: Guid(77a3d89d1deb05d43a6c6bbe5e083357) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_L45_Root.FBX using Guid(77a3d89d1deb05d43a6c6bbe5e083357) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b59f1dd96f9cbbe385c912cd8d20c755') in 0.0100056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 143

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90.FBX
  artifactKey: Guid(eee699e1f975cd944a5b462a2a16cc0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_L90.FBX using Guid(eee699e1f975cd944a5b462a2a16cc0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a6fb89c3f11c8a7263c52ba74dfc543') in 0.0128965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front.FBX
  artifactKey: Guid(9b47bd9e3b792054d9f1d8320fe0403b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front.FBX using Guid(9b47bd9e3b792054d9f1d8320fe0403b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87d2c25a926089c53b5b084184f4d2d8') in 0.016766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45.FBX
  artifactKey: Guid(75c1bc47f938fec4aa4acacc13ef050f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45.FBX using Guid(75c1bc47f938fec4aa4acacc13ef050f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff4e37807b9ab403c58121c18ddcc820') in 0.009962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90.FBX
  artifactKey: Guid(301be80f247a2894eb94606aabf7442b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R90.FBX using Guid(301be80f247a2894eb94606aabf7442b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9806af3553b3e1d6cd44a43bf3dc62d6') in 0.0379122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45.FBX
  artifactKey: Guid(a91ca0522c3c1f84795fe844cee7db6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Back_R45.FBX using Guid(a91ca0522c3c1f84795fe844cee7db6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df18e6a155c8bd0b8e8f9a8c9f45e286') in 0.0079289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_Root.FBX
  artifactKey: Guid(80c5992fa46974f4a8108ece87f48747) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_Root.FBX using Guid(80c5992fa46974f4a8108ece87f48747) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a7b4c70dd0334f82981e5f690b277d9') in 0.0095501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 62.852438 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_01_4x4.mat
  artifactKey: Guid(f93fbb5d51e6cbd498cbe491558a7a22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_01_4x4.mat using Guid(f93fbb5d51e6cbd498cbe491558a7a22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb76871d261e1aa3f8e3e7ddae8021b1') in 0.1090399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_01_4x4.mat
  artifactKey: Guid(a69f527a05f221c4294f81bb7d4800be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_01_4x4.mat using Guid(a69f527a05f221c4294f81bb7d4800be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7bf56ca514567cfc8b087a395913736e') in 0.0345584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_02_4x4.mat
  artifactKey: Guid(0c5a3bf967ce85f4d90cf117ab242978) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamSmoke_02_4x4.mat using Guid(0c5a3bf967ce85f4d90cf117ab242978) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78320beb0bc2bcab14922c7b8256b9c1') in 0.0302173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_6x4.mat
  artifactKey: Guid(2c5fc0a3f8ec5204c9a2eb72f7dabc79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_6x4.mat using Guid(2c5fc0a3f8ec5204c9a2eb72f7dabc79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e17bf6cb87b0d7e87cceab4f8826580e') in 0.0305341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DenseNoise_01.png
  artifactKey: Guid(eef497c105ae18a49b7fe1b4558ddb8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DenseNoise_01.png using Guid(eef497c105ae18a49b7fe1b4558ddb8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50e2d16427a7b5d196e896f813358ab2') in 0.0413049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_OffsetSphereMask_01 1.png
  artifactKey: Guid(8bea54f0b4b1b7146bcc54069363062d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_OffsetSphereMask_01 1.png using Guid(8bea54f0b4b1b7146bcc54069363062d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '060c4240d16bd56f458f75ed8e922732') in 0.0213165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_03.png
  artifactKey: Guid(18bfbed286f7e774ba9c1a67bea95026) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_03.png using Guid(18bfbed286f7e774ba9c1a67bea95026) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9726f4f58ebadcdd8afa3802d8aecaf6') in 0.0266658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_01.mat
  artifactKey: Guid(6b5bf5c4f0b4f4b4684801a3242fbffa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_GroundCaustic_01.mat using Guid(6b5bf5c4f0b4f4b4684801a3242fbffa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b0ac259264f09c5ad7c29ed265abe3b') in 0.0512378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_Sharp.png
  artifactKey: Guid(f841cfca36ed44746962b212c78a879b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_Sharp.png using Guid(f841cfca36ed44746962b212c78a879b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79be30ee2b2c341a7a0b13678ef358f1') in 0.0300879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.498644 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Spikes_01.mat
  artifactKey: Guid(31742df79150cea48af8897974bc0557) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactShapes_Spikes_01.mat using Guid(31742df79150cea48af8897974bc0557) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bb7f51b8ebaee72cfc84f6c8eba2e16') in 0.0402213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_02_4x4.mat
  artifactKey: Guid(b1e1b714e02f37e48953010abdcd79f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ImpactSplash_02_4x4.mat using Guid(b1e1b714e02f37e48953010abdcd79f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bedb2dfc79515edfeb8306df88ebc2c4') in 0.0726483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01.mat
  artifactKey: Guid(85d934873fcc2b34caace0131632eb71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01.mat using Guid(85d934873fcc2b34caace0131632eb71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd26098c0f185be665387081de9c06c7') in 0.3984008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_02_2x5.mat
  artifactKey: Guid(63482ceb1519c0b4eae7c6236fddb696) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_02_2x5.mat using Guid(63482ceb1519c0b4eae7c6236fddb696) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48be71da0d19628201fe60104ad43deb') in 0.0471606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_04_2x5.mat
  artifactKey: Guid(ec4732ef0ad692d49904a868c101fdbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_04_2x5.mat using Guid(ec4732ef0ad692d49904a868c101fdbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29bb3ed91e0b8b297b5d7a4cf746f067') in 0.0351663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_05_2x5.mat
  artifactKey: Guid(52cc90312a9eacc4dbdcae00c6bb7007) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_05_2x5.mat using Guid(52cc90312a9eacc4dbdcae00c6bb7007) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25e381b4ad16d383ae0729ca7d0c3ad4') in 0.3560759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 15.555684 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat
  artifactKey: Guid(abc00000000005011678449934084366) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat using Guid(abc00000000005011678449934084366) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc8fe4e071d78ca5a95115b7f5fa3b98') in 0.1042322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickWall.mat
  artifactKey: Guid(abc00000000014630764845243636829) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickWall.mat using Guid(abc00000000014630764845243636829) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e78fdbdda1ff8ec660e58c215ebb80e') in 0.0627804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_CobblePath_01.mat
  artifactKey: Guid(abc00000000015462360074013683848) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_CobblePath_01.mat using Guid(abc00000000015462360074013683848) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0858f7327766a7b1f53a2488b0f9d858') in 0.07626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Candles.mat
  artifactKey: Guid(abc00000000006039034396119061842) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Candles.mat using Guid(abc00000000006039034396119061842) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43ebf2221478e03700356a389a149e8f') in 0.0837377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Floor.mat
  artifactKey: Guid(abc00000000001525448253452710529) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Floor.mat using Guid(abc00000000001525448253452710529) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3aad844dfc58dc8d3deb756bd2154a3') in 0.0889979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Blue.mat
  artifactKey: Guid(abc00000000015303638101263416809) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Blue.mat using Guid(abc00000000015303638101263416809) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff32e96a722e1ec1b193724eb79967f8') in 0.0183524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 3.732172 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_GrainSack.mat
  artifactKey: Guid(abc00000000008700112698404816241) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_GrainSack.mat using Guid(abc00000000008700112698404816241) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b5257d982cd1163020e7f8b5253b545') in 0.0636704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Lanterns.mat
  artifactKey: Guid(abc00000000012573495227337296161) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Lanterns.mat using Guid(abc00000000012573495227337296161) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '767e7054497eb394828ec3b26f1cb758') in 0.0776594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.061772 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat
  artifactKey: Guid(abc00000000011170058242184240894) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat using Guid(abc00000000011170058242184240894) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71ec5dc5a086d955e3d9bc5bcc1973c6') in 0.1585412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles.mat
  artifactKey: Guid(abc00000000011734364806135559496) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles.mat using Guid(abc00000000011734364806135559496) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56ee50a59134afca9a336d8a9d690ba6') in 0.0920227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_02.mat
  artifactKey: Guid(abc00000000015246686425851332343) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rope_02.mat using Guid(abc00000000015246686425851332343) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '73d4fe5308e0b4b155bfcc8ae333adc1') in 0.0638728 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 1.581898 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_03.mat
  artifactKey: Guid(abc00000000000012559271505359403) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_03.mat using Guid(abc00000000000012559271505359403) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '347524509c4333c08b29db652cfe11dd') in 0.0424918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_07.mat
  artifactKey: Guid(abc00000000017923775067250159663) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_07.mat using Guid(abc00000000017923775067250159663) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a0b25754905c8da620610c4670d9e26') in 0.0447912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0