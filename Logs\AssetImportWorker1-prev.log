Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-07T11:32:00Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker1.log
-srvPort
51437
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13624]  Target information:

Player connection [13624]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 154575686 [EditorId] 154575686 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13624] Host joined multi-casting on [***********:54997]...
Player connection [13624] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
Default GameObject Tag: Player already registered
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.47 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.58 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56140
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003771 seconds.
- Loaded All Assemblies, in  0.652 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.608 seconds
Domain Reload Profiling: 1260ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (269ms)
		LoadAssemblies (193ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (265ms)
			TypeCache.Refresh (262ms)
				TypeCache.ScanAssembly (244ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (609ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (541ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (43ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.454 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.77 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.948 seconds
Domain Reload Profiling: 3402ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (979ms)
		LoadAssemblies (574ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (582ms)
			TypeCache.Refresh (442ms)
				TypeCache.ScanAssembly (406ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (1949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1565ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (268ms)
			ProcessInitializeOnLoadAttributes (1161ms)
			ProcessInitializeOnLoadMethodAttributes (120ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Default GameObject Tag: Player already registered
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 7.69 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 263 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8186 unused Assets / (11.3 MB). Loaded Objects now: 8884.
Memory consumption went from 215.0 MB to 203.7 MB.
Total: 23.809400 ms (FindLiveObjects: 1.720300 ms CreateObjectMapping: 1.893400 ms MarkObjects: 9.833700 ms  DeleteObjects: 10.359900 ms)

========================================================================
Received Import Request.
  Time since last request: 429554.892711 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/LightingData.asset
  artifactKey: Guid(51e8050f26ab504469d218c927558c5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/LightingData.asset using Guid(51e8050f26ab504469d218c927558c5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68492a5c44dd34185f632b2fbc4b73a3') in 0.0447588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/ReflectionProbe-2.exr
  artifactKey: Guid(7e3f1677bc1aead4fb8791998078dcec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/ReflectionProbe-2.exr using Guid(7e3f1677bc1aead4fb8791998078dcec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e53123ab059fab99c9b439f3592f3a6d') in 0.9484351 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/NavMesh.asset
  artifactKey: Guid(a25457b0641ee1b4e8e4cb3b34fb44c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/NavMesh.asset using Guid(a25457b0641ee1b4e8e4cb3b34fb44c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f1dd46daca0cd0a1739e81be90b1102') in 0.0026935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/ReflectionProbe-1.exr
  artifactKey: Guid(1b8a4de7469ad774c80dd6678d94e6b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Scenes/Example Scene/Lighting/ReflectionProbe-1.exr using Guid(1b8a4de7469ad774c80dd6678d94e6b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5dee56f7ccc447f7f0b9c7058ef15690') in 0.0363169 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.427 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.46 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.967 seconds
Domain Reload Profiling: 3399ms
	BeginReloadAssembly (416ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (867ms)
		LoadAssemblies (605ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (426ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (370ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (1968ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1593ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (270ms)
			ProcessInitializeOnLoadAttributes (1186ms)
			ProcessInitializeOnLoadMethodAttributes (117ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 5.31 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8182 unused Assets / (11.0 MB). Loaded Objects now: 8956.
Memory consumption went from 196.3 MB to 185.3 MB.
Total: 21.226700 ms (FindLiveObjects: 1.315300 ms CreateObjectMapping: 1.781500 ms MarkObjects: 8.421900 ms  DeleteObjects: 9.705700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 252.222972 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_100x100x100_Prefab.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_100x100x100_Prefab.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2a5cf3a0c816fbf9d42806fd9d54df0') in 4.588907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Editor/CIVIL-AI-SYSTEM.curvesNormalized
  artifactKey: Guid(acdcc32643684c8449366242e94a384c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Editor/CIVIL-AI-SYSTEM.curvesNormalized using Guid(acdcc32643684c8449366242e94a384c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6511b7cf3330182cc571eda63af5e90b') in 0.0031689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Readme.asset
  artifactKey: Guid(c0216bc4acd329447a53e74c37eb07cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Readme.asset using Guid(c0216bc4acd329447a53e74c37eb07cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66d683264ee414586eb1c63a003e3907') in 0.0038139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_350x250x200_Prefab.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_350x250x200_Prefab.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af35fc33197fa95d04af7c60d1dc491b') in 0.0408129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/CIVIL-AI-SYSTEM/RPG-AI-SYSTERM.asmdef
  artifactKey: Guid(f04311358f603fc42b3c0d267836147f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/RPG-AI-SYSTERM.asmdef using Guid(f04311358f603fc42b3c0d267836147f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1601aac0f6ee4c309e5d316adff4e031') in 0.00111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_350x250x300_Prefab.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Prefab/Enviroment/Box_350x250x300_Prefab.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdfa7ddce44c7c732b040241dcf646a2') in 0.0472914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.766512 seconds.
  path: Assets/CIVIL-AI-SYSTEM/Wiki.pdf
  artifactKey: Guid(f4cbeba2aaa1767499160cbd8f96d27e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CIVIL-AI-SYSTEM/Wiki.pdf using Guid(f4cbeba2aaa1767499160cbd8f96d27e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab37d415f7fae5b5faa754a3d8f9da9a') in 0.002076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0