%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9275d47cd9feca1438664249e57f6312, type: 3}
  m_Name: ModuleSettings
  m_EditorClassIdentifier: 
  workBehaviourRepository: '`212'
  needBehaviourRepository: Basic
  actionTypeCollectionRepository: V3
  characterPoolService: {fileID: 11400000, guid: c40a127a9b66b294ebedef5693fd006d, type: 2}
  startTime: 800
  startDay: 0
  defaultCharacter: {fileID: 2810924699631796756, guid: d2bc4081a69d01f42af017db648870bc, type: 3}
  defaultCharacterPoolData: {fileID: 11400000, guid: 99c92bba27d83a74e9db74261735c2b7, type: 2}
  itemNameCollection: {fileID: 11400000, guid: 38b46584fa2dbf84eaee8cc7faad25fa, type: 2}
  itemTypeCollection: {fileID: 11400000, guid: 7149b8f3edb649040907efc21986d3c0, type: 2}
  itemSizeCollection: {fileID: 11400000, guid: d68ddd5ce4214f343b61297c4cc7064b, type: 2}
  pointOfIntrestCollection: {fileID: 11400000, guid: 52eda01739bd9d14c83724f5e79711fe, type: 2}
