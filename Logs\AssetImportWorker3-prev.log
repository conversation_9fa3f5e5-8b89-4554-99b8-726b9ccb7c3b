Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:06:04Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker3.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [4540]  Target information:

Player connection [4540]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1608864641 [EditorId] 1608864641 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4540] Host joined multi-casting on [***********:54997]...
Player connection [4540] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.34 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.17 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56720
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002605 seconds.
- Loaded All Assemblies, in  0.609 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.496 seconds
Domain Reload Profiling: 1105ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (254ms)
		LoadAssemblies (197ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (249ms)
			TypeCache.Refresh (247ms)
				TypeCache.ScanAssembly (228ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (496ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (445ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.324 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.38 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.639 seconds
Domain Reload Profiling: 2962ms
	BeginReloadAssembly (309ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (873ms)
		LoadAssemblies (552ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (488ms)
			TypeCache.Refresh (341ms)
				TypeCache.ScanAssembly (318ms)
			BuildScriptInfoCaches (117ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1640ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (236ms)
			ProcessInitializeOnLoadAttributes (946ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 6.19 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.7 MB). Loaded Objects now: 8334.
Memory consumption went from 210.2 MB to 200.5 MB.
Total: 17.537300 ms (FindLiveObjects: 1.624800 ms CreateObjectMapping: 1.000100 ms MarkObjects: 7.303000 ms  DeleteObjects: 7.607600 ms)

========================================================================
Received Import Request.
  Time since last request: 1812037.028565 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_02_4x4.mat
  artifactKey: Guid(f5539723bd0f8b94196cc056a49f9ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamIndividual_02_4x4.mat using Guid(f5539723bd0f8b94196cc056a49f9ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b82b0e8ccf5c3d3c7f72c7398e5bfcb8') in 1.8824152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01_Back.mat
  artifactKey: Guid(9272da95f2a5ae24ba4922ed07f1fe4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01_Back.mat using Guid(9272da95f2a5ae24ba4922ed07f1fe4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c22f2030e9a4f9a94b3d5ab108a7230c') in 0.355441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialGroundCircle_01_4x4.mat
  artifactKey: Guid(200339bd447757548b59a52289ff368d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialGroundCircle_01_4x4.mat using Guid(200339bd447757548b59a52289ff368d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f839bb62e073816c2c7052f5f3f7d692') in 0.0449424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialThin_01.mat
  artifactKey: Guid(a3ef865fc864cba46a405a9c50ce6ec7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialThin_01.mat using Guid(a3ef865fc864cba46a405a9c50ce6ec7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9568744505ee0f61303486753acfe43a') in 0.0758141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_01.mat
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LateralSlide_01.mat using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5924817fcf16ce817b80a96f1d2e1b8') in 0.0438077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_01.mat
  artifactKey: Guid(b1275b10d61b0a74db4eddd2fede9885) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ProjectileHead_01.mat using Guid(b1275b10d61b0a74db4eddd2fede9885) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3bcc16196e7b054f1f2064e6e979917c') in 0.0416094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 15.518367 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Disp.mat
  artifactKey: Guid(abc00000000013869244513610678719) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Disp.mat using Guid(abc00000000013869244513610678719) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8f532d1d0af4de17c42890f3e54c311') in 0.0765028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Green.mat
  artifactKey: Guid(abc00000000010619635596411464873) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Green.mat using Guid(abc00000000010619635596411464873) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a1ec457c1b5612d9c257ad29db78c405') in 0.0491032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Plain.mat
  artifactKey: Guid(abc00000000007593937091663335591) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Plain.mat using Guid(abc00000000007593937091663335591) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '020ef669048884e81743251ec8382795') in 0.0182505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Blue.mat
  artifactKey: Guid(abc00000000001341616030546349958) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Blue.mat using Guid(abc00000000001341616030546349958) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50aec00cd1466cb0732ab6c0b2d3e504') in 0.0214955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Scale_Disp.mat
  artifactKey: Guid(abc00000000006585048051965788904) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Scale_Disp.mat using Guid(abc00000000006585048051965788904) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e3b7bab0b74397333a08f9263868ec2') in 0.0247194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Red.mat
  artifactKey: Guid(abc00000000015474946832986465021) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Red.mat using Guid(abc00000000015474946832986465021) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a0442cc83e20aa8092bc62a5cb37c07') in 0.021782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Red.mat
  artifactKey: Guid(abc00000000009153909646925066780) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Red.mat using Guid(abc00000000009153909646925066780) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20326066acfe4d29c9737f18bd9d99bd') in 0.0166545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fish_Splash_02.mat
  artifactKey: Guid(abc00000000003432095235311046902) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fish_Splash_02.mat using Guid(abc00000000003432095235311046902) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '140545fb0c8260b30bdf1f3268c2742f') in 0.0301205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.745567 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Individual_Bricks.mat
  artifactKey: Guid(abc00000000017760730636051215630) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Individual_Bricks.mat using Guid(abc00000000017760730636051215630) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aeff0a774fe472802e19fb2c5710c058') in 0.059505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Ivy_A.mat
  artifactKey: Guid(abc00000000006349057990624982018) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Ivy_A.mat using Guid(abc00000000006349057990624982018) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b3f7f64d31f30e36cf4a52509b8a2b4') in 0.0387189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000127 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_LighterPlanks.mat
  artifactKey: Guid(abc00000000000789036779032091370) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_LighterPlanks.mat using Guid(abc00000000000789036779032091370) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e0dc4fd78d79de1eb2074b18d9610fd') in 0.0476622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Overview.mat
  artifactKey: Guid(65ad572468f43374c8b24a5c7e76dc85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Overview.mat using Guid(65ad572468f43374c8b24a5c7e76dc85) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f8f39d9f82deff2454b9bd555b7786d') in 0.0122594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_02.mat
  artifactKey: Guid(abc00000000015761188811441295974) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_plaster_02.mat using Guid(abc00000000015761188811441295974) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf429f3e8c52d75705a9018021246a93') in 0.103763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rug_1.mat
  artifactKey: Guid(abc00000000013622290597065585114) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rug_1.mat using Guid(abc00000000013622290597065585114) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ff117cb43ee18b4bbf747f1d48aaf91') in 0.0259083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_03.mat
  artifactKey: Guid(abc00000000002917747318130589308) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_03.mat using Guid(abc00000000002917747318130589308) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '095751e30b5c102895764ddd8992aa4a') in 0.0290057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 1.647277 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_04.mat
  artifactKey: Guid(abc00000000015116042325195701470) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_04.mat using Guid(abc00000000015116042325195701470) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b196a69b3e41244384de4d62423c637') in 0.0426644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Candles.mat
  artifactKey: Guid(0c38fb87629211f4abd162b10be82344) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Candles.mat using Guid(0c38fb87629211f4abd162b10be82344) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b6d03b80fd5d8ac3bd7d8bb45288c9f') in 0.0399563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0