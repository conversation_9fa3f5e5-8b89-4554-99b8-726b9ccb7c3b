fileFormatVersion: 2
guid: 00cc6eff4eeef95478de9b75df6e10f8
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      74: 1827226128182048838
    second: Take 001
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: thigh_r
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: calf_r
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_r
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ball_r
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_01
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_02
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_03
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: clavicle_r
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm_r
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm_r
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_r
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck_01
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: clavicle_l
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm_l
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm_l
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_l
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thigh_l
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: calf_l
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_l
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ball_l
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_01_l
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_02_l
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_03_l
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_01_l
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_02_l
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_03_l
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_01_l
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_02_l
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_03_l
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_01_l
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_02_l
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_03_l
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_01_l
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_02_l
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_03_l
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_01_r
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_02_r
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_03_r
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_01_r
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_02_r
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_03_r
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_01_r
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_02_r
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: middle_03_r
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_01_r
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_02_r
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ring_03_r
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_01_r
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_02_r
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pinky_03_r
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: pelvis
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Unity_Grruzam_BaseModeling_no_weapon(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: root
      parentName: Unity_Grruzam_BaseModeling_no_weapon(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_foot_root
      parentName: root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_foot_l
      parentName: ik_foot_root
      position: {x: -0.09022366, y: 0.016631832, z: 0.12626895}
      rotation: {x: 0.020529445, y: 0.7122334, z: -0.011722631, w: 0.70154446}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_foot_r
      parentName: ik_foot_root
      position: {x: 0.090102546, y: 0.01663072, z: 0.12626736}
      rotation: {x: 0.70154434, y: 0.011725266, z: 0.7122334, w: -0.020532077}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_hand_root
      parentName: root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: pelvis
      parentName: root
      position: {x: -1.3536842e-30, y: 0.010561532, z: 0.967506}
      rotation: {x: 0.0012919955, y: 0.7071056, z: 0.0012919955, w: 0.7071057}
      scale: {x: 1, y: 1, z: 1}
    - name: spine_01
      parentName: pelvis
      position: {x: -0.108088985, y: -0.008514152, z: 0.000000012987311}
      rotation: {x: 0.0000000056862333, y: 0.00000005180524, z: 0.06238867, w: 0.99805194}
      scale: {x: 1, y: 1, z: 1}
    - name: spine_02
      parentName: spine_01
      position: {x: -0.18875358, y: 0.038011577, z: 0.0000000036100392}
      rotation: {x: 0.0000000029999947, y: 0.00000010394782, z: -0.122419804, w: 0.99247843}
      scale: {x: 1, y: 1, z: 1}
    - name: spine_03
      parentName: spine_02
      position: {x: -0.13407348, y: 0.00420475, z: -0.000000025649188}
      rotation: {x: 0.000000007365154, y: -0.00000007157666, z: -0.024252601, w: 0.9997059}
      scale: {x: 1, y: 1, z: 1}
    - name: clavicle_l
      parentName: spine_03
      position: {x: -0.11883682, y: -0.027320841, z: -0.037819833}
      rotation: {x: 0.053015847, y: -0.7033682, z: 0.05421793, w: 0.70676935}
      scale: {x: 1, y: 1, z: 1}
    - name: upperarm_l
      parentName: clavicle_l
      position: {x: -0.15788133, y: 0.0007407927, z: -0.000267334}
      rotation: {x: 0.1852683, y: -0.0057494766, z: 0.012149174, w: 0.98259604}
      scale: {x: 1, y: 1, z: 1}
    - name: lowerarm_l
      parentName: upperarm_l
      position: {x: -0.30357182, y: 0.00032905577, z: 0.0042515565}
      rotation: {x: -0.13273266, y: 0.004102417, z: -0.016808093, w: 0.9910009}
      scale: {x: 1, y: 1, z: 1}
    - name: hand_l
      parentName: lowerarm_l
      position: {x: -0.26981667, y: 0.00057590485, z: 0.00087905885}
      rotation: {x: -0.7189481, y: 0.023987692, z: 0.09366837, w: 0.68830556}
      scale: {x: 1, y: 1, z: 1}
    - name: index_01_l
      parentName: hand_l
      position: {x: -0.120681226, y: 0.017634887, z: -0.021093959}
      rotation: {x: 0.14582138, y: 0.029204467, z: -0.0969493, w: 0.9841159}
      scale: {x: 1, y: 1, z: 1}
    - name: index_02_l
      parentName: index_01_l
      position: {x: -0.041891173, y: -0.0002984619, z: 0.00021381378}
      rotation: {x: 0.010907523, y: 0.03379449, z: -0.023001421, w: 0.99910456}
      scale: {x: 1, y: 1, z: 1}
    - name: index_03_l
      parentName: index_02_l
      position: {x: -0.03347046, y: 0.000108032225, z: -0.00003238678}
      rotation: {x: 0.010611148, y: -0.007850849, z: 0.08285517, w: 0.9964742}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_01_l
      parentName: hand_l
      position: {x: -0.12244255, y: 0.012936401, z: 0.0057115937}
      rotation: {x: 0.038462903, y: 0.067941085, z: -0.06957623, w: 0.9945169}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_02_l
      parentName: middle_01_l
      position: {x: -0.0461586, y: -0.00017150879, z: 0.00008431434}
      rotation: {x: -0.019162547, y: 0.0007387534, z: -0.020494279, w: 0.9996061}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_03_l
      parentName: middle_02_l
      position: {x: -0.034495924, y: -0.001317749, z: 0.0006137657}
      rotation: {x: 0.0016214277, y: 0.038867068, z: 0.1336238, w: 0.99026835}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_01_l
      parentName: hand_l
      position: {x: -0.1014064, y: 0.022631682, z: 0.046431426}
      rotation: {x: -0.10860148, y: 0.09723674, z: -0.015100839, w: 0.9892031}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_02_l
      parentName: pinky_01_l
      position: {x: -0.035507277, y: -0.00022010802, z: 0.00004695892}
      rotation: {x: 0.010169312, y: -0.006366569, z: -0.0122918645, w: 0.9998525}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_03_l
      parentName: pinky_02_l
      position: {x: -0.028584441, y: -0.000488739, z: 0.00019462586}
      rotation: {x: 0.003581026, y: -0.033796214, z: -0.008930358, w: 0.99938244}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_01_l
      parentName: hand_l
      position: {x: -0.11497871, y: 0.017535247, z: 0.028469104}
      rotation: {x: -0.07320796, y: 0.0809441, z: -0.0017939684, w: 0.9940249}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_02_l
      parentName: ring_01_l
      position: {x: -0.043015745, y: -0.00082611083, z: 0.00017086028}
      rotation: {x: 0.005177221, y: 0.0039825793, z: -0.029562224, w: 0.9995416}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_03_l
      parentName: ring_02_l
      position: {x: -0.03251709, y: -0.0014707947, z: 0.00034446715}
      rotation: {x: -0.00019893344, y: -0.02625775, z: 0.11221295, w: 0.9933372}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_01_l
      parentName: hand_l
      position: {x: -0.047620162, y: 0.023749847, z: -0.025378237}
      rotation: {x: 0.5621008, y: -0.34555337, z: 0.2547382, w: 0.7069257}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_02_l
      parentName: thumb_01_l
      position: {x: -0.038321685, y: -0.0004072952, z: -0.00052200316}
      rotation: {x: -0.0017483929, y: -0.034362894, z: -0.0032875615, w: 0.9994025}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_03_l
      parentName: thumb_02_l
      position: {x: -0.04065647, y: -0.000046081543, z: 0.0012625122}
      rotation: {x: 0.019994017, y: -0.0057530003, z: 0.06466573, w: 0.9976901}
      scale: {x: 1, y: 1, z: 1}
    - name: lowerarm_twist_01_l
      parentName: lowerarm_l
      position: {x: -0.13855903, y: -0.0021912574, z: -0.0021649168}
      rotation: {x: -0.07425274, y: 0.00007897637, z: 0.00009564497, w: 0.9972395}
      scale: {x: 1, y: 1, z: 1}
    - name: upperarm_twist_01_l
      parentName: upperarm_l
      position: {x: -0.0032549095, y: -0.00091522216, z: -0.0036834716}
      rotation: {x: -0.17753275, y: 0.0066136396, z: 0.00073484797, w: 0.9840924}
      scale: {x: 1, y: 1, z: 1}
    - name: clavicle_r
      parentName: spine_03
      position: {x: -0.118837886, y: -0.027320994, z: 0.037820023}
      rotation: {x: 0.70336837, y: 0.0530146, z: 0.7067694, w: -0.054216687}
      scale: {x: 1, y: 1, z: 1}
    - name: upperarm_r
      parentName: clavicle_r
      position: {x: 0.15788054, y: -0.0007405162, z: 0.00026687622}
      rotation: {x: 0.1852674, y: -0.0057490314, z: 0.012149882, w: 0.9825962}
      scale: {x: 1, y: 1, z: 1}
    - name: lowerarm_r
      parentName: upperarm_r
      position: {x: 0.30357236, y: -0.00032878874, z: -0.0042503355}
      rotation: {x: -0.1327322, y: 0.0041032196, z: -0.016808365, w: 0.99100095}
      scale: {x: 1, y: 1, z: 1}
    - name: hand_r
      parentName: lowerarm_r
      position: {x: 0.26981765, y: -0.0005752754, z: -0.00087829586}
      rotation: {x: -0.7189482, y: 0.023987489, z: 0.09366783, w: 0.68830556}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_hand_gun
      parentName: hand_r
      position: {x: 0.08532028, y: -0.073276974, z: -0.034564782}
      rotation: {x: -0.2188116, y: 0.22540672, z: -0.7091815, w: 0.6311695}
      scale: {x: 0.999999, y: 1, z: 0.999999}
    - name: ik_hand_l
      parentName: ik_hand_gun
      position: {x: -0.15363175, y: -1.4104124, z: -0.81958556}
      rotation: {x: 0.759663, y: 0.6103921, z: 0.12527254, w: -0.18611957}
      scale: {x: 1, y: 1, z: 1}
    - name: ik_hand_r
      parentName: ik_hand_gun
      position: {x: -0.062763974, y: -0.09600883, z: -0.026203612}
      rotation: {x: 0.21881084, y: -0.22541621, z: 0.7091809, w: 0.63116705}
      scale: {x: 1, y: 1, z: 1}
    - name: index_01_r
      parentName: hand_r
      position: {x: 0.12067817, y: -0.017635651, z: 0.02109373}
      rotation: {x: 0.145821, y: 0.029203996, z: -0.09694932, w: 0.98411596}
      scale: {x: 1, y: 1, z: 1}
    - name: index_02_r
      parentName: index_01_r
      position: {x: 0.041893158, y: 0.0002975464, z: -0.00021297454}
      rotation: {x: 0.0109074, y: 0.03379411, z: -0.023001457, w: 0.99910456}
      scale: {x: 1, y: 1, z: 1.000001}
    - name: index_03_r
      parentName: index_02_r
      position: {x: 0.033471297, y: -0.00010726928, z: 0.000032920838}
      rotation: {x: 0.0106110405, y: -0.007850965, z: 0.08285525, w: 0.99647415}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_01_r
      parentName: hand_r
      position: {x: 0.12244095, y: -0.012935943, z: -0.0057118987}
      rotation: {x: 0.038462654, y: 0.06794088, z: -0.06957646, w: 0.9945169}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_02_r
      parentName: middle_01_r
      position: {x: 0.046158217, y: 0.00016891479, z: -0.00008377075}
      rotation: {x: -0.01916257, y: 0.00073869067, z: -0.020494431, w: 0.999606}
      scale: {x: 1, y: 1, z: 1}
    - name: middle_03_r
      parentName: middle_02_r
      position: {x: 0.034496307, y: 0.001316986, z: -0.00061346055}
      rotation: {x: 0.0016215958, y: 0.03886726, z: 0.13362394, w: 0.99026835}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_01_r
      parentName: hand_r
      position: {x: 0.10140457, y: -0.02263214, z: -0.046431236}
      rotation: {x: -0.10860305, y: 0.0972384, z: -0.015090166, w: 0.9892029}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_02_r
      parentName: pinky_01_r
      position: {x: 0.035506744, y: 0.00021903991, z: -0.000047225953}
      rotation: {x: 0.010168965, y: -0.0063668406, z: -0.012291645, w: 0.9998525}
      scale: {x: 1, y: 1, z: 1}
    - name: pinky_03_r
      parentName: pinky_02_r
      position: {x: 0.028587418, y: 0.0004911804, z: -0.00019439697}
      rotation: {x: 0.0035811292, y: -0.03379631, z: -0.0089301625, w: 0.99938244}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_01_r
      parentName: hand_r
      position: {x: 0.11497772, y: -0.017537536, z: -0.028468627}
      rotation: {x: -0.07320921, y: 0.08092521, z: -0.0018237074, w: 0.9940263}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_02_r
      parentName: ring_01_r
      position: {x: 0.043015897, y: 0.00082931516, z: -0.00017131805}
      rotation: {x: 0.0051770248, y: 0.003982254, z: -0.029562263, w: 0.9995416}
      scale: {x: 1, y: 1, z: 1}
    - name: ring_03_r
      parentName: ring_02_r
      position: {x: 0.032517776, y: 0.0014704894, z: -0.00034439086}
      rotation: {x: -0.00019889127, y: -0.026257658, z: 0.11221293, w: 0.9933372}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_01_r
      parentName: hand_r
      position: {x: 0.047619857, y: -0.023751525, z: 0.025378512}
      rotation: {x: 0.56210417, y: -0.34554967, z: 0.2547283, w: 0.70692855}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_02_r
      parentName: thumb_01_r
      position: {x: 0.038320806, y: 0.00040813445, z: 0.0005210876}
      rotation: {x: -0.0017488716, y: -0.034363244, z: -0.0032876735, w: 0.9994025}
      scale: {x: 1, y: 1, z: 1}
    - name: thumb_03_r
      parentName: thumb_02_r
      position: {x: 0.040656585, y: 0.000046005247, z: -0.001262207}
      rotation: {x: 0.019993922, y: -0.0057527563, z: 0.06466579, w: 0.9976901}
      scale: {x: 1, y: 1, z: 1}
    - name: lowerarm_twist_01_r
      parentName: lowerarm_r
      position: {x: 0.1385591, y: 0.00219141, z: 0.0021650696}
      rotation: {x: -0.1910397, y: 0.0000670432, z: 0.00010402325, w: 0.98158234}
      scale: {x: 1, y: 1, z: 1}
    - name: upperarm_twist_01_r
      parentName: upperarm_r
      position: {x: 0.0032548904, y: 0.00091514585, z: 0.0036834716}
      rotation: {x: -0.34532765, y: 0.0063861264, z: 0.0018694856, w: 0.9384586}
      scale: {x: 1, y: 1, z: 1}
    - name: neck_01
      parentName: spine_03
      position: {x: -0.16556899, y: -0.0035485458, z: -0.000000008574453}
      rotation: {x: -8.42828e-10, y: 0.00000024572626, z: 0.07417847, w: 0.997245}
      scale: {x: 1, y: 1, z: 1}
    - name: head
      parentName: neck_01
      position: {x: -0.09291473, y: 0.0035937356, z: -0.000000011202064}
      rotation: {x: -0.00000007644881, y: 0.0000003029586, z: -0.20676897, w: 0.9783898}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_l
      parentName: pelvis
      position: {x: 0.014488144, y: -0.005314232, z: -0.090058096}
      rotation: {x: 0.07741854, y: 0.012803749, z: 0.00013325893, w: 0.9969165}
      scale: {x: 1, y: 1, z: 1}
    - name: calf_l
      parentName: thigh_l
      position: {x: 0.4247419, y: -0.00019899367, z: 0.001386795}
      rotation: {x: -0.050222322, y: -0.023800058, z: 0.010529128, w: 0.99839896}
      scale: {x: 1, y: 1, z: 1}
    - name: calf_twist_01_l
      parentName: calf_l
      position: {x: 0.20488742, y: 0.0007075107, z: -0.000568285}
      rotation: {x: 0.0048873043, y: 0.001894914, z: 0.0075686863, w: 0.9999576}
      scale: {x: 1, y: 1, z: 1}
    - name: foot_l
      parentName: calf_l
      position: {x: 0.40230942, y: 0.0004380226, z: -0.00036435126}
      rotation: {x: -0.004424574, y: 0.01917944, z: -0.005493983, w: 0.9997912}
      scale: {x: 1, y: 1, z: 1}
    - name: ball_l
      parentName: foot_l
      position: {x: 0.10453838, y: -0.16577853, z: 0.0008015537}
      rotation: {x: 0.000080079604, y: -0.000029611301, z: 0.7186337, w: 0.6953888}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_twist_01_l
      parentName: thigh_l
      position: {x: 0.22041252, y: -0.000028873681, z: -0.000111780166}
      rotation: {x: -0.04913289, y: -0.000033939396, z: 0.00044855775, w: 0.9987922}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_r
      parentName: pelvis
      position: {x: 0.0144862365, y: -0.0053142733, z: 0.09005803}
      rotation: {x: -0.012747871, y: 0.07951431, z: 0.9967522, w: -0.00016458936}
      scale: {x: 1, y: 1, z: 1}
    - name: calf_r
      parentName: thigh_r
      position: {x: -0.4247425, y: 0.00023702144, z: -0.0014056873}
      rotation: {x: -0.050922018, y: -0.023799222, z: 0.01052826, w: 0.99836355}
      scale: {x: 1, y: 1, z: 1}
    - name: calf_twist_01_r
      parentName: calf_r
      position: {x: -0.20488933, y: -0.00071930647, z: 0.0005774784}
      rotation: {x: 0.004185337, y: 0.0019082484, z: 0.0075833704, w: 0.99996066}
      scale: {x: 1, y: 1, z: 1}
    - name: foot_r
      parentName: calf_r
      position: {x: -0.40231207, y: -0.00046319482, z: 0.00037838935}
      rotation: {x: -0.005825283, y: 0.019260721, z: -0.0054913885, w: 0.99978244}
      scale: {x: 1, y: 1, z: 1}
    - name: ball_r
      parentName: foot_r
      position: {x: -0.10453815, y: 0.16577797, z: -0.0008016014}
      rotation: {x: 0.000079882775, y: -0.000029484356, z: 0.71863365, w: 0.69538885}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_twist_01_r
      parentName: thigh_r
      position: {x: -0.22041191, y: 0.000052292347, z: 0.00010334968}
      rotation: {x: -0.049134567, y: -0.00003435569, z: 0.00044864576, w: 0.9987921}
      scale: {x: 1, y: 1, z: 1}
    - name: SK_Mannequin_Mobile
      parentName: Unity_Grruzam_BaseModeling_no_weapon(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: d3ffbb53d382790469e6c8f926efe5f6,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183324
  packageName: Powerful Sword Pack(Great Sword + Katana)
  packageVersion: 2.1.1
  assetPath: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FL90.FBX
  uploadId: 746091
