Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker11
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker11.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14196]  Target information:

Player connection [14196]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 480415721 [EditorId] 480415721 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14196] Host joined multi-casting on [***********:54997]...
Player connection [14196] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.71 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.52 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56716
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004688 seconds.
- Loaded All Assemblies, in  1.072 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.154 seconds
Domain Reload Profiling: 2226ms
	BeginReloadAssembly (309ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (105ms)
	RebuildNativeTypeToScriptingClass (39ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (498ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (491ms)
			TypeCache.Refresh (485ms)
				TypeCache.ScanAssembly (446ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1155ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1009ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (219ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.369 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.40 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.084 seconds
Domain Reload Profiling: 5449ms
	BeginReloadAssembly (447ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (120ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (118ms)
	LoadAllAssembliesAndSetupDomain (1648ms)
		LoadAssemblies (1037ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (852ms)
			TypeCache.Refresh (649ms)
				TypeCache.ScanAssembly (608ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (3085ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2450ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (608ms)
			ProcessInitializeOnLoadAttributes (1660ms)
			ProcessInitializeOnLoadMethodAttributes (158ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.25 seconds
Refreshing native plugins compatible for Editor in 8.32 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.6 MB). Loaded Objects now: 8334.
Memory consumption went from 213.9 MB to 204.3 MB.
Total: 188.616500 ms (FindLiveObjects: 2.504800 ms CreateObjectMapping: 3.174800 ms MarkObjects: 170.803100 ms  DeleteObjects: 12.130200 ms)

========================================================================
Received Import Request.
  Time since last request: 1812182.126695 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SimpleSplash_01.prefab
  artifactKey: Guid(37270d36ebbdda4458815fac6dbe8e22) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SimpleSplash_01.prefab using Guid(37270d36ebbdda4458815fac6dbe8e22) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e9abbed418b6a8f23b3bd91d5e102773') in 3.1867341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Settings/Mobile_Renderer.asset
  artifactKey: Guid(65bc7dbf4170f435aa868c779acfb082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/Mobile_Renderer.asset using Guid(65bc7dbf4170f435aa868c779acfb082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '64469729bab18cc8f0ac76b6ceff1efe') in 0.164783 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab
  artifactKey: Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab using Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3e0209a593e6d1d8f97d015afbb1b081') in 0.1578957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineAsset.asset
  artifactKey: Guid(c6a867ee639a59c4a95e661e91ab54a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineAsset.asset using Guid(c6a867ee639a59c4a95e661e91ab54a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed0bae39ef3eca90ed8dcd264b533b4e') in 0.7791612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_ComposedLateralWater_01.mat
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_ComposedLateralWater_01.mat using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d5ae6db7ac9e15f5b686333ba09d3da') in 0.7533348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab
  artifactKey: Guid(86804816d3c9e214c9a526e82ee95703) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/VFX/(Vfx)Land.prefab using Guid(86804816d3c9e214c9a526e82ee95703) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1743f695e7c8bbadbf401f5f534ce057') in 0.1339648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab
  artifactKey: Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_T-Pose_Grrrru_Man(recommend).prefab using Guid(e2abcd283cd5ae148a3445190a45ff8a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4eb62edad6b3c6e51bb766b610313372') in 0.3784218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 146

========================================================================
Received Import Request.
  Time since last request: 0.000100 seconds.
  path: Assets/Scripts/Controllers/LaserDetector.cs
  artifactKey: Guid(f42e4da75bad58740a87afb56084d29e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Controllers/LaserDetector.cs using Guid(f42e4da75bad58740a87afb56084d29e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b33a10ca9a9709c0b1960e1a1f3b919') in 0.0420703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Scenes/SampleScene.unity additively'
Loaded scene 'Assets/Scenes/SampleScene.unity'
	Deserialize:            26.447 ms
	Integration:            11.656 ms
	Integration of assets:  0.010 ms
	Thread Wait Time:       0.023 ms
	Total Operation Time:   38.136 ms
 -> (artifact id: '2b256eaf94bb0553b59159945a4eb5f2') in 0.0907013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000100 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.prefab
  artifactKey: Guid(370239a565943e94dbcf4d455779196d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.prefab using Guid(370239a565943e94dbcf4d455779196d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3bd3f1aa5b3001d7e05b5a1c76d6b97e') in 0.0756255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Black Variant.prefab
  artifactKey: Guid(2cbe962403989044783504d68897d6ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Black Variant.prefab using Guid(2cbe962403989044783504d68897d6ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4217da5dbe5493b4a1647e49f6e330ee') in 0.3136663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 772

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_4x8.mat
  artifactKey: Guid(91f50f270f7b31744a8b19e7d7cabed9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_4x8.mat using Guid(91f50f270f7b31744a8b19e7d7cabed9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7458855490108dd0643dbd1bf12e13d') in 0.8047021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_31.prefab
  artifactKey: Guid(7365b589d4abdb24aba58cdffb9c3a01) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_31.prefab using Guid(7365b589d4abdb24aba58cdffb9c3a01) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bb3ff9676be3495dc1e29758a7b4c9f9') in 0.5191204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Climb.prefab
  artifactKey: Guid(5852e38a76009af4a9930c5b14fca2f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Climb.prefab using Guid(5852e38a76009af4a9930c5b14fca2f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '33768e49f0a73d0ba097d66b2ceb821c') in 0.0528965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_MetallicSmoothness.png
  artifactKey: Guid(e7baf625b04773d40bb78890f0a61876) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_MetallicSmoothness.png using Guid(e7baf625b04773d40bb78890f0a61876) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f374019d1c17c882e0d64343464072a7') in 0.2931161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_01.prefab
  artifactKey: Guid(abc00000000004726281824012566575) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_01.prefab using Guid(abc00000000004726281824012566575) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5f6962bab4ae6a6a659af541b388c59b') in 0.0335998 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_03.png
  artifactKey: Guid(f687ce1127d592343bd2e6f7a93e210a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_03.png using Guid(f687ce1127d592343bd2e6f7a93e210a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54fbb0ad97af118e56b29b63a1ccdd03') in 0.0696711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab
  artifactKey: Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab using Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '28822625c478ec814025bca63b3dbd0c') in 0.0539067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_2.prefab
  artifactKey: Guid(abc00000000011861529990532398266) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_2.prefab using Guid(abc00000000011861529990532398266) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '27cfef736ab1892038228cc9fb72a651') in 0.0405089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_02.mat
  artifactKey: Guid(31119b284ebef534d9e6687ac571aab0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_RadialWave_02.mat using Guid(31119b284ebef534d9e6687ac571aab0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '364737019a8ede84e8deb202c282cae0') in 0.0538739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleCircle_01.mat
  artifactKey: Guid(0b3366663f5a4e84890ec608c2408832) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleCircle_01.mat using Guid(0b3366663f5a4e84890ec608c2408832) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ece5762904caebf792045251882f87b1') in 0.5418803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_02.prefab
  artifactKey: Guid(abc00000000005077865649778996978) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_02.prefab using Guid(abc00000000005077865649778996978) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8874f0204d4d9fef012de1737a731228') in 0.0527195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_02.prefab
  artifactKey: Guid(abc00000000000686510749062123180) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_02.prefab using Guid(abc00000000000686510749062123180) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'de1cf16f9501ae0e19fea5656c908051') in 0.0378668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_3.prefab
  artifactKey: Guid(abc00000000014321849382002855487) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_3.prefab using Guid(abc00000000014321849382002855487) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '71538740df2689b118c900991cac7e00') in 0.042491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bar_Shelf.prefab
  artifactKey: Guid(abc00000000008531406406469043789) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bar_Shelf.prefab using Guid(abc00000000008531406406469043789) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ca3060cd66e612b81aabcdbe4405bb1d') in 0.0398079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity
  artifactKey: Guid(afd6880a243c0854882e6dc680d01b5c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity using Guid(afd6880a243c0854882e6dc680d01b5c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f8b452973cc0131144253ef67658155') in 0.0746652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_04.prefab
  artifactKey: Guid(abc00000000005248435499528781811) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_A_04.prefab using Guid(abc00000000005248435499528781811) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '962948406a30a58d36a7f1759455b824') in 0.0366574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_01.prefab
  artifactKey: Guid(abc00000000017498136706815792837) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_01.prefab using Guid(abc00000000017498136706815792837) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c3244962a40abcf22ee3527827394faa') in 0.033887 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01.prefab
  artifactKey: Guid(abc00000000009081137454623230966) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01.prefab using Guid(abc00000000009081137454623230966) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8f87676baf7bf9913fadd9c50dd8d89a') in 0.0546691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_02.prefab
  artifactKey: Guid(abc00000000001234702104932231217) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_02.prefab using Guid(abc00000000001234702104932231217) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2f756c85760541866a7b20c7143ddb4c') in 0.0432406 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_06.prefab
  artifactKey: Guid(abc00000000012624007103874003120) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_06.prefab using Guid(abc00000000012624007103874003120) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '270b0cf6b2856041fca7c325225b92b7') in 0.0811997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_MetallicSmoothness.png
  artifactKey: Guid(07c5fc59cd1efbf4098259b4739374fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_MetallicSmoothness.png using Guid(07c5fc59cd1efbf4098259b4739374fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52a24db287c398e4df569644b51a0f2f') in 0.1144125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000271 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_9.prefab
  artifactKey: Guid(abc00000000002923321966201374329) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_9.prefab using Guid(abc00000000002923321966201374329) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '55879ddd45a87be9cd271d30f8d8ba3c') in 0.0400584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3.mat
  artifactKey: Guid(b60aeddef1389494bbd9be9a3004147c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3.mat using Guid(b60aeddef1389494bbd9be9a3004147c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '938c4cb4fdf2f0743d62a2040e35e0c6') in 0.0673623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_13_3x4.mat
  artifactKey: Guid(8928f6a7fdfc21041bb8a51777796967) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_13_3x4.mat using Guid(8928f6a7fdfc21041bb8a51777796967) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec36ad4653bed5ee7b757060dd91c28a') in 0.5611948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_04.mat
  artifactKey: Guid(4658f965400871647a3e611e92ee0df5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_04.mat using Guid(4658f965400871647a3e611e92ee0df5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94b9d02b1cfe3f862ad9f9753c65b4d1') in 0.0470101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_02.prefab
  artifactKey: Guid(abc00000000017834578992296274955) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_02.prefab using Guid(abc00000000017834578992296274955) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '16b552b9b70173b619663979df8a266f') in 0.0434975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CurtainWall_SeamHider.prefab
  artifactKey: Guid(abc00000000007989197903566043416) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CurtainWall_SeamHider.prefab using Guid(abc00000000007989197903566043416) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ec508de59e14e98ff6721302e41ded2d') in 0.0542185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3_Lava.mat
  artifactKey: Guid(ac56bb92c3e001649b4b7d277a755dd4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3_Lava.mat using Guid(ac56bb92c3e001649b4b7d277a755dd4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f55205df3427ea15fe7dfe50cc0a87b7') in 0.0492402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_26.prefab
  artifactKey: Guid(abc00000000016998756364939789157) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_26.prefab using Guid(abc00000000016998756364939789157) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd6f00fb7e314f63228a2f6c679d96149') in 0.0973282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_27.prefab
  artifactKey: Guid(abc00000000004106986877503133980) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_27.prefab using Guid(abc00000000004106986877503133980) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd12777264babdb0b4c79d01e548ddb53') in 0.0951023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_InsideCorner_01.prefab
  artifactKey: Guid(abc00000000009466584702714161502) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_InsideCorner_01.prefab using Guid(abc00000000009466584702714161502) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8061e06927ea4f728015d3647dca70c5') in 0.0511869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_03.prefab
  artifactKey: Guid(abc00000000010198080389812662350) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_03.prefab using Guid(abc00000000010198080389812662350) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a640b46cf9031bbe1dec051a24c1a3ef') in 0.0428239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_02.prefab
  artifactKey: Guid(abc00000000017518264023766144327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_EuropeanBeech_L_02.prefab using Guid(abc00000000017518264023766144327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '44eff2f6d1657e2119655a3043ff44fa') in 0.0431374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_MatID.png
  artifactKey: Guid(8f74c51dd1ec39c44a410e66fdb8ac80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_MatID.png using Guid(8f74c51dd1ec39c44a410e66fdb8ac80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fadd08f4283de3643b5cc51972250db0') in 0.062611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M_1.prefab
  artifactKey: Guid(abc00000000007203461997630695151) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M_1.prefab using Guid(abc00000000007203461997630695151) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '04456fb96bfcccc1a7cdc4549763a12c') in 0.0609698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2M.prefab
  artifactKey: Guid(abc00000000017181763308313172076) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2M.prefab using Guid(abc00000000017181763308313172076) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dd21ecb324fd211aac18aaf9e09ee9ab') in 0.0371357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab
  artifactKey: Guid(958cf4748c84fb948bc3400edc8005e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Modeling_Weapon_Big_Sword.prefab using Guid(958cf4748c84fb948bc3400edc8005e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dfeb5135c9c173750187db0176bbea96') in 0.1147874 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_02.mat
  artifactKey: Guid(e32fbf782848b6745897b17ed1685e57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_02.mat using Guid(e32fbf782848b6745897b17ed1685e57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a0d5c837edd2bf0e37569bf2e46d0e9') in 0.0404698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_IronChandelier_Base.prefab
  artifactKey: Guid(abc00000000000836361907021055290) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_IronChandelier_Base.prefab using Guid(abc00000000000836361907021055290) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3f250cb2360fadcfa255a222fcd00947') in 0.0366821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_02.prefab
  artifactKey: Guid(abc00000000000358624188340789798) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_02.prefab using Guid(abc00000000000358624188340789798) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dd7fb0f7a6152b7d9cc9c3e2264024ea') in 0.0525667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Lava_SimpleSplash_01.prefab
  artifactKey: Guid(8bda80a6040df73439f85f2ea20f2f82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Lava_SimpleSplash_01.prefab using Guid(8bda80a6040df73439f85f2ea20f2f82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1958e282315a111b9c7cb4310cd1bf1c') in 0.3420636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01.prefab
  artifactKey: Guid(abc00000000015005830544568857422) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01.prefab using Guid(abc00000000015005830544568857422) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e55e6612a5690a65bba6b7ec9d227c00') in 0.0635566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Market_Stand_01.prefab
  artifactKey: Guid(abc00000000001172960152337823974) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Market_Stand_01.prefab using Guid(abc00000000001172960152337823974) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '97609ad1e669a91b6b81f6ec238efed0') in 0.0458182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_03_4x4.mat
  artifactKey: Guid(fa1a1b4a535cc4045b987398fcfa5cb9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterWhip_03_4x4.mat using Guid(fa1a1b4a535cc4045b987398fcfa5cb9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99f13e56791c1256b705cc71435e5ccc') in 0.0643548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pot.prefab
  artifactKey: Guid(abc00000000014327047588651083102) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pot.prefab using Guid(abc00000000014327047588651083102) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '289e9d101e07ac61968221003a638a58') in 0.0393026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_C.prefab
  artifactKey: Guid(abc00000000001333846768155401957) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_C.prefab using Guid(abc00000000001333846768155401957) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b653c7ce49ed80e66220584450cd7b3f') in 0.0697219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_06.mat
  artifactKey: Guid(910e8c65ab8b87f4ea7fd634b5196849) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterHead_06.mat using Guid(910e8c65ab8b87f4ea7fd634b5196849) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e80762d78232de5234c0cc29be3c55d') in 0.5239192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_01.prefab
  artifactKey: Guid(abc00000000013811827313343407570) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_01.prefab using Guid(abc00000000013811827313343407570) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7826c3ce6a61d5ccb5368d4039aad3c9') in 0.0655573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05.prefab
  artifactKey: Guid(abc00000000005759585731758339980) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05.prefab using Guid(abc00000000005759585731758339980) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a12d323f7015c52c07a240d22aaf6719') in 0.0499315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02.prefab
  artifactKey: Guid(abc00000000006699053618561820555) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02.prefab using Guid(abc00000000006699053618561820555) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '057f3ba72510b988c5462f310cb1a132') in 0.0456279 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_FoamImpact.prefab
  artifactKey: Guid(c4d499a9e2b74fb41ad940b085b28e33) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_FoamImpact.prefab using Guid(c4d499a9e2b74fb41ad940b085b28e33) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71eb5320712e7ebbd9f1639830bf49b1') in 0.1598867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sidetable_01.prefab
  artifactKey: Guid(abc00000000014240842798744314698) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sidetable_01.prefab using Guid(abc00000000014240842798744314698) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '753e9415f046572b0dad0be919cd697c') in 0.0442735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Dissolve.prefab
  artifactKey: Guid(cdd5d1f3cb0e19342a70d68b4401e1b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Dissolve.prefab using Guid(cdd5d1f3cb0e19342a70d68b4401e1b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10466c4ef58f114c9b4783818515be4f') in 0.2090157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 75

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool_Short.prefab
  artifactKey: Guid(abc00000000007793134848975468829) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stool_Short.prefab using Guid(abc00000000007793134848975468829) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b2156bb92d836d92d9744a62700a587d') in 0.0349704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Burst.prefab
  artifactKey: Guid(f4309870c41615247af837f7c99ccfb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaveAttack_Burst.prefab using Guid(f4309870c41615247af837f7c99ccfb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '076de6596ed02357119d2293b42bf18b') in 0.1402718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/StandardTransparencyShaderFresnel.shadergraph
  artifactKey: Guid(614f255195ef23948a395fe9a915199d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/StandardTransparencyShaderFresnel.shadergraph using Guid(614f255195ef23948a395fe9a915199d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b9ce24bc3835cdc86162d25897a81a8') in 0.0589604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_D.prefab
  artifactKey: Guid(abc00000000008915781062323504628) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_D.prefab using Guid(abc00000000008915781062323504628) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '44ee5e7cabe7dd33176362367002d89c') in 0.0341234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase.prefab
  artifactKey: Guid(abc00000000018210881637664435060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Staircase.prefab using Guid(abc00000000018210881637664435060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0fa64d1c5548af7b794a1682d6ddb6ee') in 0.0394199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SimpleSplash_01.prefab
  artifactKey: Guid(37270d36ebbdda4458815fac6dbe8e22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_SimpleSplash_01.prefab using Guid(37270d36ebbdda4458815fac6dbe8e22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c8fdb12de328b1e8bc4754d72c51389') in 0.2009943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_2.prefab
  artifactKey: Guid(abc00000000002762296274349397846) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_2.prefab using Guid(abc00000000002762296274349397846) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1b186945e2d862f1a1841afbca3dc015') in 0.0389265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_1.prefab
  artifactKey: Guid(abc00000000000435323177912667922) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_1.prefab using Guid(abc00000000000435323177912667922) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '27183ec642ba7b960b9d6ba91c5b35b6') in 0.0363854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01_Splines_31.prefab
  artifactKey: Guid(abc00000000011714369972703239576) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_OuterCorner_01_Splines_31.prefab using Guid(abc00000000011714369972703239576) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '77dcbfdbd4adb6711b2cf39377addb46') in 0.0923111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_Corner_01.prefab
  artifactKey: Guid(abc00000000006704188601032085561) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_Corner_01.prefab using Guid(abc00000000006704188601032085561) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7034b509f834046e5f78bd9c3066a6d1') in 0.0748093 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Impact.prefab
  artifactKey: Guid(ee8ff96cd1b23a143b64c09a4dd5452b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Impact.prefab using Guid(ee8ff96cd1b23a143b64c09a4dd5452b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b54d7b50f288f15f7f28a5f6ece9264') in 0.150423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 52

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.prefab
  artifactKey: Guid(45d6812f23fa20e4d9eb78290da2dfde) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.prefab using Guid(45d6812f23fa20e4d9eb78290da2dfde) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '45a0256c1bb9b24f2946e732e31d9baf') in 0.0641139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01_1.prefab
  artifactKey: Guid(abc00000000016605019329462927557) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01_1.prefab using Guid(abc00000000016605019329462927557) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '63bc04c113e3d0138f56b41ae49e9e7b') in 0.0394752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04.prefab
  artifactKey: Guid(abc00000000017741063982725564442) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04.prefab using Guid(abc00000000017741063982725564442) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2081dc10ad1a978dadf0a803578a4673') in 0.0736984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 40

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader
  artifactKey: Guid(c8d12adcee749c344b8117cf7c7eb912) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader using Guid(c8d12adcee749c344b8117cf7c7eb912) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ea8b289026e1428ddaa832ae5f84da4') in 0.107679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.json
  artifactKey: Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.json using Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da5db1d2bfc30f73d92aca71ac557f5e') in 0.0534208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Reset Dark.png
  artifactKey: Guid(64abefdaa63f3b2439852c3082c8afca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Reset Dark.png using Guid(64abefdaa63f3b2439852c3082c8afca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2fe4611944ece8a0b2ea2c25d8624dcd') in 0.0675014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/TutorialInfo/Icons/Help_Icon.png
  artifactKey: Guid(9266273b8f123004195741f969177dda) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TutorialInfo/Icons/Help_Icon.png using Guid(9266273b8f123004195741f969177dda) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efac2899ac7acb770c6fa74670c79d48') in 0.0697751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/TutorialInfo/Icons/URP.png
  artifactKey: Guid(727a75301c3d24613a3ebcec4a24c2c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TutorialInfo/Icons/URP.png using Guid(727a75301c3d24613a3ebcec4a24c2c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '121e9b1ea8200f8dcd72a17d0f6d6cf7') in 0.0438477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_03_4x5.mat
  artifactKey: Guid(e33305bfc07c3ac428083be9cab61e7c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_03_4x5.mat using Guid(e33305bfc07c3ac428083be9cab61e7c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a304631ea16f8b382c22e7ab725ee6d') in 0.6478548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleSplash_02_4x4.mat
  artifactKey: Guid(33af2d77b54de3c49ae22bfe684ae3ea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleSplash_02_4x4.mat using Guid(33af2d77b54de3c49ae22bfe684ae3ea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e87a188dc938fb299d2687b6d332405a') in 0.0838207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_05.png
  artifactKey: Guid(27f7950637e404e41a84245f2e0c022c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_05.png using Guid(27f7950637e404e41a84245f2e0c022c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '686efc33c0154fe28959bb4545063af0') in 0.0739011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000144 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_Roughness.png
  artifactKey: Guid(8d0ed038bf5dc744eb44140e387b8e3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_Roughness.png using Guid(8d0ed038bf5dc744eb44140e387b8e3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49c07f4dc7ed9a55676da0923a692dd6') in 0.0916095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Maps/FeralGirlRoca.unity
  artifactKey: Guid(2348318492519774694d91f872d679fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Maps/FeralGirlRoca.unity using Guid(2348318492519774694d91f872d679fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/IdaFaber/Maps/FeralGirlRoca.unity additively'
Loaded scene 'Assets/IdaFaber/Maps/FeralGirlRoca.unity'
	Deserialize:            3.882 ms
	Integration:            416.515 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.036 ms
	Total Operation Time:   420.435 ms
 -> (artifact id: 'c9e7a81b112c3ba38388a850b5e6a25f') in 1.6441108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6225

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Rename.cs
  artifactKey: Guid(50a498e1a48278344b7ea2f4b6cc0750) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Window_Rename.cs using Guid(50a498e1a48278344b7ea2f4b6cc0750) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5feccbfb50aac114ddffd0abe564562d') in 0.0362348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Unlock Dark.png
  artifactKey: Guid(ab3734a97e43efc4a8289c188fd614e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Unlock Dark.png using Guid(ab3734a97e43efc4a8289c188fd614e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '987ce2c3b3b80bcab4802a8b40e0fdd4') in 0.0429943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_06.mat
  artifactKey: Guid(c9d4ef4e8f8139b4e8331a25ac788822) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_06.mat using Guid(c9d4ef4e8f8139b4e8331a25ac788822) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c7f55f1450d3e0e5a66007d51895193') in 0.6023241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Icon Inspector.png
  artifactKey: Guid(4724f3b6006a28f4ca8f7de7bf926d39) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Icon Inspector.png using Guid(4724f3b6006a28f4ca8f7de7bf926d39) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3aaa30de560ab0c85f16007d3e5360af') in 0.0836147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.449496 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_27.mat
  artifactKey: Guid(ca8e97b3922d1864da1018435603683e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_27.mat using Guid(ca8e97b3922d1864da1018435603683e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be8e28c30462c1468c62bf8b600e7e04') in 0.1491207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_08.mat
  artifactKey: Guid(2490ec073a917b44790745f6c0a10ea9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_08.mat using Guid(2490ec073a917b44790745f6c0a10ea9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75b3d711804051f8ddf0ad1b2aa58a71') in 0.7910236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_v02_01.mat
  artifactKey: Guid(e22b6051e40dfc746b6c41704388f196) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_v02_01.mat using Guid(e22b6051e40dfc746b6c41704388f196) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34a17d3c59148773afeb8bfd9f2dfcaa') in 0.1533475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_05.mat
  artifactKey: Guid(2b4b64038bdad9d43b5fee832ded97e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_05.mat using Guid(2b4b64038bdad9d43b5fee832ded97e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71c5e6962f9bd5ebe6befb2d571439b9') in 0.0906692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_07.mat
  artifactKey: Guid(53a358e7ed0d3314ab6a9b1bc06fe64e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_07.mat using Guid(53a358e7ed0d3314ab6a9b1bc06fe64e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f9521a4683322e9ad9ed43cbb99e58a') in 0.804591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_01.mat
  artifactKey: Guid(4878abcf47d567747af8436016b968d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_13_01.mat using Guid(4878abcf47d567747af8436016b968d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd0fb65cc56b409ca71800fb90537582') in 0.9074492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_06.png
  artifactKey: Guid(a86b5a8df1bce5e46a2e2eaf67529bfd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_06.png using Guid(a86b5a8df1bce5e46a2e2eaf67529bfd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f21a53ae24539fd6536a32d7a5a4c4b') in 0.0609152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21_Alternative.mat
  artifactKey: Guid(f845779ae7862834bb6dfb366771f15e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21_Alternative.mat using Guid(f845779ae7862834bb6dfb366771f15e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '633724d34fb57c7f519929b379e3a0f2') in 0.3244432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_LASHES_04.mat
  artifactKey: Guid(ef86a107ce1ec3b4ba9a96a3e975174c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_LASHES_04.mat using Guid(ef86a107ce1ec3b4ba9a96a3e975174c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f67c8aec39218abec2d226ae43ed76d') in 0.1190307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_03.png
  artifactKey: Guid(410ff4275a5d5d740bdce197dd789200) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_03.png using Guid(410ff4275a5d5d740bdce197dd789200) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c67c3da38c6559f7e29231ecd9324b80') in 0.0620827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NUDE Variant.prefab
  artifactKey: Guid(f9b74c6fb02af1e43b45a08c26a8111b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NUDE Variant.prefab using Guid(f9b74c6fb02af1e43b45a08c26a8111b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bab8ad8f69aa459bbd4811dc7e927fcf') in 0.161795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 697

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21.mat
  artifactKey: Guid(85e66782deec0cc41a2f2c6a4d02948e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_21.mat using Guid(85e66782deec0cc41a2f2c6a4d02948e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ef1de9152671d2fbd643fc7e669ed00') in 0.2487266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_28.png
  artifactKey: Guid(cc563cea6d3270c43b0d1abdcc5599d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_28.png using Guid(cc563cea6d3270c43b0d1abdcc5599d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40724559563e3cc31bc480403d2bbdfc') in 0.0699817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Violet.mat
  artifactKey: Guid(45059858911d16243bd54bfdbd8080ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Weapons/MAT_ROCA_SWORD_Violet.mat using Guid(45059858911d16243bd54bfdbd8080ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1906c2c0b19d4f1da2d02b384af43876') in 0.1786229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/IDA_Base.shadergraph
  artifactKey: Guid(f7274c59c7afcab42a8df44effe493b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/IDA_Base.shadergraph using Guid(f7274c59c7afcab42a8df44effe493b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62c8593dab1f60aee813f93c20694afb') in 0.3072711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_18_01.png
  artifactKey: Guid(ef23de44f59d1174d8068a69edc07f5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_18_01.png using Guid(ef23de44f59d1174d8068a69edc07f5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0e4bbe66811b1e9a49f26d994ae8ccf') in 0.1188415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_03.png
  artifactKey: Guid(01ff963bee9292044b90a6d1e100a8af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_03.png using Guid(01ff963bee9292044b90a6d1e100a8af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ea11ebf9fcf12d03786e3d5f9dd31ee') in 0.0928165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_23.png
  artifactKey: Guid(4d2ae67425ce69549acd206f55d45a49) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_23.png using Guid(4d2ae67425ce69549acd206f55d45a49) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd381a5856a6b85aaa96adc5d7174c92') in 0.0879493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_09_02.png
  artifactKey: Guid(2c00cd7cd7c0b8c4a80f1395811b0f80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_09_02.png using Guid(2c00cd7cd7c0b8c4a80f1395811b0f80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1da24a23d263ed9b74d157abec618950') in 0.0791266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_MetallicSmoothness.png
  artifactKey: Guid(e95322ca0e1f08149a537ef04cd685dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_MetallicSmoothness.png using Guid(e95322ca0e1f08149a537ef04cd685dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3ffe38d62fc7c3610b67281944efc0d') in 0.1409524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_07.mat
  artifactKey: Guid(c55283586a5b6a845b25a7f2927f9b9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_07.mat using Guid(c55283586a5b6a845b25a7f2927f9b9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4872765f243639c2defc179068140c1') in 0.2738978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_04.mat
  artifactKey: Guid(44a4561e676db0b448ecbb0cf04e2ad1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_04.mat using Guid(44a4561e676db0b448ecbb0cf04e2ad1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fa34bb302b489564ccf56d7adfc1838') in 0.3242366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Custom Variant.prefab
  artifactKey: Guid(5d21857205052454e8d939917b1f08c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Custom Variant.prefab using Guid(5d21857205052454e8d939917b1f08c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6aea5c17cf92e1edf2f866d61924765f') in 0.1300253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_04_02.png
  artifactKey: Guid(8c0455b3d61363a4ebb4b90cde89bf3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_04_02.png using Guid(8c0455b3d61363a4ebb4b90cde89bf3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76e60130ece78578026b9f3d579abc33') in 0.1148397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_03.mat
  artifactKey: Guid(003be2dcf264c4f4aab93cf03a0d95d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_03.mat using Guid(003be2dcf264c4f4aab93cf03a0d95d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afb48a360eb552aee4be5233791c6ef2') in 0.3001405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Bridge.prefab
  artifactKey: Guid(bab102f11e052da4f812440b4e2552f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Bridge.prefab using Guid(bab102f11e052da4f812440b4e2552f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4c2905463370569bf9c01c4fd141a01') in 0.1285327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 186

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/IdaFaber/Textures/Base/T_SKIN_Normal_01.png
  artifactKey: Guid(aff0f93c834647a40b3b3a8139f998dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_SKIN_Normal_01.png using Guid(aff0f93c834647a40b3b3a8139f998dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72f14d5da3474a300ec40d83a6e948f8') in 0.0678505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)PinkClear.mat
  artifactKey: Guid(7119f4676a7006b4f84f592f1f218002) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)PinkClear.mat using Guid(7119f4676a7006b4f84f592f1f218002) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73ec47afa8d4da938bc73c4650ffe070') in 0.0711921 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Cyan.mat
  artifactKey: Guid(e5be4bad6d8e29b4b8aa94631cef7eec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Cyan.mat using Guid(e5be4bad6d8e29b4b8aa94631cef7eec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec99cd204e61e2c6b053f19134588074') in 0.0713503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/HumanM_Model.fbx
  artifactKey: Guid(2faa610713d3b3c439473daa55e8c60a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/HumanM_Model.fbx using Guid(2faa610713d3b3c439473daa55e8c60a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '527f19c83e715141e38bcabcd1ae7350') in 0.1876711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 124

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Msh)Stairs.fbx
  artifactKey: Guid(10d53d94cdf177a44b8791ad4b4769f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Msh)Stairs.fbx using Guid(10d53d94cdf177a44b8791ad4b4769f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c085247b1f2f304544c34670f12fc75') in 0.0589176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab
  artifactKey: Guid(83cdd184a3eace340b4dedb44e652290) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab using Guid(83cdd184a3eace340b4dedb44e652290) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c77d8259a03a35d0dc6997def07dcf2e') in 0.0860131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Dirt_01.png
  artifactKey: Guid(eed52cf33ce3c784cafb59e12debe395) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Mask_Dirt_01.png using Guid(eed52cf33ce3c784cafb59e12debe395) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0f062633d5bb97c6f4177a81a3b7071') in 0.0973705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_02.png
  artifactKey: Guid(07d894fe2e3cccb46b1701564db40718) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_02.png using Guid(07d894fe2e3cccb46b1701564db40718) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa968c8263ea2aade6ce615792f33589') in 0.0644635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Emissive.png
  artifactKey: Guid(5adc9a1c778f25c40a729076939b87a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Emissive.png using Guid(5adc9a1c778f25c40a729076939b87a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5109931ec763ce7af87292bb1deb333a') in 0.0744947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_01_aegyosal.png
  artifactKey: Guid(8f7fd72bc046528419ceba56e5baff83) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_01_aegyosal.png using Guid(8f7fd72bc046528419ceba56e5baff83) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c09945dd239a32ae91531a6f188fa63a') in 0.089611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_MetallicSmoothness.png
  artifactKey: Guid(b5114c6e989dc614e9c2a3ff3e92a719) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_MetallicSmoothness.png using Guid(b5114c6e989dc614e9c2a3ff3e92a719) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed1a8744c2aaabb552c842193997f5fb') in 0.1280365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/IdaFaber/Textures/Base/T_SKIN_ScarAtlas.png
  artifactKey: Guid(cb877dede25a99e4ca16029533bebc1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_SKIN_ScarAtlas.png using Guid(cb877dede25a99e4ca16029533bebc1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0ba9901621f70ef7c2b2d96d6d01f68') in 0.0765133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.059379 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)FluidArea.prefab
  artifactKey: Guid(3b2a872352c65b444b94cf4fc53a49b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)FluidArea.prefab using Guid(3b2a872352c65b444b94cf4fc53a49b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c876b27b17d47b2cd6c5c73adb79426') in 0.1367997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_01.png
  artifactKey: Guid(b5eab45a417103b46a4f3166a5241518) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_01.png using Guid(b5eab45a417103b46a4f3166a5241518) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10dc587426140f7d6741bf5929ea512c') in 0.0537042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000790 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SimpleCenterSplash_4x4_01.psd
  artifactKey: Guid(e1320e25e761af64b81c02358edaf061) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_SimpleCenterSplash_4x4_01.psd using Guid(e1320e25e761af64b81c02358edaf061) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3546117a384f263b8c26f5c88095d7af') in 0.0777771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_VerticalSplash_4x4_02.psd
  artifactKey: Guid(8489687f2af98b3469347a6adeb82b98) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_VerticalSplash_4x4_02.psd using Guid(8489687f2af98b3469347a6adeb82b98) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f810b64c497cf1fa9df130f1eddf2523') in 0.0770009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000289 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWater_01_3x4.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralWater_01_3x4.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b885119c5e05bc9c03073bb6d596331') in 0.0821179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Char/Rubfish_CharBasicShader.shadergraph
  artifactKey: Guid(a86bdb95d0702f740b803b1a8185663d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Char/Rubfish_CharBasicShader.shadergraph using Guid(a86bdb95d0702f740b803b1a8185663d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cad3a8fc9cc5491ddbdc816d3f4ffad8') in 0.0390244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FrontWave_01.png
  artifactKey: Guid(4983c16eff0819648884f068fc3d8672) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FrontWave_01.png using Guid(4983c16eff0819648884f068fc3d8672) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd322808d9f444f21c94617b6f9db2e77') in 0.0527909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab
  artifactKey: Guid(048e6d455f851f140bf5df0f43f7747e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleDots.prefab using Guid(048e6d455f851f140bf5df0f43f7747e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '607ca2838df91a3f800e30682c23b262') in 0.0663559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MiniSplash_2x6_01.psd
  artifactKey: Guid(83e526f6131e28048814325e050eac04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_MiniSplash_2x6_01.psd using Guid(83e526f6131e28048814325e050eac04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '572bd33c630faa8dc428ec14aa3303c3') in 0.0837484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Char/Char_TX_Dummy_01.mat
  artifactKey: Guid(7c23a0eb65e12eb4c9cb73820b3462aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Char/Char_TX_Dummy_01.mat using Guid(7c23a0eb65e12eb4c9cb73820b3462aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5897c58cfcc17c4071a1151ad637a16e') in 0.0621439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000219 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Gradient/FX_TX_Gradients_Blue_01.psd
  artifactKey: Guid(e871620916655de49bea9c6d12267c00) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Gradient/FX_TX_Gradients_Blue_01.psd using Guid(e871620916655de49bea9c6d12267c00) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b81dbfcf9f0e7c8efdfeccb61f151a1') in 0.053958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000094 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/fx_tx_bubbles_2x2_01.png
  artifactKey: Guid(f60305698f7cd5f499c85626598e6867) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/fx_tx_bubbles_2x2_01.png using Guid(f60305698f7cd5f499c85626598e6867) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2b52f57df7eee5ad6d74f163351efc16') in 0.0629414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_3x4_01_SoftRender.psd
  artifactKey: Guid(91a12ec4d8d854a49a1c47ea3135b592) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_TiledWater_3x4_01_SoftRender.psd using Guid(91a12ec4d8d854a49a1c47ea3135b592) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '104672f5e4b9b4ee1eb28951181027eb') in 0.0717633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactShapes_Sharp_01.png
  artifactKey: Guid(bc5e872c242cd5142983b34b5f9ee603) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_ImpactShapes_Sharp_01.png using Guid(bc5e872c242cd5142983b34b5f9ee603) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2dc5ad842ed9fb0d1dd00119e365848') in 0.0521376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.458095 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_02.png
  artifactKey: Guid(7372085d1739c524a9fd06830a6e3206) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_02.png using Guid(7372085d1739c524a9fd06830a6e3206) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1e570c2ebfc35cee306f41fcf9f17e4') in 0.0860286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_02.png
  artifactKey: Guid(75d3466497a12fc4ea2e924c5df350eb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_MainSlash_4x4_02.png using Guid(75d3466497a12fc4ea2e924c5df350eb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb920cd077fccaef4e3d32138062eec5') in 0.0680775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue.png
  artifactKey: Guid(79f7c3b689801724cb4d892de53f670f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Blue.png using Guid(79f7c3b689801724cb4d892de53f670f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd0249bcc9baa1d02da3460bd11e225f') in 0.1335451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x5_04.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralSplash_2x5_04.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76c34bc135a5f71d30db167b509268c2') in 0.1078987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundFoam_4x8_01.png
  artifactKey: Guid(a8c28149c8194ad41b5ba36a5f69c525) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundFoam_4x8_01.png using Guid(a8c28149c8194ad41b5ba36a5f69c525) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c76b201d1c8a1e4094e282f6420c66ae') in 0.052344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_White_Ombre_alpha.TGA
  artifactKey: Guid(a24131730467df04ebeffb4f17927d44) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_White_Ombre_alpha.TGA using Guid(a24131730467df04ebeffb4f17927d44) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4685d3e8c7dc336067f4ff1525506f1e') in 0.0798569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_ambient.png
  artifactKey: Guid(3ebf7034b03c7154489e88d918c6269a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_ambient.png using Guid(3ebf7034b03c7154489e88d918c6269a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fdc831b803fe7d40668b6418b6e18ff8') in 0.0518001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_18.mat
  artifactKey: Guid(f656065773931f54b8aab80940ae73aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_18.mat using Guid(f656065773931f54b8aab80940ae73aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08c25aa0789b0e19641375eacfa1cbc4') in 0.9585992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Orange_Ombre.png
  artifactKey: Guid(f99bcda3c1734054894077a2a77ab230) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Orange_Ombre.png using Guid(f99bcda3c1734054894077a2a77ab230) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e941c17870230007cb3f6533a1438412') in 0.0620114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Red_Ombre_alpha.png
  artifactKey: Guid(8e81456b62907db4d9b3a607d28c64bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_BaseColor_Red_Ombre_alpha.png using Guid(8e81456b62907db4d9b3a607d28c64bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '20e4f040cd9e21566c0ff7e37632269c') in 0.1424845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000218 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity
  artifactKey: Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity using Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e474ebb8720f850832648547b1d5c364') in 9.1309888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_BW_Rolling_StandUp.FBX
  artifactKey: Guid(ed5d1215b85b2a1409858dae01f8efd3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_BW_Rolling_StandUp.FBX using Guid(ed5d1215b85b2a1409858dae01f8efd3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3691c5e4ed7bc657a9d014bfac9d0d80') in 0.0866693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_ALL_Inplace.FBX
  artifactKey: Guid(0750f4b9580eb1e4593b9f33932a024b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_ALL_Inplace.FBX using Guid(0750f4b9580eb1e4593b9f33932a024b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1677c4d96522fc30497b1744c9c811cc') in 0.059582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_B.FBX
  artifactKey: Guid(540afbf51bfe6564b8bd85af0ec82b3e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Big_ver_B.FBX using Guid(540afbf51bfe6564b8bd85af0ec82b3e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5599276d246abd22f2d5a8b447d0d961') in 0.080979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Down_StandUp.FBX
  artifactKey: Guid(bf1b6ab6cf8c1594fa085163c00423b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Down_StandUp.FBX using Guid(bf1b6ab6cf8c1594fa085163c00423b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '033af5f6cb36f03af209082dacb704d7') in 0.0536631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000828 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Root.FBX
  artifactKey: Guid(2f779da1823f7b546a1155a8df1ead6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Root.FBX using Guid(2f779da1823f7b546a1155a8df1ead6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c68641023010714ca7814b0e894ee9e') in 0.0594925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000209 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B.FBX
  artifactKey: Guid(c4e25880db7b0734b9cb89c8844af5c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B.FBX using Guid(c4e25880db7b0734b9cb89c8844af5c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3025d5752b99ba4f0292cfae7e3ed99e') in 0.0568838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop.FBX
  artifactKey: Guid(3166361ab808dc343a2e90b9656cc8ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/6__Jump/M_katana_Blade@Jump_Loop.FBX using Guid(3166361ab808dc343a2e90b9656cc8ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '319dc0e4121d47cdaf517d71edbfef49') in 0.1090046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Run_Fast_Dodge_Left.FBX
  artifactKey: Guid(044c5a94fd6e2de418677db856ac2c25) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Run_Fast_Dodge_Left.FBX using Guid(044c5a94fd6e2de418677db856ac2c25) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '181e11860c8a0c26a451844514b723de') in 0.0742008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_ALL_Inplace.FBX
  artifactKey: Guid(9caf229d8dfedcf4382f84d2dadaa6f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_ALL_Inplace.FBX using Guid(9caf229d8dfedcf4382f84d2dadaa6f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fb8a04f9550d5debaffcd34def0a57f') in 0.0707493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5.FBX
  artifactKey: Guid(2d69fe3d8a2363c4089d058b918b120b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5.FBX using Guid(2d69fe3d8a2363c4089d058b918b120b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd76fb2321eb558834f108dfda0b1a1e1') in 0.061477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Down_Loop.FBX
  artifactKey: Guid(1e881401c4fd685478d457f4d86bc8b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Down_Loop.FBX using Guid(1e881401c4fd685478d457f4d86bc8b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91db6aa8ba370baad37b66f19d1dd709') in 0.2699416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5_Attach_ZeroHeight.FBX
  artifactKey: Guid(d03fe99b627f24a499aec653f4f475ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_5_Attach_ZeroHeight.FBX using Guid(d03fe99b627f24a499aec653f4f475ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4cf93c9907ce93b611d917567be93159') in 0.098378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_01.png
  artifactKey: Guid(17e456dee4dfb8a438ed54d42b0323e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_01.png using Guid(17e456dee4dfb8a438ed54d42b0323e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6526dbb1365e13120cb34bdb146dec3') in 0.0647909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Dodge_Front.FBX
  artifactKey: Guid(2623cfadbe3de8542b5092f93192627b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/8__Dodge/M_katana_Blade@Dodge_Front.FBX using Guid(2623cfadbe3de8542b5092f93192627b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d29b8f2843eeb1515694ce98c104c17') in 0.0757828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ArchWall_01_Splines_22.fbx
  artifactKey: Guid(abc00000000015799274548275702329) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ArchWall_01_Splines_22.fbx using Guid(abc00000000015799274548275702329) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b4541268743a1dc6bda8ce061d38101') in 0.1230024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Terracotta.mat
  artifactKey: Guid(abc00000000009141264405808251767) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Terracotta.mat using Guid(abc00000000009141264405808251767) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8df9bd38c684dcd1c0f34ba2c62cd820') in 0.1066408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_TavernSign.mat
  artifactKey: Guid(abc00000000000048986154355014043) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_TavernSign.mat using Guid(abc00000000000048986154355014043) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e85f345ecdd53a5b5937328ee93a9d17') in 0.1562795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fruit.mat
  artifactKey: Guid(abc00000000017826112794728260833) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Fruit.mat using Guid(abc00000000017826112794728260833) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6dfd5622b4cbf83db85a7ef35b7bc3a2') in 0.0960305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Slabs_03.mat
  artifactKey: Guid(abc00000000015405381821992586715) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Slabs_03.mat using Guid(abc00000000015405381821992586715) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10e3a28b0669f33d659af364d6ea6cd3') in 0.0866458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_02.prefab
  artifactKey: Guid(f863d9e8791748141a0cce0fb6d9eb3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_RandomisedSpline_C_02.prefab using Guid(f863d9e8791748141a0cce0fb6d9eb3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed25fea4c860f17457cd5c42ca31b0bf') in 0.1678374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Floor.mat
  artifactKey: Guid(abc00000000001525448253452710529) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Floor.mat using Guid(abc00000000001525448253452710529) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d9c0f7af03c47cff3570f6295c1d5e7') in 0.1070848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.001004 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Green.mat
  artifactKey: Guid(abc00000000010619635596411464873) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Market_Green.mat using Guid(abc00000000010619635596411464873) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed8b52a5c6a2fc66135ef5554ccf08b3') in 0.138056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Moss.mat
  artifactKey: Guid(abc00000000013168663486102302862) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Moss.mat using Guid(abc00000000013168663486102302862) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca01d51489eae3c97a54cbc75b17fae4') in 0.1274136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Lanterns.mat
  artifactKey: Guid(abc00000000012573495227337296161) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Lanterns.mat using Guid(abc00000000012573495227337296161) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '054790416033ef6ff7c9703ed688aa18') in 0.0836908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneDamage_02.mat
  artifactKey: Guid(abc00000000001852096774828944827) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneDamage_02.mat using Guid(abc00000000001852096774828944827) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'db3dc4bf449fd273bec55c9e9ead72c7') in 0.1251685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BannerPole_01.prefab
  artifactKey: Guid(abc00000000017523910351110215581) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BannerPole_01.prefab using Guid(abc00000000017523910351110215581) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8e09116ea5e2b261642098d201beb61') in 0.0884454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_08.prefab
  artifactKey: Guid(abc00000000004352778982903466030) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_08.prefab using Guid(abc00000000004352778982903466030) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8415d38d5c374364467e1a95634b0b9e') in 0.0781016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_2.prefab
  artifactKey: Guid(abc00000000011861529990532398266) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_2.prefab using Guid(abc00000000011861529990532398266) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d0aa6795b15aed9c190d7cad2e4625a') in 0.0827713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_2.prefab
  artifactKey: Guid(abc00000000003552623074302713318) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_2.prefab using Guid(abc00000000003552623074302713318) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0014ebc2b0ac633d4785fdd67d8cf563') in 0.0843235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_02.prefab
  artifactKey: Guid(abc00000000003958368600663530461) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_02.prefab using Guid(abc00000000003958368600663530461) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4eee2bd0c4471282c398d6347ac881f') in 0.0800411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_22.prefab
  artifactKey: Guid(abc00000000010477518808022030609) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_22.prefab using Guid(abc00000000010477518808022030609) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23586f46b599d7a5d119e1e582ac142e') in 0.108075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01.prefab
  artifactKey: Guid(abc00000000009081137454623230966) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_01.prefab using Guid(abc00000000009081137454623230966) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0733d7bea922811a17ac60b78c1061f') in 0.0714521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_12.prefab
  artifactKey: Guid(4cb2c5a61df84f840961e0691c184bca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_12.prefab using Guid(4cb2c5a61df84f840961e0691c184bca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0644f1e3f679d8bc9c8390b600bb1765') in 0.1485167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_02.prefab
  artifactKey: Guid(abc00000000017755707725320845443) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_02.prefab using Guid(abc00000000017755707725320845443) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6af766472414931e1b1bdb815a8ac9f2') in 0.0875226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_03.prefab
  artifactKey: Guid(abc00000000004819125062046549367) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_WallTopper_A_03.prefab using Guid(abc00000000004819125062046549367) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76afdc654ad0adc28ca3a9f0df83eee7') in 0.0666332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_04.prefab
  artifactKey: Guid(abc00000000009266294131336485834) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_04.prefab using Guid(abc00000000009266294131336485834) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e0dab23c013c274a024a2b03bf001568') in 0.0663751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bowl_01.prefab
  artifactKey: Guid(abc00000000004828717454480502761) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bowl_01.prefab using Guid(abc00000000004828717454480502761) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89defe4cc02d4fa688a690f091422ad1') in 0.0611683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.FBX
  artifactKey: Guid(64d4b71f20b353845934fb20822b3177) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_32.FBX using Guid(64d4b71f20b353845934fb20822b3177) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b670c38fc75ec82b31b570cfb750bf6') in 0.8590304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_04.prefab
  artifactKey: Guid(abc00000000017595615307471600298) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_04.prefab using Guid(abc00000000017595615307471600298) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ec0a5821ad54df6d0177fdc415d4eee') in 0.0617738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_A.mat
  artifactKey: Guid(abc00000000015730279596845295603) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_A.mat using Guid(abc00000000015730279596845295603) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1ea9095fe855ee1102b7f47ed68bb47') in 0.1084427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_19.prefab
  artifactKey: Guid(abc00000000005245857699689613390) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_19.prefab using Guid(abc00000000005245857699689613390) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '01d8b446daddde1c1be6bb923087b7b9') in 0.0881919 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_40.prefab
  artifactKey: Guid(abc00000000015688684306399976053) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_40.prefab using Guid(abc00000000015688684306399976053) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '975224d6f596dbf0bfdea4273ef32462') in 0.1315502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockBeam_01.prefab
  artifactKey: Guid(abc00000000000117202645433188285) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockBeam_01.prefab using Guid(abc00000000000117202645433188285) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'add10667c0ab259c319ccfbfcf978ab3') in 0.0580834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_01.prefab
  artifactKey: Guid(abc00000000001138482080542250624) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ConicalCauldron_01.prefab using Guid(abc00000000001138482080542250624) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4f790cd192d3c0de63df479092f575a') in 0.0687443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_26.prefab
  artifactKey: Guid(abc00000000016998756364939789157) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_26.prefab using Guid(abc00000000016998756364939789157) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0896885a8b8b485f491e375c94bfbd5e') in 0.094555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_02.prefab
  artifactKey: Guid(abc00000000018238010764999236038) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Door_02.prefab using Guid(abc00000000018238010764999236038) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e16dee10b4a12bda38767bf35478ea9') in 0.0622946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_01.prefab
  artifactKey: Guid(abc00000000005328908562217007902) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crate_Lid_A_01.prefab using Guid(abc00000000005328908562217007902) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c329d514e2aee4b9e59170f70cebaa5b') in 0.0740735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_A.prefab
  artifactKey: Guid(abc00000000007748233610491119230) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_A.prefab using Guid(abc00000000007748233610491119230) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8249d2c8baf7adda25b3900f12042c0') in 0.0598285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long_Loose.prefab
  artifactKey: Guid(abc00000000017222846733412788914) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_Long_Loose.prefab using Guid(abc00000000017222846733412788914) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed779383dfe0cda487708185862e38cc') in 0.0679183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_10.fbx
  artifactKey: Guid(abc00000000012412623444775483619) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_10.fbx using Guid(abc00000000012412623444775483619) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a69cd668d512bcabae17bbe05d005fc') in 0.0706724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_5.fbx
  artifactKey: Guid(abc00000000000635129406053119061) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_5.fbx using Guid(abc00000000000635129406053119061) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd350c3fbb547e33eb7b1bf2d151ccf7d') in 0.0677593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_10.prefab
  artifactKey: Guid(abc00000000003391889486116700163) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_10.prefab using Guid(abc00000000003391889486116700163) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e77d213adf8f4e98500d649ab251c84c') in 0.0839126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Horse_Shoe.prefab
  artifactKey: Guid(abc00000000012257470158374124914) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Horse_Shoe.prefab using Guid(abc00000000012257470158374124914) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '284fe39d3152de0f1fbec5ba284fd171') in 0.0839516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Masked Poses/<EMAIL>
  artifactKey: Guid(a92ad04671c1e2c42ab2feb0e9ac684c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Masked Poses/<EMAIL> using Guid(a92ad04671c1e2c42ab2feb0e9ac684c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44b00c94251b148e2ab561a0aaab939e') in 0.0766652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01_1.prefab
  artifactKey: Guid(abc00000000003611456838156076667) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_01_1.prefab using Guid(abc00000000003611456838156076667) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e552cc50617a0cbd82a362280513b94c') in 0.0753605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000382 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern_Hanging.prefab
  artifactKey: Guid(abc00000000006982472483048244726) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern_Hanging.prefab using Guid(abc00000000006982472483048244726) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c463fd2f666b594a6a433455b3f6ff10') in 0.0651161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000713 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M.prefab
  artifactKey: Guid(abc00000000009676534734759330999) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M.prefab using Guid(abc00000000009676534734759330999) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a3483d032dcbc3372865e4a02376a29') in 0.0656715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01_1.prefab
  artifactKey: Guid(abc00000000016605019329462927557) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01_1.prefab using Guid(abc00000000016605019329462927557) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50e7052074a9ac2b59bea46729c25bcb') in 0.0611237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M_1.prefab
  artifactKey: Guid(abc00000000007203461997630695151) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x4M_1.prefab using Guid(abc00000000007203461997630695151) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc5cc1862e219f82cf92099eb7b35412') in 0.0579407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_05.prefab
  artifactKey: Guid(abc00000000011305282193036375362) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_05.prefab using Guid(abc00000000011305282193036375362) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '276c76b96e17d434175df93863961a31') in 0.0588368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000172 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_01.prefab
  artifactKey: Guid(abc00000000010042614864576633131) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_01.prefab using Guid(abc00000000010042614864576633131) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b813ede73800e693130e110fd451c8ef') in 0.0523953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_A.prefab
  artifactKey: Guid(abc00000000006595051334539448741) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_A.prefab using Guid(abc00000000006595051334539448741) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56498d0a49fe4f3166cbef90b9632f9f') in 0.0613758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_09.prefab
  artifactKey: Guid(abc00000000010456034354059906739) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_09.prefab using Guid(abc00000000010456034354059906739) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30ed1d68b4e2ab7f0ed1cb455f666731') in 0.0639918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Top.prefab
  artifactKey: Guid(abc00000000013515879934136038546) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Top.prefab using Guid(abc00000000013515879934136038546) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03c2267cb1fdbea5e074185bea220737') in 0.0657402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.FBX
  artifactKey: Guid(9af0d11b5ae4a5e408d76a9e9616755c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_18.FBX using Guid(9af0d11b5ae4a5e408d76a9e9616755c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd476167d3ed9845ecbbef683499b7ab9') in 0.0753684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_01.prefab
  artifactKey: Guid(abc00000000006264877392217652874) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_L_01.prefab using Guid(abc00000000006264877392217652874) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b87f43e579eebc56457c4f7c46b3946a') in 0.0985477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_04.prefab
  artifactKey: Guid(abc00000000017552042681331075390) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_04.prefab using Guid(abc00000000017552042681331075390) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc3f3d504759f62bb4a199f4b060a2c0') in 0.0961398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_A.prefab
  artifactKey: Guid(abc00000000015566476325916018874) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_A.prefab using Guid(abc00000000015566476325916018874) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb56e5b8c75b69f017e7d87f259153ef') in 0.0615634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_BrickKit.shadergraph
  artifactKey: Guid(22690df2bbe7a4647a451cb386dd4169) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_BrickKit.shadergraph using Guid(22690df2bbe7a4647a451cb386dd4169) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf23af8e4f7867a6c5739d43a3115327') in 0.057286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_2.prefab
  artifactKey: Guid(abc00000000006084286906293382597) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap_2.prefab using Guid(abc00000000006084286906293382597) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bed206dbc8753eb5c4b3a4084cd5633') in 0.0622034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stair_Support_2M.prefab
  artifactKey: Guid(abc00000000012634657579491898552) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Stair_Support_2M.prefab using Guid(abc00000000012634657579491898552) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86f5fdd62f94cd8b52bdc09289c7f90f') in 0.0685524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_03.prefab
  artifactKey: Guid(abc00000000018284835566804858819) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_03.prefab using Guid(abc00000000018284835566804858819) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94e3a88bdf0c9299f0f1d63c7cb74c0a') in 0.0689478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_2.prefab
  artifactKey: Guid(abc00000000002762296274349397846) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_2.prefab using Guid(abc00000000002762296274349397846) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '217c46656db579c3f00f09ad02f90196') in 0.0640548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_02.prefab
  artifactKey: Guid(abc00000000010276451470589532804) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_1x4_02.prefab using Guid(abc00000000010276451470589532804) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '102e3989eae571bd426e6ff6e185145b') in 0.0653176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_01.prefab
  artifactKey: Guid(abc00000000000047688043517622466) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_01.prefab using Guid(abc00000000000047688043517622466) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5740c2edd62a81e8ee9e71e208ef3d70') in 0.0602213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_1.prefab
  artifactKey: Guid(abc00000000011812170210108673851) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_1.prefab using Guid(abc00000000011812170210108673851) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81edcd089aaeb5e59c5020055f1d6c04') in 0.0577474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_D.PNG
  artifactKey: Guid(f5d00e685d3573b408186437e513484c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_01_D.PNG using Guid(f5d00e685d3573b408186437e513484c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9428ddbc2e78c329a6cdcdb8a51b5ec') in 0.0692491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_MASK.PNG
  artifactKey: Guid(82f1860b892174246813ddf92d1bba7f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_MASK.PNG using Guid(82f1860b892174246813ddf92d1bba7f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c2996c3e2c615664d461edd4fa84565') in 0.0718264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_Normal.PNG
  artifactKey: Guid(e624dd9d31d95d746aeb894684574f30) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flag_02_Normal.PNG using Guid(e624dd9d31d95d746aeb894684574f30) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dbb23eb1d96b9ff44ab87583075c247') in 0.0500855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_Snow_M.PNG
  artifactKey: Guid(5c3c0590b0069f043b4beadefca307cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_01_Snow_M.PNG using Guid(5c3c0590b0069f043b4beadefca307cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e4280a80ddd803ad5a9a9f6d72dc214') in 0.0646918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle.prefab
  artifactKey: Guid(abc00000000008498653728592441981) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Table_Rectangle.prefab using Guid(abc00000000008498653728592441981) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '713138fe10ecfcb3aa9c93c6e9ada6ae') in 0.058248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Potato.prefab
  artifactKey: Guid(abc00000000017693825223293799880) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Potato.prefab using Guid(abc00000000017693825223293799880) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd231b49ce25a4045fa5fa2b5a2f0b0e5') in 0.060667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Top_1.prefab
  artifactKey: Guid(abc00000000005093376879765640058) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Front_Top_1.prefab using Guid(abc00000000005093376879765640058) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34f05f2d98269433d14663c95bb69a1c') in 0.0604587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_03.PNG
  artifactKey: Guid(d844fb143b4272e48b0d904ef0a65f63) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_mask_dirt_03.PNG using Guid(d844fb143b4272e48b0d904ef0a65f63) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f97c4db003800ef3ce86a176f4589d0b') in 0.0652883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_normal.PNG
  artifactKey: Guid(4766c88ae63ecd44b8df6e5174238637) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ForestGround_normal.PNG using Guid(4766c88ae63ecd44b8df6e5174238637) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1aeade3aad48075855073594b8384be5') in 0.0772777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_D.PNG
  artifactKey: Guid(d8f07e77388d65a4d9d8aaf904c80469) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_D.PNG using Guid(d8f07e77388d65a4d9d8aaf904c80469) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af24a95a8702906be31a41e1f7abe7fe') in 0.0689269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_MetalTiling_01_BaseColor.PNG
  artifactKey: Guid(90e1b98a89d129e48804a4df952e6e66) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_MetalTiling_01_BaseColor.PNG using Guid(90e1b98a89d129e48804a4df952e6e66) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de1e87fce117d1794472df075b508845') in 0.051522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_basecolor.PNG
  artifactKey: Guid(685775fff4637db44abb79e586b5a260) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_basecolor.PNG using Guid(685775fff4637db44abb79e586b5a260) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4a2fc03c193ef84f98242fdffef81c1a') in 0.056037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_normal.PNG
  artifactKey: Guid(b1dc8332c0a82d246892e1a131922a1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_normal.PNG using Guid(b1dc8332c0a82d246892e1a131922a1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89a53097ed5d0a93385b0df62a6a5942') in 0.0702849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG_3.PNG
  artifactKey: Guid(79ce71a45bea0264e99629120814be21) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG_3.PNG using Guid(79ce71a45bea0264e99629120814be21) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '216bf0f2c468dd6ed374af188d487baf') in 0.0503205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_BaseColor.PNG
  artifactKey: Guid(19ce40b2900e850449821b15f6f7da9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_BaseColor.PNG using Guid(19ce40b2900e850449821b15f6f7da9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc6e15570df3187b5dce74e2de209e62') in 0.0587221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_ORMH.PNG
  artifactKey: Guid(f5fcd6fe4cfeed745ad4f6ee94b26d04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_ORMH.PNG using Guid(f5fcd6fe4cfeed745ad4f6ee94b26d04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67e2674dd374802d24de0aaab7d9b213') in 0.0538375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_basecolor.PNG
  artifactKey: Guid(6cf132df719f6e946adfdd5efeb09d76) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_basecolor.PNG using Guid(6cf132df719f6e946adfdd5efeb09d76) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1f0ab6bf89c8a1a0c869ffe2d3bb451') in 0.0657318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDamage_basecolor.PNG
  artifactKey: Guid(7651affdadbbf724d8d9ea7fffb4ad4e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDamage_basecolor.PNG using Guid(7651affdadbbf724d8d9ea7fffb4ad4e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9abc95542c60679977c3dc09540de63') in 0.0484473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Water01_N.TGA
  artifactKey: Guid(1da1b304098cb6547a58bab9c45ec8b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Water01_N.TGA using Guid(1da1b304098cb6547a58bab9c45ec8b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75a51b0f092122bfd15518299dcd7f41') in 0.0728139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterCaustic_E.TGA
  artifactKey: Guid(a013e69cbaff35545a8293b0d20d2a69) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WaterCaustic_E.TGA using Guid(a013e69cbaff35545a8293b0d20d2a69) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc6adc729b12a3728f82da593f51daea') in 0.0455066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000106 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Right.controller
  artifactKey: Guid(dc9ef3ef925ef4f4aaca849afe8bf77b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_Right.controller using Guid(dc9ef3ef925ef4f4aaca849afe8bf77b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e5f35595817a21ed002e23c3d810d84') in 0.0440054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/ThirdPersonCameraController.cs
  artifactKey: Guid(1e74dbb2e7dbd1b47bc6ed0dfe4103bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Camera/ThirdPersonCameraController.cs using Guid(1e74dbb2e7dbd1b47bc6ed0dfe4103bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '588b063fcd59b17ded72f61d8d678c90') in 0.0298988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Jump01 [RM].controller
  artifactKey: Guid(3a52e43da1b6b604c8ed63485967953f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Jump01 [RM].controller using Guid(3a52e43da1b6b604c8ed63485967953f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '213ab85d4c4d03c3c9bb6b9a65f34686') in 0.0319087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_normal.PNG
  artifactKey: Guid(8a818951e6160f14fb2fe2395fe50806) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Wood_02_normal.PNG using Guid(8a818951e6160f14fb2fe2395fe50806) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a35ef28518db7747767e7f1115a1be32') in 0.0497328 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Forward.controller
  artifactKey: Guid(27625ccad7bdcec42b99626637b8d1cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Walk01_Forward.controller using Guid(27625ccad7bdcec42b99626637b8d1cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '628021dd94cf37b2cddb5d4f64fdd9da') in 0.0370138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(ede3b2daf5145564fa8880d33b74c121) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(ede3b2daf5145564fa8880d33b74c121) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75c342065e2ae8c50bdac2a38e0b26b9') in 0.0477353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL>
  artifactKey: Guid(3da359268264e0d4db38c1ce951c30ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL> using Guid(3da359268264e0d4db38c1ce951c30ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a301ec3f17734c25b943964017156fd8') in 0.0592903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_Backward.controller
  artifactKey: Guid(b0fd912b264479d4caab7d086e90f5e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_Backward.controller using Guid(b0fd912b264479d4caab7d086e90f5e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d2bcbcde2acd27de62437e6680c9e86') in 0.0340231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/GravityArea.cs
  artifactKey: Guid(5f5851b6d19a0e74199e338fd20802d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/GravityArea.cs using Guid(5f5851b6d19a0e74199e338fd20802d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf044ed96fa59ceafe9f968c42b50f8e') in 0.0332261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_Moss_normal.PNG
  artifactKey: Guid(4ccc4bf0386170c4bb27cc85c4fad198) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RoofTile_Moss_normal.PNG using Guid(4ccc4bf0386170c4bb27cc85c4fad198) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f0790f05f1efbd82755a5f406362da9') in 0.0686791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_grass_RMA.PNG
  artifactKey: Guid(27dcabbb14169ce4aaa031b37d087f23) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_grass_RMA.PNG using Guid(27dcabbb14169ce4aaa031b37d087f23) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7094aa51e9ee626c9b2cd86afb4cb0ca') in 0.0686765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_A.FBX
  artifactKey: Guid(2d297b48b646d344d8624368bceb7a20) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_A.FBX using Guid(2d297b48b646d344d8624368bceb7a20) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73b2a47ec152a7e700719bb2e196f33a') in 0.0829191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Coin_2.wav
  artifactKey: Guid(e07f061a6eba2874997249736347a621) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Coin_2.wav using Guid(e07f061a6eba2874997249736347a621) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '840a3b0c6465c8bc3ed4c8263ee023b0') in 0.1611775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_A.FBX
  artifactKey: Guid(ee26c0f6fb6e0144f9f34d1dd2a89abc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_A.FBX using Guid(ee26c0f6fb6e0144f9f34d1dd2a89abc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '195ae7e353e12401437f364770d8a752') in 0.0602386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Run_A.FBX
  artifactKey: Guid(8f9fba14332ad944e896ebb62ba027b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_B_to_Run_A.FBX using Guid(8f9fba14332ad944e896ebb62ba027b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b21bc3355f59ca98a7cfd7e00027de06') in 0.066401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_B.FBX
  artifactKey: Guid(0e8eeef0e8fd7ff4f8b7faae63752650) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_B_to_Walk_B.FBX using Guid(0e8eeef0e8fd7ff4f8b7faae63752650) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aac2881746e2d7ee232f5080b40591bc') in 0.0666163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_A_Root.FBX
  artifactKey: Guid(d3fa14c7a71153241bea00733e955530) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Walk_to_Idle/M_Big_Sword@Walk_To_Idle_ver_A_Root.FBX using Guid(d3fa14c7a71153241bea00733e955530) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75731ac0d55867a1728c0772a4a4c2ec') in 0.0650106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Walk_A_Turn_L90.FBX
  artifactKey: Guid(d7bfc956cb7849746a472f3fd7adaf71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Idle_To_Walk_A_Turn_L90.FBX using Guid(d7bfc956cb7849746a472f3fd7adaf71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1de61c0e82b3e5f7c94dede66fd396a6') in 0.0782708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Melodic_1.wav
  artifactKey: Guid(816f6f503c5eb7e4fba268d9add4876f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Melodic_1.wav using Guid(816f6f503c5eb7e4fba268d9add4876f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5437a138a5e746296b2fc55f0fa5028c') in 0.1071914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_BL45_Root.FBX
  artifactKey: Guid(01d8c8e64d73fbf46bcd8e9da1dc7955) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_BL45_Root.FBX using Guid(01d8c8e64d73fbf46bcd8e9da1dc7955) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b5f2ef4c52f321d1b5337c6ee4740fd') in 0.05935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Walk_A_Turn_R90_Root.FBX
  artifactKey: Guid(5542da8f1ab189e4c8fe387a5396006a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Walk_A_Turn_R90_Root.FBX using Guid(5542da8f1ab189e4c8fe387a5396006a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '016feaa1ba55799f61f3ff87e42a4b15') in 0.0733072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R90_Root.FBX
  artifactKey: Guid(7111b28f8ee90a54ca2b64c641ae3e32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_R90_Root.FBX using Guid(7111b28f8ee90a54ca2b64c641ae3e32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e95addfd9227f2aac1ca9a46aa5f25e2') in 0.0544869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L45.FBX
  artifactKey: Guid(2eea8ce3082710e42b1fa2599ac27982) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Front_L45.FBX using Guid(2eea8ce3082710e42b1fa2599ac27982) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f0f015234f85d83ce03624d951800ef') in 0.0550144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Walk_A_Turn_L90_Root.FBX
  artifactKey: Guid(46c5034f414b54e4c895e3dd4631bf2c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Walk_A_To_Walk_A_Turn_L90_Root.FBX using Guid(46c5034f414b54e4c895e3dd4631bf2c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ae17a85c677b426764c6fa5ca1eb74d') in 0.0808825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Walk_A_Root.FBX
  artifactKey: Guid(1c99625553bebc94f93dbbcc8e82020c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Walk_A_Root.FBX using Guid(1c99625553bebc94f93dbbcc8e82020c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7110be73a848be9123d1ade8b0f11a4a') in 0.0622799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_L90.FBX
  artifactKey: Guid(485a912c399d33c4c9409cd6b3cc65c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Back_L90.FBX using Guid(485a912c399d33c4c9409cd6b3cc65c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab75388b1874f2f0cd1a2661d5121882') in 0.0532664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_B_Turn_L90_Root.FBX
  artifactKey: Guid(09834a7598c91e44a8b9baf50c05266a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_B_Turn_L90_Root.FBX using Guid(09834a7598c91e44a8b9baf50c05266a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2cdba385d784c5375f6ff5805dc96e86') in 0.0750988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L90.FBX
  artifactKey: Guid(c6227842a5d532046bfa82fb70bbaf03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L90.FBX using Guid(c6227842a5d532046bfa82fb70bbaf03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52129488343a5a2e1ff57141d0f8a699') in 0.0630799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_B_Root.FBX
  artifactKey: Guid(9fac66104b558744fab5f8b8a669c091) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_A_To_Idle_ver_B_Root.FBX using Guid(9fac66104b558744fab5f8b8a669c091) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da86aaaba9777e739df19f191e390cce') in 0.0564104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90.FBX
  artifactKey: Guid(1ab969bb4346ea9438b959ef9c66091b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90.FBX using Guid(1ab969bb4346ea9438b959ef9c66091b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac11678140c6c921ac845c578e3b1e29') in 0.0587246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_B.FBX
  artifactKey: Guid(626f9f6624f7b8446802c12a70a73849) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_B.FBX using Guid(626f9f6624f7b8446802c12a70a73849) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '768a73e9baf0cd7585f24d6c02360d7b') in 0.0731035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BR45_Root.FBX
  artifactKey: Guid(b0d2665b92540fd43bd61b0a2a528489) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BR45_Root.FBX using Guid(b0d2665b92540fd43bd61b0a2a528489) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bf41322987af4f43fdf19fcc6cc35fe') in 0.0486768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FR45.FBX
  artifactKey: Guid(d483e6d2a40d81b41ab698826973b80d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_FR45.FBX using Guid(d483e6d2a40d81b41ab698826973b80d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '831642cf2767e866a07769628056db44') in 0.0592418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90.FBX
  artifactKey: Guid(6a1b5f9ecd71d6a4390d4b3b58de0361) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Jogging_B_Turn_L90.FBX using Guid(6a1b5f9ecd71d6a4390d4b3b58de0361) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0612b57dc67b7ecc8dcdf5742bf9c53') in 0.0686612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90.FBX
  artifactKey: Guid(55dd84ae07e0733428550dae82ec8834) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90.FBX using Guid(55dd84ae07e0733428550dae82ec8834) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fc92b0f508c5a425e2f05c0d3a9a0fd') in 0.0598358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Walk_B_Turn_L90.FBX
  artifactKey: Guid(7d27a4a2a77a0eb4282732d78db421c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_To_Walk_B_Turn_L90.FBX using Guid(7d27a4a2a77a0eb4282732d78db421c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb73306e3640c265b02224336c26cd23') in 0.0573736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_A_Root.FBX
  artifactKey: Guid(b3366d88c855dc54c9a2759444c5028c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_A_Root.FBX using Guid(b3366d88c855dc54c9a2759444c5028c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c72cd904b62eb9ce01956420f00fe5d') in 0.0684909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Walk_B_Turn_R90_Root.FBX
  artifactKey: Guid(9bebb73c55fbf2a49b24572a450c84e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Walk_B_Turn_R90_Root.FBX using Guid(9bebb73c55fbf2a49b24572a450c84e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '276adf4407fb6fd2b25371d865ef0bd7') in 0.0601506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Run_A.FBX
  artifactKey: Guid(c6e82760e647c374285249bf473204b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Run_A.FBX using Guid(c6e82760e647c374285249bf473204b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9368208fd4e5704c09ca0f4c8f83fd48') in 0.0604624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_B.FBX
  artifactKey: Guid(5531154b5b9e40e4783887368351e466) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Walk_B.FBX using Guid(5531154b5b9e40e4783887368351e466) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b592b06f51fdaf204d51354f42c0e504') in 0.0621888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_L90.FBX
  artifactKey: Guid(964773c4febf2844aa607df69c307080) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_L90.FBX using Guid(964773c4febf2844aa607df69c307080) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a656127df70931e81eb375f790485974') in 0.080574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_R90.FBX
  artifactKey: Guid(8610be469a9e1cf438f9c64364b5c6b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_A_To_Idle_ver_A_Turn_R90.FBX using Guid(8610be469a9e1cf438f9c64364b5c6b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf2edea8c96cdd615c470c431eb60c32') in 0.0657282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Jog_A_Root.FBX
  artifactKey: Guid(f268b5fb6678d92449c55ebc93f2d1d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Jog_A_Root.FBX using Guid(f268b5fb6678d92449c55ebc93f2d1d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbb3fdab2f709e9d12443200afa8c3c5') in 0.1644338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Walk_A_Turn_R90_Root.FBX
  artifactKey: Guid(43eb0cde03dc7074aa1e5f5e5f015250) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_A_To_Walk_A_Turn_R90_Root.FBX using Guid(43eb0cde03dc7074aa1e5f5e5f015250) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4faa53eb89e5d0f104a75cfb842a3237') in 0.1126475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Run_A_Root.FBX
  artifactKey: Guid(eb22da47dff1a3c4c8f5cc6b0f25b37c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Run_A_Root.FBX using Guid(eb22da47dff1a3c4c8f5cc6b0f25b37c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ad260abf9a966093af0529f0848f10e') in 0.076259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_Turn_Root.FBX
  artifactKey: Guid(3fd08dfecc3d5964b9ee2e46cec805d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_Turn_Root.FBX using Guid(3fd08dfecc3d5964b9ee2e46cec805d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '418a6015dcc451480b47ecbaa5e2d06a') in 0.0648927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_FR45_Root.FBX
  artifactKey: Guid(2d240a6f783c14d4baad7fa06b3276e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_FR45_Root.FBX using Guid(2d240a6f783c14d4baad7fa06b3276e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37604f36bdb7e3fae4dacfb14be291b3') in 0.0630689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_A.FBX
  artifactKey: Guid(0cb33a8bca45b9741909eaec27e95363) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_A.FBX using Guid(0cb33a8bca45b9741909eaec27e95363) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '115692e70dee6ff4e90e9968c6bfb106') in 0.0699662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_B_Root.FBX
  artifactKey: Guid(89a60c566cdbfcc42a5bb85072c31e97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_B_Root.FBX using Guid(89a60c566cdbfcc42a5bb85072c31e97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49783e0209d4fae43675defc855f51ca') in 0.0513711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_A.FBX
  artifactKey: Guid(0ab9dd13279256e459667a78eb5758d5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Jog_A.FBX using Guid(0ab9dd13279256e459667a78eb5758d5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c957f4e01989fb91b06eeed5b44ff17e') in 0.0616281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BL45.FBX
  artifactKey: Guid(bfcccce75310ed94f951147eea6f1290) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_BL45.FBX using Guid(bfcccce75310ed94f951147eea6f1290) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4bef5c9ae852d1179da03a34e5686509') in 0.0711722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L45.FBX
  artifactKey: Guid(939ccb1d2d065fc4194390079cc5557f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Front_L45.FBX using Guid(939ccb1d2d065fc4194390079cc5557f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d19939cbc08c4371b698949631a1471') in 0.0655626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_B.FBX
  artifactKey: Guid(6c46b6b7220f111498712dcc62f6e2d0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_B.FBX using Guid(6c46b6b7220f111498712dcc62f6e2d0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '852527c74491659cd89b4d3594592423') in 0.0617233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_Root.FBX
  artifactKey: Guid(bbb62ed1f43eebd428e0b1959c2dfd8f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_L90_Root.FBX using Guid(bbb62ed1f43eebd428e0b1959c2dfd8f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dd728c585d2620877066a14c392ea03') in 0.0733838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FR45_Root.FBX
  artifactKey: Guid(3221ecca15cc04c4ca51b60ff33f69cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FR45_Root.FBX using Guid(3221ecca15cc04c4ca51b60ff33f69cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef671961b2996128a70dc1418cbc2caa') in 0.0683486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_Root.FBX
  artifactKey: Guid(fdc93f0be1d9d2c44ad602c884d03aff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_Root.FBX using Guid(fdc93f0be1d9d2c44ad602c884d03aff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96d59afd44576cf86786dac6420772a6') in 0.0630748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_Root_vol2.FBX
  artifactKey: Guid(b5f843a881d240549947cbcbf8b53ef9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_Root_vol2.FBX using Guid(b5f843a881d240549947cbcbf8b53ef9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd0bf3a07af457cceebe802b7768479a') in 0.0852563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BlockB.fbx
  artifactKey: Guid(abc00000000002880236398370116423) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BlockB.fbx using Guid(abc00000000002880236398370116423) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89591d4f60702660291d498ef9e3abfa') in 0.0544916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_3x4_01.fbx
  artifactKey: Guid(abc00000000007140271365249181144) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_3x4_01.fbx using Guid(abc00000000007140271365249181144) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e4ee385414f94766fea4f527ac973ee') in 0.0713371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Bloking/SM_mountain.fbx
  artifactKey: Guid(abc00000000012960738077311051831) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Bloking/SM_mountain.fbx using Guid(abc00000000012960738077311051831) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57163d5bbd05e3a19091def95f0a143f') in 0.0855936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.fbx
  artifactKey: Guid(abc00000000015271687413939890015) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.fbx using Guid(abc00000000015271687413939890015) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e44c4062735cf12e2349246b902dd0a6') in 0.0677494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_1x4_02.fbx
  artifactKey: Guid(abc00000000011620263885016436263) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_1x4_02.fbx using Guid(abc00000000011620263885016436263) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8edc242388189535ceb8821461a84915') in 0.0860793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sledge_Hammer.fbx
  artifactKey: Guid(abc00000000013339058382193064927) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sledge_Hammer.fbx using Guid(abc00000000013339058382193064927) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4aee0b70df83c2a0a6db40afc62017eb') in 0.053975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sword_01.fbx
  artifactKey: Guid(abc00000000003389098702134213079) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Sword_01.fbx using Guid(abc00000000003389098702134213079) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dac58f229bd3ced80c9969ae5e86036e') in 0.0789378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Saw.fbx
  artifactKey: Guid(abc00000000009564398993020985055) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Saw.fbx using Guid(abc00000000009564398993020985055) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '415ceff91d275fd39d974ad93fc26971') in 0.0694011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cauldron.fbx
  artifactKey: Guid(abc00000000013187192525092738092) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Cauldron.fbx using Guid(abc00000000013187192525092738092) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b1380a6b3b3a92b07cad49e7dea512f') in 0.0658544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Bottle_01.fbx
  artifactKey: Guid(abc00000000000309903068215120380) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Bottle_01.fbx using Guid(abc00000000000309903068215120380) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f913f36ed3214d2ca803d96cc78538a') in 0.0696472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_01.fbx
  artifactKey: Guid(abc00000000006938260938916311203) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_01.fbx using Guid(abc00000000006938260938916311203) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c36fdf0489b2c5eb6bccfd4b9b0e2789') in 0.0671332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 - Begin.fbx
  artifactKey: Guid(b1844fbe628f5bf4ab29e6c68912a708) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 - Begin.fbx using Guid(b1844fbe628f5bf4ab29e6c68912a708) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1ed153e8ad903b38d761a182bbb2a92') in 0.0816101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/<EMAIL>
  artifactKey: Guid(1455f282db7117d419994bb5c5f3acc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/<EMAIL> using Guid(1455f282db7117d419994bb5c5f3acc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b718c8dcc416357d2e32d4c65799b022') in 0.0657507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_ForwardLeft.fbx
  artifactKey: Guid(cdb2264de10c42b4799822b736b5de9c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_ForwardLeft.fbx using Guid(cdb2264de10c42b4799822b736b5de9c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72ff80169c40b31266f9cd44c039904f') in 0.1116431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_BackwardRight.fbx
  artifactKey: Guid(ca7bf50d255ff5749a3a9ff602076af8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_BackwardRight.fbx using Guid(ca7bf50d255ff5749a3a9ff602076af8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2b8ad4a14e040fde3fc2d6a66293881') in 0.063097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Left.fbx
  artifactKey: Guid(6f466407f9f035f4fbf426e686e6444b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Left.fbx using Guid(6f466407f9f035f4fbf426e686e6444b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd44d51232563be26f774686e1ba8d4e2') in 0.0761662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Right.fbx
  artifactKey: Guid(2cd37dc84c089ac4981cd6d36abd33eb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/HumanM@Walk01_Right.fbx using Guid(2cd37dc84c089ac4981cd6d36abd33eb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'edcc9b63613e0794f892e6e84d0cacb5') in 0.0528331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Wooden_Generic_2.wav
  artifactKey: Guid(bb0786ff878554d4db2c40568546e2bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Wooden_Generic_2.wav using Guid(bb0786ff878554d4db2c40568546e2bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '78a2c3049181da3d5c052b7b22f77c1d') in 0.1502956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_Right.fbx
  artifactKey: Guid(067a556d54da4e94b8c6cfc96e97d680) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_Right.fbx using Guid(067a556d54da4e94b8c6cfc96e97d680) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '634948f708daf457ec034fa4f220782b') in 0.0544358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM].fbx
  artifactKey: Guid(33332cc79a8e983428d3ba32af4c8997) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Jump/HumanF@Jump01 [RM].fbx using Guid(33332cc79a8e983428d3ba32af4c8997) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eaf50b100392369775536aaa7d437e38') in 0.0627623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_Flag_02.mat
  artifactKey: Guid(31668398527de6042af55388c6180403) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_Flag_02.mat using Guid(31668398527de6042af55388c6180403) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'faa25cb4da3733781b2ae9020c583ba2') in 0.0579105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/Materials/No Name.mat
  artifactKey: Guid(a3fdefc7757900348a9092187b25b3bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Hills/Materials/No Name.mat using Guid(a3fdefc7757900348a9092187b25b3bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e435119ba660a06031aadd472723e50c') in 0.0615916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/Materials/No Name.mat
  artifactKey: Guid(e6c1f6c54e3f4bf4b8203d7c2cc40367) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/Materials/No Name.mat using Guid(e6c1f6c54e3f4bf4b8203d7c2cc40367) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6abc16b5aea842b162e3541026d715f9') in 0.0579194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Cannon.fbx
  artifactKey: Guid(abc00000000000849855360597353302) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/SM_Cannon.fbx using Guid(abc00000000000849855360597353302) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad4709126df9ce49d60b8e56180ddce5') in 0.085541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_Pillar.fbx
  artifactKey: Guid(abc00000000005507057690745990822) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_Pillar.fbx using Guid(abc00000000005507057690745990822) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43e6d246d9c202002edb044adc5e4667') in 0.0768219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_01.fbx
  artifactKey: Guid(abc00000000015737417759407170776) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_01.fbx using Guid(abc00000000015737417759407170776) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0302facd46a9e5ff0b3a0e4b8fc6fa72') in 0.0800031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_HallTable_01.fbx
  artifactKey: Guid(abc00000000000265796969235726058) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_HallTable_01.fbx using Guid(abc00000000000265796969235726058) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2251342885c91c285832c4f6bab3742e') in 0.0719083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_Corner_01.fbx
  artifactKey: Guid(abc00000000016699448409062077771) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_Corner_01.fbx using Guid(abc00000000016699448409062077771) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d98be704c020f556e8f9b2934a9d467') in 0.0847713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000542 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Potato.fbx
  artifactKey: Guid(abc00000000011366965043140825084) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Potato.fbx using Guid(abc00000000011366965043140825084) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3579bd61b8c268f4a78d006906d8c3d') in 0.0646315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000110 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_ForwardLeft [RM].fbx
  artifactKey: Guid(e43888617a116f943a10e74301b8fed1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Sprint/RootMotion/HumanF@Sprint01_ForwardLeft [RM].fbx using Guid(e43888617a116f943a10e74301b8fed1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e3dd6015d0e1ed66cbdf5e7d719e798') in 0.0870637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Metal.mat
  artifactKey: Guid(f378c67e96be01e4eba96ba600b66387) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Metal.mat using Guid(f378c67e96be01e4eba96ba600b66387) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81278c0942cfbe2dfa142557c13b59ee') in 0.064274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Wood_A.mat
  artifactKey: Guid(6d224b4687d8c3148a9a9710b2112529) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Wood_A.mat using Guid(6d224b4687d8c3148a9a9710b2112529) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '613bad95801d13f5a8d786ed7c835e78') in 0.0681434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bar_Shelf.fbx
  artifactKey: Guid(abc00000000010354095688107625671) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bar_Shelf.fbx using Guid(abc00000000010354095688107625671) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '72897369c94cf2a7ac1d10e46f585af8') in 0.0746244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_BackwardRight [RM].fbx
  artifactKey: Guid(4751c211b658f37459b5165f3a027e22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_BackwardRight [RM].fbx using Guid(4751c211b658f37459b5165f3a027e22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad0cda27b26422f698249ff729d840da') in 0.0952934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/Materials/No Name.mat
  artifactKey: Guid(7553c4992a572a740896bbb5514b9a78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Building_Kit/WoodenFences/Materials/No Name.mat using Guid(7553c4992a572a740896bbb5514b9a78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6eab03f7d97631f17cb4cdd643e8c1a3') in 0.0634752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_01.fbx
  artifactKey: Guid(abc00000000016641955203740369324) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_01.fbx using Guid(abc00000000016641955203740369324) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af94e66921702fddc8881b0ef08f1d09') in 0.0758397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Castle_SeamHider_02.fbx
  artifactKey: Guid(abc00000000017134283349780545185) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_Castle_SeamHider_02.fbx using Guid(abc00000000017134283349780545185) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6bd3ff85e7178409a161f7a251374d4c') in 0.1108236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Podium.fbx
  artifactKey: Guid(abc00000000001503279170236268265) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Podium.fbx using Guid(abc00000000001503279170236268265) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '664c5d956038f5872197cec5853c808a') in 0.0875836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Table_Rectangle.fbx
  artifactKey: Guid(abc00000000010765438307446803899) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Table_Rectangle.fbx using Guid(abc00000000010765438307446803899) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'caedee4511c0fde39c0c7a95069ca2ea') in 0.0648758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/Materials/No Name.mat
  artifactKey: Guid(5b860ea17cad7e841a8ac674601ff4cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/Materials/No Name.mat using Guid(5b860ea17cad7e841a8ac674601ff4cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f80fd370c98a6084bd995bfe8b600ec') in 0.0555358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_StoneFence_03.mat
  artifactKey: Guid(fe84987fec3893e4c923d49439dcdfe5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/Materials/MI_StoneFence_03.mat using Guid(fe84987fec3893e4c923d49439dcdfe5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'debdd6e05d298a85619bdf01d7ce2745') in 0.0636894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Main_Stairs.fbx
  artifactKey: Guid(abc00000000007374134889558527619) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/SM_Main_Stairs.fbx using Guid(abc00000000007374134889558527619) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2828a2f36e2ee62775191d987610d12d') in 0.0940836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_03.fbx
  artifactKey: Guid(abc00000000013864597937092084429) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_03.fbx using Guid(abc00000000013864597937092084429) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc856e2d9a4563e71a9a76ffaa9b455b') in 0.0842785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/MI_Rope_02.mat
  artifactKey: Guid(e4eaba95afd999443945607d5fec0455) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/MI_Rope_02.mat using Guid(e4eaba95afd999443945607d5fec0455) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f89b6b6fee204e2c70e3ce78853d52cf') in 0.0560643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(71b58ac1486a7484c8f3c1ed70b00401) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Market/Materials/MI_WoodPlanks.mat using Guid(71b58ac1486a7484c8f3c1ed70b00401) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e486573608edaef36f8254032705d69a') in 0.0593466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/No Name.mat
  artifactKey: Guid(643ed2c2cf5b19a49a80b677daee63f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/Materials/No Name.mat using Guid(643ed2c2cf5b19a49a80b677daee63f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59484ebdfe0322cc836d90412ea0d12d') in 0.0553448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_05.fbx
  artifactKey: Guid(abc00000000010788870380137322407) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Beams/SM_Beam_B_05.fbx using Guid(abc00000000010788870380137322407) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2f71004cd9e31fe4f1d8a200566b4c8') in 0.0623592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Banister.fbx
  artifactKey: Guid(abc00000000013959961593562589737) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Banister.fbx using Guid(abc00000000013959961593562589737) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50b7b9b1c0a193cbf4d443f2e77c0a40') in 0.0950432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_C.fbx
  artifactKey: Guid(abc00000000000163149550508099668) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_C.fbx using Guid(abc00000000000163149550508099668) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b6b40da293d7a5eb5766de3902d1036') in 0.1254127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_03.mat
  artifactKey: Guid(4c98d3df85b4c734382984e2f844e854) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_03.mat using Guid(4c98d3df85b4c734382984e2f844e854) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b73c962adb889b26c338491ff49574ff') in 0.0671987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Slope_2x5.fbx
  artifactKey: Guid(abc00000000003436854493128777270) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Slope_2x5.fbx using Guid(abc00000000003436854493128777270) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4471947d38e54d068eeb9e5936b5d577') in 0.0736685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_02.fbx
  artifactKey: Guid(abc00000000011388236154574225556) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Window_02.fbx using Guid(abc00000000011388236154574225556) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad313559fd97175ae90eb31364568ca6') in 0.0874601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_07.mat
  artifactKey: Guid(9c6ef99740b1cb941a6c44ad82bca90b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_BrickKit_07.mat using Guid(9c6ef99740b1cb941a6c44ad82bca90b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b299123166e7df22b9eb439d8481c2e0') in 0.0637801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000905 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_Floor.mat
  artifactKey: Guid(57ee0f9dbf00a3944a1aea064d0e90d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_Floor.mat using Guid(57ee0f9dbf00a3944a1aea064d0e90d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'debc212abd786efe588e14f261fe79d3') in 0.0641602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_07.fbx
  artifactKey: Guid(abc00000000003652696058173894503) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_07.fbx using Guid(abc00000000003652696058173894503) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '645cacf2acfcfbf9b6a356fbc3c46fc2') in 0.1508667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/Materials/MI_Rope_01.mat
  artifactKey: Guid(57499698f12905a438462134610074bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/Materials/MI_Rope_01.mat using Guid(57499698f12905a438462134610074bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea8210ceac5f368c0603803d6f6fe750') in 0.0753656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_03.fbx
  artifactKey: Guid(abc00000000005507137683333253171) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_EuropeanBeech_L_03.fbx using Guid(abc00000000005507137683333253171) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '594b37226498b01bb8c90cdbe6a98e69') in 0.0891729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_M_01.fbx
  artifactKey: Guid(abc00000000011285415969455392684) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_M_01.fbx using Guid(abc00000000011285415969455392684) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80f5d603bf6fa83557e412d93415cdd7') in 0.0839128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stair_Support_2M.fbx
  artifactKey: Guid(abc00000000011851549293887038686) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stair_Support_2M.fbx using Guid(abc00000000011851549293887038686) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbf90ea9646f295cfa1a6afc3090b1df') in 0.0792201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Materials/M_SilverFir_Bark_01.mat
  artifactKey: Guid(abc00000000002952352208987506198) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Materials/M_SilverFir_Bark_01.mat using Guid(abc00000000002952352208987506198) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f14aeb120cbf226535fbd12b96a7d1ce') in 0.044925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_S_01.fbx
  artifactKey: Guid(abc00000000017802581030287458830) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_S_01.fbx using Guid(abc00000000017802581030287458830) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '392c89b08958aa6729c515730a9ae133') in 0.0686352 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_01.fbx
  artifactKey: Guid(abc00000000002894492221727484887) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_B_01.fbx using Guid(abc00000000002894492221727484887) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58d994e31fe1eb874565bfdfb1a39e24') in 0.0711293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_Floor.mat
  artifactKey: Guid(5ee8d355c7d9bc1439ac595a4d3c1fa1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_Floor.mat using Guid(5ee8d355c7d9bc1439ac595a4d3c1fa1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55a016b8779e7d51637d4cad9d57c162') in 0.0649034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/MI_Wood_A.mat
  artifactKey: Guid(8f8ed55411f8e31409074cf0bfa7e2e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/MI_Wood_A.mat using Guid(8f8ed55411f8e31409074cf0bfa7e2e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5faece711a75d3fdd9cc67f73af104aa') in 0.0657151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_plaster_01.mat
  artifactKey: Guid(4943ed3f3861a424696cd12d4e74f922) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/Materials/MI_plaster_01.mat using Guid(4943ed3f3861a424696cd12d4e74f922) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '980d6a552004275c2867ba610766d175') in 0.0634614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_01.fbx
  artifactKey: Guid(abc00000000016358648134323745486) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/SM_Roof_B_01.fbx using Guid(abc00000000016358648134323745486) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec34023fb0a503862c0177d4d877543a') in 0.099912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/Materials/MI_WildGrass_Atlas_01.mat
  artifactKey: Guid(32c799f7747656645a08b99fd410ae7b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/Materials/MI_WildGrass_Atlas_01.mat using Guid(32c799f7747656645a08b99fd410ae7b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9465dd9560727494ae2c324f99d8ce58') in 0.0335691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/No Name.mat
  artifactKey: Guid(c667f0e488755ff41b6d2ec5cbffb37a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/Materials/No Name.mat using Guid(c667f0e488755ff41b6d2ec5cbffb37a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58d05e73607ccb33ecd56d62d8818714') in 0.0489904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_05.fbx
  artifactKey: Guid(abc00000000008832259089047812122) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_05.fbx using Guid(abc00000000008832259089047812122) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03e8c416f010abef0111d48a72d0eb74') in 0.0958511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_B_Root.FBX
  artifactKey: Guid(b24358063e27bef43ab4dfddd73f5270) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Walk_B_to_Jog_B_Root.FBX using Guid(b24358063e27bef43ab4dfddd73f5270) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58133594c31a8fec4c9d759681e0ab62') in 0.0765786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Run_B.FBX
  artifactKey: Guid(4636993fad8b2664cb97b76bd0f59a9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_B_to_Run_B.FBX using Guid(4636993fad8b2664cb97b76bd0f59a9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '409780a9fc7842364da0c9a6dab2b9db') in 0.0626056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/Materials/No Name.mat
  artifactKey: Guid(8836af01b9ea2ad4db78c46f21547660) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/Materials/No Name.mat using Guid(8836af01b9ea2ad4db78c46f21547660) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd995359c84270375a1fc04ab98c6bc50') in 0.0563413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/No Name.mat
  artifactKey: Guid(a17272efaa364d1479016597c93962ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/Materials/No Name.mat using Guid(a17272efaa364d1479016597c93962ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3d1b19471c8c80511f6a3dd2a997067') in 0.0587109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_BaseColor.PNG
  artifactKey: Guid(e32c286be6b4c664fba3c7a4b6e3de3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WoodTile_01_BaseColor.PNG using Guid(e32c286be6b4c664fba3c7a4b6e3de3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '215ea9fd6529bd51738e2d1f13414247') in 0.0460945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.prefab
  artifactKey: Guid(e538a836618cf084abf085e296e78e5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.prefab using Guid(e538a836618cf084abf085e296e78e5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef838c51526d25a09f15f593891ab5a3') in 0.0555776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fire_RA_01.PNG
  artifactKey: Guid(68cdf79dc6d11094eae6ca3f98a3bf9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fire_RA_01.PNG using Guid(68cdf79dc6d11094eae6ca3f98a3bf9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9b03044094d6888e159611661aec410') in 0.0782225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/4__Right/M_Big_Sword@Damage_Right_Small_ver_A.FBX
  artifactKey: Guid(be04c2b969d31dd409d6de94d5b08674) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/4__Right/M_Big_Sword@Damage_Right_Small_ver_A.FBX using Guid(be04c2b969d31dd409d6de94d5b08674) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e7170c3a507a59ce01b9f3ccf1cde33') in 0.0793897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_ver_B_Root.FBX
  artifactKey: Guid(027fff6607d164a44a0d6b45c8d3cf33) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_ver_B_Root.FBX using Guid(027fff6607d164a44a0d6b45c8d3cf33) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3848832f1661c81ec30fbfb0aefe039') in 0.0877766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_ALL.FBX
  artifactKey: Guid(8885d9240be087a42ad30fc936903541) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/5_Revenges/Guard_Revenges/M_Big_Sword@Revenge_Guard_ALL.FBX using Guid(8885d9240be087a42ad30fc936903541) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d552d02ad6db23e47e3adda0e56e843') in 0.0662136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Ship_Cargo_01.fbx
  artifactKey: Guid(abc00000000014603840551286894048) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Crates/SM_Ship_Cargo_01.fbx using Guid(abc00000000014603840551286894048) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65c834f106255ce5f99d1157007b7e86') in 0.1024803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_11.png
  artifactKey: Guid(75ebd4afa77062b4cb62897edd1838b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_11.png using Guid(75ebd4afa77062b4cb62897edd1838b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4efb68627942afee62e0eaf3011a6365') in 0.0631095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_End.FBX
  artifactKey: Guid(370838575a9f7654784394f3b436da36) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_End.FBX using Guid(370838575a9f7654784394f3b436da36) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45a19ea1f2c3cad6d95166272b064426') in 0.0850386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0