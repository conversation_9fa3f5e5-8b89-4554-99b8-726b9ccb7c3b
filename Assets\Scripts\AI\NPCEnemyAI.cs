/*
 * Script: NPCEnemyAI
 * Purpose: Comprehensive NPC Enemy AI system with player detection, line-of-sight, state-based behavior, and audio detection
 * 
 * Setup Instructions:
 * 1. Attach this script to an NPC GameObject with NavMeshAgent component
 * 2. Ensure the NPC has a Collider component for detection
 * 3. Set up patrol points by creating empty GameObjects and assigning them to patrolPoints array
 * 4. Configure detection ranges and behavior settings in the inspector
 * 5. Ensure player GameObject has "Player" tag for detection
 * 
 * CRITICAL: Default Values Must Work Out-of-Box
 * - Default sight range: 10 units (good for most environments)
 * - Default hearing range: 5 units (realistic audio detection)
 * - Default sight angle: 60 degrees (natural field of view)
 * - NavMesh movement speed: 3.5f (natural walking pace)
 * - All detection layers set to reasonable defaults
 * 
 * Dependencies:
 * - NavMeshAgent component (auto-added if missing)
 * - PlayerController3D script on player GameObject
 * - NavMesh baked in scene for pathfinding
 * - Player GameObject tagged as "Player"
 * 
 * Usage:
 * Attach to any NPC GameObject to give it intelligent AI behavior.
 * Configure patrol points for wandering behavior.
 * Adjust detection settings based on environment size.
 * 
 * Public Methods:
 * - SetTarget(Transform target): Manually set AI target
 * - SetState(AIState newState): Force AI into specific state
 * - AddPatrolPoint(Transform point): Add new patrol point at runtime
 * - SetAlertLevel(float level): Adjust AI alertness (0-1)
 * 
 * Public Properties:
 * - CurrentState: Current AI state for external monitoring
 * - CanSeePlayer: Whether AI currently has line of sight to player
 * - DistanceToPlayer: Current distance to player
 * - AlertLevel: Current alertness level (0-1)
 * 
 * Author: Claude AI Assistant
 * Created: 2025-08-01
 * Last Modified: 2025-08-01
 */

using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;

namespace Blazeout.AI
{
    /// <summary>
    /// AI states for NPC behavior management
    /// </summary>
    public enum AIState
    {
        Patrol,     // Default wandering behavior
        Alert,      // Investigating suspicious activity
        Chase,      // Actively pursuing player
        Attack,     // In combat range
        Search,     // Lost player, searching last known position
        Stunned     // Temporarily disabled
    }

    /// <summary>
    /// Detection types for AI awareness system
    /// </summary>
    public enum DetectionType
    {
        Visual,     // Saw the player
        Audio,      // Heard player footsteps
        Alert,      // Alerted by another AI
        Damage      // Took damage from player
    }

    /// <summary>
    /// Comprehensive NPC Enemy AI with detection, pathfinding, and state management
    /// Attach this script to: NPC GameObject with NavMeshAgent
    /// </summary>
    [RequireComponent(typeof(NavMeshAgent))]
    public class NPCEnemyAI : MonoBehaviour
    {
        #region Inspector Settings
        [Header("Detection Settings")]
        [Tooltip("How far the NPC can see the player")]
        [SerializeField] private float sightRange = 10.0f;
        
        [Tooltip("Field of view angle in degrees")]
        [SerializeField] private float sightAngle = 60.0f;
        
        [Tooltip("How far the NPC can hear player footsteps")]
        [SerializeField] private float hearingRange = 5.0f;
        
        [Tooltip("Layers that can block line of sight")]
        [SerializeField] private LayerMask obstacleLayers = -1;
        
        [Tooltip("Layer mask for player detection")]
        [SerializeField] private LayerMask playerLayer = -1;

        [Header("Movement Settings")]
        [Tooltip("Normal patrol movement speed")]
        [SerializeField] private float patrolSpeed = 2.0f;
        
        [Tooltip("Alert investigation speed")]
        [SerializeField] private float alertSpeed = 3.0f;
        
        [Tooltip("Chase pursuit speed")]
        [SerializeField] private float chaseSpeed = 5.0f;
        
        [Tooltip("How close to get before attacking")]
        [SerializeField] private float attackRange = 2.0f;
        
        [Tooltip("Time to wait at each patrol point")]
        [SerializeField] private float patrolWaitTime = 2.0f;

        [Header("Behavior Settings")]
        [Tooltip("How long to search after losing player")]
        [SerializeField] private float searchDuration = 10.0f;
        
        [Tooltip("How long to stay alert after detection")]
        [SerializeField] private float alertDuration = 5.0f;
        
        [Tooltip("Time between attack attempts")]
        [SerializeField] private float attackCooldown = 1.5f;
        
        [Tooltip("How quickly alertness decays (0-1 per second)")]
        [SerializeField] private float alertDecayRate = 0.1f;

        [Header("Patrol System")]
        [Tooltip("Points to patrol between (leave empty for random wandering)")]
        [SerializeField] private Transform[] patrolPoints;
        
        [Tooltip("Random wander radius if no patrol points set")]
        [SerializeField] private float wanderRadius = 10.0f;

        [Header("Audio Detection")]
        [Tooltip("Minimum player speed to generate detectable footsteps")]
        [SerializeField] private float minimumFootstepSpeed = 0.5f;
        
        [Tooltip("Multiplier for footstep detection based on player speed")]
        [SerializeField] private float footstepVolumeMultiplier = 1.0f;

        [Header("Debug Settings")]
        [Tooltip("Show debug gizmos and information")]
        [SerializeField] private bool enableDebug = true;
        
        [Tooltip("Show detection ranges in scene view")]
        [SerializeField] private bool showDetectionGizmos = true;
        
        [Tooltip("Show current state in console")]
        [SerializeField] private bool logStateChanges = true;
        #endregion

        #region Private Variables
        // Core components
        private NavMeshAgent navAgent;
        private Animator animator;
        
        // Player reference and detection
        private Transform playerTransform;
        private Blazeout.Controllers.PlayerController3D playerController;
        private bool canSeePlayer = false;
        private bool canHearPlayer = false;
        private float distanceToPlayer = Mathf.Infinity;
        
        // AI state management
        private AIState currentState = AIState.Patrol;
        private AIState previousState = AIState.Patrol;
        private float alertLevel = 0.0f;
        private float stateTimer = 0.0f;
        
        // Patrol system
        private int currentPatrolIndex = 0;
        private Vector3 lastKnownPlayerPosition;
        private Vector3 originalPosition;
        private bool isWaitingAtPatrolPoint = false;
        
        // Detection system
        private float lastPlayerDetectionTime = 0.0f;
        private float lastAttackTime = 0.0f;
        private DetectionType lastDetectionType;
        
        // Performance optimization
        private float lastDetectionCheck = 0.0f;
        private const float DETECTION_CHECK_INTERVAL = 0.1f; // Check 10 times per second
        #endregion

        #region Public Properties
        /// <summary>Current AI state</summary>
        public AIState CurrentState => currentState;
        
        /// <summary>Whether AI can currently see the player</summary>
        public bool CanSeePlayer => canSeePlayer;
        
        /// <summary>Current distance to player</summary>
        public float DistanceToPlayer => distanceToPlayer;
        
        /// <summary>Current alertness level (0-1)</summary>
        public float AlertLevel => alertLevel;
        
        /// <summary>Last known position of the player</summary>
        public Vector3 LastKnownPlayerPosition => lastKnownPlayerPosition;
        
        /// <summary>Whether AI is currently moving</summary>
        public bool IsMoving => navAgent.velocity.magnitude > 0.1f;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize the AI system
        /// </summary>
        private void Start()
        {
            InitializeAI();
        }

        /// <summary>
        /// Update AI behavior and detection
        /// </summary>
        private void Update()
        {
            if (playerTransform == null) return;
            
            UpdateDetection();
            UpdateStateMachine();
            UpdateAnimator();
            
            if (enableDebug && logStateChanges && currentState != previousState)
            {
                Debug.Log($"[NPCEnemyAI] State changed: {previousState} -> {currentState}");
                previousState = currentState;
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize all AI components and references
        /// </summary>
        private void InitializeAI()
        {
            // Get or add required components
            navAgent = GetComponent<NavMeshAgent>();
            if (navAgent == null)
            {
                navAgent = gameObject.AddComponent<NavMeshAgent>();
            }
            
            animator = GetComponent<Animator>();
            
            // Find player in scene
            GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
            if (playerObject != null)
            {
                playerTransform = playerObject.transform;
                playerController = playerObject.GetComponent<Blazeout.Controllers.PlayerController3D>();
            }
            else
            {
                Debug.LogError("[NPCEnemyAI] No GameObject with 'Player' tag found in scene!");
                enabled = false;
                return;
            }
            
            // Store original position for wandering
            originalPosition = transform.position;
            
            // Configure NavMeshAgent
            navAgent.speed = patrolSpeed;
            navAgent.stoppingDistance = 0.5f;
            navAgent.autoBraking = true;
            
            // Initialize state
            SetState(AIState.Patrol);
            
            if (enableDebug)
            {
                Debug.Log($"[NPCEnemyAI] AI initialized on {gameObject.name}");
            }
        }
        #endregion

        #region Detection System
        /// <summary>
        /// Update all detection systems
        /// </summary>
        private void UpdateDetection()
        {
            // Optimize detection checks
            if (Time.time - lastDetectionCheck < DETECTION_CHECK_INTERVAL)
                return;

            lastDetectionCheck = Time.time;

            // Calculate distance to player
            distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);

            // Check visual detection
            CheckVisualDetection();

            // Check audio detection
            CheckAudioDetection();

            // Update alert level
            UpdateAlertLevel();
        }

        /// <summary>
        /// Check if AI can see the player
        /// </summary>
        private void CheckVisualDetection()
        {
            canSeePlayer = false;

            // Check if player is within sight range
            if (distanceToPlayer > sightRange)
                return;

            // Check if player is within sight angle
            Vector3 directionToPlayer = (playerTransform.position - transform.position).normalized;
            float angleToPlayer = Vector3.Angle(transform.forward, directionToPlayer);

            if (angleToPlayer > sightAngle * 0.5f)
                return;

            // Perform line of sight check
            if (HasLineOfSight(playerTransform.position))
            {
                canSeePlayer = true;
                lastKnownPlayerPosition = playerTransform.position;
                lastPlayerDetectionTime = Time.time;
                lastDetectionType = DetectionType.Visual;

                // Increase alert level based on distance
                float detectionStrength = 1.0f - (distanceToPlayer / sightRange);
                alertLevel = Mathf.Min(1.0f, alertLevel + detectionStrength * Time.deltaTime);
            }
        }

        /// <summary>
        /// Check if AI can hear the player's footsteps
        /// </summary>
        private void CheckAudioDetection()
        {
            canHearPlayer = false;

            // Check if player is within hearing range
            if (distanceToPlayer > hearingRange)
                return;

            // Check if player is making noise (moving)
            if (playerController != null && playerController.CurrentSpeed > minimumFootstepSpeed)
            {
                // Calculate footstep volume based on player speed and distance
                float footstepVolume = (playerController.CurrentSpeed * footstepVolumeMultiplier) / distanceToPlayer;
                float hearingThreshold = 0.5f; // Adjust based on desired sensitivity

                if (footstepVolume > hearingThreshold)
                {
                    canHearPlayer = true;
                    lastKnownPlayerPosition = playerTransform.position;
                    lastPlayerDetectionTime = Time.time;
                    lastDetectionType = DetectionType.Audio;

                    // Audio detection provides less alert increase than visual
                    float detectionStrength = 0.5f * (1.0f - (distanceToPlayer / hearingRange));
                    alertLevel = Mathf.Min(1.0f, alertLevel + detectionStrength * Time.deltaTime);
                }
            }
        }

        /// <summary>
        /// Check line of sight to target position using raycast
        /// </summary>
        private bool HasLineOfSight(Vector3 targetPosition)
        {
            Vector3 rayOrigin = transform.position + Vector3.up * 1.6f; // Eye level
            Vector3 rayDirection = (targetPosition - rayOrigin).normalized;
            float rayDistance = Vector3.Distance(rayOrigin, targetPosition);

            // Perform raycast to check for obstacles
            if (Physics.Raycast(rayOrigin, rayDirection, out RaycastHit hit, rayDistance, obstacleLayers))
            {
                // Check if we hit the player or an obstacle
                if (hit.collider.CompareTag("Player"))
                {
                    return true; // Clear line of sight to player
                }
                else
                {
                    return false; // Obstacle blocking view
                }
            }

            return true; // No obstacles detected
        }

        /// <summary>
        /// Update alert level over time
        /// </summary>
        private void UpdateAlertLevel()
        {
            // Decay alert level when not detecting player
            if (!canSeePlayer && !canHearPlayer)
            {
                alertLevel = Mathf.Max(0.0f, alertLevel - alertDecayRate * Time.deltaTime);
            }

            // Clamp alert level
            alertLevel = Mathf.Clamp01(alertLevel);
        }
        #endregion

        #region State Machine
        /// <summary>
        /// Update the AI state machine
        /// </summary>
        private void UpdateStateMachine()
        {
            stateTimer += Time.deltaTime;

            switch (currentState)
            {
                case AIState.Patrol:
                    UpdatePatrolState();
                    break;
                case AIState.Alert:
                    UpdateAlertState();
                    break;
                case AIState.Chase:
                    UpdateChaseState();
                    break;
                case AIState.Attack:
                    UpdateAttackState();
                    break;
                case AIState.Search:
                    UpdateSearchState();
                    break;
                case AIState.Stunned:
                    UpdateStunnedState();
                    break;
            }
        }

        /// <summary>
        /// Handle patrol state behavior
        /// </summary>
        private void UpdatePatrolState()
        {
            // Check for player detection
            if (canSeePlayer || canHearPlayer)
            {
                if (canSeePlayer)
                    SetState(AIState.Chase);
                else
                    SetState(AIState.Alert);
                return;
            }

            // Handle patrol movement
            if (!isWaitingAtPatrolPoint)
            {
                if (patrolPoints.Length > 0)
                {
                    PatrolBetweenPoints();
                }
                else
                {
                    WanderRandomly();
                }
            }
        }

        /// <summary>
        /// Handle alert state behavior
        /// </summary>
        private void UpdateAlertState()
        {
            // Check for direct visual contact
            if (canSeePlayer)
            {
                SetState(AIState.Chase);
                return;
            }

            // Move towards last known position
            if (Vector3.Distance(transform.position, lastKnownPlayerPosition) > 1.0f)
            {
                navAgent.SetDestination(lastKnownPlayerPosition);
            }

            // Return to patrol after alert duration
            if (stateTimer > alertDuration && alertLevel < 0.3f)
            {
                SetState(AIState.Patrol);
            }
        }

        /// <summary>
        /// Handle chase state behavior
        /// </summary>
        private void UpdateChaseState()
        {
            // Check if close enough to attack
            if (distanceToPlayer <= attackRange)
            {
                SetState(AIState.Attack);
                return;
            }

            // Continue chasing if we can see the player
            if (canSeePlayer)
            {
                navAgent.SetDestination(playerTransform.position);
                lastKnownPlayerPosition = playerTransform.position;
            }
            else
            {
                // Lost sight of player, enter search mode
                SetState(AIState.Search);
            }
        }

        /// <summary>
        /// Handle attack state behavior
        /// </summary>
        private void UpdateAttackState()
        {
            // Stop moving when attacking
            navAgent.SetDestination(transform.position);

            // Face the player
            Vector3 lookDirection = (playerTransform.position - transform.position).normalized;
            lookDirection.y = 0; // Keep on horizontal plane
            if (lookDirection != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(lookDirection);
            }

            // Perform attack if cooldown is ready
            if (Time.time - lastAttackTime > attackCooldown)
            {
                PerformAttack();
                lastAttackTime = Time.time;
            }

            // Check if player moved out of attack range
            if (distanceToPlayer > attackRange * 1.2f) // Add hysteresis
            {
                if (canSeePlayer)
                    SetState(AIState.Chase);
                else
                    SetState(AIState.Search);
            }
        }

        /// <summary>
        /// Handle search state behavior
        /// </summary>
        private void UpdateSearchState()
        {
            // Check if we found the player again
            if (canSeePlayer)
            {
                SetState(AIState.Chase);
                return;
            }

            // Move to last known position and search around it
            if (Vector3.Distance(transform.position, lastKnownPlayerPosition) > 2.0f)
            {
                navAgent.SetDestination(lastKnownPlayerPosition);
            }
            else
            {
                // Search around the area
                SearchAroundPosition(lastKnownPlayerPosition);
            }

            // Return to patrol after search duration
            if (stateTimer > searchDuration)
            {
                SetState(AIState.Patrol);
            }
        }

        /// <summary>
        /// Handle stunned state behavior
        /// </summary>
        private void UpdateStunnedState()
        {
            // Stop all movement
            navAgent.SetDestination(transform.position);

            // Return to patrol after stun duration (can be set externally)
            if (stateTimer > 3.0f) // Default stun duration
            {
                SetState(AIState.Patrol);
            }
        }
        #endregion

        #region Movement and Patrol
        /// <summary>
        /// Handle patrol movement between defined points
        /// </summary>
        private void PatrolBetweenPoints()
        {
            if (patrolPoints.Length == 0) return;

            Transform targetPoint = patrolPoints[currentPatrolIndex];

            // Move to current patrol point
            navAgent.SetDestination(targetPoint.position);

            // Check if we've reached the patrol point
            if (Vector3.Distance(transform.position, targetPoint.position) < 1.0f)
            {
                if (!isWaitingAtPatrolPoint)
                {
                    StartCoroutine(WaitAtPatrolPoint());
                }
            }
        }

        /// <summary>
        /// Wait at patrol point before moving to next
        /// </summary>
        private IEnumerator WaitAtPatrolPoint()
        {
            isWaitingAtPatrolPoint = true;
            navAgent.SetDestination(transform.position); // Stop moving

            yield return new WaitForSeconds(patrolWaitTime);

            // Move to next patrol point
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            isWaitingAtPatrolPoint = false;
        }

        /// <summary>
        /// Handle random wandering when no patrol points are set
        /// </summary>
        private void WanderRandomly()
        {
            // Check if we need a new destination
            if (!navAgent.hasPath || navAgent.remainingDistance < 1.0f)
            {
                Vector3 randomDirection = Random.insideUnitSphere * wanderRadius;
                randomDirection += originalPosition;
                randomDirection.y = transform.position.y; // Keep on same Y level

                // Check if the random position is valid on NavMesh
                if (NavMesh.SamplePosition(randomDirection, out NavMeshHit hit, wanderRadius, NavMesh.AllAreas))
                {
                    navAgent.SetDestination(hit.position);
                }
            }
        }

        /// <summary>
        /// Search around a specific position
        /// </summary>
        private void SearchAroundPosition(Vector3 centerPosition)
        {
            if (!navAgent.hasPath || navAgent.remainingDistance < 1.0f)
            {
                Vector3 searchDirection = Random.insideUnitSphere * 5.0f; // Search radius
                searchDirection += centerPosition;
                searchDirection.y = transform.position.y;

                if (NavMesh.SamplePosition(searchDirection, out NavMeshHit hit, 5.0f, NavMesh.AllAreas))
                {
                    navAgent.SetDestination(hit.position);
                }
            }
        }
        #endregion

        #region Combat and Actions
        /// <summary>
        /// Perform attack action
        /// </summary>
        private void PerformAttack()
        {
            if (enableDebug)
            {
                Debug.Log($"[NPCEnemyAI] {gameObject.name} attacking player!");
            }

            // Trigger attack animation if animator exists
            if (animator != null)
            {
                animator.SetTrigger("Attack");
            }

            // Here you would implement actual damage dealing
            // For example, if player has a health system:
            // var playerHealth = playerTransform.GetComponent<HealthSystem>();
            // if (playerHealth != null) playerHealth.TakeDamage(attackDamage);
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Manually set AI target
        /// </summary>
        public void SetTarget(Transform target)
        {
            if (target != null)
            {
                playerTransform = target;
                playerController = target.GetComponent<Blazeout.Controllers.PlayerController3D>();
                lastKnownPlayerPosition = target.position;
            }
        }

        /// <summary>
        /// Force AI into specific state
        /// </summary>
        public void SetState(AIState newState)
        {
            if (currentState != newState)
            {
                previousState = currentState;
                currentState = newState;
                stateTimer = 0.0f;

                // Update NavMeshAgent speed based on state
                switch (newState)
                {
                    case AIState.Patrol:
                        navAgent.speed = patrolSpeed;
                        break;
                    case AIState.Alert:
                        navAgent.speed = alertSpeed;
                        break;
                    case AIState.Chase:
                        navAgent.speed = chaseSpeed;
                        break;
                    case AIState.Attack:
                        navAgent.speed = 0.0f; // Stop during attack
                        break;
                    case AIState.Search:
                        navAgent.speed = alertSpeed;
                        break;
                    case AIState.Stunned:
                        navAgent.speed = 0.0f;
                        break;
                }
            }
        }

        /// <summary>
        /// Add new patrol point at runtime
        /// </summary>
        public void AddPatrolPoint(Transform point)
        {
            if (point != null)
            {
                var newPatrolPoints = new Transform[patrolPoints.Length + 1];
                patrolPoints.CopyTo(newPatrolPoints, 0);
                newPatrolPoints[patrolPoints.Length] = point;
                patrolPoints = newPatrolPoints;
            }
        }

        /// <summary>
        /// Adjust AI alertness level
        /// </summary>
        public void SetAlertLevel(float level)
        {
            alertLevel = Mathf.Clamp01(level);
        }

        /// <summary>
        /// Stun the AI for a specified duration
        /// </summary>
        public void Stun(float duration = 3.0f)
        {
            SetState(AIState.Stunned);
            StartCoroutine(StunCoroutine(duration));
        }

        private IEnumerator StunCoroutine(float duration)
        {
            yield return new WaitForSeconds(duration);
            if (currentState == AIState.Stunned)
            {
                SetState(AIState.Patrol);
            }
        }
        #endregion

        #region Animation and Visual Updates
        /// <summary>
        /// Update animator parameters based on AI state
        /// </summary>
        private void UpdateAnimator()
        {
            if (animator == null) return;

            // Update movement parameters
            float speed = navAgent.velocity.magnitude;
            animator.SetFloat("Speed", speed);
            animator.SetBool("IsMoving", speed > 0.1f);

            // Update state parameters
            animator.SetBool("IsAlert", currentState == AIState.Alert || currentState == AIState.Chase);
            animator.SetBool("IsAttacking", currentState == AIState.Attack);
            animator.SetBool("IsSearching", currentState == AIState.Search);

            // Update detection parameters
            animator.SetBool("CanSeePlayer", canSeePlayer);
            animator.SetFloat("AlertLevel", alertLevel);
        }
        #endregion

        #region Debug and Gizmos
        /// <summary>
        /// Draw debug gizmos in scene view
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!enableDebug || !showDetectionGizmos) return;

            // Draw sight range and cone
            Gizmos.color = canSeePlayer ? Color.red : Color.yellow;
            Gizmos.DrawWireSphere(transform.position, sightRange);

            // Draw sight cone
            Vector3 leftBoundary = Quaternion.Euler(0, -sightAngle * 0.5f, 0) * transform.forward * sightRange;
            Vector3 rightBoundary = Quaternion.Euler(0, sightAngle * 0.5f, 0) * transform.forward * sightRange;

            Gizmos.color = canSeePlayer ? Color.red : Color.yellow;
            Gizmos.DrawLine(transform.position, transform.position + leftBoundary);
            Gizmos.DrawLine(transform.position, transform.position + rightBoundary);

            // Draw hearing range
            Gizmos.color = canHearPlayer ? Color.blue : Color.cyan;
            Gizmos.DrawWireSphere(transform.position, hearingRange);

            // Draw line of sight ray to player
            if (playerTransform != null)
            {
                Vector3 rayOrigin = transform.position + Vector3.up * 1.6f;
                Gizmos.color = canSeePlayer ? Color.green : Color.red;
                Gizmos.DrawLine(rayOrigin, playerTransform.position);
            }

            // Draw last known player position
            if (lastKnownPlayerPosition != Vector3.zero)
            {
                Gizmos.color = Color.orange;
                Gizmos.DrawWireSphere(lastKnownPlayerPosition, 0.5f);
                Gizmos.DrawLine(transform.position, lastKnownPlayerPosition);
            }

            // Draw patrol points and path
            if (patrolPoints.Length > 0)
            {
                Gizmos.color = Color.green;
                for (int i = 0; i < patrolPoints.Length; i++)
                {
                    if (patrolPoints[i] != null)
                    {
                        Gizmos.DrawWireSphere(patrolPoints[i].position, 0.5f);

                        // Draw path between patrol points
                        int nextIndex = (i + 1) % patrolPoints.Length;
                        if (patrolPoints[nextIndex] != null)
                        {
                            Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[nextIndex].position);
                        }
                    }
                }

                // Highlight current target patrol point
                if (currentPatrolIndex < patrolPoints.Length && patrolPoints[currentPatrolIndex] != null)
                {
                    Gizmos.color = Color.magenta;
                    Gizmos.DrawWireSphere(patrolPoints[currentPatrolIndex].position, 0.8f);
                }
            }

            // Draw NavMesh path
            if (navAgent != null && navAgent.hasPath)
            {
                Gizmos.color = Color.white;
                Vector3[] pathCorners = navAgent.path.corners;
                for (int i = 0; i < pathCorners.Length - 1; i++)
                {
                    Gizmos.DrawLine(pathCorners[i], pathCorners[i + 1]);
                }
            }
        }

        /// <summary>
        /// Draw debug GUI information
        /// </summary>
        private void OnGUI()
        {
            if (!enableDebug) return;

            // Calculate screen position above the AI
            Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + Vector3.up * 3.0f);

            if (screenPos.z > 0) // Only draw if in front of camera
            {
                GUI.color = Color.white;
                GUI.Label(new Rect(screenPos.x - 50, Screen.height - screenPos.y - 60, 100, 60),
                    $"State: {currentState}\n" +
                    $"Alert: {alertLevel:F2}\n" +
                    $"Dist: {distanceToPlayer:F1}\n" +
                    $"See: {canSeePlayer} | Hear: {canHearPlayer}");
            }
        }
        #endregion
    }
}
