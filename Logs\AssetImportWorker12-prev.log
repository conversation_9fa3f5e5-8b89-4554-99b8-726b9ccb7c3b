Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker12
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker12.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11348]  Target information:

Player connection [11348]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2208874432 [EditorId] 2208874432 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11348] Host joined multi-casting on [***********:54997]...
Player connection [11348] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 12.19 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 3.14 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56468
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008722 seconds.
- Loaded All Assemblies, in  1.097 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.144 seconds
Domain Reload Profiling: 2241ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (107ms)
	RebuildNativeTypeToScriptingClass (41ms)
	initialDomainReloadingComplete (144ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (483ms)
			TypeCache.Refresh (480ms)
				TypeCache.ScanAssembly (439ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1145ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (988ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (68ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (229ms)
			ProcessInitializeOnLoadAttributes (543ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.304 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 10.56 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.969 seconds
Domain Reload Profiling: 5268ms
	BeginReloadAssembly (455ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (123ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (1584ms)
		LoadAssemblies (976ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (850ms)
			TypeCache.Refresh (638ms)
				TypeCache.ScanAssembly (593ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (2970ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2364ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (497ms)
			ProcessInitializeOnLoadAttributes (1690ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.40 seconds
Refreshing native plugins compatible for Editor in 122.89 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.6 MB). Loaded Objects now: 8334.
Memory consumption went from 210.0 MB to 200.4 MB.
Total: 146.052200 ms (FindLiveObjects: 2.358200 ms CreateObjectMapping: 2.655900 ms MarkObjects: 24.584900 ms  DeleteObjects: 116.450200 ms)

========================================================================
Received Import Request.
  Time since last request: 1812182.954660 seconds.
  path: Assets/playercontroller/playercontroller.unitypackage
  artifactKey: Guid(7f90c4ad12e088c45814385bec0279e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/playercontroller/playercontroller.unitypackage using Guid(7f90c4ad12e088c45814385bec0279e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e00a31a60f825155524bd3cc4cfa7f0a') in 2.1169885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Cast.prefab
  artifactKey: Guid(3a42d8d03a34231428341ac591cfde3a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Cast.prefab using Guid(3a42d8d03a34231428341ac591cfde3a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1543fbdf8a7ce226db3be6613cadefd7') in 1.261636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 57

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/DefaultLookDevProfile.asset
  artifactKey: Guid(4594f4a3fb14247e192bcca6dc23c8ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/DefaultLookDevProfile.asset using Guid(4594f4a3fb14247e192bcca6dc23c8ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '11272b317895d29775a4ed1b4f7a9696') in 0.1603463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_01.mat
  artifactKey: Guid(303e547f146d14b498066c2e7765552c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FlipbookDots_01.mat using Guid(303e547f146d14b498066c2e7765552c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7e3ca0f1cbfd6fc3a8aa77b47505a64') in 1.9947749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Settings/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9535aa92af9d81fa55300f4ef7a6c8ff') in 0.0422935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_04.mat
  artifactKey: Guid(6732b1b45e78c374998c53030da987e7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_04.mat using Guid(6732b1b45e78c374998c53030da987e7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b8c12deb53760c5daec126ec8b86985') in 0.8221701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_7.prefab
  artifactKey: Guid(2fea2eba55fb1dd4ebe7d12630fb7d68) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_7.prefab using Guid(2fea2eba55fb1dd4ebe7d12630fb7d68) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cec5c5b46032a42e038c0cecd78c5369') in 0.0535084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_6x4.mat
  artifactKey: Guid(2c5fc0a3f8ec5204c9a2eb72f7dabc79) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_FoamLoop_01_6x4.mat using Guid(2c5fc0a3f8ec5204c9a2eb72f7dabc79) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22eb0fa09092e73804814589cc1b4bb7') in 0.2917975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/New Actions.inputactions
  artifactKey: Guid(207f8313a45bb11478143173eda8325a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/New Actions.inputactions using Guid(207f8313a45bb11478143173eda8325a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c02c85af87535a7fdae637b8546dbf21') in 0.0899485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Impact.prefab
  artifactKey: Guid(3a5c33b0b845583419675161aff09dc2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Impact.prefab using Guid(3a5c33b0b845583419675161aff09dc2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fe762618a2a17452629def65a322605f') in 0.136985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 132

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_FallingWater_01.prefab
  artifactKey: Guid(48216c1b9d28e7c4fb70a30171820180) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_FallingWater_01.prefab using Guid(48216c1b9d28e7c4fb70a30171820180) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6d29ddc6cd764df91b5dac806015b925') in 0.0746014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 60

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.prefab
  artifactKey: Guid(98e11877557dba94da7b865dbfcbdf82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_01.prefab using Guid(98e11877557dba94da7b865dbfcbdf82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b033e819a70e0043b6ab5114ad6d9dc') in 0.6278304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Corner_B.prefab
  artifactKey: Guid(abc00000000018252124555131516279) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Arch_Walkway_Corner_B.prefab using Guid(abc00000000018252124555131516279) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eb3d556fc11d9f5fe23acc7056ebccef') in 0.04433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Purple Variant.prefab
  artifactKey: Guid(8dbe21b9a1a525a40b0b0d5ab31261dc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Purple Variant.prefab using Guid(8dbe21b9a1a525a40b0b0d5ab31261dc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fb73164cc592ca4cc8c658db14de1759') in 0.2747508 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 772

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books.prefab
  artifactKey: Guid(abc00000000012964185573243371773) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books.prefab using Guid(abc00000000012964185573243371773) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '739f86c338f219237255ba2c841e49ee') in 0.0416713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWaterMask_02_4x5.mat
  artifactKey: Guid(3529064fce4bb134daf23a1bd5523467) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWaterMask_02_4x5.mat using Guid(3529064fce4bb134daf23a1bd5523467) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '940d202b393d8c080b98fc2cb5e8476b') in 0.5927086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_04.mat
  artifactKey: Guid(c3538553094cd5f4b9386a7b6300a910) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SimpleImpact_04.mat using Guid(c3538553094cd5f4b9386a7b6300a910) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92834d194aab7f4ae49562ab3bcedd1b') in 0.0568256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_04.prefab
  artifactKey: Guid(abc00000000005319752499175810146) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_04.prefab using Guid(abc00000000005319752499175810146) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '719b59f8f4b3606fee9bacbe7fe1c2c4') in 0.0315074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_03.prefab
  artifactKey: Guid(abc00000000012716303510755586549) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_03.prefab using Guid(abc00000000012716303510755586549) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '28b29d8652c6c28116ef30c139dc0e30') in 0.0276438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_02_2x8.mat
  artifactKey: Guid(5b89f7019eb51ce4a93ba6ab8afcb483) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledTravelWater_02_2x8.mat using Guid(5b89f7019eb51ce4a93ba6ab8afcb483) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f6803a880b35af483f121cb035308c14') in 0.5210706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_02.mat
  artifactKey: Guid(bae1bcbf5aa83184fb23385abe07f39d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_DistortWave_02.mat using Guid(bae1bcbf5aa83184fb23385abe07f39d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12348dab8e727e0b58fa544715350c41') in 0.5182065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000097 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockA.prefab
  artifactKey: Guid(abc00000000004486004173860205213) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_BlockA.prefab using Guid(abc00000000004486004173860205213) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6b996c2e5d6375ca9f5d7df1132b06c2') in 0.0377535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_10.prefab
  artifactKey: Guid(abc00000000003391889486116700163) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_10.prefab using Guid(abc00000000003391889486116700163) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7df497489ab849a2c3baef2cf6b1b32e') in 0.0400971 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_07_3x4.mat
  artifactKey: Guid(c4c7b726214eb44498ea10a07c464236) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_TiledWater_07_3x4.mat using Guid(c4c7b726214eb44498ea10a07c464236) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bee2a7609e9e31b53e99e7be1221e9e8') in 0.6364451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_8M.prefab
  artifactKey: Guid(abc00000000014075729282340197617) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_8M.prefab using Guid(abc00000000014075729282340197617) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ebf180007c0da22a31c46f8d5d6083b7') in 0.1087742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_02.prefab
  artifactKey: Guid(abc00000000003590945754320045061) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Crenel_A_02.prefab using Guid(abc00000000003590945754320045061) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ba9175c58874454bfd7aead680bdbba2') in 0.0386523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_03_4x3_Poison.mat
  artifactKey: Guid(8074591cea5ee274b9f9a00c0cf921f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_03_4x3_Poison.mat using Guid(8074591cea5ee274b9f9a00c0cf921f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3dad9b9afadb46d5df3f1b2d4ecb9bc2') in 0.284331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_OutsideCorner_01.prefab
  artifactKey: Guid(abc00000000011143252428546293327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_OutsideCorner_01.prefab using Guid(abc00000000011143252428546293327) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ab1c0d5d051de060b70fcbf709265227') in 0.0522623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cup_01.prefab
  artifactKey: Guid(abc00000000006862668080870461067) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cup_01.prefab using Guid(abc00000000006862668080870461067) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '781ec78f87da69d9887641b4832ca976') in 0.1021454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_01_4x3.mat
  artifactKey: Guid(63d7ac59d99ad6d4fbc66717811e1815) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_01_4x3.mat using Guid(63d7ac59d99ad6d4fbc66717811e1815) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f37435af4140a900113903ebefe42af') in 0.0547964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_14.prefab
  artifactKey: Guid(abc00000000013275564946604705107) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_14.prefab using Guid(abc00000000013275564946604705107) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c712de9a438e60cc5e42720225a9522e') in 0.0735446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_B_01.prefab
  artifactKey: Guid(abc00000000010809404477212661523) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DoorFrame_B_01.prefab using Guid(abc00000000010809404477212661523) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '77f965a74d41e76fcfbb0bb4009aa467') in 0.075078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Scripts/Editor/PickupObjectHoldingSetupTool.cs
  artifactKey: Guid(082bb219352c8d241a9525e07325cef9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Editor/PickupObjectHoldingSetupTool.cs using Guid(082bb219352c8d241a9525e07325cef9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90d84332f87d5c43e6360d50154fb8f0') in 0.065636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x2M.prefab
  artifactKey: Guid(abc00000000001968411429557024277) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_2x2M.prefab using Guid(abc00000000001968411429557024277) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '76e146c49f7b01eb79371d2f43b876d6') in 0.063604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_19.prefab
  artifactKey: Guid(abc00000000005245857699689613390) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_19.prefab using Guid(abc00000000005245857699689613390) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b8df8707e777e833fdf1cd77fcfbf0aa') in 0.0468197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity
  artifactKey: Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity using Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16fe3c714ac6b844e73c580bfde5ed42') in 0.0371861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_04.mat
  artifactKey: Guid(ec13ac5bd54e0964c82ede1d8e7a6ec7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterLaser_Core_04.mat using Guid(ec13ac5bd54e0964c82ede1d8e7a6ec7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b22950f7cdb9d95677846059d01b8933') in 0.0303485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C.prefab
  artifactKey: Guid(abc00000000010483527000914307316) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Rock_C.prefab using Guid(abc00000000010483527000914307316) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b0bbc6d977701f772c886a933015a313') in 0.0490326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_A.prefab
  artifactKey: Guid(abc00000000006595051334539448741) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_A.prefab using Guid(abc00000000006595051334539448741) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '697908320e275645fe11451ef546513f') in 0.0381244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_C.prefab
  artifactKey: Guid(abc00000000006537966796779105927) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_C.prefab using Guid(abc00000000006537966796779105927) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '28b4c21eec1e841a334d0da4ec3ff3c4') in 0.0394839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Keg.prefab
  artifactKey: Guid(abc00000000000865317528067898336) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Keg.prefab using Guid(abc00000000000865317528067898336) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '06488d588ce29197118e66b9bc6c5e57') in 0.0367043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HighBackChair.prefab
  artifactKey: Guid(abc00000000012652205390547645271) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HighBackChair.prefab using Guid(abc00000000012652205390547645271) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c76131d803c17bb67aab1a0518375540') in 0.0316261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_04.prefab
  artifactKey: Guid(abc00000000007611508329726168971) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_04.prefab using Guid(abc00000000007611508329726168971) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd9afeca100048eec5dc1c63a8300ca78') in 0.0652888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_B.prefab
  artifactKey: Guid(abc00000000002115521984995160350) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_B.prefab using Guid(abc00000000002115521984995160350) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cb0c8d0be78c5b76cb5f062e573376e2') in 0.0302342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01.prefab
  artifactKey: Guid(abc00000000014892343687679203641) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_KettlePot_01.prefab using Guid(abc00000000014892343687679203641) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c2441baf0b7fd1dfa161f3cf4e523364') in 0.0294214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_C.prefab
  artifactKey: Guid(abc00000000013476178078290774999) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Ivy_C.prefab using Guid(abc00000000013476178078290774999) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '20f9d02884f454bcca11ed5647116d43') in 0.067662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_01.prefab
  artifactKey: Guid(abc00000000010042614864576633131) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_01.prefab using Guid(abc00000000010042614864576633131) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '09cc67b6eee5a57260be2b6819dddb25') in 0.027598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02_1.prefab
  artifactKey: Guid(abc00000000002548068665834369709) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_02_1.prefab using Guid(abc00000000002548068665834369709) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f30600456bf1caa4b61350e53b051901') in 0.0357443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_05.prefab
  artifactKey: Guid(abc00000000011305282193036375362) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_05.prefab using Guid(abc00000000011305282193036375362) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cc0207103b6438d85800b8c93cdba7d9') in 0.0658611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_09.prefab
  artifactKey: Guid(abc00000000010456034354059906739) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_09.prefab using Guid(abc00000000010456034354059906739) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '741f3b29b500c69dd4c540dda94f53cc') in 0.0453816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08_1.prefab
  artifactKey: Guid(abc00000000010343720456142068162) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08_1.prefab using Guid(abc00000000010343720456142068162) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd74eac057fe6c086a43ec5856f36a6e2') in 0.0479709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_05.prefab
  artifactKey: Guid(abc00000000003172539429430554663) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_05.prefab using Guid(abc00000000003172539429430554663) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fce00d8b0b95f8bf32211fd3696381d8') in 0.0518069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_01.png
  artifactKey: Guid(21f9582af14ac6742929e28377ad0eb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_BOT_BaseColor_01.png using Guid(21f9582af14ac6742929e28377ad0eb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd163d89a7208961b206541bc094cf924') in 0.1829259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02_1.prefab
  artifactKey: Guid(abc00000000005887249695792533851) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_02_1.prefab using Guid(abc00000000005887249695792533851) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f07148151f300797b98c909507a811b9') in 0.061658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Saw.prefab
  artifactKey: Guid(abc00000000012955199857811331708) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Saw.prefab using Guid(abc00000000012955199857811331708) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9f4536ba42481fdfa34f71dc8de13c09') in 0.0450128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/TextMesh Pro/Shaders/TMPro_Surface.cginc
  artifactKey: Guid(d930090c0cd643c7b55f19a38538c162) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMPro_Surface.cginc using Guid(d930090c0cd643c7b55f19a38538c162) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2f942d1f978ff5fd943b99d79b098e5') in 0.0455637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabA.prefab
  artifactKey: Guid(abc00000000017965742289626602030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabA.prefab using Guid(abc00000000017965742289626602030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '43ad816ccb0341f8eb1e57cc413e9c81') in 0.0543028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/LerpTransparencies.shadergraph
  artifactKey: Guid(1a2a78951f90d6345a818229ec534919) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/LerpTransparencies.shadergraph using Guid(1a2a78951f90d6345a818229ec534919) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9ec704741c9c429d6f784eab0dd7ee9') in 0.0571901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_E.prefab
  artifactKey: Guid(abc00000000002381047540023078438) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_E.prefab using Guid(abc00000000002381047540023078438) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e2cd4a42d4c7dbc70335728c0aa52926') in 0.0667463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_03.prefab
  artifactKey: Guid(abc00000000012302921541177371029) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_03.prefab using Guid(abc00000000012302921541177371029) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3ce9520fcd03eebb3fcae807f83fa6b5') in 0.0362593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl
  artifactKey: Guid(96de908384869cd409c75efa351d5edf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl using Guid(96de908384869cd409c75efa351d5edf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48f627538a59a96b6ae9cffab6f076a2') in 0.0444417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Traveling.prefab
  artifactKey: Guid(7aea3730afb11ba4793afc1179183511) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Traveling.prefab using Guid(7aea3730afb11ba4793afc1179183511) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52a92dee1a6d641b5766a1cdbb36f7e8') in 0.4441897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 82

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_01.prefab
  artifactKey: Guid(abc00000000018247251753517368499) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_01.prefab using Guid(abc00000000018247251753517368499) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '776676b0848d5ecb1920513f9a9ff2c3') in 0.0366254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_1.prefab
  artifactKey: Guid(abc00000000016091956856614776893) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_01_1.prefab using Guid(abc00000000016091956856614776893) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '817fcb401d152f36891c3512fde3b0e3') in 0.0322053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x5.prefab
  artifactKey: Guid(abc00000000009156416213942264455) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x5.prefab using Guid(abc00000000009156416213942264455) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'edcd71fda86df8c75d14e128ce782fb5') in 0.0412566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/SrRubfish_VFX_02/Shaders/StandardTransparency_OffsetGrad.shadergraph
  artifactKey: Guid(00c593c5b3542d549b331dcc82ee5828) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Shaders/StandardTransparency_OffsetGrad.shadergraph using Guid(00c593c5b3542d549b331dcc82ee5828) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '42bd31541fdff116bbe8c6dc68aef8b8') in 0.6235208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_FireParticle.prefab
  artifactKey: Guid(b06bcf97bece9c64fa9cf91dfac6e1f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/VFX/Prefabs/PS_FireParticle.prefab using Guid(b06bcf97bece9c64fa9cf91dfac6e1f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c35accb17cf3006c6e52e33843e5930c') in 0.0285973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_WITH_FUR Variant.prefab
  artifactKey: Guid(4ab5b2ea6d4daca4abe025f3239e3b80) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_WITH_FUR Variant.prefab using Guid(4ab5b2ea6d4daca4abe025f3239e3b80) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0ffa376e8e0251d666f916d1c94e9ac0') in 0.3116467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 787

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-Surface.shader
  artifactKey: Guid(f7ada0af4f174f0694ca6a487b8f543d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-Surface.shader using Guid(f7ada0af4f174f0694ca6a487b8f543d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f764f06b54f083e9da5bf2c59003234') in 0.0573211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic Outline.png
  artifactKey: Guid(e852f0685fd3abe47963dc46c163d662) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic Outline.png using Guid(e852f0685fd3abe47963dc46c163d662) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '433d43be96d340fd84124804b61b2793') in 0.0758001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000168 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Lock Light.png
  artifactKey: Guid(333afc89a8fd56748b229256b30beda2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Icon Lock Light.png using Guid(333afc89a8fd56748b229256b30beda2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c94cec5088a546731d65b4f9df719be') in 0.0656033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader
  artifactKey: Guid(dd89cf5b9246416f84610a006f916af7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader using Guid(dd89cf5b9246416f84610a006f916af7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95c5b8dc116a78e69c7ef1513ae6ea3d') in 0.031416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Default Texture.png
  artifactKey: Guid(e9799cab19e81094ba207ebbba17eecd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Default Texture.png using Guid(e9799cab19e81094ba207ebbba17eecd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a84dc06ac1654f498dceff62380bdbf') in 0.0398525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bench.prefab
  artifactKey: Guid(abc00000000013782433643290634477) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bench.prefab using Guid(abc00000000013782433643290634477) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7c48284c3bc91255bca1eec8b33d0b36') in 0.0265628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_05_Custom.mat
  artifactKey: Guid(eeefeb6f09e3e694cba59620280b4dcd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_05_Custom.mat using Guid(eeefeb6f09e3e694cba59620280b4dcd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68f02fae228c4c0df5b6f6c52d015b07') in 0.7762786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab
  artifactKey: Guid(3361286819423b84189232b9d797cb59) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab using Guid(3361286819423b84189232b9d797cb59) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed395b23e4fdd09c4035e61bc97036b4') in 0.0339281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_01.prefab
  artifactKey: Guid(abc00000000016444828941082041913) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleSlim_01.prefab using Guid(abc00000000016444828941082041913) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '430cc5ef3e2db2478ee20b1a1acea257') in 0.0315073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab
  artifactKey: Guid(a8b3630e141365046a63788d12883c2b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab using Guid(a8b3630e141365046a63788d12883c2b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8ad5fb212dfb56bc9b15e3abd66254b7') in 0.0316298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Empty/Settings/HDRP Performant.asset
  artifactKey: Guid(168a2336534e4e043b2a210b6f8d379a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRP Performant.asset using Guid(168a2336534e4e043b2a210b6f8d379a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0c49f6e0ba059e78af861c10fe863fa1') in 0.0301614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Dissolve.prefab
  artifactKey: Guid(0b310bd220d148d418f2538140fc7480) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterLaser_Dissolve.prefab using Guid(0b310bd220d148d418f2538140fc7480) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '14e1960035335845a8120255c6542129') in 0.0661693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 92

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset
  artifactKey: Guid(ac0316ca287ba459492b669ff1317a6f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset using Guid(ac0316ca287ba459492b669ff1317a6f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '21b3ab1ed5159d5ec1b3f5f10e48e4f1') in 0.0317156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted T.png
  artifactKey: Guid(a922462e80e208a48a191026f396b3db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Dotted T.png using Guid(a922462e80e208a48a191026f396b3db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67b7c235c91fcceb0febc0f04322822f') in 0.0751747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Classic II.png
  artifactKey: Guid(337d0d2cecd75e8459f18cf48c331191) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Classic II.png using Guid(337d0d2cecd75e8459f18cf48c331191) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '937793f0f69890ff9c9ac510d6f39098') in 0.0722631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_09.mat
  artifactKey: Guid(1bf5bbb41311a39418d7ca27f69b45ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_09.mat using Guid(1bf5bbb41311a39418d7ca27f69b45ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25b262b067241b39a66ced04a47b71ed') in 0.6686115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_23.mat
  artifactKey: Guid(58f386b41f1f707489dc7a8024998b86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_23.mat using Guid(58f386b41f1f707489dc7a8024998b86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ba8f95b712e64bb1391c736e25bad67') in 0.1411284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Operations.cs
  artifactKey: Guid(4550f509374cf9247af928b802605a19) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_Operations.cs using Guid(4550f509374cf9247af928b802605a19) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e2b207911edddd7a45103922afec253') in 0.0352599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved L.png
  artifactKey: Guid(cdfc6ba1b0505bf40b9f27ff65aa9d68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved L.png using Guid(cdfc6ba1b0505bf40b9f27ff65aa9d68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cbb187e7c98a34e05cf432c247d4f88b') in 0.0728257 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_21.mat
  artifactKey: Guid(52de0a7cf7ee244439a842513dd2ee77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_21.mat using Guid(52de0a7cf7ee244439a842513dd2ee77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e523666dd7b763e72bbe292c0332e62e') in 0.1127051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_07.mat
  artifactKey: Guid(c72815c8d4959c94b9ce3fb13ecdd2b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_07.mat using Guid(c72815c8d4959c94b9ce3fb13ecdd2b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9519b7846430bf57529441751c2601b6') in 0.1141784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default.png
  artifactKey: Guid(3d5895f4fcb1c56438f3935338e1325d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Separator Background Image Default.png using Guid(3d5895f4fcb1c56438f3935338e1325d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '198c878245e0c76c21fdc5a7ebb59823') in 0.0662296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Common_File.cs
  artifactKey: Guid(33d14bb2abe530946a53cee3f2a1f371) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Common_File.cs using Guid(33d14bb2abe530946a53cee3f2a1f371) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '061f6fb526fb76d7bca1e763f7e57a70') in 0.02513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.386690 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_28.mat
  artifactKey: Guid(1d1c2075b6e9bb949a4882a3dabc46c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_28.mat using Guid(1d1c2075b6e9bb949a4882a3dabc46c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b242569104d54119bd548c248b117b01') in 0.1924986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/SkinDiffusionProfile.asset
  artifactKey: Guid(f84f836b876b06244bc938065726d90c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/SkinDiffusionProfile.asset using Guid(f84f836b876b06244bc938065726d90c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ae47642d6708e35e33ee53d560a2725') in 0.0361759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/legal.md
  artifactKey: Guid(f43071b4c4effb544b94c45f747c183f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/legal.md using Guid(f43071b4c4effb544b94c45f747c183f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c10243743061d1261964a202e425795') in 0.0483373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_27.mat
  artifactKey: Guid(b9d0a37c46b904a4f82917d3be3e3d65) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_27.mat using Guid(b9d0a37c46b904a4f82917d3be3e3d65) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4aa18f7008c1a83d245fe8817fc82c54') in 1.1711784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset
  artifactKey: Guid(335855edb797e1a4bbdb1dd3439fee97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset using Guid(335855edb797e1a4bbdb1dd3439fee97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ef2cf14db448e3659768da52f0e1172') in 0.0383819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000096 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_Dirty.mat
  artifactKey: Guid(3349ed0d94a0239429abff5c3ad7f9f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_Dirty.mat using Guid(3349ed0d94a0239429abff5c3ad7f9f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c0c69e6ac57527a24c609a4e92b2ed8') in 0.9176116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_09.mat
  artifactKey: Guid(dc5a054523e0c9041ac5aa90e1a4277d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_09.mat using Guid(dc5a054523e0c9041ac5aa90e1a4277d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d3c596f502d057768017ba28bd9a49a') in 0.6837776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/T_Placeholder_01.png
  artifactKey: Guid(8dae0fe04fdb4944eb443066499e0e2d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/T_Placeholder_01.png using Guid(8dae0fe04fdb4944eb443066499e0e2d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53d4762e89fed57b41c037a58925e05c') in 0.0623346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_26.mat
  artifactKey: Guid(990a52721518d9944bc2229571aa814a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_26.mat using Guid(990a52721518d9944bc2229571aa814a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7f3405610589bc72bb2c50a70acff38') in 0.4760395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/T_Placeholder_03.png
  artifactKey: Guid(b88b3d7ba7e77a14a9ef85ac35b0fe48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/T_Placeholder_03.png using Guid(b88b3d7ba7e77a14a9ef85ac35b0fe48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '558cc16ccde2b08b6e7f0f2f3980bbb0') in 0.0538514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_Mask_Blood_01.png
  artifactKey: Guid(4045d538d6f86f841800900611fba4c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_Mask_Blood_01.png using Guid(4045d538d6f86f841800900611fba4c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d7174aa308a6d7237023b14d8d3de41') in 0.0790382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Meshes/Girl/SK_ROCA_FERAL_NUDE.fbx
  artifactKey: Guid(605e98213d85664419e736c1112b8c7b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Meshes/Girl/SK_ROCA_FERAL_NUDE.fbx using Guid(605e98213d85664419e736c1112b8c7b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad38cd74d0c147efde0f1db714fe8a54') in 0.3585451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 360

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_01.mat
  artifactKey: Guid(755525e11fc799b4ba9f2c57be2951f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_16_01.mat using Guid(755525e11fc799b4ba9f2c57be2951f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a4840eff7f65c1bf7e516deb5ca9697') in 0.2766906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_11_02.mat
  artifactKey: Guid(fc944e3eaa1973642a23eac3cb0ca075) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_11_02.mat using Guid(fc944e3eaa1973642a23eac3cb0ca075) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e4e5a87b039cd09cd93e1e43e280087') in 0.3164233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_14_03.png
  artifactKey: Guid(cf91253082a102f4fad53e71842ed4d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_14_03.png using Guid(cf91253082a102f4fad53e71842ed4d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c2091142392ba3c2b05809796214cb2') in 0.1224457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000113 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_02.png
  artifactKey: Guid(7e835f9e9bca27c4bb3e3ff71ed1d578) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_02.png using Guid(7e835f9e9bca27c4bb3e3ff71ed1d578) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a162d605c2becda250080a6c48487cb5') in 0.0527678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_01.png
  artifactKey: Guid(6e4dbc649f0556e4f9e832086ef65e3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_12_01.png using Guid(6e4dbc649f0556e4f9e832086ef65e3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df759b7f5752bc7b387a4d27628d8eb1') in 0.0629215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_19.png
  artifactKey: Guid(d7943d4c14fcf254cb01c5fe3d907fae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_19.png using Guid(d7943d4c14fcf254cb01c5fe3d907fae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79c7b1e6439c754d4d3a1b4611858d30') in 0.0702252 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_29.png
  artifactKey: Guid(086def10cf263ff4194a75ca9c571871) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_29.png using Guid(086def10cf263ff4194a75ca9c571871) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33452f61983470abba640d8dfe040d53') in 0.0609191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_28.png
  artifactKey: Guid(323b5809bea082e48a15bb77f1891761) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_28.png using Guid(323b5809bea082e48a15bb77f1891761) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04ab49503de98250b4b141fe492da067') in 0.064738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN_Nude Variant.prefab
  artifactKey: Guid(29733b8cadf4a5641b3c540355fd5562) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_HUMAN_Nude Variant.prefab using Guid(29733b8cadf4a5641b3c540355fd5562) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be6c7b5f0975e0988a4c304009107ee0') in 0.1162671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 602

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_31.mat
  artifactKey: Guid(d9a07a5cf36723747b9cf8f03ca7da71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_31.mat using Guid(d9a07a5cf36723747b9cf8f03ca7da71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de04ca82cfcda46e5ac4d878f8ac7b42') in 0.2601815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_31.png
  artifactKey: Guid(a281f20794a866d4aa62df8d42921324) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_31.png using Guid(a281f20794a866d4aa62df8d42921324) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97d8b33af2e1c165ba174b6e1118d4bf') in 0.0752137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_F_BaseColor.png
  artifactKey: Guid(92ca30aed23fd71468d3d43b5fa9dc04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_F_BaseColor.png using Guid(92ca30aed23fd71468d3d43b5fa9dc04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f590db0c2a5f1f38d67210cd151d5b5c') in 0.0919825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_27.png
  artifactKey: Guid(ec2dbf7a19af7e841af46080cbb8d62c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_27.png using Guid(ec2dbf7a19af7e841af46080cbb8d62c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19e5a53811156f354d80f5f121cc5ab2') in 0.0903481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_06.mat
  artifactKey: Guid(b3f9024b9ea39364ca22d073b9134cd3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_06.mat using Guid(b3f9024b9ea39364ca22d073b9134cd3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '821d91ae42144c3c0823992da8bc5d84') in 0.1784691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_01.png
  artifactKey: Guid(d7f705ec5e6dcc346b8c415a9807504d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_01.png using Guid(d7f705ec5e6dcc346b8c415a9807504d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '276a57b3107e89c59b64b90d223c1a06') in 0.0842104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_09.png
  artifactKey: Guid(b82cda3551e8f6d4486fedbb279b1f4f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_09.png using Guid(b82cda3551e8f6d4486fedbb279b1f4f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e691a1aacf7c4e10cde17aeaa81b68c3') in 0.0529072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_03.png
  artifactKey: Guid(e7e05892173674a469d5d58c9954a542) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_03.png using Guid(e7e05892173674a469d5d58c9954a542) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '773460f8e889a6d0481ce83c0882492a') in 0.0644782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Pink.mat
  artifactKey: Guid(803490b4b81853744ad39dce2bd24c7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Pink.mat using Guid(803490b4b81853744ad39dce2bd24c7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2581c4909a1e803e6f91315c083c638') in 0.073208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Red.mat
  artifactKey: Guid(eaf1b4dae53bbaa418c9ce4ff9e2c2c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Red.mat using Guid(eaf1b4dae53bbaa418c9ce4ff9e2c2c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4730704f5c27f2c0c8c0390d7f84f3b9') in 0.0678927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_1.mat
  artifactKey: Guid(8f3f66c592950e24d8d76f93f49046fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_1.mat using Guid(8f3f66c592950e24d8d76f93f49046fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dde4d9e9441f097f47aa4ba732dd1066') in 0.0459669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)LightBlue.mat
  artifactKey: Guid(810f2207d0c19a94980377788bfa507c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)LightBlue.mat using Guid(810f2207d0c19a94980377788bfa507c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f04755d5f229df246e7785a5beccea3e') in 0.0531659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene/ReflectionProbe-0.exr
  artifactKey: Guid(29c76b2da2e73674294e3d8c68cee651) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene/ReflectionProbe-0.exr using Guid(29c76b2da2e73674294e3d8c68cee651) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f67f99d9fc8c51c89d5acddd310e62d') in 0.0554613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Running.fbx
  artifactKey: Guid(52e19e9667b8f4546ad61f69c8b6c77a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Running.fbx using Guid(52e19e9667b8f4546ad61f69c8b6c77a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe009215f53fd67e0771237cbc653919') in 0.0923031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_v2_01.png
  artifactKey: Guid(e6e56b183941a834f97abd49bb60b177) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_v2_01.png using Guid(e6e56b183941a834f97abd49bb60b177) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02fce768c15ca57df0bd2f7a8e845d87') in 0.0483319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_SpecMask.png
  artifactKey: Guid(812cb94a1f674e543bfcc2cd4a97c5f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_SpecMask.png using Guid(812cb94a1f674e543bfcc2cd4a97c5f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c12186719d98cc6c55790f1975b37e9') in 0.0871154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)ClimbingUpWall.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)ClimbingUpWall.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb7602ac886e799976bdbe284ccd2dad') in 0.0693749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_08.png
  artifactKey: Guid(44e2c0140fe2a704ebfcf619df340b31) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_08.png using Guid(44e2c0140fe2a704ebfcf619df340b31) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30b812b563440d506a08be27374253b9') in 0.1279665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000167 seconds.
  path: Assets/IdaFaber/Materials/Other/MAT_Skybox_01.mat
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Other/MAT_Skybox_01.mat using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a37f8b2cf018b61ccf5c9057d0d5cee') in 0.1431189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)PlayerAnimated.controller
  artifactKey: Guid(7340e95af663dfc4098759cb5383ee02) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Ctrl)PlayerAnimated.controller using Guid(7340e95af663dfc4098759cb5383ee02) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c951309c7cb513fb3b1defde6c68b30') in 0.0433525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.025222 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab
  artifactKey: Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Ladder.prefab using Guid(98a0f2f9421d51c41912a51c60739ecc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e15fe2c32a3cc6700d1c1338883aad2') in 0.1117012 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab
  artifactKey: Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab using Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44554cd65c7d4ded9d079e51789d2c23') in 0.0817908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_MT_GlowAlpha_01.png
  artifactKey: Guid(2f6642b43dc405d498acba7e288c068c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_MT_GlowAlpha_01.png using Guid(2f6642b43dc405d498acba7e288c068c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4a18926cba83b574d99b90263f280371') in 0.0627648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_CicularSlash_4x4_01.psd
  artifactKey: Guid(c5e19cfdbd8d9a54aa7f9f219f0130dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_CicularSlash_4x4_01.psd using Guid(c5e19cfdbd8d9a54aa7f9f219f0130dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3968bdf3f1fd18a4757d7a7fabbc34e6') in 0.0864643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_01 1.png
  artifactKey: Guid(3eadddce319c71946828713a70bf99ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_01 1.png using Guid(3eadddce319c71946828713a70bf99ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e6f82fb85c63aa903e457616c2848a55') in 0.0535365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/PP Profile.asset
  artifactKey: Guid(b176d70af7536e8418281ba8ea518d6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/PP Profile.asset using Guid(b176d70af7536e8418281ba8ea518d6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c44c19e5e9bbf345f19ca26b59752fac') in 0.0362651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_03.png
  artifactKey: Guid(18bfbed286f7e774ba9c1a67bea95026) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_FlamesNoise_03.png using Guid(18bfbed286f7e774ba9c1a67bea95026) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d8187dc2f8ef28d35a5e0caa70ae71e') in 0.0562828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab
  artifactKey: Guid(9b059dc6477a3be49baa04addc732e67) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab using Guid(9b059dc6477a3be49baa04addc732e67) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'becd8901d3754590d52797c053837b78') in 0.0625461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab
  artifactKey: Guid(a8b3630e141365046a63788d12883c2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubblePiramid.prefab using Guid(a8b3630e141365046a63788d12883c2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '172c3e0711a03a7a88e2b5e4ce813931') in 0.0659819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterLateralImpact_4x3_01.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterLateralImpact_4x3_01.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26a9a6cb4bddf55412131b0fc3b6555e') in 0.0700137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CausticNoise_01_Aberration.psd
  artifactKey: Guid(66e37b339a7d7ac4193a9e5d2e713cfa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_CausticNoise_01_Aberration.psd using Guid(66e37b339a7d7ac4193a9e5d2e713cfa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21e5d9e4cc6196a215687f5b0dd5b616') in 0.058229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_01.psd
  artifactKey: Guid(c08fe553da44f344c99c50a00b66dbcf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_01.psd using Guid(c08fe553da44f344c99c50a00b66dbcf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44663ed85d5786234ab31a64a9341079') in 0.0517541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Char/Char_MS_Dummy.fbx
  artifactKey: Guid(b092a9b9046839a4ca91751c2a2c02b2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Char/Char_MS_Dummy.fbx using Guid(b092a9b9046839a4ca91751c2a2c02b2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '032c88f0d192e6d465a7eee62e74b12e') in 0.0857355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000148 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_01_4x3.psd
  artifactKey: Guid(416c6457ccc3afb40bdbc1fd1602985e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_RainSplash_01_4x3.psd using Guid(416c6457ccc3afb40bdbc1fd1602985e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7393e407c26af707bf8e813fcc23015') in 0.1121896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.412070 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_Sharp.png
  artifactKey: Guid(f841cfca36ed44746962b212c78a879b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterNoise_Sharp.png using Guid(f841cfca36ed44746962b212c78a879b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7e8e8db3db23026251954e20f396cdb') in 0.0548633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_20.mat
  artifactKey: Guid(4c8a17841d2317f4693cadc991f86e5d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_20.mat using Guid(4c8a17841d2317f4693cadc991f86e5d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c24f24b5fbecc2a519a93f5c940cc1e4') in 1.2877022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_17.mat
  artifactKey: Guid(1632973a21dd8ba49997e9c161dca3bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_17.mat using Guid(1632973a21dd8ba49997e9c161dca3bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1735835c5e81f38b5ebcc92eca1196d5') in 0.2070056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_B.FBX
  artifactKey: Guid(841adc2ec8c3240429fcfc35e9810f52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_B.FBX using Guid(841adc2ec8c3240429fcfc35e9810f52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5266d1ae596c22eb63729855ddbc606') in 0.0784396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_CicularSlash_4x4_01.png
  artifactKey: Guid(8d8bfc2797c19c346af385dc5ce87f0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_CicularSlash_4x4_01.png using Guid(8d8bfc2797c19c346af385dc5ce87f0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d9b8b9e929311996feb00dbca395435') in 0.091701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Textures/Env_TX_GroundGrid_Normal.png
  artifactKey: Guid(bd90f1c8e2cb02a439bf27a6fd2c329f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/DemoSceneAssets/Textures/Env_TX_GroundGrid_Normal.png using Guid(bd90f1c8e2cb02a439bf27a6fd2c329f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6e7fcd25eeca9b2bea18bcaebaef6a3') in 0.0663318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_05.png
  artifactKey: Guid(c35a421add28a0c44ad238aac098c7b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_05.png using Guid(c35a421add28a0c44ad238aac098c7b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d869331ed564e5351ba6e0477a13a02') in 0.0584471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundWaterSplash_4x4_01.png
  artifactKey: Guid(468408a07fa35064ba6c663186e90fec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_GroundWaterSplash_4x4_01.png using Guid(468408a07fa35064ba6c663186e90fec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48d27bb5626ae677f2f5a654e29d3091') in 0.0827212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_E.FBX
  artifactKey: Guid(b0136c017fda75d4da687733598ab195) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_E.FBX using Guid(b0136c017fda75d4da687733598ab195) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b46448e369c355788efc56d54f60ff3e') in 0.0575686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000094 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamSmoke_4x4_01.png
  artifactKey: Guid(5067602e5b2cb134ea6acaabefc29c92) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FoamSmoke_4x4_01.png using Guid(5067602e5b2cb134ea6acaabefc29c92) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc45c7b04befa2081a7d9a7dc2f36f00') in 0.0503616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_Loop_1.wav
  artifactKey: Guid(d4b93f312b6b8df48a199986d83ed60e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_Loop_1.wav using Guid(d4b93f312b6b8df48a199986d83ed60e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ff4a1035c555c4ef11c776a1e72226f') in 0.4113003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_4x4_02_Grayscale.png
  artifactKey: Guid(0abbf54d58abf86499a6587d90c329a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_4x4_02_Grayscale.png using Guid(0abbf54d58abf86499a6587d90c329a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a8806efaaac9c06d4d914c27e27ca0b') in 0.0521979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_4x4_02.png
  artifactKey: Guid(6104f71babe2e3148a71fd8b04b9d1fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_VerticalSplash_4x4_02.png using Guid(6104f71babe2e3148a71fd8b04b9d1fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cacca43d82483f3585725dedf01ef1cc') in 0.0668687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/14__Idle_To_Crouch/M_Big_Sword@Idle_To_Crouch.FBX
  artifactKey: Guid(ac6b7db4c3f10234bb4bd517b3da4d98) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/14__Idle_To_Crouch/M_Big_Sword@Idle_To_Crouch.FBX using Guid(ac6b7db4c3f10234bb4bd517b3da4d98) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d899a908d92405bbfc781707e0c70cf') in 0.0746428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_3.wav
  artifactKey: Guid(9461cda836e01a548b77b4157b09520a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_3.wav using Guid(9461cda836e01a548b77b4157b09520a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98ace7cc3309bf1d1bab9ffa0c655f7e') in 0.1588947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/16__Equip_Unequip/<EMAIL>
  artifactKey: Guid(176296c2cbe8ac44daa619ee404c4c51) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/16__Equip_Unequip/<EMAIL> using Guid(176296c2cbe8ac44daa619ee404c4c51) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85dda2dadaa1c61a31cb680f2f0f2361') in 0.0731639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_B.FBX
  artifactKey: Guid(81e073fa2fefb6f4aa9d9dbf0cd3e5d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_B.FBX using Guid(81e073fa2fefb6f4aa9d9dbf0cd3e5d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee09d27ce6d14cb9a38fd2560d24eaa3') in 0.0583771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_11.mat
  artifactKey: Guid(b2d0b7f89a3893a4d9606563218e16a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_11.mat using Guid(b2d0b7f89a3893a4d9606563218e16a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c41e78713f0c18d6b5532546c0657170') in 0.1604695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_C_Loop_1.wav
  artifactKey: Guid(1bcb563abfd177d489f7a30bf0225b14) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/MUSIC/MUSC_C_Loop_1.wav using Guid(1bcb563abfd177d489f7a30bf0225b14) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca516d4b7d217830b4b12f1ce18ac313') in 1.0026974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_B_Root.FBX
  artifactKey: Guid(86399112b7ec1164d89dca32f17ded71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_B_Root.FBX using Guid(86399112b7ec1164d89dca32f17ded71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77da3a4a2743960961a1e51a9e0808be') in 0.0706592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000142 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FL45_Root.FBX
  artifactKey: Guid(2ede1b5d892c4d141b49342a046b9fcc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_FL45_Root.FBX using Guid(2ede1b5d892c4d141b49342a046b9fcc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64c852caeedd907775d461033bd1c770') in 0.0632628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_End.FBX
  artifactKey: Guid(29a826513b0d8d1408feb76ba62c29a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_End.FBX using Guid(29a826513b0d8d1408feb76ba62c29a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2374c21684c9c3c4226f4ed87704e10') in 0.0807055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_21.mat
  artifactKey: Guid(f35c91404c86b0c4a8c743b30bfe4ed3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_21.mat using Guid(f35c91404c86b0c4a8c743b30bfe4ed3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '334ae5634b8fe5a9037486f02e87469b') in 0.2349649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_08.mat
  artifactKey: Guid(ec7f3d2cdc306b34ab041c1e59218efe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_08.mat using Guid(ec7f3d2cdc306b34ab041c1e59218efe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b461d9abee0b662a86652c3411e50814') in 0.2120435 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_1.wav
  artifactKey: Guid(5ece54a5cb7d2394aafa3b7b135044ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Player/SFX_Player_Meditate_1.wav using Guid(5ece54a5cb7d2394aafa3b7b135044ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c22ec3639db461fe37df7b3f8df43cb') in 0.189322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_LevelStart_1.wav
  artifactKey: Guid(7c8f692fea1765842ac388f1eb93a250) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_LevelStart_1.wav using Guid(7c8f692fea1765842ac388f1eb93a250) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d6269eb2463f0fc286cbe6d4f19b69d') in 0.2024519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Depth.png
  artifactKey: Guid(d7996387873c1de4591b3851ad9d7c86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_Depth.png using Guid(d7996387873c1de4591b3851ad9d7c86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79d408d7d822876db280927633050bad') in 0.0955179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Front_Root.FBX
  artifactKey: Guid(30295695bbb305745bd4a3f28650fb9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_Front_Root.FBX using Guid(30295695bbb305745bd4a3f28650fb9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff595e89cfc6766ae3e1ca07c1fccfab') in 0.0599941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Meditative_1.wav
  artifactKey: Guid(5078735f615ddce47b736af75c3bd4bc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Lose_Meditative_1.wav using Guid(5078735f615ddce47b736af75c3bd4bc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05a877725fa8d1ed27d38241bb20131e') in 0.21602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL90_Root.FBX
  artifactKey: Guid(39b5a120aaa5523458a0dd2bf02a4309) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_B_To_Walk_BL90_Root.FBX using Guid(39b5a120aaa5523458a0dd2bf02a4309) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c048dd351965b4d47fc4e51e7b4bb000') in 0.0733052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Front_Root.FBX
  artifactKey: Guid(d752f9e9e22e7684bbc35b30a31da967) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_Front_Root.FBX using Guid(d752f9e9e22e7684bbc35b30a31da967) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6cb0ca9eceb1bc39cb0bc5eb8a5f997') in 0.0702991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A.FBX
  artifactKey: Guid(41a6295a81dcdfd4ab1970b57b92b0d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured_Damage_ver_A.FBX using Guid(41a6295a81dcdfd4ab1970b57b92b0d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7dced6f5d6d7ef6988083f663578ded8') in 0.060649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die_Root.FBX
  artifactKey: Guid(e0f492aa502b6824aaa02e36313cbc47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/6__Die/M_Big_Sword@Damage_Die_Root.FBX using Guid(e0f492aa502b6824aaa02e36313cbc47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '015c3b3772bf1889b86e350a86faaf1f') in 0.0773553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Big_ver_B.FBX
  artifactKey: Guid(aeee472955420dc458d817bddb1c619e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Big_ver_B.FBX using Guid(aeee472955420dc458d817bddb1c619e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41ba2b23011531489013c79bbdebfc7e') in 0.0713242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_A_Turn.FBX
  artifactKey: Guid(56bf1803d5ae68647bb3f5a38652f34b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_A_Turn.FBX using Guid(56bf1803d5ae68647bb3f5a38652f34b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13ffc11c97f6a386597d3eca4a561c3a') in 0.0654901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_A_to_B.FBX
  artifactKey: Guid(c32d21769dd96664fbd0e245cb247bca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/1__Idle/M_katana_Blade@Idle_A_to_B.FBX using Guid(c32d21769dd96664fbd0e245cb247bca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6aa14c2e9b054171b67da1727953a38b') in 0.0549922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/0__Intro/<EMAIL>
  artifactKey: Guid(af5e97a7b3911fe4d93337bf22a947a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/0__Intro/<EMAIL> using Guid(af5e97a7b3911fe4d93337bf22a947a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e00da04b45b6e0f143f947b66b3ddc8') in 0.1038468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000892 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_BW_Rolling_StandUp.FBX
  artifactKey: Guid(0860300cd9221f548b8949d61536b3cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_BW_Rolling_StandUp.FBX using Guid(0860300cd9221f548b8949d61536b3cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e9fd6e56f1b1a5b9355259a0ec75e4c') in 0.0901299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_ver_A.FBX
  artifactKey: Guid(7f3a6899db57611459be733271b7154e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_ver_A.FBX using Guid(7f3a6899db57611459be733271b7154e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '016cdfb9574f432e18782d6e664f19da') in 0.0704148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_A_Idle.FBX
  artifactKey: Guid(1174a5137fab6c5409b2cd36a46855b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/15__Walk_To_Crouch/M_katana_Blade@Walk_ver_B_To_Crouch_ver_A_Idle.FBX using Guid(1174a5137fab6c5409b2cd36a46855b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '394c9f1f64b560009b15599d3ef0b83a') in 0.0559061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/5__Upper_Attack/M_Big_Sword@UpperAttack_ZeroHeight.FBX
  artifactKey: Guid(ec7b3ec177e7ac64a8a6d76909e1c4ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/5__Upper_Attack/M_Big_Sword@UpperAttack_ZeroHeight.FBX using Guid(ec7b3ec177e7ac64a8a6d76909e1c4ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c0321000f7e0cbedf80b046a45107c0') in 0.0773202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_3_Inplace.FBX
  artifactKey: Guid(789accfdbdd57ce4ba3a794ab0e0a8a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/1__4Combos/M_Big_Sword@Attack_4Combo_3_Inplace.FBX using Guid(789accfdbdd57ce4ba3a794ab0e0a8a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccbb7331736a1e99a49c02234d2e84ce') in 0.0635391 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Damage.FBX
  artifactKey: Guid(48c6f1a361c475748a1621c979e9570f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Back_Damage.FBX using Guid(48c6f1a361c475748a1621c979e9570f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8cd2c2bf54c93160b7a0c06a3b98346') in 0.0498295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Forest_River_Loop_1.wav
  artifactKey: Guid(19e48973be273d740b935c1d36c8672c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/AMB/AMB_Forest_River_Loop_1.wav using Guid(19e48973be273d740b935c1d36c8672c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0fc600956a96811640726c5983e360ad') in 0.4568105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_1.wav
  artifactKey: Guid(190eae341147c844ea3b799b45238b38) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_1.wav using Guid(190eae341147c844ea3b799b45238b38) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f1f1352ceafe0ca4f8f8a02716d373e') in 0.1796141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_ZeroHeight.FBX
  artifactKey: Guid(42cb0a1435110a44791d012fcc847486) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3_ZeroHeight.FBX using Guid(42cb0a1435110a44791d012fcc847486) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94caf592cdf0ffcc0fe5a16dbddf7535') in 0.1032974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 322

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Damage.FBX
  artifactKey: Guid(753397663fa91af45ab6ee69589a9c9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_Damage.FBX using Guid(753397663fa91af45ab6ee69589a9c9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5683f79999e02b506d3a3c463d22c5a8') in 0.0591378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/3__Left/M_katana_Blade@Damage_Left_Small_ver_B.FBX
  artifactKey: Guid(f5f14416d2831044aa29dcb917201ba3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/3__Left/M_katana_Blade@Damage_Left_Small_ver_B.FBX using Guid(f5f14416d2831044aa29dcb917201ba3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de2150b8bd9081919240a8b5652a3a2f') in 0.076112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_B.FBX
  artifactKey: Guid(c74e90aa6e0f35244844016336eb263f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Small_ver_B.FBX using Guid(c74e90aa6e0f35244844016336eb263f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99862056f1aaade83cad2dcf49c3bd6d') in 0.0538868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_2_Inplace.FBX
  artifactKey: Guid(0faafc763f001ca4d95fcc89fcc40111) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_2_Inplace.FBX using Guid(0faafc763f001ca4d95fcc89fcc40111) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47c7519fe3c67f94bcdd5bb9f6db8a8e') in 0.0536399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Block_Root.FBX
  artifactKey: Guid(f2a86e2f0b73a564a81abcbf635b4321) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Block_Root.FBX using Guid(f2a86e2f0b73a564a81abcbf635b4321) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56a36dc3b4d19fffd37d7e02730cafb5') in 0.0834702 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_2.FBX
  artifactKey: Guid(35a7fdd9f21767d43b469fbea5ea7f42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_2.FBX using Guid(35a7fdd9f21767d43b469fbea5ea7f42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'adda4612e26e2c0399c698a4b5693b0c') in 0.0561981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_3_Inplace.FBX
  artifactKey: Guid(c5e14bed6fb6ce14eb126584b6865385) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/1__4Combos/M_katana_Blade@Attack_4Combo_3_Inplace.FBX using Guid(c5e14bed6fb6ce14eb126584b6865385) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65a9a033c3d4d54376d087d562e0f638') in 0.089416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_BW_Rolling_StandUp.FBX
  artifactKey: Guid(6d8b0198da32b234ab03b6a0caa62cf4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Back_BW_Rolling_StandUp.FBX using Guid(6d8b0198da32b234ab03b6a0caa62cf4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86742d0b1314a30dc776aaaef002a7d1') in 0.2342502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A.FBX
  artifactKey: Guid(73d5d4e0d6cd9554ba4d6ed5d76cb0fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A.FBX using Guid(73d5d4e0d6cd9554ba4d6ed5d76cb0fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '314501e0c791b21713fb7503ed83ae91') in 0.11583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_04.png
  artifactKey: Guid(f06052f83c83b124591f0f726c91d21f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_04.png using Guid(f06052f83c83b124591f0f726c91d21f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b96d03762ab792a1417d1059b3d35d60') in 0.051231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_2.FBX
  artifactKey: Guid(7880debb8c11bb24fb791405e0967658) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_2.FBX using Guid(7880debb8c11bb24fb791405e0967658) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83171eb526046038dcadb5be0d19c50b') in 0.0622774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_69.fbx
  artifactKey: Guid(abc00000000017745848836579849995) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_69.fbx using Guid(abc00000000017745848836579849995) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68065ad98dd1e4d40624ebe8e75b46ff') in 0.1134672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_14.prefab
  artifactKey: Guid(69d074200c80fc041be1fce32b9208da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_14.prefab using Guid(69d074200c80fc041be1fce32b9208da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a810e126182168070739c80428505701') in 0.1415363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_04.mat
  artifactKey: Guid(abc00000000015136448172506926395) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_04.mat using Guid(abc00000000015136448172506926395) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7714f1fcdab9c6dbe43444fb67e0a9ff') in 0.0850976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Attack_ver_B.FBX
  artifactKey: Guid(a0b6c3a86c4fcce40aedd9ce6b370470) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Attack_ver_B.FBX using Guid(a0b6c3a86c4fcce40aedd9ce6b370470) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e740c18cca5f4fbb5ee34bfaf54742b5') in 0.1034847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickWall.mat
  artifactKey: Guid(abc00000000014630764845243636829) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickWall.mat using Guid(abc00000000014630764845243636829) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b8107e6b1136454b97f41eac8f2b24e') in 0.1058902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_VarnishedWood_01.mat
  artifactKey: Guid(abc00000000014639684016786500895) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_VarnishedWood_01.mat using Guid(abc00000000014639684016786500895) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fa30bc419f9a908580cabf461379e80') in 0.2170021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Water_02.mat
  artifactKey: Guid(8d4ac63cff333714389e5ec627901bdd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Water_02.mat using Guid(8d4ac63cff333714389e5ec627901bdd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b9199cae1c725cbc10360a8cbff981b') in 0.0746412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_66.fbx
  artifactKey: Guid(abc00000000017382714042356393586) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_ChainLink_01_Splines_66.fbx using Guid(abc00000000017382714042356393586) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e42a5e7eaca8c18875268ecea7882dd') in 0.1630832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Blue.mat
  artifactKey: Guid(abc00000000015303638101263416809) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Cloth_Blue.mat using Guid(abc00000000015303638101263416809) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b04bba48952abcad929b3ce51b88d2b') in 0.1112036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Spear_Inplace.FBX
  artifactKey: Guid(9c1cab0e6d1bdbb43a6c8842b005914f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Spear_Inplace.FBX using Guid(9c1cab0e6d1bdbb43a6c8842b005914f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19e1cf893962e4a9d5eebb416b00f792') in 0.0794416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A.prefab
  artifactKey: Guid(abc00000000001070414280521708689) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A.prefab using Guid(abc00000000001070414280521708689) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5468d8d42929a4fe34e9e2677f47ef9') in 0.1243521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_29.fbx
  artifactKey: Guid(abc00000000007497764754662338920) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_29.fbx using Guid(abc00000000007497764754662338920) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abc9da46a35bcd5b1f691559182a9e8c') in 0.1655078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_02.prefab
  artifactKey: Guid(abc00000000002367564937674463636) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_02.prefab using Guid(abc00000000002367564937674463636) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '61d1e08d1cfe84368dc6f01448e01153') in 0.0913208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000496 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_16.FBX
  artifactKey: Guid(0a0351dd1dbf31d4e8b05882acfe3a31) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_16.FBX using Guid(0a0351dd1dbf31d4e8b05882acfe3a31) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eed46ef08e65651ecf935d9a274d2996') in 0.1151244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rug_1.mat
  artifactKey: Guid(abc00000000013622290597065585114) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Rug_1.mat using Guid(abc00000000013622290597065585114) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abf64ecdc8ecab277eabee36dbe02424') in 0.0662607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.FBX
  artifactKey: Guid(583f9c8ea7b82b74181effd98702edb6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.FBX using Guid(583f9c8ea7b82b74181effd98702edb6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '921377f2e0301555875005b5efc45141') in 0.7775973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_C.prefab
  artifactKey: Guid(abc00000000005698991249062767216) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Candle_C.prefab using Guid(abc00000000005698991249062767216) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81eb11595e8fa555b9a90f0aa40c2a6e') in 0.0616801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_02.prefab
  artifactKey: Guid(abc00000000000170237559120467836) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_02.prefab using Guid(abc00000000000170237559120467836) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf49f49ede3fa5de0589d74eb07ba4d5') in 0.0736019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_01.prefab
  artifactKey: Guid(abc00000000003251423913722411099) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Pillar_A_01.prefab using Guid(abc00000000003251423913722411099) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a25f083edde4043a108944f80802ef0') in 0.075357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_02.mat
  artifactKey: Guid(abc00000000001475646603878286191) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Roof_Tiles_02.mat using Guid(abc00000000001475646603878286191) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee3dcb51126524bb28c91b0fc7205816') in 0.0716025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.FBX
  artifactKey: Guid(addbaa1a24e0ce64c866e498e88318c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_38.FBX using Guid(addbaa1a24e0ce64c866e498e88318c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bf1dc1e6d5f44e39e1423ab38c94903') in 0.4387124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_03.prefab
  artifactKey: Guid(abc00000000004347845447509042274) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_03.prefab using Guid(abc00000000004347845447509042274) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3cc86a52646f84689459d35406a07ad0') in 0.0695674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_09.prefab
  artifactKey: Guid(abc00000000007139590172207961599) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_09.prefab using Guid(abc00000000007139590172207961599) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '659d72ed083dfb27ef3a41febf6ecca3') in 0.0743721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Start.FBX
  artifactKey: Guid(aeb2fcdf254360041945eff804035045) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/5_Revenges/Guard_Revenges/M_katana_Blade@Revenge_Guard_Start.FBX using Guid(aeb2fcdf254360041945eff804035045) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43096745cd6b610782bfaec0b6920a9f') in 0.0581153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_02.prefab
  artifactKey: Guid(abc00000000001234702104932231217) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_02.prefab using Guid(abc00000000001234702104932231217) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '657d58b694ba58964343ad9e1ff935d3') in 0.0745035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01.prefab
  artifactKey: Guid(abc00000000009298666442671915430) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01.prefab using Guid(abc00000000009298666442671915430) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7da44a141d969e6a9135f5227f87b9d9') in 0.059916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong_Loose.prefab
  artifactKey: Guid(abc00000000004332266398211673837) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong_Loose.prefab using Guid(abc00000000004332266398211673837) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be364c706a968f6adf3864c0319800d8') in 0.0715913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Doorway_2M.prefab
  artifactKey: Guid(abc00000000016025576490165946346) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Doorway_2M.prefab using Guid(abc00000000016025576490165946346) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '650ad83ce93207f34adf66bb3e0cf21a') in 0.0687293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000130 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_drinkstable.prefab
  artifactKey: Guid(abc00000000008452398761007879384) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_drinkstable.prefab using Guid(abc00000000008452398761007879384) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '199010b7b84f89b0af8d0c4ee16876ac') in 0.0762588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_24.prefab
  artifactKey: Guid(abc00000000017506454398514029207) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_24.prefab using Guid(abc00000000017506454398514029207) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c61bd12a7c4fc1d007ea1000b34773ba') in 0.1198678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01_1.prefab
  artifactKey: Guid(abc00000000010210916380694647040) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Drawer_01_1.prefab using Guid(abc00000000010210916380694647040) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '364b0b1270eb4f4802ecc831d2565a02') in 0.0571812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_8.prefab
  artifactKey: Guid(abc00000000014308775244425524208) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_8.prefab using Guid(abc00000000014308775244425524208) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31db0bfc19d828215a34a04ce2ec9438') in 0.0761738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_20.prefab
  artifactKey: Guid(abc00000000010128640633105371986) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_20.prefab using Guid(abc00000000010128640633105371986) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04f9a24ff3315863e7592dbacd25d40b') in 0.1028654 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_68.prefab
  artifactKey: Guid(abc00000000007327313589950001394) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_68.prefab using Guid(abc00000000007327313589950001394) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ad97c0631df87ea85c7429569357410') in 0.0777738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_03.prefab
  artifactKey: Guid(abc00000000012716303510755586549) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_A_03.prefab using Guid(abc00000000012716303510755586549) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '256ec757fe03969699c82f1b9b95ad35') in 0.0697413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_10.png
  artifactKey: Guid(4a4d8df361ca7074396482afdb727b86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_10.png using Guid(4a4d8df361ca7074396482afdb727b86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d17cfba55fc85e4a771da2ed38e4083') in 0.0503013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_02.mat
  artifactKey: Guid(abc00000000007230725241517378017) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_02.mat using Guid(abc00000000007230725241517378017) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e745cac917513a71e6fabab1923d9b9c') in 0.0840898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_04.prefab
  artifactKey: Guid(abc00000000014686189744945388786) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_04.prefab using Guid(abc00000000014686189744945388786) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9522219de6fc79f16112b519785196d') in 0.0867877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01_1.prefab
  artifactKey: Guid(abc00000000003804069267254648650) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_B_01_1.prefab using Guid(abc00000000003804069267254648650) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7d633a0f6018cd03cf7dbe01bbb9d0a') in 0.0793225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08_1.prefab
  artifactKey: Guid(abc00000000010343720456142068162) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_08_1.prefab using Guid(abc00000000010343720456142068162) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40acce1b4d97f5593320109735fcc85b') in 0.0770888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01_1.prefab
  artifactKey: Guid(abc00000000008997333683276363716) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_01_1.prefab using Guid(abc00000000008997333683276363716) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49c5f49525e6ec5446384586d1b0124c') in 0.0825955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Keg.prefab
  artifactKey: Guid(abc00000000000865317528067898336) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Large_Keg.prefab using Guid(abc00000000000865317528067898336) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c1e31f8b4328c45e1cfdda372938905') in 0.0654906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05_1.prefab
  artifactKey: Guid(abc00000000002725194695316289400) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_05_1.prefab using Guid(abc00000000002725194695316289400) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bf3a00a585da09d0d4f99e5bd412a225') in 0.0695853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern.prefab
  artifactKey: Guid(abc00000000009577592741665352263) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Lantern.prefab using Guid(abc00000000009577592741665352263) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffc7d0ae73f4b9c5bde76dd5ebefed8b') in 0.0605871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_02.prefab
  artifactKey: Guid(abc00000000007864659359926381920) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_02.prefab using Guid(abc00000000007864659359926381920) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09f8259c68a74d4247df1ded3499cad0') in 0.0736011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HallTable_01.prefab
  artifactKey: Guid(abc00000000013875482050015112762) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_HallTable_01.prefab using Guid(abc00000000013875482050015112762) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6cdc7b31370fe124c9f9ce5044f639bd') in 0.0662691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sack_01.prefab
  artifactKey: Guid(abc00000000012836946732148259247) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sack_01.prefab using Guid(abc00000000012836946732148259247) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70f7d7ed1973f0bafa112546f1fcc944') in 0.0653082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_02.prefab
  artifactKey: Guid(abc00000000005111369591461232409) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LargeRoof_02.prefab using Guid(abc00000000005111369591461232409) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd47e3f0c8fe143716e199151f910f1e8') in 0.0987085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_02.prefab
  artifactKey: Guid(abc00000000001904259696300257621) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_02.prefab using Guid(abc00000000001904259696300257621) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b896018c160c3d817b50176758f692d') in 0.0550143 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabB.prefab
  artifactKey: Guid(abc00000000009222023049361081909) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabB.prefab using Guid(abc00000000009222023049361081909) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fde7857d7232c8db0162d465faf64621') in 0.0566588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_basecolor.PNG
  artifactKey: Guid(9c32248e66f61104692fc4f2389e6a77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_basecolor.PNG using Guid(9c32248e66f61104692fc4f2389e6a77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cf05763be5ed3393d113fa1fd44f936') in 0.0432687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_Normal.PNG
  artifactKey: Guid(60c23e1392ee268449d7f2f9bdb5717c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Candles_low_M_Candles_Normal.PNG using Guid(60c23e1392ee268449d7f2f9bdb5717c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '835dc1cb2087d7bf099d1356a395f167') in 0.0627876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_A.prefab
  artifactKey: Guid(abc00000000002018264587820517585) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_A.prefab using Guid(abc00000000002018264587820517585) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83142d2b334ba6f0097db8c98c2c1480') in 0.0810291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_03.prefab
  artifactKey: Guid(abc00000000015316129970873746105) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Stone_03.prefab using Guid(abc00000000015316129970873746105) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '953369d5fc5f60bb5f48b2a5088c5e0c') in 0.066356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_02.prefab
  artifactKey: Guid(abc00000000001978223226480770024) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_02.prefab using Guid(abc00000000001978223226480770024) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bdf9eb81b84270a32f48e8a8eaea62a4') in 0.061457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_CandleSmoke_01.PNG
  artifactKey: Guid(a41f34877ab319443aab911cbf847ac6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_CandleSmoke_01.PNG using Guid(a41f34877ab319443aab911cbf847ac6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16a70e54fbfcba762f72ea42d3d45761') in 0.0659915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_normal.PNG
  artifactKey: Guid(e3f8a6a1df205ef4e9c06174be373674) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_normal.PNG using Guid(e3f8a6a1df205ef4e9c06174be373674) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84b45de14be98ff5f7f48cddf416f7a3') in 0.073015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Square_Planks_4M.prefab
  artifactKey: Guid(abc00000000002059608317990159834) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Square_Planks_4M.prefab using Guid(abc00000000002059608317990159834) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '033a5aff8761aea9a9d918cac6be902d') in 0.0765401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Road.terrainlayer
  artifactKey: Guid(12400039da877f04eaabfb0bc6dbd59a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Road.terrainlayer using Guid(12400039da877f04eaabfb0bc6dbd59a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9175e7154c60d5d0a2cd87753d851c5') in 0.0447805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Movement.shadersubgraph
  artifactKey: Guid(27667dc9122cb524aa72371e8e4c3d5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/S_Movement.shadersubgraph using Guid(27667dc9122cb524aa72371e8e4c3d5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4c975d2b772f52cc6a18d8473d8c198') in 0.0622869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Land_Mask_A.EXR
  artifactKey: Guid(48e38a88bb6cac64dbd842eedc0ac0cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Land_Mask_A.EXR using Guid(48e38a88bb6cac64dbd842eedc0ac0cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc244b9e8c7586ffd3655f9d6839c19d') in 0.0703417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_Snow_M.PNG
  artifactKey: Guid(a72ea602594154140a1add005317b8a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EagleFern_01_Snow_M.PNG using Guid(a72ea602594154140a1add005317b8a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41f92f6c4be24fabb59f4235704b1899') in 0.0584589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lantern_Normal.PNG
  artifactKey: Guid(88b126c1a4f095c4692f362f15f40ee2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Lantern_Normal.PNG using Guid(88b126c1a4f095c4692f362f15f40ee2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '163fd4a5a9dbf3094fdb703de106f299') in 0.0506163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_BC.PNG
  artifactKey: Guid(69c2ad7f2543c3d408c64da2e8de3562) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_BC.PNG using Guid(69c2ad7f2543c3d408c64da2e8de3562) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59fa51d6d08d3c6269d1c9e495859a70') in 0.048634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_01.prefab
  artifactKey: Guid(abc00000000001869525803899943501) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Top_01.prefab using Guid(abc00000000001869525803899943501) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54fa7fb197f4f8121bdd4da92afa3dfe') in 0.0637799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_Normal.PNG
  artifactKey: Guid(993fa04a27c46764fb1d48523e25c5f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_Normal.PNG using Guid(993fa04a27c46764fb1d48523e25c5f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9c8047f9cb4df05aad02ac9929f0738') in 0.0725317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_OcclusionRoughnessMetallic.PNG
  artifactKey: Guid(b221d129e833be647a8808751e018cb5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GrainSacks_01_OcclusionRoughnessMetallic.PNG using Guid(b221d129e833be647a8808751e018cb5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e1ed11d2d6d76d35d9cbbd9782f61c3') in 0.0492446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fish.PNG
  artifactKey: Guid(7b757734d47022340b4e99bc84cdd3a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Fish.PNG using Guid(7b757734d47022340b4e99bc84cdd3a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17afd9e5c5d794e25449221ccaf86304') in 0.0481908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_basecolor.PNG
  artifactKey: Guid(e4e8aa50a2ded0d4fba1ffa40b1175f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_DockFloor_basecolor.PNG using Guid(e4e8aa50a2ded0d4fba1ffa40b1175f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff8f53b8deda2c059e6b866bdfc2abdd') in 0.0490497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_basecolor.PNG
  artifactKey: Guid(111141c0829b1af42b4a79c7dd418b8f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_basecolor.PNG using Guid(111141c0829b1af42b4a79c7dd418b8f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b1598fcc3810ba2f76f370cd538d164') in 0.0554614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_01_OREH.PNG
  artifactKey: Guid(c5b42849e382eaa44a9f0c044666a0b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_01_OREH.PNG using Guid(c5b42849e382eaa44a9f0c044666a0b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e313431d3b29cd097370b21e4d108051') in 0.0544434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_A.PNG
  artifactKey: Guid(5ba79f283df0cc74d811dd06a8f8a279) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_A.PNG using Guid(5ba79f283df0cc74d811dd06a8f8a279) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '807fdf39950e2feee0fd439fbe572267') in 0.0873643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_basecolor - Copy.PNG
  artifactKey: Guid(d02361f307e9f7845afe34addcd8f8b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_basecolor - Copy.PNG using Guid(d02361f307e9f7845afe34addcd8f8b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '380bab28ea88e7ab5a6e94ef5158ed53') in 0.0859499 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_D.PNG
  artifactKey: Guid(db28688565d6aba41a7b9335384e1968) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_D.PNG using Guid(db28688565d6aba41a7b9335384e1968) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52461c7bcec87dc84dd40f00970cb6a0') in 0.0976999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_normal.PNG
  artifactKey: Guid(cc09c8e3c7d34074e93a56759ed53b70) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBrickWall_normal.PNG using Guid(cc09c8e3c7d34074e93a56759ed53b70) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80b961a8dadf99cb3b05d865abc0ce26') in 0.0514686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_02_basecolor.PNG
  artifactKey: Guid(ca3ff5e58c5e53241b86c46b792cfc1d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_02_basecolor.PNG using Guid(ca3ff5e58c5e53241b86c46b792cfc1d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f3cc2acaad9585747e830a77081f4d2') in 0.0444764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_02_OCG.PNG
  artifactKey: Guid(cf51fbdae31087b42894fa1f42c0cbf6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_02_OCG.PNG using Guid(cf51fbdae31087b42894fa1f42c0cbf6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91cf33467f0baa2f22fd781b2259a89e') in 0.0428402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG.PNG
  artifactKey: Guid(3855763e785de584ebbe22ce3d91c28b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_OCG.PNG using Guid(3855763e785de584ebbe22ce3d91c28b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '142699657891311c29cf75f0d8e9ac19') in 0.0421437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_opacity.PNG
  artifactKey: Guid(2ca1e3e198cc6a34a8a48019cfd97bc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_opacity.PNG using Guid(2ca1e3e198cc6a34a8a48019cfd97bc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '32a73dddd4ade9c61b85a5164dc3be25') in 0.071296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_stoneSurface_ORMH.PNG
  artifactKey: Guid(c190140fb3234814f9885b1244312e3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_stoneSurface_ORMH.PNG using Guid(c190140fb3234814f9885b1244312e3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f599c1232bb12ddd40aae55e23623f3') in 0.044188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_01_OCG.PNG
  artifactKey: Guid(415c508fc1af53f49953832ef4446f82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_01_OCG.PNG using Guid(415c508fc1af53f49953832ef4446f82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec8e60e6c53414f4d07542e93d26581e') in 0.0461204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown/Sky and Fog Settings Profile.asset
  artifactKey: Guid(15903b6d22d20b2489e20ad183b10773) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/PL_CastleTown/Sky and Fog Settings Profile.asset using Guid(15903b6d22d20b2489e20ad183b10773) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e29b5a9f670bce2d52a1223fbcbc4047') in 0.0303404 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_N.PNG
  artifactKey: Guid(c64ee6bdfcf527f4ca08d21dbb609c46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildCarrot_01_N.PNG using Guid(c64ee6bdfcf527f4ca08d21dbb609c46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '858dde9c83a8debd847f6d0e414c75fa') in 0.0674051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Left.controller
  artifactKey: Guid(ca1a3d9ed1032b94398d5b4277d93570) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Sprint01_Left.controller using Guid(ca1a3d9ed1032b94398d5b4277d93570) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cd2ffcc8bf4adaff754518eb639ec95') in 0.0319481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL>
  artifactKey: Guid(02f82df7d301d274883b41f32dca59a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Idles/<EMAIL> using Guid(02f82df7d301d274883b41f32dca59a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ffcdff79f2c0f5f944e514acbd47417') in 0.0596259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_D.PNG
  artifactKey: Guid(7eb1976884da4eb418bbbaa97a9fda98) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_WildGrass_01_D.PNG using Guid(7eb1976884da4eb418bbbaa97a9fda98) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73531f0e46f7e12a63658ad1b6e6d063') in 0.0753975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Prefabs/Human_BasicMotionsDummy_F.prefab
  artifactKey: Guid(73e24cf6536bfef4b98ccd6eb529a5fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Prefabs/Human_BasicMotionsDummy_F.prefab using Guid(73e24cf6536bfef4b98ccd6eb529a5fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3915d61e7582cc4a38f9f67308abeeaa') in 0.0761073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 247

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL>
  artifactKey: Guid(0f69fd4f4ad106447896fb775199ed37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Idles/<EMAIL> using Guid(0f69fd4f4ad106447896fb775199ed37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f53512e38ae4854d75d986ba6f8ff51') in 0.0618432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_BackwardRight.controller
  artifactKey: Guid(50ffa223b60736b49985f93cf2a0a2bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Run01_BackwardRight.controller using Guid(50ffa223b60736b49985f93cf2a0a2bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f08ce7fe8e8f4b843d15568647ed9b59') in 0.0377759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_Normal.PNG
  artifactKey: Guid(2f2b9774e936015418e1f8a7e289a02f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Slab_Normal.PNG using Guid(2f2b9774e936015418e1f8a7e289a02f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b277ba55a95db18f80919a6bc47e634') in 0.048841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_1.prefab
  artifactKey: Guid(abc00000000000435323177912667922) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_02_1.prefab using Guid(abc00000000000435323177912667922) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a72bbcf7f177f994965d4ca99e92ecd') in 0.0560836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_L90.FBX
  artifactKey: Guid(8aba2f4d515ee9d42b82d488be8fff9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_L90.FBX using Guid(8aba2f4d515ee9d42b82d488be8fff9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3711c970312efa559cf80d65e816268') in 0.0597395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_R90_Root.FBX
  artifactKey: Guid(fa4275a85d7df8143a87a604be477cf2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_R90_Root.FBX using Guid(fa4275a85d7df8143a87a604be477cf2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbafe77d3c88d7e780bb62800c903b80') in 0.0732677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_L90_Root.FBX
  artifactKey: Guid(e9e500bdf518fff41aad8afb468a6a83) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Jogging_B_To_Jogging_B_Turn_L90_Root.FBX using Guid(e9e500bdf518fff41aad8afb468a6a83) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dedf7cc61408cb38e8a85c2a3a54f354') in 0.0630556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_1.wav
  artifactKey: Guid(d4442bc5a036dcd47b141a74c7f2a1d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_1.wav using Guid(d4442bc5a036dcd47b141a74c7f2a1d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3707b07624e0f98f586ef3240fc54f39') in 0.1541763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_A_Turn_L90_Root.FBX
  artifactKey: Guid(60ac49f914291b44eba14f3f9e9f00c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Jogging_A_Turn_L90_Root.FBX using Guid(60ac49f914291b44eba14f3f9e9f00c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40783c0d329384a8e31c22626b22be15') in 0.0600051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/Shooter.cs
  artifactKey: Guid(83204418a23cdca4bb3f5173e2229c55) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Utils/Shooter.cs using Guid(83204418a23cdca4bb3f5173e2229c55) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb03839b5f3776e4f399470d391f7618') in 0.0373807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Idle_Turn_L90.FBX
  artifactKey: Guid(711d9169713d26a4db530329a4ea80da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_B_To_Idle_Turn_L90.FBX using Guid(711d9169713d26a4db530329a4ea80da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f444e6468dd8e0e9b029d2db742e181d') in 0.0649422 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_2.wav
  artifactKey: Guid(1c05ecbfb689cb0499584eb5c83a6254) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_2.wav using Guid(1c05ecbfb689cb0499584eb5c83a6254) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b0d51940696c0126f65691033c6608a') in 0.1012675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_A_to_Run_B_Root.FBX
  artifactKey: Guid(5224d7f15a6934449881b066e86b26f1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_A_to_Run_B_Root.FBX using Guid(5224d7f15a6934449881b066e86b26f1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ef2076028c6c4ec2968f5c66394c2f5') in 0.0657212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R45_Root.FBX
  artifactKey: Guid(cb4e7b5ad48303840955c6f4e7942954) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back_R45_Root.FBX using Guid(cb4e7b5ad48303840955c6f4e7942954) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b60713f7489d8a76528c07474d33db1f') in 0.0592979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_R90.FBX
  artifactKey: Guid(6660e63557051234396a5d8fe05cccc7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_R90.FBX using Guid(6660e63557051234396a5d8fe05cccc7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b18c2155af559396aba306f9440b307e') in 0.0613614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_A_Root.FBX
  artifactKey: Guid(2ffe85c063249324ba7540cc8f1fa572) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Jog_A_Root.FBX using Guid(2ffe85c063249324ba7540cc8f1fa572) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e1033798acdd84f49bbb157f6716fc3') in 0.0776484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000127 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back.FBX
  artifactKey: Guid(8f802456df060f5459080a2465ff25b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/A/M_Big_Sword@Walk_ver_A_Back.FBX using Guid(8f802456df060f5459080a2465ff25b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea34f6dd217f8ab654e7c7c59e59cd15') in 0.0548993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Jog_B_Root.FBX
  artifactKey: Guid(5629e2cdfa13f3845bf363b32ea9fd6e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Jog_B_Root.FBX using Guid(5629e2cdfa13f3845bf363b32ea9fd6e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a72b7684292ee7558cfd57e37f77feb') in 0.064387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Gem_1.wav
  artifactKey: Guid(00e4fb1c2d2d6f84ebf82c433709d9a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Fillup/SFX_UI_Fillup_Gem_1.wav using Guid(00e4fb1c2d2d6f84ebf82c433709d9a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '488510186a6d3ce8ed6915dcd543b6b8') in 0.1754723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_3.wav
  artifactKey: Guid(0dbdb75303ba8a9408ac74224606fe74) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Swipe/SFX_UI_Swipe_Wind_3.wav using Guid(0dbdb75303ba8a9408ac74224606fe74) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9dd5346939b5d82fa7a55bfebbb3a6c1') in 0.1025051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_vol2.FBX
  artifactKey: Guid(c00e5975b0f8a2f4688432647609110d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_L90_vol2.FBX using Guid(c00e5975b0f8a2f4688432647609110d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f85b6ccfb8c643f4d2395b9dd2017d6') in 0.0596473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90_Root.FBX
  artifactKey: Guid(b00573b88d625bb41a1b43f09c6f84b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_L90_Root.FBX using Guid(b00573b88d625bb41a1b43f09c6f84b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ab5f7bf6c79a0a89d7d4e0daee8bf5f') in 0.0725138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Walk_A_Turn_R90.FBX
  artifactKey: Guid(aabb32450da87d34dbc2ee489156a828) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Walk_A_Turn_R90.FBX using Guid(aabb32450da87d34dbc2ee489156a828) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57835e9215c3289daab408672da67156') in 0.0681181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_L90.FBX
  artifactKey: Guid(12d501d0fbfdd8840a89bd54b2137fbb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_L90.FBX using Guid(12d501d0fbfdd8840a89bd54b2137fbb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b329ec62aef5e5e51eecb5afeb73112') in 0.0669258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Run_A_Turn_R90.FBX
  artifactKey: Guid(18dd0113ca6b8f74eac434b1b2cbc2d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_To_Run_A_Turn_R90.FBX using Guid(18dd0113ca6b8f74eac434b1b2cbc2d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef049bea8f91a969aa44c821191bf52c') in 0.0735842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FR45_Root.FBX
  artifactKey: Guid(a16e4eb8486201b41bca80c91aecd55a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FR45_Root.FBX using Guid(a16e4eb8486201b41bca80c91aecd55a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dcb500ee346400114bed9080d44dff4a') in 0.0578638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_Turn_R90.FBX
  artifactKey: Guid(e8ac742ca6f057f4a89173f947653755) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_Turn_R90.FBX using Guid(e8ac742ca6f057f4a89173f947653755) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '756f21acd60e52b29eb496aed347190f') in 0.0632503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FR45.FBX
  artifactKey: Guid(c980d0f5670ee294b9983cde346a66fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_FR45.FBX using Guid(c980d0f5670ee294b9983cde346a66fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25423d167499aa9c781d37ac68417090') in 0.0718262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90_Root.FBX
  artifactKey: Guid(164ef9f6a3d4b4a4b9c7a3152ea50a4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90_Root.FBX using Guid(164ef9f6a3d4b4a4b9c7a3152ea50a4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d618bce4a52ea997e3b38fcf99160e1') in 0.068723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Run_B.FBX
  artifactKey: Guid(e065fe7304510084ba7c5b3fed00a04a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Run_B.FBX using Guid(e065fe7304510084ba7c5b3fed00a04a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7e04419ed5790abcbdfb65eef10311e') in 0.0624028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_R90.FBX
  artifactKey: Guid(b2e304f600ab37a41b14aa826efcaa34) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Jogging_B_To_Jogging_B_Turn_R90.FBX using Guid(b2e304f600ab37a41b14aa826efcaa34) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a165f32c2e0e225151a2cd9742fcf447') in 0.0755366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Run_B_Root.FBX
  artifactKey: Guid(2c2e397f3eec72c4d9ff62e465b809b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_B_to_Run_B_Root.FBX using Guid(2c2e397f3eec72c4d9ff62e465b809b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd1ae3113aeb11442b374d9fb23c6dfab') in 0.0675652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90_Root.FBX
  artifactKey: Guid(9d452a12ef8c233478238ca122fee5f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_A_To_Jogging_A_Turn_L90_Root.FBX using Guid(9d452a12ef8c233478238ca122fee5f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bfbd8b01893e9c3292a1285ce6fe2f98') in 0.0608663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_Turn_L90_Root.FBX
  artifactKey: Guid(c408773168b45f64784f7f75342aa330) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_Turn_L90_Root.FBX using Guid(c408773168b45f64784f7f75342aa330) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2be15a7a3a2342162b181ee2798ee84d') in 0.0949779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_Turn_R90_Root.FBX
  artifactKey: Guid(9694d798f71ae074cb6e1649e8a42df4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Idle_B_Turn_R90_Root.FBX using Guid(9694d798f71ae074cb6e1649e8a42df4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c7d1b943118ecf78459d1eee6823ec8') in 0.216477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Fwd.FBX
  artifactKey: Guid(4df97b93229e23d4ab296d33ac649f77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_Fwd.FBX using Guid(4df97b93229e23d4ab296d33ac649f77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e6d7a5a5159b9f2063a1e35f005a41ba') in 0.0613455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_BL45_Root.FBX
  artifactKey: Guid(9b99f48a0c3f2d54f93951c87270ecfb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_BL45_Root.FBX using Guid(9b99f48a0c3f2d54f93951c87270ecfb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a039d309dd0d7fc96e256f72a2547ffe') in 0.0633354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_BL45.FBX
  artifactKey: Guid(3d77e74b597649b4d8be09849f817c3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_BL45.FBX using Guid(3d77e74b597649b4d8be09849f817c3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e587708d067820e63e9471173ed4e661') in 0.0684493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_R90.FBX
  artifactKey: Guid(e80d8f2ffd5d9bc49b40636c0f048b19) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_R90.FBX using Guid(e80d8f2ffd5d9bc49b40636c0f048b19) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da1887ec3b110e9fc1dbb421881e5392') in 0.0620684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_FR45.FBX
  artifactKey: Guid(f67edc0a78b833444ae86404ce85b137) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_B_FR45.FBX using Guid(f67edc0a78b833444ae86404ce85b137) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2bf641e3c1ab58271cfe553c7eb56a6d') in 0.0601973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_A_Root.FBX
  artifactKey: Guid(d8bc8aa2248746f45af58bbaf7bdcb32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_Idle_To_Idle_ver_A_Root.FBX using Guid(d8bc8aa2248746f45af58bbaf7bdcb32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '678c336e44a5ada77d0bb290954d4b47') in 0.055758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_R90_Root.FBX
  artifactKey: Guid(9aa05b40de3191a4fbb4b2fc3a9643ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Walk_B_To_Idle_ver_B_Turn_R90_Root.FBX using Guid(9aa05b40de3191a4fbb4b2fc3a9643ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f98457bb721fd1ce6dff51bd41fae8e9') in 0.0735306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_R90.FBX
  artifactKey: Guid(b9372dff72c2b5146952e3af2bb9d784) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_R90.FBX using Guid(b9372dff72c2b5146952e3af2bb9d784) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d00efd5fa82aa50cdd8f2e51b276963') in 0.0606618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R90_Root.FBX
  artifactKey: Guid(6dd83a08112e3be47845db7d87796088) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_R90_Root.FBX using Guid(6dd83a08112e3be47845db7d87796088) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc3b9b29a23f8b1098f0eb11bf5b3e22') in 0.0631064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45.FBX
  artifactKey: Guid(75c1bc47f938fec4aa4acacc13ef050f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L45.FBX using Guid(75c1bc47f938fec4aa4acacc13ef050f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '945dfd041699be9a9a37671be455ac63') in 0.0741987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_R90_Root.FBX
  artifactKey: Guid(3f72a1b089862e740a0aaba5dd0c5cf3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_R90_Root.FBX using Guid(3f72a1b089862e740a0aaba5dd0c5cf3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '525d25197bd7cc88d57caf24ecb02cef') in 0.063762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_R90_Root_vol2.FBX
  artifactKey: Guid(2a518321d7b5ee642aabfab16ef433f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_R90_Root_vol2.FBX using Guid(2a518321d7b5ee642aabfab16ef433f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0912a82da37dcef995a33347740f18b') in 0.0584987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FR45.FBX
  artifactKey: Guid(6087872646a754348b489228afb8940f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FR45.FBX using Guid(6087872646a754348b489228afb8940f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66bfe7f95cd099bf7c927925a9b2553b') in 0.0689996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L90.FBX
  artifactKey: Guid(c440e1aa46d609c42859239d9d875a2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_L90.FBX using Guid(c440e1aa46d609c42859239d9d875a2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70b485463f59deacc9264efbb2d959eb') in 0.0744544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_2x4_01.fbx
  artifactKey: Guid(abc00000000016796544405065670025) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/SM_Walkway_2x4_01.fbx using Guid(abc00000000016796544405065670025) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee12fbdfc02d893a48347ff94e636996') in 0.0695668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.prefab
  artifactKey: Guid(45d6812f23fa20e4d9eb78290da2dfde) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_05.prefab using Guid(45d6812f23fa20e4d9eb78290da2dfde) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56b077082e1e1b96a4b9a71e2b3d5dac') in 0.0570965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobbles_8M.fbx
  artifactKey: Guid(abc00000000015187976004260178181) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobbles_8M.fbx using Guid(abc00000000015187976004260178181) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bb450aba5bd35a478ce6b23be3393f4') in 0.1492755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_SlabB.fbx
  artifactKey: Guid(abc00000000002102230627260121988) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_SlabB.fbx using Guid(abc00000000002102230627260121988) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ee9bc49bba0fe8e5389bcadc8503b6a') in 0.0616552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.fbx
  artifactKey: Guid(abc00000000000163447860476688198) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_06.fbx using Guid(abc00000000000163447860476688198) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a26b4f77471f618c3fb6d764f441e23e') in 0.0969898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Flagon_01.fbx
  artifactKey: Guid(abc00000000012660658525475767149) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Flagon_01.fbx using Guid(abc00000000012660658525475767149) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a948d9b8fd7116bd1e62870850a8260') in 0.064405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Chest_Open_01.fbx
  artifactKey: Guid(abc00000000016157569688099299158) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Chest_Open_01.fbx using Guid(abc00000000016157569688099299158) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c814e0792595fab7b36eb703d92ba85e') in 0.0638209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Chest_Lid_01.fbx
  artifactKey: Guid(abc00000000015448291707254376189) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Chest_Lid_01.fbx using Guid(abc00000000015448291707254376189) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc2827b8c0a8a839366a77b9a05754bd') in 0.0682716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Axe_01.fbx
  artifactKey: Guid(abc00000000006647464958915934024) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Axe_01.fbx using Guid(abc00000000006647464958915934024) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a996c2f11090ded5dec7965ab3632cdd') in 0.074656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Hammer.fbx
  artifactKey: Guid(abc00000000000141445719351527752) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Hammer.fbx using Guid(abc00000000000141445719351527752) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09e471cffb4ec6dd7ae57e67b9ed2a6e') in 0.0938462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_BackwardRight.fbx
  artifactKey: Guid(1067eff693e77f143bc9982fef2aa0a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_BackwardRight.fbx using Guid(1067eff693e77f143bc9982fef2aa0a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ac6ac17cc42a736952735c53ec6bef1') in 0.0559519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 - Land.fbx
  artifactKey: Guid(c969c57136eab8b48b882fdc45e975c4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Jump/HumanM@Jump01 - Land.fbx using Guid(c969c57136eab8b48b882fdc45e975c4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '848480aa7e33f609d2b793b8e1b1672c') in 0.093762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Right.fbx
  artifactKey: Guid(70a2ad9ea5ea5cb49ab388ad93a00d27) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Right.fbx using Guid(70a2ad9ea5ea5cb49ab388ad93a00d27) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5654cff14b31994942ec3ebc4c1f330') in 0.0683041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000094 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Forward.fbx
  artifactKey: Guid(40786e67f3ceb094b9c00543f295cb5f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Forward.fbx using Guid(40786e67f3ceb094b9c00543f295cb5f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4ee733050d7ce7b4a9734def931ab80') in 0.0732759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BlockA.fbx
  artifactKey: Guid(abc00000000011145735966083729962) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_BlockA.fbx using Guid(abc00000000011145735966083729962) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7eaa88d838131c2e4bbb2bb9447e604') in 0.0539721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Left [RM].fbx
  artifactKey: Guid(74a8e870fd625914099b99d4caa5bf0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Turn/HumanM@Turn01_Left [RM].fbx using Guid(74a8e870fd625914099b99d4caa5bf0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '679bc623b7ebf81b0c052a9191899da3') in 0.0514074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Boxy_Generic_2.wav
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Boxy_Generic_2.wav using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa1a9745edac020c19e273a3705c8036') in 0.1823838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Cute_Generic_1.wav
  artifactKey: Guid(9f2c37de2833c02418018b465f0ed06a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Cute_Generic_1.wav using Guid(9f2c37de2833c02418018b465f0ed06a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acdd8ef1940e123979b4f9a1da43b40a') in 0.1515026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_2.wav
  artifactKey: Guid(484569bfbc399b443b8c416c8b0cd523) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_2.wav using Guid(484569bfbc399b443b8c416c8b0cd523) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2851fa28211ccdef93d5d655faa51bc3') in 0.1795377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/Materials/MI_Cobblestone_A.mat
  artifactKey: Guid(013b62cf373e70b4981c53804d987ffc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/Materials/MI_Cobblestone_A.mat using Guid(013b62cf373e70b4981c53804d987ffc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba9b8bbda5e95d41e3b1ff18a7c8fa69') in 0.0609199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Fish/Mesh/SM_Fish_2.fbx
  artifactKey: Guid(abc00000000002860404477196470680) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Fish/Mesh/SM_Fish_2.fbx using Guid(abc00000000002860404477196470680) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09d0c8952af6ff49e491c5455571b051') in 0.0623269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_B.fbx
  artifactKey: Guid(abc00000000001890096605178773251) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/SM_Candle_B.fbx using Guid(abc00000000001890096605178773251) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '964012e164cb15d764a032927513f663') in 0.0732175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Wood_Algae.mat
  artifactKey: Guid(3d7b21cec18eeb24ca550a24d5289b64) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Wood_Algae.mat using Guid(3d7b21cec18eeb24ca550a24d5289b64) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '643231b3e7b9c3515a19d3d4a53e20b0') in 0.0630546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_drinkstable.fbx
  artifactKey: Guid(abc00000000004435988435552961728) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_drinkstable.fbx using Guid(abc00000000004435988435552961728) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41cafdcdae7be36f21d3e85e61c48ab0') in 0.0745873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000183 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_03.fbx
  artifactKey: Guid(abc00000000014611768502475131494) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_03.fbx using Guid(abc00000000014611768502475131494) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3ed7e6dc05833eba0e1dfa63f4cd1c2') in 0.0781796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.prefab
  artifactKey: Guid(765c662294e2d934c800874481979a1d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/SM_Gangway_01.prefab using Guid(765c662294e2d934c800874481979a1d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4766194057734bbeddd4adf271727346') in 0.0566186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_KettlePot_01.fbx
  artifactKey: Guid(abc00000000013873861386753371766) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_KettlePot_01.fbx using Guid(abc00000000013873861386753371766) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95bee69f03ca3618f40a379637026dfc') in 0.0599786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Pumpkin.fbx
  artifactKey: Guid(abc00000000005439705990911094333) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Fruit/SM_Pumpkin.fbx using Guid(abc00000000005439705990911094333) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e858e3c3e1c15a0a6f2358a9d9eaef26') in 0.0644306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Rug_1.mat
  artifactKey: Guid(f4329ecef684f024b8b6a92e0d16f667) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Materials/MI_Rug_1.mat using Guid(f4329ecef684f024b8b6a92e0d16f667) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afcb817f52bef5e5fa47d41f565a6040') in 0.0627909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Sleeve.fbx
  artifactKey: Guid(abc00000000012800483482802217013) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Sleeve.fbx using Guid(abc00000000012800483482802217013) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6924408b12b787af737eb5cf523bcdd0') in 0.0848313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/SM_Lantern.fbx
  artifactKey: Guid(abc00000000012014624160616495634) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Lanterns/SM_Lantern.fbx using Guid(abc00000000012014624160616495634) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c4d7e3e23b71e457ac8cf416c10c067') in 0.0822002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bench.fbx
  artifactKey: Guid(abc00000000009736681289114657615) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bench.fbx using Guid(abc00000000009736681289114657615) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad2c42bf75766ac1cc701b6c740a5800') in 0.0669588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_ForwardRight [RM].fbx
  artifactKey: Guid(954b6bbde5b5f5a46bb68711335f9f90) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_ForwardRight [RM].fbx using Guid(954b6bbde5b5f5a46bb68711335f9f90) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b6d2a70cd863080c67360b4c555185f') in 0.0918889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Forward [RM].fbx
  artifactKey: Guid(9500e467e35d4684880037a0762df59b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/RootMotion/HumanM@Run01_Forward [RM].fbx using Guid(9500e467e35d4684880037a0762df59b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a62fa533264e9388d5295a8326b3099') in 0.0841478 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Portcullis_Frame.fbx
  artifactKey: Guid(abc00000000001316661917028014881) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Portcullis_Frame.fbx using Guid(abc00000000001316661917028014881) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a7dd6b3dee901c4a6438f803a529f02') in 0.0873704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Front_Top.fbx
  artifactKey: Guid(abc00000000011121785819511072447) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/SM_Front_Top.fbx using Guid(abc00000000011121785819511072447) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b34aed6f71b5d8298b0d18030136e0c7') in 0.074585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/SM_Arch_Walkway_Side_B.fbx
  artifactKey: Guid(abc00000000004366290380131846481) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Arch_Walkway/SM_Arch_Walkway_Side_B.fbx using Guid(abc00000000004366290380131846481) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4871b55804a4522d18575a6e9e83fe4') in 0.0659221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_ForwardLeft [RM].fbx
  artifactKey: Guid(bf3d864026a7e1146a27e29a8626bc69) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_ForwardLeft [RM].fbx using Guid(bf3d864026a7e1146a27e29a8626bc69) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e5eb17dbae1cab3e8fb2b26db0b9ea7') in 0.0859678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/No Name.mat
  artifactKey: Guid(9bbcdeeff840ec9408c8ecdc5bd5310d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Bridges/Materials/No Name.mat using Guid(9bbcdeeff840ec9408c8ecdc5bd5310d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f87ed0ef5cda7d9e58fd21abfae352c') in 0.0608148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_LargeRoof_02.fbx
  artifactKey: Guid(abc00000000013123350250095336273) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_LargeRoof_02.fbx using Guid(abc00000000013123350250095336273) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1504fcb31e1512c8df78bc198fbf0682') in 0.0994009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_BackwardLeft [RM].fbx
  artifactKey: Guid(81d028f9ab5659b4c9e31d07fe9e3733) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_BackwardLeft [RM].fbx using Guid(81d028f9ab5659b4c9e31d07fe9e3733) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd65e953afd0d0ef57ac2e9ab61b49955') in 0.0840608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_Terracotta.mat
  artifactKey: Guid(81da19df7c25a3c4cb0dbb6de68d86d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_Terracotta.mat using Guid(81da19df7c25a3c4cb0dbb6de68d86d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9e63b1088b1a24859ff8135b06834f1') in 0.0631589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_Metal.mat
  artifactKey: Guid(31b14c1553b495f4288b98b2791ee6a2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/Materials/MI_Metal.mat using Guid(31b14c1553b495f4288b98b2791ee6a2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10dcc47d17e444bca812ba663e308a00') in 0.0666831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/No Name.mat
  artifactKey: Guid(a6b09079dfcc9d947ac7256b8671c21f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/Materials/No Name.mat using Guid(a6b09079dfcc9d947ac7256b8671c21f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c6e4546589cec7df2c60e5868c7d2f4') in 0.0543567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_B.fbx
  artifactKey: Guid(abc00000000018417913441655804575) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Logs/SM_Log_B.fbx using Guid(abc00000000018417913441655804575) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad4537fcb8ea080abac6cc256c4188a2') in 0.0814376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Fireplace_02.fbx
  artifactKey: Guid(abc00000000009356394089132345612) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Fireplace_02.fbx using Guid(abc00000000009356394089132345612) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8ca4ba849fa30add11b0c8390035aae') in 0.1062213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Staircase_Corner.fbx
  artifactKey: Guid(abc00000000013160604453576738914) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/SM_Staircase_Corner.fbx using Guid(abc00000000013160604453576738914) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '928fd033153891948d219373e054ff17') in 0.0739584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Door_01.fbx
  artifactKey: Guid(abc00000000013298857644599823629) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_Door_01.fbx using Guid(abc00000000013298857644599823629) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd045c9436cc22353dbd109b70c216f8') in 0.0665419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_2x2M.fbx
  artifactKey: Guid(abc00000000008371934835482379312) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_2x2M.fbx using Guid(abc00000000008371934835482379312) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2758823a74972586f4b5f9a77c560d54') in 0.1248061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_1x2_A.fbx
  artifactKey: Guid(abc00000000009624020556822386180) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_1x2_A.fbx using Guid(abc00000000009624020556822386180) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e4de076ff15d6b1b19858838ac4a0f8') in 0.0729657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_StoneFence_03.mat
  artifactKey: Guid(719ba9c8ebcf562448c26b979928f97c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_StoneFence_03.mat using Guid(719ba9c8ebcf562448c26b979928f97c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a468461ccfe911d99c1d45ff8014d4c5') in 0.0459762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(aa85e58d5d537c74181ad1f8e5b9696d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Materials/MI_WoodPlanks.mat using Guid(aa85e58d5d537c74181ad1f8e5b9696d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '707e9b24ee8028ae8439b83fbe9a25f0') in 0.0700578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_04.fbx
  artifactKey: Guid(abc00000000011864992272059761098) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_04.fbx using Guid(abc00000000011864992272059761098) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c2f77b425a856c90ddf8d8e8381d9bc') in 0.1424325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Pillar_A_01.fbx
  artifactKey: Guid(abc00000000013195379751118333459) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Pillar_A_01.fbx using Guid(abc00000000013195379751118333459) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e79b34bbba7cb11cbdc25b3a7c2676de') in 0.0882928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_S_01.fbx
  artifactKey: Guid(abc00000000017910719653763065922) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/SM_LeaveDerbis_S_01.fbx using Guid(abc00000000017910719653763065922) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8cda3fc7db3a3dff60c1166e311c974') in 0.1241335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/Materials/No Name.mat
  artifactKey: Guid(caa3943af26dd924f95dfd5e7396bb0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/Materials/No Name.mat using Guid(caa3943af26dd924f95dfd5e7396bb0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '879523026b3b8bf115dfa4513ef3bdb7') in 0.0619939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stairs_5M.fbx
  artifactKey: Guid(abc00000000016167716938407541078) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Courtyard/SM_Stairs_5M.fbx using Guid(abc00000000016167716938407541078) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09bcfc935eeb8b0a9ee6a6d849d3c543') in 0.0721649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/No Name.mat
  artifactKey: Guid(19eb382e02a8e2846a2e21d361e0c69a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/Ivy/Materials/No Name.mat using Guid(19eb382e02a8e2846a2e21d361e0c69a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b479f3299d36dc82181ede2c65cfcadd') in 0.0669694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_L_02.fbx
  artifactKey: Guid(abc00000000008163499072197691480) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_L_02.fbx using Guid(abc00000000008163499072197691480) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad16eb951f3beb1dfb8cb8e250d95a9d') in 0.0937456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_02.fbx
  artifactKey: Guid(abc00000000006959206014343515367) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_02.fbx using Guid(abc00000000006959206014343515367) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '078f0ff9a88f2b1a676bd9c91a822e25') in 0.0730408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(fb96103eb9146204285d5c20b7d85bb6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFences/Materials/MI_LighterPlanks.mat using Guid(fb96103eb9146204285d5c20b7d85bb6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1e2f212ee93dd1addd2613e0c21e98b') in 0.0640672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(3fb7b91d4c3b59048b980c85cbed832b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Stairs/Materials/MI_StoneFence_02.mat using Guid(3fb7b91d4c3b59048b980c85cbed832b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f541ed377577792007d149c7d4c6f10f') in 0.0697955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_09.fbx
  artifactKey: Guid(abc00000000005128458441468309398) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_09.fbx using Guid(abc00000000005128458441468309398) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48c6e3335d2d625646cb79ddbb2b29e2') in 0.0706205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_Roof_Tiles.mat
  artifactKey: Guid(26455c0778d3ce74a96545f62aee5e63) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/Materials/MI_Roof_Tiles.mat using Guid(26455c0778d3ce74a96545f62aee5e63) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff41af8bd39532cd8d09f694fe63dfdf') in 0.0602823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/Materials/MI_EuropeanBeech_Atlas_01.mat
  artifactKey: Guid(65fdc8ddc460f154d840dcaa337f3d7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Meshes/Materials/MI_EuropeanBeech_Atlas_01.mat using Guid(65fdc8ddc460f154d840dcaa337f3d7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '33b947c3464c0d2d7ca439a1ef831865') in 0.062627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(d41d085f2abb4d14c9b75df85b4204f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_WoodPlanks.mat using Guid(d41d085f2abb4d14c9b75df85b4204f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2e917b698972456dcc0c995311bfc66') in 0.0618064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(9633393010f959b4b91cc1dfa4318703) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Internal_Floors/Materials/MI_LighterPlanks.mat using Guid(9633393010f959b4b91cc1dfa4318703) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a24210a380f9e3554563512ca2a9068b') in 0.0738717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_FL45_Root.FBX
  artifactKey: Guid(0dfa9e1373f37ea4f811b743c66542ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_A_FL45_Root.FBX using Guid(0dfa9e1373f37ea4f811b743c66542ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4db6e986ce50c65bb019f597cbd0b7c2') in 0.0767201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_F.FBX
  artifactKey: Guid(c55708165ee5d0c49aa26e5ba59fe391) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_F.FBX using Guid(c55708165ee5d0c49aa26e5ba59fe391) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9db764ea9470b1db21915d535c90ea48') in 0.0661556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_XL_01.fbx
  artifactKey: Guid(abc00000000002327125867225994499) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/SilverFir/Meshes/SM_SilverFir_XL_01.fbx using Guid(abc00000000002327125867225994499) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4a6381f8ff2a1e53a1d2a54cd83c3e0d') in 0.0619333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_B_Root.FBX
  artifactKey: Guid(1656a222d03c8b74db420a6de9ad9676) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/10__Move_to_Idle/Run_to_Idle/M_Big_Sword@Run_Fast_to_Idle_ver_B_Root.FBX using Guid(1656a222d03c8b74db420a6de9ad9676) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bda3b4c68838fa874e9ca0bf8dd31a5e') in 0.0776853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_basecolor.PNG
  artifactKey: Guid(4eb0bbd1538ddaa4bad6d61af18cc854) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Ivy_basecolor.PNG using Guid(4eb0bbd1538ddaa4bad6d61af18cc854) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5160acef33822b41b220e5d4e0de12d3') in 0.0532664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_ORMH.PNG
  artifactKey: Guid(9016a734648626945aad458d35c5d65b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Bricks_ORMH.PNG using Guid(9016a734648626945aad458d35c5d65b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8880b9bc402d4059a682c5d68793230b') in 0.049205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_ORMH.PNG
  artifactKey: Guid(913be7a5b21a7914984b4ac41fb188d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Rope_ORMH.PNG using Guid(913be7a5b21a7914984b4ac41fb188d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e56c1cf17157878a56e1584d176e3c06') in 0.0552929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_wind.png
  artifactKey: Guid(bf0a6ea7669b93047a183b9c3fb814b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Straight/T_HAIR_STRAIGHT_wind.png using Guid(bf0a6ea7669b93047a183b9c3fb814b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec5049c257394855252db7da566a3fcd') in 0.0681324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_17.mat
  artifactKey: Guid(21c578bb27dea1341937bba3d7cb50c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_17.mat using Guid(21c578bb27dea1341937bba3d7cb50c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '038abd82de900260e77168315039dba1') in 0.2062506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Disp.mat
  artifactKey: Guid(abc00000000013869244513610678719) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Disp.mat using Guid(abc00000000013869244513610678719) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9060ef020c33161432735b7ebf5da384') in 0.0566865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.prefab
  artifactKey: Guid(70c482706ba2a504ab47e4bfd710ac9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_34.prefab using Guid(70c482706ba2a504ab47e4bfd710ac9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d8924e693f49d17f71db5f8f3b7c056') in 0.2352888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_15.mat
  artifactKey: Guid(e08a971fe7e1678428eabef9c08b04ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_15.mat using Guid(e08a971fe7e1678428eabef9c08b04ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccdfe0e0418ed42882e05a0653caeb3e') in 0.1847031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0