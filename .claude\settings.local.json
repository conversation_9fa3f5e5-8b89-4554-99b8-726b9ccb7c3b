{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(ls:*)", "<PERSON><PERSON>(powershell:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(dir /b AssetsScriptsControllers*Pickup*)", "WebFetch(domain:docs.anthropic.com)", "Bash(uvx:*)", "Bash(where node)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(where python)", "Bash(where uvx)", "Bash(rg:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["serena"]}