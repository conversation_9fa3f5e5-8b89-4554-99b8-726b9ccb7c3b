%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ad7c4818d88252e40987c58b52106813, type: 3}
  m_Name: c2c1273b-5fee-42e8-a529-b06bf6f99365
  m_EditorClassIdentifier: 
  entries:
  - rid: 3724099938041725023
  references:
    version: 2
    RefIds:
    - rid: 3724099938041725023
      type: {class: PoolEntry, ns: AISystem.Common.Spawn, asm: RPG-AI-SYSTERM}
      data:
        characterGameObject: {fileID: 2113130614865191184, guid: 8678f1ff3a882874da3bbc0743e646c2, type: 3}
        poolEntryRequirements: []
