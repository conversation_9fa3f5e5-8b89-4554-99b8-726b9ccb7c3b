Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:16:12Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker19
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker19.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22056]  Target information:

Player connection [22056]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3933480483 [EditorId] 3933480483 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22056] Host joined multi-casting on [***********:54997]...
Player connection [22056] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 12.37 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 2.19 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56632
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004765 seconds.
- Loaded All Assemblies, in  0.808 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1494ms
	BeginReloadAssembly (256ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (84ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (99ms)
	LoadAllAssembliesAndSetupDomain (345ms)
		LoadAssemblies (254ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (339ms)
			TypeCache.Refresh (338ms)
				TypeCache.ScanAssembly (311ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.530 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.42 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.751 seconds
Domain Reload Profiling: 3277ms
	BeginReloadAssembly (336ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (1027ms)
		LoadAssemblies (648ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (571ms)
			TypeCache.Refresh (430ms)
				TypeCache.ScanAssembly (404ms)
			BuildScriptInfoCaches (108ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1751ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1347ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (245ms)
			ProcessInitializeOnLoadAttributes (972ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 4.98 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.7 MB). Loaded Objects now: 8334.
Memory consumption went from 210.1 MB to 200.4 MB.
Total: 19.782200 ms (FindLiveObjects: 1.546300 ms CreateObjectMapping: 1.214000 ms MarkObjects: 8.039800 ms  DeleteObjects: 8.979900 ms)

========================================================================
Received Import Request.
  Time since last request: 1812745.851312 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)CharacterSmall.prefab
  artifactKey: Guid(b4647abb91dd7f740a30ee465203d81a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)CharacterSmall.prefab using Guid(b4647abb91dd7f740a30ee465203d81a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb89b277dd3e3cbfbcce36943c837c46') in 1.0227852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab
  artifactKey: Guid(a94136958165ffb4e91f034a7974d498) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab using Guid(a94136958165ffb4e91f034a7974d498) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3bab8372b1f8e12185351137e0822d87') in 0.0247239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab
  artifactKey: Guid(83cdd184a3eace340b4dedb44e652290) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab using Guid(83cdd184a3eace340b4dedb44e652290) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a7e5deee2dd5daa162e416c6c666e3c4') in 0.0638405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab
  artifactKey: Guid(341e9b8977d6d734c8edeb00889eacab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab using Guid(341e9b8977d6d734c8edeb00889eacab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4c0c79f4c947b622b3d53c0053b9718a') in 0.0337485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab
  artifactKey: Guid(193ad1fe34a53bb448f9e76740531510) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab using Guid(193ad1fe34a53bb448f9e76740531510) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca2d25d476857859601f3a70f80aec48') in 0.0429963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab
  artifactKey: Guid(59089dd7f73b56f489328596f173dc5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab using Guid(59089dd7f73b56f489328596f173dc5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc5159f61e3eee7845643a1beedc6c70') in 0.0316282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 2.221576 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene/ReflectionProbe-0.exr
  artifactKey: Guid(29c76b2da2e73674294e3d8c68cee651) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene/ReflectionProbe-0.exr using Guid(29c76b2da2e73674294e3d8c68cee651) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db5c8bdef0c8f2d5216882e36fd471c7') in 0.1730199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.947816 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BlueClear.mat
  artifactKey: Guid(011adb4644f2d9546a847bcd5820f2d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BlueClear.mat using Guid(011adb4644f2d9546a847bcd5820f2d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a21c2a236b99a0bd7befd8f8eae97cb0') in 5.2514175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Pink.mat
  artifactKey: Guid(803490b4b81853744ad39dce2bd24c7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Pink.mat using Guid(803490b4b81853744ad39dce2bd24c7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '33e35d576687a8fec1274edc28b4d733') in 0.0666969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)GreenClear.mat
  artifactKey: Guid(a83e62349221e8a4387de81b5430af6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)GreenClear.mat using Guid(a83e62349221e8a4387de81b5430af6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99da41d1375682733611b63ea784d9e9') in 0.0820864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Crack.mat
  artifactKey: Guid(11fadd09ce06eed459ea1ca7255a92a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Crack.mat using Guid(11fadd09ce06eed459ea1ca7255a92a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c94469176b6ac4e7429835c0877dfedd') in 0.0527898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Lines.mat
  artifactKey: Guid(95cc11ee576b1364688ab8a034b57069) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Lines.mat using Guid(95cc11ee576b1364688ab8a034b57069) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd462180f3622338d9c82d5d2b9095bc') in 0.0643506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchedWalking.fbx
  artifactKey: Guid(bed18790e98ab204bbd6e0b7682be266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchedWalking.fbx using Guid(bed18790e98ab204bbd6e0b7682be266) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e450bc04492a7babb12ed130eecf4a3') in 0.0136035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Floor.mat
  artifactKey: Guid(924b2eaec01bc40439c0b5338f555b8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Floor.mat using Guid(924b2eaec01bc40439c0b5338f555b8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29d26646b39cc46a5362cdaf635c0cf0') in 3.3815096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 37.402603 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_3.mat
  artifactKey: Guid(4fa769696321da846a1897098e952791) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_3.mat using Guid(4fa769696321da846a1897098e952791) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1795311df03a4ee7c4d5247aba2fc237') in 0.0330253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.647129 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab
  artifactKey: Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab using Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '59b6212e6d601ef20cef089e1a6bcf7d') in 0.0395421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab
  artifactKey: Guid(3361286819423b84189232b9d797cb59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleHump.prefab using Guid(3361286819423b84189232b9d797cb59) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f5c3e8beb5dde8605db8c5524e00cfe') in 0.0613343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab
  artifactKey: Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab using Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95cde2809949de88ec934a21862b42bc') in 0.0645354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab
  artifactKey: Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGeneric.prefab using Guid(daf7017cd65ac8e4e80bb6cf231784e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '199197362e9b70d0d715ced94a09e097') in 0.0702689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab
  artifactKey: Guid(9e4757aed32f476419bb6acd9168541a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab using Guid(9e4757aed32f476419bb6acd9168541a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9c384b8635ef74cf5b0500a0509b7b2') in 0.066306 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 81.753471 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid/ReflectionProbe-0.exr
  artifactKey: Guid(524ffbb93aa96a64d890170a0c01d998) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid/ReflectionProbe-0.exr using Guid(524ffbb93aa96a64d890170a0c01d998) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '146a9f2f1a3e1ec9fd8d2f0e5dee2933') in 0.36948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.189 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.12 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.635 seconds
Domain Reload Profiling: 2829ms
	BeginReloadAssembly (396ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (129ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (672ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (331ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (291ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1325ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (234ms)
			ProcessInitializeOnLoadAttributes (964ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 5.53 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7636 unused Assets / (10.7 MB). Loaded Objects now: 8420.
Memory consumption went from 191.9 MB to 181.2 MB.
Total: 20.223900 ms (FindLiveObjects: 1.143000 ms CreateObjectMapping: 1.329000 ms MarkObjects: 9.161100 ms  DeleteObjects: 8.588500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.166 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.46 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.592 seconds
Domain Reload Profiling: 2761ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (700ms)
		LoadAssemblies (520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (336ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (290ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1309ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (243ms)
			ProcessInitializeOnLoadAttributes (934ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 6.92 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (8.9 MB). Loaded Objects now: 8422.
Memory consumption went from 190.1 MB to 181.1 MB.
Total: 18.636300 ms (FindLiveObjects: 1.190200 ms CreateObjectMapping: 1.367800 ms MarkObjects: 8.364000 ms  DeleteObjects: 7.711900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.167 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.27 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.622 seconds
Domain Reload Profiling: 2795ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (739ms)
		LoadAssemblies (501ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (383ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (332ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1334ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (242ms)
			ProcessInitializeOnLoadAttributes (960ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.84 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.24 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.3 MB). Loaded Objects now: 8424.
Memory consumption went from 190.0 MB to 180.7 MB.
Total: 18.166100 ms (FindLiveObjects: 1.084800 ms CreateObjectMapping: 1.034200 ms MarkObjects: 8.355300 ms  DeleteObjects: 7.690100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 528.924542 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_02.mat
  artifactKey: Guid(10014e3003695844e96b4c0baf91638c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_02.mat using Guid(10014e3003695844e96b4c0baf91638c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '050c29ea7280fdcc8fc5c753a6d3618f') in 1.2466162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_04.mat
  artifactKey: Guid(9259b81c7e9debc44a41366b55665fad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_04.mat using Guid(9259b81c7e9debc44a41366b55665fad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7662168a8375ed320b848cd59c8fae3e') in 0.1253335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_03.mat
  artifactKey: Guid(dc7f5e3fcb0099442a355af24094faef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_03.mat using Guid(dc7f5e3fcb0099442a355af24094faef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e7a35f54a04e01f19ae09617a27e5f1') in 0.0621064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_03.mat
  artifactKey: Guid(5aeda54659e1dbb4b81ca1505d034ec0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_03.mat using Guid(5aeda54659e1dbb4b81ca1505d034ec0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '09387eeb41872b77e41b9b05111df668') in 0.1102587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/IdaFaber/Materials/MAT_ROCA_BOT_05_Custom.mat
  artifactKey: Guid(eeefeb6f09e3e694cba59620280b4dcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_ROCA_BOT_05_Custom.mat using Guid(eeefeb6f09e3e694cba59620280b4dcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db0cc7d94cef1f12d46e3351b80fe48a') in 0.1102396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01_Dirty.mat
  artifactKey: Guid(4c214d70a511d8848a68e4dc18719299) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01_Dirty.mat using Guid(4c214d70a511d8848a68e4dc18719299) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58ca9ab2a7dcf113712907bee4de1f03') in 5.730711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 73.269699 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL>
  artifactKey: Guid(9ff8f9cbbbf3f9a4699940f5590cacaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/<EMAIL> using Guid(9ff8f9cbbbf3f9a4699940f5590cacaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b192715d1bb1ef18b8c377e0561433c') in 0.0213077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.255 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.94 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.568 seconds
Domain Reload Profiling: 2829ms
	BeginReloadAssembly (383ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (754ms)
		LoadAssemblies (516ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (385ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (347ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1569ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1285ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (236ms)
			ProcessInitializeOnLoadAttributes (927ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 5.92 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.0 MB). Loaded Objects now: 8446.
Memory consumption went from 191.1 MB to 181.1 MB.
Total: 20.698200 ms (FindLiveObjects: 1.149800 ms CreateObjectMapping: 1.929100 ms MarkObjects: 8.709100 ms  DeleteObjects: 8.908400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.373 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.04 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.936 seconds
Domain Reload Profiling: 3314ms
	BeginReloadAssembly (374ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (860ms)
		LoadAssemblies (598ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (379ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (33ms)
			BeforeProcessingInitializeOnLoad (315ms)
			ProcessInitializeOnLoadAttributes (1098ms)
			ProcessInitializeOnLoadMethodAttributes (127ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 8.23 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.2 MB). Loaded Objects now: 8448.
Memory consumption went from 190.9 MB to 180.8 MB.
Total: 32.004900 ms (FindLiveObjects: 2.090200 ms CreateObjectMapping: 3.165400 ms MarkObjects: 14.721300 ms  DeleteObjects: 12.025000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.257 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.87 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.650 seconds
Domain Reload Profiling: 2914ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (775ms)
		LoadAssemblies (538ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (405ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (361ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1650ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1356ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (260ms)
			ProcessInitializeOnLoadAttributes (969ms)
			ProcessInitializeOnLoadMethodAttributes (110ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 4.67 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.6 MB). Loaded Objects now: 8450.
Memory consumption went from 191.0 MB to 181.4 MB.
Total: 18.579800 ms (FindLiveObjects: 1.203000 ms CreateObjectMapping: 1.617600 ms MarkObjects: 7.865500 ms  DeleteObjects: 7.891600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.224 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.69 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.604 seconds
Domain Reload Profiling: 2833ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (763ms)
		LoadAssemblies (524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (397ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (356ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (263ms)
			ProcessInitializeOnLoadAttributes (899ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 6.30 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.9 MB). Loaded Objects now: 8452.
Memory consumption went from 191.1 MB to 181.2 MB.
Total: 20.252000 ms (FindLiveObjects: 1.179500 ms CreateObjectMapping: 1.310600 ms MarkObjects: 9.277300 ms  DeleteObjects: 8.482200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.352 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.62 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.559 seconds
Domain Reload Profiling: 2914ms
	BeginReloadAssembly (364ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (84ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (833ms)
		LoadAssemblies (627ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (397ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (358ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1560ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1259ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (233ms)
			ProcessInitializeOnLoadAttributes (901ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 6.27 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.0 MB). Loaded Objects now: 8454.
Memory consumption went from 191.1 MB to 181.1 MB.
Total: 19.970200 ms (FindLiveObjects: 1.114700 ms CreateObjectMapping: 1.343800 ms MarkObjects: 9.188900 ms  DeleteObjects: 8.321000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.249 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.99 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.630 seconds
Domain Reload Profiling: 2882ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (792ms)
		LoadAssemblies (543ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (398ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (356ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1631ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1348ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (252ms)
			ProcessInitializeOnLoadAttributes (969ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 4.56 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.5 MB). Loaded Objects now: 8456.
Memory consumption went from 191.1 MB to 180.6 MB.
Total: 17.219900 ms (FindLiveObjects: 1.136300 ms CreateObjectMapping: 1.038300 ms MarkObjects: 7.299000 ms  DeleteObjects: 7.744700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.208 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.28 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.583 seconds
Domain Reload Profiling: 2795ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (743ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (375ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (335ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1584ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (247ms)
			ProcessInitializeOnLoadAttributes (929ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 5.44 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (9.9 MB). Loaded Objects now: 8458.
Memory consumption went from 191.1 MB to 181.2 MB.
Total: 19.530600 ms (FindLiveObjects: 1.247800 ms CreateObjectMapping: 1.455500 ms MarkObjects: 9.119900 ms  DeleteObjects: 7.705600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.27 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7608 unused Assets / (10.2 MB). Loaded Objects now: 8458.
Memory consumption went from 191.1 MB to 180.9 MB.
Total: 22.970700 ms (FindLiveObjects: 1.337000 ms CreateObjectMapping: 1.325300 ms MarkObjects: 9.483500 ms  DeleteObjects: 10.822100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.168 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.49 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.519 seconds
Domain Reload Profiling: 2692ms
	BeginReloadAssembly (357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (706ms)
		LoadAssemblies (542ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (342ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (299ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1520ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1221ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (243ms)
			ProcessInitializeOnLoadAttributes (865ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 5.81 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.0 MB). Loaded Objects now: 8460.
Memory consumption went from 191.1 MB to 181.2 MB.
Total: 21.483500 ms (FindLiveObjects: 1.265300 ms CreateObjectMapping: 1.483200 ms MarkObjects: 9.696400 ms  DeleteObjects: 9.036400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.180 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.70 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.628 seconds
Domain Reload Profiling: 2831ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (730ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (356ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1647ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (280ms)
			ProcessInitializeOnLoadAttributes (945ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 6.07 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.4 MB). Loaded Objects now: 8462.
Memory consumption went from 191.2 MB to 180.8 MB.
Total: 17.649600 ms (FindLiveObjects: 1.065800 ms CreateObjectMapping: 0.980300 ms MarkObjects: 7.776400 ms  DeleteObjects: 7.825100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.406 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 6.23 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.528 seconds
Domain Reload Profiling: 2938ms
	BeginReloadAssembly (393ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (869ms)
		LoadAssemblies (713ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (361ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (316ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1244ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (248ms)
			ProcessInitializeOnLoadAttributes (874ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 6.26 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.1 MB). Loaded Objects now: 8464.
Memory consumption went from 191.2 MB to 181.1 MB.
Total: 16.720500 ms (FindLiveObjects: 1.199500 ms CreateObjectMapping: 1.087000 ms MarkObjects: 7.602700 ms  DeleteObjects: 6.827300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.196 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.60 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.547 seconds
Domain Reload Profiling: 2750ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (762ms)
		LoadAssemblies (519ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (323ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1548ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1256ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (263ms)
			ProcessInitializeOnLoadAttributes (848ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 5.43 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7634 unused Assets / (10.3 MB). Loaded Objects now: 8466.
Memory consumption went from 191.3 MB to 181.0 MB.
Total: 18.184900 ms (FindLiveObjects: 1.164000 ms CreateObjectMapping: 1.039900 ms MarkObjects: 8.099900 ms  DeleteObjects: 7.879400 ms)

Prepare: number of updated asset objects reloaded= 0
