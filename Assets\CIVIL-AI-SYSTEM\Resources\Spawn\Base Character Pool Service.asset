%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa73771daade0ea42a00261ab8688982, type: 3}
  m_Name: Base Character Pool Service
  m_EditorClassIdentifier: 
  pools:
    collection:
    - scriptableObjectPassing: {fileID: 11400000, guid: bb992bf3b95e40841b6da8ff938c70da, type: 2}
    - scriptableObjectPassing: {fileID: 11400000, guid: 85c9da20b091b8c4aa6ed6511f43e290, type: 2}
    - scriptableObjectPassing: {fileID: 11400000, guid: 85c9da20b091b8c4aa6ed6511f43e290, type: 2}
    - scriptableObjectPassing: {fileID: 11400000, guid: 99c92bba27d83a74e9db74261735c2b7, type: 2}
    - scriptableObjectPassing: {fileID: 11400000, guid: aa8b2d0c4bac657428f8cefda4d36a5c, type: 2}
  defaultPoolId: 1
