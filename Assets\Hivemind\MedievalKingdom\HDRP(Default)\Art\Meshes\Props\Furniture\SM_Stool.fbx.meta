fileFormatVersion: 2
guid: abc00000000017099141804546668910
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: SM_Stool_LOD0
    100004: SM_Stool_LOD1
    100006: SM_Stool_LOD2
    100008: SM_Stool_LOD3
    400000: //RootNode
    400002: SM_Stool_LOD0
    400004: SM_Stool_LOD1
    400006: SM_Stool_LOD2
    400008: SM_Stool_LOD3
    2100000: MI_WoodPlanks
    2300000: SM_Stool_LOD0
    2300002: SM_Stool_LOD1
    2300004: SM_Stool_LOD2
    2300006: SM_Stool_LOD3
    3300000: SM_Stool_LOD0
    3300002: SM_Stool_LOD1
    3300004: SM_Stool_LOD2
    3300006: SM_Stool_LOD3
    4300000: SM_Stool_LOD0
    4300002: SM_Stool_LOD1
    4300004: SM_Stool_LOD2
    4300006: SM_Stool_LOD3
    4300008: SM_Stool_ConvexHulls
    20500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 1
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 0
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 2
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 268667
  packageName: Medieval Kingdom (Medieval Castle, Medieval Town, Medieval Village,
    Castle)
  packageVersion: 1.0
  assetPath: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Stool.fbx
  uploadId: 682233
