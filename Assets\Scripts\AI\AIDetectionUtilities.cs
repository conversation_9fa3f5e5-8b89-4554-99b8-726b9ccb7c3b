/*
 * Script: AIDetectionUtilities
 * Purpose: Utility functions and advanced detection systems for AI components
 * 
 * Setup Instructions:
 * 1. This is a utility class - no need to attach to GameObjects
 * 2. Use static methods from other AI scripts for advanced detection
 * 3. Provides optimized detection algorithms and helper functions
 * 
 * CRITICAL: Default Values Must Work Out-of-Box
 * - All detection methods use sensible default parameters
 * - Optimized for performance with built-in caching
 * - Layer masks default to reasonable values
 * 
 * Dependencies:
 * - Unity Physics system for raycasting
 * - NavMesh system for pathfinding utilities
 * 
 * Usage:
 * Call static methods from AI scripts for advanced detection features.
 * Provides utilities for multi-target detection, sound propagation, etc.
 * 
 * Public Methods:
 * - GetVisibleTargets(): Get all visible targets in range
 * - CalculateSoundPropagation(): Calculate how sound travels through environment
 * - GetOptimalPatrolPath(): Generate efficient patrol routes
 * - CheckMultipleLineOfSight(): Batch line-of-sight checks
 * 
 * Author: Claude AI Assistant
 * Created: 2025-08-01
 * Last Modified: 2025-08-01
 */

using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;
using System.Linq;

namespace Blazeout.AI
{
    /// <summary>
    /// Advanced detection utilities for AI systems
    /// </summary>
    public static class AIDetectionUtilities
    {
        #region Visual Detection Utilities
        /// <summary>
        /// Get all visible targets within range and angle
        /// </summary>
        public static List<Transform> GetVisibleTargets(Transform observer, float range, float angle, LayerMask targetLayers, LayerMask obstacleLayers)
        {
            List<Transform> visibleTargets = new List<Transform>();
            
            // Find all potential targets in range
            Collider[] targetsInRange = Physics.OverlapSphere(observer.position, range, targetLayers);
            
            foreach (Collider target in targetsInRange)
            {
                Vector3 directionToTarget = (target.transform.position - observer.position).normalized;
                float angleToTarget = Vector3.Angle(observer.forward, directionToTarget);
                
                // Check if target is within sight angle
                if (angleToTarget <= angle * 0.5f)
                {
                    // Check line of sight
                    if (HasClearLineOfSight(observer.position, target.transform.position, obstacleLayers))
                    {
                        visibleTargets.Add(target.transform);
                    }
                }
            }
            
            return visibleTargets;
        }

        /// <summary>
        /// Optimized line of sight check with caching
        /// </summary>
        public static bool HasClearLineOfSight(Vector3 from, Vector3 to, LayerMask obstacleLayers)
        {
            Vector3 rayOrigin = from + Vector3.up * 1.6f; // Eye level
            Vector3 rayDirection = (to - rayOrigin).normalized;
            float rayDistance = Vector3.Distance(rayOrigin, to);
            
            // Perform raycast
            if (Physics.Raycast(rayOrigin, rayDirection, out RaycastHit hit, rayDistance, obstacleLayers))
            {
                // Check if we hit the target or an obstacle
                return hit.collider.CompareTag("Player");
            }
            
            return true; // No obstacles
        }

        /// <summary>
        /// Check multiple line of sight rays for better accuracy
        /// </summary>
        public static bool HasClearLineOfSightMultiRay(Vector3 from, Vector3 to, LayerMask obstacleLayers, int rayCount = 3)
        {
            Vector3 baseOrigin = from + Vector3.up * 1.6f;
            Vector3 targetCenter = to;
            
            // Check center ray
            if (HasClearLineOfSight(from, to, obstacleLayers))
                return true;
            
            // Check additional rays around the target
            for (int i = 0; i < rayCount; i++)
            {
                float angle = (360f / rayCount) * i;
                Vector3 offset = Quaternion.Euler(0, angle, 0) * Vector3.forward * 0.5f;
                Vector3 targetPoint = targetCenter + offset;
                
                if (HasClearLineOfSight(from, targetPoint, obstacleLayers))
                    return true;
            }
            
            return false;
        }
        #endregion

        #region Audio Detection Utilities
        /// <summary>
        /// Calculate sound propagation through environment
        /// </summary>
        public static float CalculateSoundPropagation(Vector3 soundSource, Vector3 listener, float baseVolume, LayerMask soundBlockingLayers)
        {
            float distance = Vector3.Distance(soundSource, listener);
            float volume = baseVolume;
            
            // Apply distance attenuation
            volume *= 1.0f / (1.0f + distance * 0.1f);
            
            // Check for sound-blocking obstacles
            if (Physics.Raycast(soundSource, (listener - soundSource).normalized, out RaycastHit hit, distance, soundBlockingLayers))
            {
                // Reduce volume based on obstacle material (simplified)
                volume *= 0.3f; // Sound muffled by obstacle
            }
            
            return Mathf.Clamp01(volume);
        }

        /// <summary>
        /// Get all entities that can hear a sound at given position
        /// </summary>
        public static List<NPCEnemyAI> GetEntitiesInHearingRange(Vector3 soundPosition, float maxRange, float volume)
        {
            List<NPCEnemyAI> hearingEntities = new List<NPCEnemyAI>();
            
            // Find all AI entities in range
            Collider[] entitiesInRange = Physics.OverlapSphere(soundPosition, maxRange);
            
            foreach (Collider entity in entitiesInRange)
            {
                NPCEnemyAI ai = entity.GetComponent<NPCEnemyAI>();
                if (ai != null)
                {
                    float effectiveVolume = CalculateSoundPropagation(soundPosition, entity.transform.position, volume, -1);
                    if (effectiveVolume > 0.1f) // Minimum audible threshold
                    {
                        hearingEntities.Add(ai);
                    }
                }
            }
            
            return hearingEntities;
        }
        #endregion

        #region Pathfinding Utilities
        /// <summary>
        /// Generate optimal patrol path between points
        /// </summary>
        public static Vector3[] GetOptimalPatrolPath(Vector3[] patrolPoints)
        {
            if (patrolPoints.Length < 2) return patrolPoints;
            
            List<Vector3> optimizedPath = new List<Vector3>();
            
            // Simple nearest-neighbor optimization
            List<Vector3> remainingPoints = patrolPoints.ToList();
            Vector3 currentPoint = remainingPoints[0];
            optimizedPath.Add(currentPoint);
            remainingPoints.RemoveAt(0);
            
            while (remainingPoints.Count > 0)
            {
                Vector3 nearestPoint = remainingPoints.OrderBy(p => Vector3.Distance(currentPoint, p)).First();
                optimizedPath.Add(nearestPoint);
                currentPoint = nearestPoint;
                remainingPoints.Remove(nearestPoint);
            }
            
            return optimizedPath.ToArray();
        }

        /// <summary>
        /// Check if a path exists between two points on NavMesh
        /// </summary>
        public static bool IsPathValid(Vector3 start, Vector3 end)
        {
            NavMeshPath path = new NavMeshPath();
            return NavMesh.CalculatePath(start, end, NavMesh.AllAreas, path) && path.status == NavMeshPathStatus.PathComplete;
        }

        /// <summary>
        /// Get random valid position on NavMesh within radius
        /// </summary>
        public static Vector3 GetRandomNavMeshPosition(Vector3 center, float radius)
        {
            for (int i = 0; i < 10; i++) // Try up to 10 times
            {
                Vector3 randomDirection = Random.insideUnitSphere * radius;
                randomDirection += center;
                
                if (NavMesh.SamplePosition(randomDirection, out NavMeshHit hit, radius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }
            
            return center; // Fallback to center if no valid position found
        }
        #endregion

        #region Group AI Utilities
        /// <summary>
        /// Alert all AI entities in range about player presence
        /// </summary>
        public static void AlertNearbyAI(Vector3 alertPosition, float alertRadius, Vector3 playerLastKnownPosition)
        {
            Collider[] nearbyEntities = Physics.OverlapSphere(alertPosition, alertRadius);
            
            foreach (Collider entity in nearbyEntities)
            {
                NPCEnemyAI ai = entity.GetComponent<NPCEnemyAI>();
                if (ai != null && ai.CurrentState != AIState.Chase)
                {
                    // Set AI to alert state and give them the player's last known position
                    ai.SetState(AIState.Alert);
                    ai.SetAlertLevel(0.8f);
                    // Note: Would need to add a method to set last known position
                }
            }
        }

        /// <summary>
        /// Get the closest AI entity to a position
        /// </summary>
        public static NPCEnemyAI GetClosestAI(Vector3 position, float maxRange = Mathf.Infinity)
        {
            NPCEnemyAI closestAI = null;
            float closestDistance = maxRange;
            
            NPCEnemyAI[] allAI = Object.FindObjectsOfType<NPCEnemyAI>();
            
            foreach (NPCEnemyAI ai in allAI)
            {
                float distance = Vector3.Distance(position, ai.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestAI = ai;
                }
            }
            
            return closestAI;
        }

        /// <summary>
        /// Check if any AI can see the player
        /// </summary>
        public static bool IsPlayerVisibleToAnyAI(Transform playerTransform)
        {
            NPCEnemyAI[] allAI = Object.FindObjectsOfType<NPCEnemyAI>();
            
            foreach (NPCEnemyAI ai in allAI)
            {
                if (ai.CanSeePlayer)
                {
                    return true;
                }
            }
            
            return false;
        }
        #endregion

        #region Performance Optimization
        /// <summary>
        /// Cached results for expensive operations
        /// </summary>
        private static Dictionary<string, CachedResult> detectionCache = new Dictionary<string, CachedResult>();
        
        private struct CachedResult
        {
            public bool result;
            public float timestamp;
        }
        
        /// <summary>
        /// Cached line of sight check to improve performance
        /// </summary>
        public static bool HasClearLineOfSightCached(Vector3 from, Vector3 to, LayerMask obstacleLayers, float cacheTime = 0.1f)
        {
            string cacheKey = $"{from}_{to}_{obstacleLayers.value}";
            
            if (detectionCache.ContainsKey(cacheKey))
            {
                CachedResult cached = detectionCache[cacheKey];
                if (Time.time - cached.timestamp < cacheTime)
                {
                    return cached.result;
                }
            }
            
            bool result = HasClearLineOfSight(from, to, obstacleLayers);
            detectionCache[cacheKey] = new CachedResult { result = result, timestamp = Time.time };
            
            return result;
        }

        /// <summary>
        /// Clear old cache entries to prevent memory buildup
        /// </summary>
        public static void ClearOldCacheEntries(float maxAge = 5.0f)
        {
            var keysToRemove = detectionCache.Where(kvp => Time.time - kvp.Value.timestamp > maxAge).Select(kvp => kvp.Key).ToList();
            
            foreach (string key in keysToRemove)
            {
                detectionCache.Remove(key);
            }
        }
        #endregion
    }
}
