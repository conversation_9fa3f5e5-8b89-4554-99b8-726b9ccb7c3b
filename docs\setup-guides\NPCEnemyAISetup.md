# NPC Enemy AI System Setup Guide

## 📋 Overview

This guide provides complete setup instructions for the NPC Enemy AI system in Blazeout. The system includes player detection, line-of-sight checking, state-based behavior, audio detection, and comprehensive debug tools.

## 🎯 Features

- **Player Detection:** Visual sight and audio footstep detection
- **Line of Sight:** Proper raycast-based sight checking through obstacles
- **State Machine:** Patrol, <PERSON>ert, Chase, Attack, Search, and Stunned states
- **NavMesh Integration:** Intelligent pathfinding and movement
- **Debug Tools:** Visual gizmos, state display, and performance monitoring
- **Audio Detection:** Responds to player movement and footsteps

## 📦 Required Components

### Core Scripts
- `NPCEnemyAI.cs` - Main AI behavior script
- `AIDetectionUtilities.cs` - Advanced detection utilities
- `AIDebugVisualizer.cs` - Debug and visualization tools

### Unity Components (Auto-added)
- `NavMeshAgent` - For pathfinding and movement
- `Collider` - For detection and physics
- `Animator` (Optional) - For animation integration

## 🚀 Quick Setup

### Step 1: Prepare the Scene

1. **Bake NavMesh:**
   - Go to `Window > AI > Navigation`
   - Select all ground/floor objects
   - Mark as `Navigation Static`
   - Click `Bake` to generate NavMesh

2. **Ensure Player Setup:**
   - Player GameObject must have "Player" tag
   - PlayerController3D script must be attached
   - Player should have appropriate colliders

### Step 2: Create NPC Enemy

1. **Create NPC GameObject:**
   ```
   - Create Empty GameObject
   - Name it "NPCEnemy"
   - Add a Capsule or Character mesh
   - Position at ground level
   ```

2. **Add Required Components:**
   ```
   - Add Capsule Collider (for physics)
   - Add NPCEnemyAI script (NavMeshAgent auto-added)
   ```

3. **Configure Basic Settings:**
   ```csharp
   // Detection Settings
   Sight Range: 10.0f
   Sight Angle: 60.0f
   Hearing Range: 5.0f
   
   // Movement Settings
   Patrol Speed: 2.0f
   Alert Speed: 3.0f
   Chase Speed: 5.0f
   Attack Range: 2.0f
   ```

### Step 3: Setup Patrol System (Optional)

1. **Create Patrol Points:**
   ```
   - Create Empty GameObjects for patrol points
   - Name them "PatrolPoint_1", "PatrolPoint_2", etc.
   - Position them where you want the NPC to patrol
   ```

2. **Assign Patrol Points:**
   ```
   - In NPCEnemyAI inspector
   - Expand "Patrol System" section
   - Set array size to number of patrol points
   - Drag patrol point GameObjects into array slots
   ```

### Step 4: Configure Layer Masks

1. **Setup Layers:**
   ```
   - Create "Player" layer for player objects
   - Create "Obstacles" layer for walls/barriers
   - Assign appropriate layers to GameObjects
   ```

2. **Configure Detection Layers:**
   ```csharp
   // In NPCEnemyAI inspector
   Player Layer: Player (layer mask)
   Obstacle Layers: Default, Obstacles (layer mask)
   ```

## 🔧 Advanced Configuration

### Detection Tuning

```csharp
// Visual Detection
Sight Range: 8-15f     // Adjust based on environment size
Sight Angle: 45-90f    // Narrower = more realistic, wider = easier detection
Hearing Range: 3-8f    // Should be less than sight range

// Audio Detection
Minimum Footstep Speed: 0.5f    // Player speed threshold for noise
Footstep Volume Multiplier: 1.0f // Sensitivity adjustment
```

### Behavior Tuning

```csharp
// State Durations
Search Duration: 8-15f     // How long to search after losing player
Alert Duration: 3-8f       // How long to stay alert
Attack Cooldown: 1-3f      // Time between attacks

// Movement Speeds
Patrol Speed: 1.5-3f       // Casual walking
Alert Speed: 2.5-4f        // Investigating
Chase Speed: 4-7f          // Pursuit (should be close to player speed)
```

### Performance Optimization

```csharp
// Detection Optimization
Detection Check Interval: 0.1f  // Check 10 times per second
Alert Decay Rate: 0.05-0.2f     // How fast alertness decreases

// Debug Settings (Disable in builds)
Enable Debug: false        // Turn off for production
Show Detection Gizmos: false
Log State Changes: false
```

## 🎮 Debug and Testing

### Setup Debug Visualizer

1. **Add Debug Component:**
   ```
   - Create Empty GameObject named "AIDebugManager"
   - Add AIDebugVisualizer script
   - Configure debug settings in inspector
   ```

2. **Debug Controls (During Play):**
   ```
   F1: Toggle AI state display
   F2: Toggle detection range visualization
   F3: Toggle pathfinding visualization
   F4: Toggle performance metrics
   F5: Toggle all debug features
   F6: Refresh AI list
   ```

### Visual Debug Features

- **Sight Cones:** Yellow/red wireframe showing detection range
- **Hearing Radius:** Cyan sphere showing audio detection
- **Line of Sight:** Green/red rays showing visibility checks
- **Patrol Paths:** Green lines connecting patrol points
- **NavMesh Paths:** White lines showing current movement path
- **State Display:** Text above each NPC showing current state

## 🔍 Testing Scenarios

### Basic Functionality Test

1. **Patrol Behavior:**
   - NPC should move between patrol points
   - Should wait at each point before moving
   - Should return to patrol after losing player

2. **Detection Test:**
   - Walk into NPC's sight cone → Should enter Chase state
   - Hide behind wall → Should lose sight and enter Search
   - Move quietly vs. running → Test audio detection

3. **State Transitions:**
   - Patrol → Alert (audio detection)
   - Alert → Chase (visual detection)
   - Chase → Attack (close range)
   - Attack → Search (player escapes)
   - Search → Patrol (timeout)

### Advanced Testing

1. **Line of Sight:**
   - Stand behind walls/obstacles
   - Test detection through transparent objects
   - Verify sight blocked by appropriate layers

2. **Audio Detection:**
   - Test different player movement speeds
   - Verify hearing range accuracy
   - Test audio blocked by obstacles

3. **Performance:**
   - Multiple NPCs in scene
   - Check frame rate impact
   - Monitor detection update frequency

## 🐛 Common Issues and Solutions

### NPC Not Moving
**Problem:** NPC stays in place, doesn't patrol
**Solutions:**
- Ensure NavMesh is baked in scene
- Check that patrol points are on NavMesh
- Verify NavMeshAgent component is enabled
- Check that patrol points array is populated

### Detection Not Working
**Problem:** NPC doesn't detect player
**Solutions:**
- Verify player has "Player" tag
- Check layer mask settings match scene setup
- Ensure sight/hearing ranges are appropriate
- Test with debug visualization enabled

### Performance Issues
**Problem:** Frame rate drops with multiple NPCs
**Solutions:**
- Increase detection check interval (0.1f → 0.2f)
- Reduce number of NPCs or sight ranges
- Disable debug features in builds
- Use AIDetectionUtilities caching features

### Line of Sight Problems
**Problem:** NPC sees through walls
**Solutions:**
- Check obstacle layer mask includes wall layers
- Verify wall GameObjects have appropriate layers
- Test raycast origin height (eye level)
- Use multi-ray detection for better accuracy

## 📊 Performance Guidelines

### Recommended Limits
- **NPCs per scene:** 5-15 (depending on complexity)
- **Detection range:** 8-12 units (balance realism vs. performance)
- **Update frequency:** 0.1-0.2 seconds (10-5 times per second)
- **Patrol points:** 3-8 per NPC (avoid overly complex routes)

### Optimization Tips
- Use larger detection intervals for distant NPCs
- Implement LOD system for far NPCs
- Cache expensive calculations
- Disable debug features in production builds
- Use object pooling for multiple similar NPCs

## 🎯 Integration with Existing Systems

### PlayerController3D Integration
```csharp
// AI automatically detects:
- Player position (transform.position)
- Player movement speed (CurrentSpeed property)
- Player grounded state (IsGrounded property)
```

### Health System Integration
```csharp
// Add to NPCEnemyAI for damage handling:
private HealthSystem healthSystem;

void Start() {
    healthSystem = GetComponent<HealthSystem>();
    healthSystem.OnDamageTaken.AddListener(OnDamaged);
}

void OnDamaged(float damage, float newHealth) {
    SetState(AIState.Alert);
    SetAlertLevel(1.0f);
}
```

### Alarm System Integration
```csharp
// Connect to existing AlarmSystem:
void OnPlayerDetected() {
    AlarmSystem alarm = FindObjectOfType<AlarmSystem>();
    if (alarm != null) alarm.TriggerAlarm();
}
```

## 📝 Next Steps

1. **Test basic setup** with single NPC
2. **Add multiple NPCs** and test interactions
3. **Tune parameters** based on gameplay feel
4. **Integrate with combat system** for damage dealing
5. **Add animation integration** for visual polish
6. **Implement group AI behaviors** for coordinated responses

## 🔗 Related Documentation

- `docs/CodeExplanation.md` - Detailed system explanations
- `docs/setup-guides/PlayerController3DSetup.md` - Player setup
- `docs/setup-guides/HealthSystemSetup.md` - Health integration
- `Assets/Scripts/AI/NPCEnemyAI.cs` - Main script documentation

---

*Setup guide for NPC Enemy AI System v1.0*  
*Created: 2025-08-01*  
*Compatible with Blazeout Unity project*
