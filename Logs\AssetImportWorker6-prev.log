Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:19Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker6.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21832]  Target information:

Player connection [21832]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 345497436 [EditorId] 345497436 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21832] Host joined multi-casting on [***********:54997]...
Player connection [21832] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 17.05 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 5.21 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56060
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004658 seconds.
- Loaded All Assemblies, in  1.141 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.986 seconds
Domain Reload Profiling: 2127ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (3ms)
	RebuildCommonClasses (145ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (519ms)
		LoadAssemblies (311ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (511ms)
			TypeCache.Refresh (508ms)
				TypeCache.ScanAssembly (476ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (987ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (67ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (431ms)
			ProcessInitializeOnLoadMethodAttributes (145ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.329 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 10.01 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.850 seconds
Domain Reload Profiling: 5177ms
	BeginReloadAssembly (432ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (110ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (1665ms)
		LoadAssemblies (830ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1070ms)
			TypeCache.Refresh (847ms)
				TypeCache.ScanAssembly (799ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (2851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (359ms)
			ProcessInitializeOnLoadAttributes (1696ms)
			ProcessInitializeOnLoadMethodAttributes (205ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 13.06 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (9.5 MB). Loaded Objects now: 8334.
Memory consumption went from 208.1 MB to 198.6 MB.
Total: 70.911000 ms (FindLiveObjects: 2.159700 ms CreateObjectMapping: 2.406100 ms MarkObjects: 56.022100 ms  DeleteObjects: 10.319900 ms)

========================================================================
Received Import Request.
  Time since last request: 1812180.635013 seconds.
  path: Assets/IdaFaber/Shaders/Double-sided.shader
  artifactKey: Guid(9b5cc7c3cd538694db0c0490360ec62d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/Double-sided.shader using Guid(9b5cc7c3cd538694db0c0490360ec62d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65631d4ed298ae47e3fb58e75612b90c') in 2.9748642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity
  artifactKey: Guid(f56951a420d7af648a3ae7527f83bc25) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity using Guid(f56951a420d7af648a3ae7527f83bc25) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity additively'
Loaded scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_01_Slashes-Projectiles_Explosions.unity'
	Deserialize:            85.512 ms
	Integration:            2041.198 ms
	Integration of assets:  0.191 ms
	Thread Wait Time:       17.832 ms
	Total Operation Time:   2144.734 ms
 -> (artifact id: 'e83efc9025625d7ee332cf4d96a0a398') in 5.6544685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 901

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab
  artifactKey: Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Prefebs/Unity_Grruzam_BaseModeling_Katana.prefab using Guid(5d6cbd1259a7dc24b9e3e7a26eacd3f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ea4ab4e92e3e64e2ba22cc9d678a547') in 2.0635462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1101

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity
  artifactKey: Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity using Guid(29e85e86a30c59c4c8f6cb4a51dd0e71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity additively'
Loaded scene 'Assets/Hivemind/MedievalKingdom/HDRP(Default)/Scenes/L_CastleTown.unity'
	Deserialize:            12358.813 ms
	Integration:            93491.742 ms
	Integration of assets:  12.621 ms
	Thread Wait Time:       -9.227 ms
	Total Operation Time:   105853.953 ms
 -> (artifact id: '32a7cb0dc85b67ef4631964266fd93ad') in 246.66309089999999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9364

========================================================================
Received Import Request.
  Time since last request: 142.126631 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat
  artifactKey: Guid(abc00000000011170058242184240894) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Mountain_Grass.mat using Guid(abc00000000011170058242184240894) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e240d6f7033cd40b2e5f27e48a237a0a') in 0.2000284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab
  artifactKey: Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab using Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '318b9fa52d4d0d8d931bb52a21ded782') in 0.1045529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Maps/Scene.unity
  artifactKey: Guid(741b12c7941492e4394818ab3666f864) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Maps/Scene.unity using Guid(741b12c7941492e4394818ab3666f864) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c191b4df0cb0761930d52ac2e648a87') in 0.0250183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_AnimationSettings.lighting
  artifactKey: Guid(f36d6051a9275cb4ea181458167c35c1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_AnimationSettings.lighting using Guid(f36d6051a9275cb4ea181458167c35c1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ee05cfc2b437944b238c22618061c8a') in 0.0367969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab
  artifactKey: Guid(59089dd7f73b56f489328596f173dc5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Slope.prefab using Guid(59089dd7f73b56f489328596f173dc5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09e388cb686c23704993c89a87183628') in 0.0270253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab
  artifactKey: Guid(9e4757aed32f476419bb6acd9168541a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab using Guid(9e4757aed32f476419bb6acd9168541a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7baeede752431080b8ebc8eafc78771e') in 0.1389001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab
  artifactKey: Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[Cameras].prefab using Guid(60542c78ea7861c4381d91d66fa28c9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc76b0d0b4d0f84266a6e4a544667ad9') in 0.1665589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene.unity
  artifactKey: Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene.unity using Guid(9abc3f3d8d505ad428b61115ca6865cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '671f002720495320646639ecb80c849f') in 0.0395754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab
  artifactKey: Guid(83cdd184a3eace340b4dedb44e652290) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Climb.prefab using Guid(83cdd184a3eace340b4dedb44e652290) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8cc9c1000a701f3dfc7d899202ed792d') in 0.0304974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab
  artifactKey: Guid(32d5f0cabfc0be846a4c217337d74479) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab using Guid(32d5f0cabfc0be846a4c217337d74479) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '725494370c5dc426961ec12a149ac170') in 0.0311507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity
  artifactKey: Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Demo/Hierarchy Designer Demo.unity using Guid(a6d696c143d441e4fa0c5d67dc0a7abf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95f6d4cafdceb5f69e068c2ff52a3530') in 0.0241949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab
  artifactKey: Guid(a94136958165ffb4e91f034a7974d498) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Disks.prefab using Guid(a94136958165ffb4e91f034a7974d498) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47d155e9536269d8ebbcfcb30fd5ed6f') in 0.0256799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation.unity
  artifactKey: Guid(4eea59229e01a4644ae2a9b135859269) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Scenes/Scenes_Grruzam_Animation.unity using Guid(4eea59229e01a4644ae2a9b135859269) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7765b7d61a23e69a5bc659bb43b5526b') in 0.0253927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoidSettings.lighting
  artifactKey: Guid(5825f5d6be57b73429261d4d6a3762ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoidSettings.lighting using Guid(5825f5d6be57b73429261d4d6a3762ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '472fdaae199daf69d0aa52b288794e27') in 0.0262985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab
  artifactKey: Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab using Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '50f60f82a6c537148e0be6a736709b09') in 0.0307773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity
  artifactKey: Guid(afd6880a243c0854882e6dc680d01b5c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleSceneHumanoid.unity using Guid(afd6880a243c0854882e6dc680d01b5c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '845d728d93cfc16e46e97e8fbfd9ec6f') in 0.0411425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab
  artifactKey: Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab using Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a3594a58091f92b6df2c27c05ae2bda') in 0.0219772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)CharacterSmall.prefab
  artifactKey: Guid(b4647abb91dd7f740a30ee465203d81a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)CharacterSmall.prefab using Guid(b4647abb91dd7f740a30ee465203d81a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ce60c51c087f19d5fae42b0a7ee8d97') in 0.0258243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Scenes/Start 01.unity
  artifactKey: Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/Start 01.unity using Guid(fcc614a680c6d234f995cbd8903bbdaa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ed4a437a9d31ddf80f777bc3cc7150f') in 0.02496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab
  artifactKey: Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab using Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f5fef0b42ae694773f691d8efe0a50e') in 0.0217294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 52.337669 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat
  artifactKey: Guid(abc00000000005011678449934084366) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_BrickStoneWall_Damage.mat using Guid(abc00000000005011678449934084366) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ad23d3a40b2f0b87416c3f3d22eecba') in 0.0685458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 109.214861 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab
  artifactKey: Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Character.prefab using Guid(b214255a4b4d9fc46a78e1959156bd18) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fdd6dcecb1c5e0b1f91fc10911cdcf4c') in 1.5505524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab
  artifactKey: Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Terrain.prefab using Guid(016565f1f4c96ea4e9ff7611f4ef81de) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '42803526e13bd38338567254d42a6c03') in 0.0433202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab
  artifactKey: Guid(32d5f0cabfc0be846a4c217337d74479) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Rubble.prefab using Guid(32d5f0cabfc0be846a4c217337d74479) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1eb86675f9b43c3d27e9142a46e58949') in 0.034417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 2.105902 seconds.
  path: Assets/Nappin/PhysicsCharacterController/ExampleScene/LightingData.asset
  artifactKey: Guid(2fe5bc3a26eeb404cb658b4242bbfb6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/ExampleScene/LightingData.asset using Guid(2fe5bc3a26eeb404cb658b4242bbfb6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da3bb5afb4806786d338fad65b5cc751') in 0.0029648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.097879 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Blue.mat
  artifactKey: Guid(d8b06aed0f8442a4693303bcffe3c7c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Blue.mat using Guid(d8b06aed0f8442a4693303bcffe3c7c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '200c65b3faec76e70943a18cfda71c77') in 5.6049712 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Green.mat
  artifactKey: Guid(41cdb632b6e13624c86ce356489d6e6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Green.mat using Guid(41cdb632b6e13624c86ce356489d6e6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6305d08b8c34a3a0ac12e05738c2e643') in 0.0449226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Running.fbx
  artifactKey: Guid(52e19e9667b8f4546ad61f69c8b6c77a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Running.fbx using Guid(52e19e9667b8f4546ad61f69c8b6c77a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d4dff5e9f6196d30f912ff64db661a3') in 0.0456962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchingIdle.fbx
  artifactKey: Guid(91532bfa84527d84bafd6f536e5a614a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)CrouchingIdle.fbx using Guid(91532bfa84527d84bafd6f536e5a614a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25689f1e19b4f1751d09b8bd7caff73c') in 0.0374195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)HangingIdle.fbx
  artifactKey: Guid(9a08a88c3b0406f4e884aa5442eaabbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)HangingIdle.fbx using Guid(9a08a88c3b0406f4e884aa5442eaabbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2fb040796abc2277a896c23dec458a67') in 0.0432302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyEyes.mat
  artifactKey: Guid(32bb946ef33bf9a4eabd0af551277452) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyEyes.mat using Guid(32bb946ef33bf9a4eabd0af551277452) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5fb9428b840be4ab0bd110c8eddf917f') in 0.073754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.007007 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Editor/(Txt)PhysicsBasedCharacterController_Icon.png
  artifactKey: Guid(6511cb88e1f6d3e45b37f06a14d6fca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Editor/(Txt)PhysicsBasedCharacterController_Icon.png using Guid(6511cb88e1f6d3e45b37f06a14d6fca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f72a484e5825e505a47abf65f10b60d1') in 0.0696463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)FallingIdle.fbx
  artifactKey: Guid(35e3e1f632c392241ba3328c02b89693) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)FallingIdle.fbx using Guid(35e3e1f632c392241ba3328c02b89693) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c48a21fb922e0c236a5536b94e51d694') in 0.0403518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)DarkGrey.mat
  artifactKey: Guid(7f187a645b06e36419e453cfa9085266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)DarkGrey.mat using Guid(7f187a645b06e36419e453cfa9085266) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2d085a98173324686582cce6c440542') in 0.0351707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Red.mat
  artifactKey: Guid(eaf1b4dae53bbaa418c9ce4ff9e2c2c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Red.mat using Guid(eaf1b4dae53bbaa418c9ce4ff9e2c2c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad7903d9ab9ba45b4a904997b66a7915') in 0.0372601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Idle.fbx
  artifactKey: Guid(b926ca16ddf3eca4f93dc0ac8b542ee9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Idle.fbx using Guid(b926ca16ddf3eca4f93dc0ac8b542ee9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ad51a15c7e5caaa1df1c704794b65a6') in 0.2311356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 145

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Orange.mat
  artifactKey: Guid(9503aa2300e0bf34fb32ef8d63124d55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Orange.mat using Guid(9503aa2300e0bf34fb32ef8d63124d55) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6de1e920c71043644d80a1beff154f29') in 0.0460283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)RedClear.mat
  artifactKey: Guid(379b9e3ae55cbc24e8b774854c72f561) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)RedClear.mat using Guid(379b9e3ae55cbc24e8b774854c72f561) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '42093d248c23393bed7e3051576f155f') in 0.0348496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Clouds.mat
  artifactKey: Guid(e2be606e9870063459c8700747c3b572) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Clouds.mat using Guid(e2be606e9870063459c8700747c3b572) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd309b67d9445cc653bdb3323c961bb94') in 0.0620938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Walking.fbx
  artifactKey: Guid(b1140fd1818841e47892a909aeb6ca9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)Walking.fbx using Guid(b1140fd1818841e47892a909aeb6ca9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15040765eb2e65c04f29f82084ddb25c') in 0.0431454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)ClimbingUpWall.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)ClimbingUpWall.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7225e7c36e3e1a2128ebffc00dbd7df') in 0.0384922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Animations/(Anim)JumpingUp.fbx
  artifactKey: Guid(d58d297844693e346ac6a6edad3f4f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Animations/(Anim)JumpingUp.fbx using Guid(d58d297844693e346ac6a6edad3f4f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65f37b5ec7686ac6febeff8fa06a5593') in 0.0459357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyFace.mat
  artifactKey: Guid(c70c7660792bc524193d77a2df89539a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)BodyFace.mat using Guid(c70c7660792bc524193d77a2df89539a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '75a7fd81be495ee4cdc750702c8f3ebd') in 0.0748128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Grey.mat
  artifactKey: Guid(32391099aad710644a06cc941264b32a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Grey.mat using Guid(32391099aad710644a06cc941264b32a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fad3b2c5f590c02abc5119fe6dce0ccd') in 0.0874015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 38.731096 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_2.mat
  artifactKey: Guid(f4ef35987ca7ed64aab846c9be2a00a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Water_2.mat using Guid(f4ef35987ca7ed64aab846c9be2a00a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5c304cf4bf317f77b7d88a6e7fab00d') in 0.03361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.646230 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovablePlatform_Plank.prefab
  artifactKey: Guid(f47091266aceec646a411325962aaaec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovablePlatform_Plank.prefab using Guid(f47091266aceec646a411325962aaaec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7dbf5888798e36e95aa6350a6421e643') in 0.0394646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab
  artifactKey: Guid(fe987b373409bdd4e92698a48f7505b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Projectile.prefab using Guid(fe987b373409bdd4e92698a48f7505b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a373ef072fa4afdf5596855d56f24b5') in 0.0569205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab
  artifactKey: Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab using Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44031ee9cf8bb46e36214fcfcd346442') in 0.0688152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab
  artifactKey: Guid(9b059dc6477a3be49baa04addc732e67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeGeneric.prefab using Guid(9b059dc6477a3be49baa04addc732e67) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8b7ddd346689365e425c0fe0d730587') in 0.0688626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeCone.prefab
  artifactKey: Guid(180bc4f9625b9044a85a20709235bfa6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SlopeCone.prefab using Guid(180bc4f9625b9044a85a20709235bfa6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71a862827498fad1984007057bf46e6f') in 0.0506486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0