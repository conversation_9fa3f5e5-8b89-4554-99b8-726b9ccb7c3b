%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21818473e9e5e88419781342de5bb3d6, type: 3}
  m_Name: DutyTaskList
  m_EditorClassIdentifier: 
  nodes:
    _keys:
    - 0e142c56-dc84-43a7-8d26-45fd4e5e4049
    _values:
    - id: 0e142c56-dc84-43a7-8d26-45fd4e5e4049
      nodeConnection:
      - guid: fa6a8908-d0ea-4b22-917a-d6284de948a7
        requirementData:
          rid: -2
      iterator: 1
      globalWeighting: 0.75
      localWeighting: 0.75
      name: find Tree and cut it
      desc: 
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
