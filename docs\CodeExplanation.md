# Blazeout Code Learning Guide

## 🎮 Game Architecture Overview

This document explains how Blazeout is built, designed for beginners to understand every part of the codebase.

## 🏗️ Core Systems

### Controller System (Implemented)
Advanced input management system that handles multiple controller types and provides enhanced input features.

**Key Components:**
- **ControllerManager.cs**: Main singleton that manages controller detection and settings
- **EnhancedInputReader.cs**: Enhanced input processing with buffering and legacy support
- **ControllerUI.cs**: User interface for controller configuration

**Features:**
- Auto-detection of keyboard/mouse and gamepad controllers
- Seamless switching between controller types
- Configurable sensitivity and deadzone settings
- Input buffering for improved responsiveness
- Vibration support for gamepads
- Settings persistence
- Legacy InputReader compatibility

```csharp
// Example usage:
var controllerManager = ControllerManager.Instance;
controllerManager.OnControllerTypeChanged += (type) => {
    Debug.Log($"Controller switched to: {type}");
};

// Apply controller-specific processing
Vector2 processedInput = controllerManager.ApplyDeadzoneAndSensitivity(rawInput, true);
```

### Player Movement System (Implemented)
Comprehensive 3D character movement system with integrated animation control.

**Key Components:**
- **PlayerController3D.cs**: Complete 3D movement system with integrated animator control
- **MovementStateMachine.cs**: Advanced state machine for movement states
- **InputBufferSystem.cs**: Input buffering, coyote time, and sequence detection
- **MovementConfigManager.cs**: Configuration profiles and settings persistence

**Movement States:**
- Idle, Walking, Running, Crouching, Jumping, Falling, Landing

**Features:**
- Physics-based 3D movement with Rigidbody integration

### NPC Enemy AI System (Implemented)
Comprehensive AI system for enemy NPCs with advanced detection and behavior systems.

**Key Components:**
- **NPCEnemyAI.cs**: Main AI behavior script with state machine and detection
- **AIDetectionUtilities.cs**: Advanced detection utilities and optimization tools
- **AIDebugVisualizer.cs**: Debug visualization and testing tools

**AI States:**
- **Patrol**: Default wandering behavior between patrol points
- **Alert**: Investigating suspicious activity or sounds
- **Chase**: Actively pursuing detected player
- **Attack**: In combat range, performing attacks
- **Search**: Lost player, searching last known position
- **Stunned**: Temporarily disabled state

**Detection Systems:**
```csharp
// Visual Detection - Cone-based sight with line-of-sight checking
private void CheckVisualDetection() {
    // 1. Check if player is within sight range
    if (distanceToPlayer > sightRange) return;

    // 2. Check if player is within sight angle (field of view)
    Vector3 directionToPlayer = (playerTransform.position - transform.position).normalized;
    float angleToPlayer = Vector3.Angle(transform.forward, directionToPlayer);
    if (angleToPlayer > sightAngle * 0.5f) return;

    // 3. Perform line-of-sight raycast to check for obstacles
    if (HasLineOfSight(playerTransform.position)) {
        canSeePlayer = true;
        // Update alert level and last known position
    }
}

// Audio Detection - Sphere-based hearing for player footsteps
private void CheckAudioDetection() {
    // Check if player is making detectable noise
    if (playerController.CurrentSpeed > minimumFootstepSpeed) {
        float footstepVolume = (playerController.CurrentSpeed * footstepVolumeMultiplier) / distanceToPlayer;
        if (footstepVolume > hearingThreshold) {
            canHearPlayer = true;
            // Audio detection provides less alert than visual
        }
    }
}
```

**State Machine Logic:**
```csharp
// State transitions based on detection and behavior
private void UpdateStateMachine() {
    switch (currentState) {
        case AIState.Patrol:
            // Check for player detection
            if (canSeePlayer) SetState(AIState.Chase);
            else if (canHearPlayer) SetState(AIState.Alert);
            else HandlePatrolMovement();
            break;

        case AIState.Alert:
            // Investigate last known position
            if (canSeePlayer) SetState(AIState.Chase);
            else if (stateTimer > alertDuration) SetState(AIState.Patrol);
            break;

        case AIState.Chase:
            // Pursue player
            if (distanceToPlayer <= attackRange) SetState(AIState.Attack);
            else if (!canSeePlayer) SetState(AIState.Search);
            break;
    }
}
```

**NavMesh Integration:**
```csharp
// Intelligent pathfinding and movement
private void PatrolBetweenPoints() {
    Transform targetPoint = patrolPoints[currentPatrolIndex];
    navAgent.SetDestination(targetPoint.position);

    // Check if reached patrol point
    if (Vector3.Distance(transform.position, targetPoint.position) < 1.0f) {
        StartCoroutine(WaitAtPatrolPoint());
    }
}

// Dynamic speed adjustment based on AI state
public void SetState(AIState newState) {
    currentState = newState;
    switch (newState) {
        case AIState.Patrol: navAgent.speed = patrolSpeed; break;
        case AIState.Alert: navAgent.speed = alertSpeed; break;
        case AIState.Chase: navAgent.speed = chaseSpeed; break;
        case AIState.Attack: navAgent.speed = 0.0f; break; // Stop during attack
    }
}
```

**Debug and Visualization:**
```csharp
// Visual debug gizmos for development and tuning
private void OnDrawGizmos() {
    // Draw sight range and cone
    Gizmos.color = canSeePlayer ? Color.red : Color.yellow;
    Gizmos.DrawWireSphere(transform.position, sightRange);

    // Draw sight cone boundaries
    Vector3 leftBoundary = Quaternion.Euler(0, -sightAngle * 0.5f, 0) * transform.forward * sightRange;
    Vector3 rightBoundary = Quaternion.Euler(0, sightAngle * 0.5f, 0) * transform.forward * sightRange;
    Gizmos.DrawLine(transform.position, transform.position + leftBoundary);

    // Draw hearing range
    Gizmos.color = canHearPlayer ? Color.blue : Color.cyan;
    Gizmos.DrawWireSphere(transform.position, hearingRange);

    // Draw line of sight ray to player
    if (playerTransform != null) {
        Gizmos.color = canSeePlayer ? Color.green : Color.red;
        Gizmos.DrawLine(transform.position + Vector3.up * 1.6f, playerTransform.position);
    }
}
```

**Performance Optimization:**
- Detection checks run at 10Hz (0.1s intervals) instead of every frame
- Line-of-sight caching to avoid redundant raycasts
- Smart update intervals based on AI state and distance
- Batch processing for multiple AI entities
- Stamina system for running with regeneration
- Ultra-strict jump system with ground detection
- **Integrated Animator Control**: Full animation parameter management within movement system
- Performance-optimized animation hash caching
- Trigger-based animations for responsive jump/land events
- Camera control system (First Person / Third Person switching)
- Advanced ground detection with multiple check points
- Settings persistence and debug visualization

**Animation Integration:**
- **11 Animation Parameters**: Speed, IsGrounded, IsRunning, IsCrouching, IsJumping, IsFalling, Jump (trigger), Land (trigger), Vertical, Horizontal, MovementState
- **State Synchronization**: Movement states automatically sync with animator
- **Performance Optimized**: Uses hashed parameter names for faster animator updates
- **Kevin Iglesias Compatible**: Designed to work with Human Animations pack

```csharp
// Example usage:
var controller = GetComponent<PlayerController3D>();
controller.events.OnStateChanged.AddListener((state) => {
    Debug.Log($"Movement state changed to: {state}");
});

// Check movement capabilities
if (controller.CanJump())
{
    // Jump logic is handled automatically
}

// Switch camera modes
controller.SetCameraViewMode(CameraViewMode.FirstPerson);

// Animation parameters are automatically updated:
// - Speed reflects current movement speed
// - IsGrounded shows ground contact status
// - Jump/Land triggers fire automatically
// - All boolean states sync with movement
```

**Architecture Decision:**
- **Unified System**: All animation control integrated directly into PlayerController3D
- **No Separate Scripts**: Eliminated MovementAnimationController for simplified architecture
- **Tight Coupling**: Ensures perfect synchronization between movement and animation
- **Performance Focused**: Hash-based parameter updates for optimal frame rates

**Prefab Export System:**
- **Complete Self-Contained Package**: Includes all scripts, prefabs, and animator controllers
- **Cross-Project Compatibility**: Unity package format for easy deployment
- **Comprehensive Documentation**: Full setup guides and API reference included
- **Reusable Architecture**: Designed for use across multiple Unity projects

### Pickup Object Holding System (Implemented)
Advanced pickup and interaction system integrated with PlayerController3D.

**Key Components:**
- **PickupObjectHolding.cs**: Complete pickup system with physics-based interactions
- **PickupObjectHoldingSetupTool.cs**: One-click setup tool with automatic configuration
- **PlayerController3DEditor.cs**: Integrated inspector with "Setup Pickup Controller" button

**Features:**
- **Physics-Based Interactions**: SpringJoint system for smooth object holding
- **Dual Detection Methods**: Camera raycast (priority) and proximity detection (fallback)
- **UI Integration**: TextMeshPro prompts with distance-based fading
- **Layer-Based Filtering**: Configurable LayerMask for pickupable objects
- **Throw Mechanics**: Configurable force with realistic physics simulation
- **Animation Integration**: Seamless integration with PlayerController3D animations
- **Auto-Setup Tools**: One-click installation with automatic layer and UI creation

**Setup Integration:**
```csharp
// Access from PlayerController3D inspector
// 1. Select PlayerController3D GameObject
// 2. Look for "Setup Tools" section in inspector
// 3. Click "Setup Pickup Controller" button
// 4. Configure options and click "Complete Setup"

// Or use via menu
// Blazeout → Setup Tools → Pickup Object Holding Setup
```

**Architecture Benefits:**
- **Seamless Integration**: Works directly with existing PlayerController3D system
- **No Script Conflicts**: Designed to complement, not replace existing functionality
- **Editor Integration**: Setup tools built into Unity inspector for easy access
- **Configurable Parameters**: Extensive customization options for different game types
- **Debug Visualization**: Built-in debugging tools with visual feedback in Scene view

### Laser Detection System (Implemented)
Advanced security and detection system with laser beam detection and alarm activation.

**Key Components:**
- **LaserDetector.cs**: Raycast-based laser beam that detects objects crossing the beam
- **AlarmSystem.cs**: Comprehensive alarm system with audio, visual effects, and state management

**Features:**
- **Raycast Detection**: Uses Physics.Raycast for precise object detection
- **Visual Laser Beam**: Optional red LineRenderer showing laser path in game
- **Layer-Based Filtering**: Configurable LayerMask for what objects trigger detection
- **Alarm Integration**: Automatically connects to AlarmSystem for audio/visual alerts
- **Cooldown System**: Prevents alarm spam with configurable trigger cooldown
- **Range Control**: Adjustable laser beam distance with visual feedback

**LaserDetector Features:**
- Real-time laser visualization with hit point detection
- Gizmo debugging in Scene view (shows laser direction and range)
- Auto-finding of AlarmSystem in scene
- Thread-safe detection with minimal performance impact

**AlarmSystem Features:**
- **Audio Management**: AudioSource integration with volume control and looping
- **Visual Effects**: Flashing lights and UI elements during alarm
- **State Management**: Active/inactive states with proper cleanup
- **Duration Control**: Configurable alarm length or infinite until stopped
- **Multiple Triggers**: Supports multiple LaserDetectors triggering same alarm

```csharp
// Example usage:
// 1. Create empty GameObject for AlarmSystem
AlarmSystem alarm = alarmSystemObject.GetComponent<AlarmSystem>();
alarm.TriggerAlarm(); // Manual trigger

// 2. Create LaserDetector GameObject positioned where laser should emit
LaserDetector laser = laserObject.GetComponent<LaserDetector>();
laser.SetDetectionEnabled(true); // Enable/disable detection
laser.LaserRange = 15f; // Set beam distance

// Automatic connection - LaserDetector finds AlarmSystem automatically
```

**Use Cases:**
- **Security Systems**: Perimeter defense, door sensors, area monitoring
- **Trap Mechanisms**: Dungeon traps, puzzle triggers, hidden switches  
- **Machine Safety**: Industrial safety systems, emergency stops
- **Game Mechanics**: Stealth detection, puzzle elements, interactive environments

### Health System (Implemented)
Comprehensive health management system that can be attached to any GameObject for damage, healing, and death handling.

**Key Components:**
- **HealthSystem.cs**: Complete health management component with damage, healing, invulnerability, and event system

**Features:**
- **Universal Compatibility**: Can be attached to any GameObject (Player, Enemy, Destructible objects)
- **Damage & Healing**: Robust damage application with healing capabilities
- **Invulnerability Frames**: Configurable immunity period after taking damage to prevent spam
- **Event System**: Comprehensive UnityEvent integration for custom behavior
- **UI Integration**: Built-in support for health bars and text displays
- **Death Handling**: Configurable death behavior with optional GameObject destruction
- **Debug Visualization**: Scene view gizmos and console logging for development

**Core Properties:**
- **Health Management**: CurrentHealth, MaxHealth, HealthPercentage for easy UI integration
- **State Checking**: IsAlive, IsInvulnerable properties for game logic
- **Dynamic Configuration**: Runtime changes to max health with proportional scaling

**Event System:**
- **OnHealthChanged**: Fired when health value changes (current, max)
- **OnDamageTaken**: Fired when damage is applied (damage amount, new health)
- **OnHealed**: Fired when health is restored (heal amount, new health)  
- **OnDeath**: Fired when health reaches zero
- **OnInvulnerabilityStart/End**: Fired when invulnerability state changes

```csharp
// Example usage:
// 1. Add to any GameObject - works immediately with good defaults
HealthSystem health = gameObject.AddComponent<HealthSystem>();

// 2. Basic damage and healing
bool damageApplied = health.TakeDamage(25.0f);
float actualHeal = health.Heal(50.0f);

// 3. Subscribe to events for custom behavior
health.OnDeath.AddListener(() => {
    Debug.Log("Entity died!");
    // Play death animation, drop loot, trigger game over, etc.
});

health.OnDamageTaken.AddListener((damage, newHealth) => {
    // Screen shake, damage sounds, visual feedback
    CameraShake.Instance.Shake(0.3f, 0.1f);
});

// 4. UI Integration (automatic updates)
healthBar.value = health.HealthPercentage; // 0.0 to 1.0
healthText.text = $"{health.CurrentHealth}/{health.MaxHealth}";

// 5. Advanced control
health.SetInvulnerable(true); // Manual invulnerability control
health.SetMaxHealth(150.0f); // Dynamic max health changes
health.ResetHealth(); // Full heal and revive
```

**Integration Examples:**
```csharp
// Player integration with PlayerController3D
public class PlayerCombat : MonoBehaviour {
    private HealthSystem playerHealth;
    private PlayerController3D playerController;
    
    private void Start() {
        playerHealth = GetComponent<HealthSystem>();
        playerController = GetComponent<PlayerController3D>();
        
        playerHealth.OnDeath.AddListener(() => {
            playerController.enabled = false; // Disable control
            GameManager.Instance.GameOver();
        });
    }
}

// Enemy AI integration
public class EnemyAI : MonoBehaviour {
    private HealthSystem enemyHealth;
    
    private void Start() {
        enemyHealth = GetComponent<HealthSystem>();
        enemyHealth.OnDeath.AddListener(() => {
            DropLoot();
            GameManager.Instance.AddKill();
        });
    }
}

// Hazard integration (built-in trigger detection)
// Tag hazards as "Hazard" - automatically deals 10 damage
// Tag health pickups as "HealthPickup" - automatically heals 25 points
```

**Architecture Benefits:**
- **Seamless Integration**: Works with all existing Blazeout systems (PlayerController3D, PickupObjectHolding, LaserDetector)
- **Zero Dependencies**: No required components - works standalone
- **Event-Driven Design**: Game-specific behavior handled through events, keeping core system generic
- **Performance Optimized**: Minimal overhead, UI updates only when needed
- **Developer Friendly**: Comprehensive debug tools and clear documentation
- **Default Values**: Works great out-of-the-box with sensible defaults for immediate use

**Use Cases:**
- **Player Characters**: Health tracking with respawn or game over mechanics
- **Enemy Entities**: Health management with death cleanup and loot drops
- **Destructible Objects**: Environmental destruction with visual feedback
- **Hazard Systems**: Integration with trap mechanics and environmental dangers
- **Boss Battles**: Multi-phase health management with event-driven state changes

### Game Loop (Not Yet Implemented)
The heart of any game - runs continuously while the game is active.

```javascript
// Basic game loop structure
function gameLoop() {
    // 1. Handle Input - Process player actions first
    handleInput();
    
    // 2. Update Game State - Move objects, check collisions
    updateGame(deltaTime);
    
    // 3. Render - Draw everything to screen
    render();
    
    // 4. Schedule Next Frame
    requestAnimationFrame(gameLoop);
}
```

**Why this order?**
- Input first: So player actions feel responsive
- Update second: Calculate new positions, collisions
- Render last: Draw the current state to screen

### Entity Component System (Planned)
Modern game architecture that separates data from behavior.

```javascript
// Example structure:
const player = {
    // Components (data)
    position: { x: 100, y: 100 },
    velocity: { x: 0, y: 0 },
    sprite: { image: "player.png", width: 32, height: 32 },
    health: { current: 100, max: 100 }
};

// Systems (behavior)
MovementSystem.update(player);  // Moves player based on velocity
RenderSystem.draw(player);      // Draws player sprite at position
```

**Benefits:**
- Easy to add new features (just add components)
- Performance: Process similar components together
- Flexibility: Mix and match components for different entities

## 🎯 Game Mechanics (To Be Implemented)

### Player Movement
How the player character moves around the screen.

**Planned Implementation:**
```javascript
function updatePlayerMovement(player, input, deltaTime) {
    // Apply input to velocity
    if (input.left) player.velocity.x = -PLAYER_SPEED;
    if (input.right) player.velocity.x = PLAYER_SPEED;
    
    // Apply velocity to position
    player.position.x += player.velocity.x * deltaTime;
    player.position.y += player.velocity.y * deltaTime;
    
    // Keep player on screen
    clampToScreen(player);
}
```

### Collision Detection (Planned)
How we detect when game objects hit each other.

**Algorithm: AABB (Axis-Aligned Bounding Box)**
```
Object A: [x1, y1, width1, height1]
Object B: [x2, y2, width2, height2]

Collision occurs when:
- A.x1 < B.x2 + B.width2  (A's left edge is left of B's right edge)
- A.x1 + A.width1 > B.x2  (A's right edge is right of B's left edge)
- A.y1 < B.y2 + B.height2 (A's top edge is above B's bottom edge)
- A.y1 + A.height1 > B.y2 (A's bottom edge is below B's top edge)
```

**Why AABB?**
- Simple to calculate (just 4 comparisons)
- Fast performance for many objects
- Good enough for most 2D games

## 🎨 Rendering System (Planned)

### Canvas-Based Rendering
Drawing graphics to the HTML5 Canvas element.

```javascript
function render(gameState) {
    // Clear previous frame
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    drawBackground();
    
    // Draw all game objects
    gameState.entities.forEach(entity => {
        if (entity.sprite) {
            drawSprite(entity.sprite, entity.position);
        }
    });
    
    // Draw UI on top
    drawUI(gameState.player);
}
```

## 🎵 Audio System (Planned)

### Sound Effects and Music
How we play audio in the game.

```javascript
class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.music = null;
    }
    
    loadSound(name, path) {
        const audio = new Audio(path);
        this.sounds.set(name, audio);
    }
    
    playSound(name) {
        const sound = this.sounds.get(name);
        if (sound) {
            sound.currentTime = 0; // Reset to beginning
            sound.play();
        }
    }
}
```

## 📊 Game State Management

### Current State Structure
Based on `GameState.json`, here's how we organize game data:

```javascript
const gameState = {
    player: {
        position: { x: 400, y: 300 },
        health: 100,
        score: 0
    },
    enemies: [],
    powerups: [],
    ui: {
        showDebugInfo: false
    }
};
```

**Why this structure?**
- Centralized: All game data in one place
- Serializable: Easy to save/load
- Debuggable: Can inspect entire state

## 🚀 Performance Considerations

### Object Pooling (Future Optimization)
Instead of creating/destroying objects constantly:

```javascript
class BulletPool {
    constructor(size) {
        this.pool = [];
        this.activeIndex = 0;
        
        // Pre-create bullets
        for (let i = 0; i < size; i++) {
            this.pool.push(createBullet());
        }
    }
    
    getBullet() {
        const bullet = this.pool[this.activeIndex];
        this.activeIndex = (this.activeIndex + 1) % this.pool.length;
        return bullet;
    }
}
```

**Benefits:**
- No garbage collection hiccups
- Consistent frame rates
- Better memory usage patterns

## 🐛 Common Gotchas & Solutions

### Frame Rate Independence
**Problem:** Game runs at different speeds on different computers.
**Solution:** Use deltaTime in all calculations.

```javascript
// BAD - speed depends on framerate
player.x += 5;

// GOOD - speed is consistent regardless of framerate
player.x += SPEED * deltaTime;
```

### Floating Point Precision
**Problem:** Numbers like 0.1 + 0.2 ≠ 0.3 in JavaScript.
**Solution:** Round when needed, or use integer math.

```javascript
// For pixel positions, round to avoid blurry graphics
player.x = Math.round(player.x + velocity.x * deltaTime);
```

## 📚 Learning Resources

### Game Development Patterns
1. **Game Loop**: The main cycle that keeps the game running
2. **Entity Component System**: Modern way to organize game objects
3. **State Machines**: For managing game states (menu, playing, paused)
4. **Observer Pattern**: For events and notifications

### Math for Games
- Vector math: For positions, velocities, directions
- Trigonometry: For rotation, circular motion, aiming
- Linear interpolation: For smooth animations

## 🔧 Development Tools & Commands

### Testing Commands (To Be Determined)
```bash
# Test the game
npm test

# Lint the code
npm run lint

# Build for production
npm run build
```

### Debugging Tips
1. Use `console.log()` to track variable values
2. Use browser dev tools to inspect canvas rendering
3. Add debug visualizations for collision boxes
4. Monitor frame rate with built-in browser tools

---

## 📝 Notes for Future Development

This file will be updated as we implement each system. Each new feature should include:
- Clear explanation of what it does
- Code examples showing how to use it
- Explanation of why we chose this approach
- Common pitfalls and how to avoid them

Remember: **Code is written once, but read many times. Make it count!**