%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6e9167a1a085746489316f2f3bf4e276, type: 3}
  m_Name: TaskMethodList
  m_EditorClassIdentifier: 
  nodes:
    _keys:
    - fa6a8908-d0ea-4b22-917a-d6284de948a7
    _values:
    - id: fa6a8908-d0ea-4b22-917a-d6284de948a7
      nodeConnection:
      - guid: 2854c638-d12a-47f8-baa8-1fd8f115b6e0
        requirementData:
          rid: -2
      - guid: 03ff13f5-0937-4ccc-8c15-0eb5105d7a23
        requirementData:
          rid: -2
      - guid: c72f54d2-2b0e-4eb2-a383-ec55a47a5700
        requirementData:
          rid: -2
      - guid: 4a4be282-608e-4f49-9334-b624d46de5e3
        requirementData:
          rid: -2
      - guid: 7db2d2dd-e8db-458f-9aa1-ece531f46663
        requirementData:
          rid: -2
      iterator: 0
      globalWeighting: 0.75
      localWeighting: 0.75
      name: small tree with axes
      desc: testing
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
