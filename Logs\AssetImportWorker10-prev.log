Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T21:09:20Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker10
-projectPath
E:/2025/Boder/Blazeout
-logFile
Logs/AssetImportWorker10.log
-srvPort
55000
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/Blazeout
E:/2025/Boder/Blazeout
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23952]  Target information:

Player connection [23952]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3099104754 [EditorId] 3099104754 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23952] Host joined multi-casting on [***********:54997]...
Player connection [23952] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.47 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 5.48 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/Blazeout/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56088
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005051 seconds.
- Loaded All Assemblies, in  1.104 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.129 seconds
Domain Reload Profiling: 2233ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (108ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (521ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (513ms)
			TypeCache.Refresh (509ms)
				TypeCache.ScanAssembly (464ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1130ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (957ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (58ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (370ms)
			ProcessInitializeOnLoadAttributes (386ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.369 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 8.55 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.070 seconds
Domain Reload Profiling: 5435ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (135ms)
	RebuildNativeTypeToScriptingClass (48ms)
	initialDomainReloadingComplete (108ms)
	LoadAllAssembliesAndSetupDomain (1609ms)
		LoadAssemblies (993ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (868ms)
			TypeCache.Refresh (639ms)
				TypeCache.ScanAssembly (595ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (3071ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (481ms)
			ProcessInitializeOnLoadAttributes (1683ms)
			ProcessInitializeOnLoadMethodAttributes (187ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.58 seconds
Refreshing native plugins compatible for Editor in 9.93 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 262 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7638 unused Assets / (10.3 MB). Loaded Objects now: 8334.
Memory consumption went from 208.1 MB to 197.8 MB.
Total: 120.642500 ms (FindLiveObjects: 2.305100 ms CreateObjectMapping: 3.140900 ms MarkObjects: 103.847700 ms  DeleteObjects: 11.345800 ms)

========================================================================
Received Import Request.
  Time since last request: 1812183.286705 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Loop.prefab
  artifactKey: Guid(c12ac9e839f59454e8a71d940042bcb3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_WaterOrb_Loop.prefab using Guid(c12ac9e839f59454e8a71d940042bcb3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2d9ae33eb4a446fb7d0d18e5a5fa32d8') in 2.9026007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 171

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Scripts/Editor/QuickSceneSetup3D.cs
  artifactKey: Guid(3bcfb7dee6d244d499adf26c331a1add) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Editor/QuickSceneSetup3D.cs using Guid(3bcfb7dee6d244d499adf26c331a1add) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '691d94f9ffdad8cff15d8c7343ec1920') in 0.3872346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Demo_02_Orb-Buff-Environmental.unity
  artifactKey: Guid(34717b2494c4d334c938f368f94e598f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Demo_02_Orb-Buff-Environmental.unity using Guid(34717b2494c4d334c938f368f94e598f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_02_Orb-Buff-Environmental.unity additively'
Loaded scene 'Assets/SrRubfish_VFX_02/DemoScene/Demo_02_Orb-Buff-Environmental.unity'
	Deserialize:            15.619 ms
	Integration:            1990.680 ms
	Integration of assets:  0.162 ms
	Thread Wait Time:       50.380 ms
	Total Operation Time:   2056.841 ms
 -> (artifact id: '0c4a2a0dd6b261b09e43e216175b4759') in 2.8941577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 860

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_03_3x4.mat
  artifactKey: Guid(10c1dfdaee409364388911503797be3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_LinearSplash_03_3x4.mat using Guid(10c1dfdaee409364388911503797be3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fac2f4c3184d3c9a617284d94824a7d3') in 0.3922079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.prefab
  artifactKey: Guid(6c5597952c90b064aad5bac1d985fd5b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_14.prefab using Guid(6c5597952c90b064aad5bac1d985fd5b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e2f03de90c04ccb9711c51c22638ae03') in 0.4338557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 38

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Lava_SimpleSplash_01.prefab
  artifactKey: Guid(8bda80a6040df73439f85f2ea20f2f82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Lava_SimpleSplash_01.prefab using Guid(8bda80a6040df73439f85f2ea20f2f82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '557a1ef83ac0f2bcb49dd92cca39239b') in 0.0687922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 67

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Settings/SampleSceneProfile.asset
  artifactKey: Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/SampleSceneProfile.asset using Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b23db2706a7268b74eb0457a22504393') in 0.0439345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.prefab
  artifactKey: Guid(6e1b469716b53eb4ba251f35819666db) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.prefab using Guid(6e1b469716b53eb4ba251f35819666db) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '56d5310c877e9a358acf7025343a629d') in 0.5940866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch.prefab
  artifactKey: Guid(abc00000000006919238686442401839) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Back_Arch.prefab using Guid(abc00000000006919238686442401839) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '782772849c80339e994ef4eabb53c7c5') in 0.0461651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/Polycount.asset
  artifactKey: Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/Polycount.asset using Guid(632ff411dbbf5d54593d6e684940a2bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4eca13125ec3d023b46b8ed015f9969') in 0.0576997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Impact.prefab
  artifactKey: Guid(ee8ff96cd1b23a143b64c09a4dd5452b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Impact.prefab using Guid(ee8ff96cd1b23a143b64c09a4dd5452b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ae9b2705ded898ff128225bfe2db244') in 0.0580769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 60

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab
  artifactKey: Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Stairs_1.prefab using Guid(5b73f55316a1c0b4bac6f488ec9d3233) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '458ae6d80842f474d62c78a5caadb830') in 0.0356303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack_1.prefab
  artifactKey: Guid(abc00000000010125621650837922687) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Book_Stack_1.prefab using Guid(abc00000000010125621650837922687) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4dba9e929fe8af5e3ec53823648defa6') in 0.0363821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_04.prefab
  artifactKey: Guid(abc00000000017595615307471600298) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_B_04.prefab using Guid(abc00000000017595615307471600298) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '17f2b6146fe8e9fa5324c167568e03c0') in 0.0339892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_1.prefab
  artifactKey: Guid(abc00000000004952311748196858445) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Books_A_1.prefab using Guid(abc00000000004952311748196858445) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e4c13507ae5e7dfdaa664f714a37302c') in 0.0479545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cabinet_02.prefab
  artifactKey: Guid(abc00000000016902396061287896548) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cabinet_02.prefab using Guid(abc00000000016902396061287896548) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '47819aa6fd47790748885bb66d64e9dc') in 0.0363053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Connect_01.prefab
  artifactKey: Guid(abc00000000000067416317721915197) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Connect_01.prefab using Guid(abc00000000000067416317721915197) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '851c78565d2a5bf92cacbe95371428d3') in 0.0616402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 60

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashHighlight_01_2x5.mat
  artifactKey: Guid(01a88f29bb9aabf429b2a783ae2244c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_SlashHighlight_01_2x5.mat using Guid(01a88f29bb9aabf429b2a783ae2244c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0da64ddff1aeb4a8ca2f50f39a6e580') in 0.5036498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Empty/Settings/SkyandFogSettingsProfile.asset
  artifactKey: Guid(8ba92e2dd7f884a0f88b98fa2d235fe7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Empty/Settings/SkyandFogSettingsProfile.asset using Guid(8ba92e2dd7f884a0f88b98fa2d235fe7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '54564220341a333456cb43d67e8ca733') in 0.0319286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_01.prefab
  artifactKey: Guid(abc00000000012833982667677876559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_01.prefab using Guid(abc00000000012833982667677876559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '428fb67fccaa3780c83b88c31f487dd1') in 0.0362164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_04.prefab
  artifactKey: Guid(abc00000000014978965448417498893) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_04.prefab using Guid(abc00000000014978965448417498893) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4243dc4f7173c382e687b5e5733c53a4') in 0.0408305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04_1.prefab
  artifactKey: Guid(abc00000000017747089960328179570) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_A_04_1.prefab using Guid(abc00000000017747089960328179570) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bcd0ec19c8000eab89323fad62f34c52') in 0.0684007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 41

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01_1.prefab
  artifactKey: Guid(abc00000000016763874823569565126) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SKM_MarketCloth_01_1.prefab using Guid(abc00000000016763874823569565126) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9c890096050e48c7c329d57eef946067') in 0.0507316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_1.prefab
  artifactKey: Guid(abc00000000008116155505184561409) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_1.prefab using Guid(abc00000000008116155505184561409) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dc147b89749d53e2e1508a9626f66968') in 0.0840432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_02.prefab
  artifactKey: Guid(abc00000000000170237559120467836) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_02.prefab using Guid(abc00000000000170237559120467836) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '96f45f992c7a3581d6f6b224b212f48c') in 0.0887624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_5.prefab
  artifactKey: Guid(abc00000000005689209917045709389) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_5.prefab using Guid(abc00000000005689209917045709389) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd2e222e4922120100c32bdf32f530f0d') in 0.0358272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000230 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_11.prefab
  artifactKey: Guid(abc00000000006801270852270562234) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_11.prefab using Guid(abc00000000006801270852270562234) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '61906d0e2d6b2917f874c1417d8afadc') in 0.0383025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4.mat
  artifactKey: Guid(27be55bd5ba1a1c4eaa2206e9c37a42e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_08_4x4.mat using Guid(27be55bd5ba1a1c4eaa2206e9c37a42e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5ea6e96f309af6bf5fda1bfe8e3b9db') in 0.0517433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3_Poison.mat
  artifactKey: Guid(9b924ea169c246948b3dfc5205202c2e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaterDroplet_04_4x3_Poison.mat using Guid(9b924ea169c246948b3dfc5205202c2e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23004422518b1ca19c1dfbddd8be87d9') in 0.2956199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Cap.prefab
  artifactKey: Guid(abc00000000004882846274196076131) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Cap.prefab using Guid(abc00000000004882846274196076131) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '64dc0b63119624b779572cd92e092991') in 0.0511611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_02_5x5.mat
  artifactKey: Guid(b8dae676d4c990947838bbfe4598a83e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_VerticalSplash_02_5x5.mat using Guid(b8dae676d4c990947838bbfe4598a83e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4656675a125dc1b936e603c56af6d5e') in 0.5936839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_Pillar.prefab
  artifactKey: Guid(abc00000000011696577099668539015) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Curtain_Wall_Pillar.prefab using Guid(abc00000000011696577099668539015) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7f23b286afe1a5956993e36f68bc9c1') in 0.0449925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_40.prefab
  artifactKey: Guid(abc00000000015688684306399976053) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_40.prefab using Guid(abc00000000015688684306399976053) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dbc1541800abd0c036f6d264b87b04c7') in 0.2204218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Waterfall_Main_01.mat
  artifactKey: Guid(38d8e39c64a371f4e98b97b8b1231e83) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Waterfall_Main_01.mat using Guid(38d8e39c64a371f4e98b97b8b1231e83) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc6eae5e77f65c23309e1b4c147b2710') in 0.5506125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlowerPot_01.prefab
  artifactKey: Guid(abc00000000001758106287957150513) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlowerPot_01.prefab using Guid(abc00000000001758106287957150513) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '73f907efef0911bb72b40d3b4ae27b42') in 0.0347895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlagPole.prefab
  artifactKey: Guid(abc00000000007993684537440899761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_FlagPole.prefab using Guid(abc00000000007993684537440899761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd8042d32013cf8f0b1c83ce51fc2719e') in 0.070267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab
  artifactKey: Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)RubbleGrid.prefab using Guid(8c1ffa3fac709614a82ea5ffa2a8ac44) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '08b6bc34f368b3741c4f66b2d976679d') in 0.0369049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_34.prefab
  artifactKey: Guid(abc00000000006079690300159667556) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ArchWall_01_Splines_34.prefab using Guid(abc00000000006079690300159667556) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1c03143ac4a572e0114289536a8528f7') in 0.0515808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_WaveFront_01.mat
  artifactKey: Guid(a32b65c9a843d8a4296d885aa3a4801e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_WaveFront_01.mat using Guid(a32b65c9a843d8a4296d885aa3a4801e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ed831db7be6e6d2fc672ad3787979de') in 0.5791731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug_1.prefab
  artifactKey: Guid(abc00000000007717172125062927024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Long_Rug_1.prefab using Guid(abc00000000007717172125062927024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '973d4ac0ab6c012670e7f3a57477a442') in 0.033637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/SrRubfish_VFX_02/Meshes/FX_MS_ExamplesMeshes_01.fbx
  artifactKey: Guid(d5ac0a326cca90140929d590dd20f191) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Meshes/FX_MS_ExamplesMeshes_01.fbx using Guid(d5ac0a326cca90140929d590dd20f191) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14d781542f04ffa76c00a78d95463d1a') in 0.1892116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 74

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_A.prefab
  artifactKey: Guid(abc00000000014770711582536660373) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_A.prefab using Guid(abc00000000014770711582536660373) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '93107ac3ecc345def5c47767a6b2b1d5') in 0.033357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01_1.prefab
  artifactKey: Guid(abc00000000016855284128515356830) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_MossClump_01_1.prefab using Guid(abc00000000016855284128515356830) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6d2785a56f140688271d58ddbc0bf690') in 0.0497669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_02.prefab
  artifactKey: Guid(abc00000000004411290568376933152) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Plate_02.prefab using Guid(abc00000000004411290568376933152) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ce890068a5ff2e4ac07e9a4a1aacef73') in 0.0337927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Podium.prefab
  artifactKey: Guid(abc00000000010819368574505770761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Podium.prefab using Guid(abc00000000010819368574505770761) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '56068641bb26e51f611d56b48e3e651e') in 0.0382409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis_Frame.prefab
  artifactKey: Guid(abc00000000012574944629173708194) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Portcullis_Frame.prefab using Guid(abc00000000012574944629173708194) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b2678ebb986fa050ccef9c4634e1e05f') in 0.0364217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_SimpleWaterImpact_01.prefab
  artifactKey: Guid(8cf651a7455605447a72526bde98a106) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_SimpleWaterImpact_01.prefab using Guid(8cf651a7455605447a72526bde98a106) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'edac705edca394d226bba7905c5385da') in 0.3989354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 47

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_05.png
  artifactKey: Guid(3654ac3b4253a794798e85a20d9abcab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/T_ROCA_TOP_BaseColor_05.png using Guid(3654ac3b4253a794798e85a20d9abcab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2cb04603695fab0d2df2ec77cb872f23') in 0.15877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Small.prefab
  artifactKey: Guid(abc00000000016608912069669358665) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Rope_Small.prefab using Guid(abc00000000016608912069669358665) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '03fe45df70206771455b843883cf7f1e') in 0.0305302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_04.prefab
  artifactKey: Guid(abc00000000000991804985135909205) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_04.prefab using Guid(abc00000000000991804985135909205) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '285933c26f27f99ebf887a80c5d1906e') in 0.055945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Cast.prefab
  artifactKey: Guid(5cb8c3b6b12f4fd41ba43c9206c62496) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_StrongProjectile_Cast.prefab using Guid(5cb8c3b6b12f4fd41ba43c9206c62496) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e17834ac246725df1b17d9080f4072a') in 0.1731727 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 61

========================================================================
Received Import Request.
  Time since last request: 0.000213 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_02.prefab
  artifactKey: Guid(abc00000000001978223226480770024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_02.prefab using Guid(abc00000000001978223226480770024) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '49960ee8294684fb0458361eed26978d') in 0.0321588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader
  artifactKey: Guid(1e3b057af24249748ff873be7fafee47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader using Guid(1e3b057af24249748ff873be7fafee47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bebcb78a2e767331a81f6abe7881eb8d') in 0.037746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Preexplosion.prefab
  artifactKey: Guid(3c04cc1754764144ab59fd862981bf9f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Prefabs/FX_PF_Water_Grenade_Preexplosion.prefab using Guid(3c04cc1754764144ab59fd862981bf9f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb57cda1ec6f236316f22ea89df82836') in 0.1765905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 48

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_01.prefab
  artifactKey: Guid(abc00000000010816312911577100658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_XL_01.prefab using Guid(abc00000000010816312911577100658) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd0cf230ee1616b570f99c825edf1a6c5') in 0.0674218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap.prefab
  artifactKey: Guid(abc00000000009141512139643502126) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap.prefab using Guid(abc00000000009141512139643502126) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '96a8a6dd2cb79374a9efe03eb150c345') in 0.0459967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_01.prefab
  artifactKey: Guid(abc00000000010148249731873018540) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Walkway_4x4_01.prefab using Guid(abc00000000010148249731873018540) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '91da337978967eef2328d9faa0fe697c') in 0.0381299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_01.prefab
  artifactKey: Guid(abc00000000004070176755419816583) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneWall_B_01.prefab using Guid(abc00000000004070176755419816583) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e02e32f131c63056ad18bbf7d2d9ead7') in 0.0362877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt
  artifactKey: Guid(fade42e8bc714b018fac513c043d323b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt using Guid(fade42e8bc714b018fac513c043d323b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7af8ed08a42344516184fec0023ec542') in 0.0292421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_B.prefab
  artifactKey: Guid(abc00000000016649879693021862794) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Tile_B.prefab using Guid(abc00000000016649879693021862794) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ead4995d635f64fdb344027ec93e666b') in 0.0378206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Edging.prefab
  artifactKey: Guid(abc00000000004095657826792136418) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Edging.prefab using Guid(abc00000000004095657826792136418) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1a936c1ba037e038b6a231d669c648ba') in 0.0314815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04_1.prefab
  artifactKey: Guid(abc00000000018350026996297542240) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_04_1.prefab using Guid(abc00000000018350026996297542240) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f5531fc8bfe8e32289ff9b62fdf35a00') in 0.0343754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sword_01.prefab
  artifactKey: Guid(abc00000000011290631944958838382) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sword_01.prefab using Guid(abc00000000011290631944958838382) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2351c69dcb5e7a0c12dc66841a94e175') in 0.0422657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_01.prefab
  artifactKey: Guid(abc00000000007737752430094090627) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_S_01.prefab using Guid(abc00000000007737752430094090627) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3ccd87f394fd71746f5300c60d56ffad') in 0.0395611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_02.prefab
  artifactKey: Guid(abc00000000016399473995144652937) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Window_02.prefab using Guid(abc00000000016399473995144652937) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '43e6f305a487d409ed8693e8db40c66d') in 0.0394821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/New Terrain 1.asset
  artifactKey: Guid(6a37b4c8016e49d48a34d0babade91e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/New Terrain 1.asset using Guid(6a37b4c8016e49d48a34d0babade91e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'de5c0a75930b7c453384ba8377fd562f') in 0.9369311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 350

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic I.png
  artifactKey: Guid(7b980be17f6a55346b05e87ecf32883f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Classic I.png using Guid(7b980be17f6a55346b05e87ecf32883f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '01217aa66bd5bb2f7ada34ff9badf5a4') in 0.070348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Empty/Settings/HDRPDefaultResources/FoliageDiffusionProfile.asset
  artifactKey: Guid(879ffae44eefa4412bb327928f1a96dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Empty/Settings/HDRPDefaultResources/FoliageDiffusionProfile.asset using Guid(879ffae44eefa4412bb327928f1a96dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '676106b90a839e46cb025c0639f754cf') in 0.0278081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Default.png
  artifactKey: Guid(57cdf080239052a4aa1af307b66afc1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Folder Icon Default.png using Guid(57cdf080239052a4aa1af307b66afc1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17b3a46eb51ee26a4a8faad3f87c0606') in 0.066914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF.shader
  artifactKey: Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF.shader using Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7c16b6d2397d6f631f7cdbb9596586a1') in 0.0334741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01.mat
  artifactKey: Guid(85d934873fcc2b34caace0131632eb71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Materials/FX_MT_Laser_AroundWater_01.mat using Guid(85d934873fcc2b34caace0131632eb71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9efe3e19d5c09061f749ab2cd11c415f') in 0.6301053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab
  artifactKey: Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)SpeedArea.prefab using Guid(9a2af18f241b4944b90098ffec5d3ac0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3d8344547bb48f608e0462e8f2903fc0') in 0.0395057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01.mat
  artifactKey: Guid(94d75aad618805044b3b9e5974266526) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/MAT_T_ROCA_TOP_01.mat using Guid(94d75aad618805044b3b9e5974266526) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13a865e3471c5dfb3bcc689a03441bac') in 0.6362643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt
  artifactKey: Guid(381dcb09d5029d14897e55f98031fca5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt using Guid(381dcb09d5029d14897e55f98031fca5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ab00afe02cd259c96a6cbbe8b3b8069') in 0.0477753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_6.prefab
  artifactKey: Guid(abc00000000004289521243151564262) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_6.prefab using Guid(abc00000000004289521243151564262) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ebb0044ed62eb52b97c07cf820659cd4') in 0.0445779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Separators.cs
  artifactKey: Guid(77eddb2b37f06ae419ba4e5a8d328615) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Settings_Separators.cs using Guid(77eddb2b37f06ae419ba4e5a8d328615) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f09676637eab46317921aad5176d234d') in 0.0274475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_04.mat
  artifactKey: Guid(3807d11b05d0a2642a61dc02d62ad026) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_04.mat using Guid(3807d11b05d0a2642a61dc02d62ad026) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5902ae005510438ee3e6e49c2a7f303') in 0.6617382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default T.png
  artifactKey: Guid(a948a3de04fd6164f8f9a5080faa5b0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Default T.png using Guid(a948a3de04fd6164f8f9a5080faa5b0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '526e24d5fd988703a06f1f12c909c7b2') in 0.0635529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented L.png
  artifactKey: Guid(7bdabba2f49e7e24f81ac5bb9a9ab606) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Segmented L.png using Guid(7bdabba2f49e7e24f81ac5bb9a9ab606) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '371a8152fedd78f89dda3a7b4bf2532d') in 0.0916107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_22.mat
  artifactKey: Guid(560fbe644bc1f2f49ae8da734484ea3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_22.mat using Guid(560fbe644bc1f2f49ae8da734484ea3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86e1ee77336d1239ed76484b97b2d679') in 0.120322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000223 seconds.
  path: Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved I.png
  artifactKey: Guid(888b29fd32ec8e94c8107e04fb01fb5c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Resources/Hierarchy Designer Tree Branch Icon Curved I.png using Guid(888b29fd32ec8e94c8107e04fb01fb5c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b4194523c0dced840d2f3bd3e0148c8') in 0.0669684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_18.mat
  artifactKey: Guid(448a192a2b6c47c428befdc4060273eb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_18.mat using Guid(448a192a2b6c47c428befdc4060273eb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81bec8abc85ef944d4350a80b9a224b3') in 0.1201314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_14.mat
  artifactKey: Guid(5900505635687684cb83e69b293e12a2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_14.mat using Guid(5900505635687684cb83e69b293e12a2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e1f087b29f7d49328e52e35c6e4a9da') in 0.1381253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_Editor.cs
  artifactKey: Guid(276ff96aaf7a87342942c021874e6ee0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hierarchy Designer/Editor/Scripts/HD_Manager_Editor.cs using Guid(276ff96aaf7a87342942c021874e6ee0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a95f5311115707a6f050b8ab32fd317') in 0.0301194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_03.mat
  artifactKey: Guid(c6399df592247214faf55b1fda4963d1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_03.mat using Guid(c6399df592247214faf55b1fda4963d1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e61b2d7cf88305f13d1fcdab0cf252d6') in 0.09322 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.343115 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_30.mat
  artifactKey: Guid(d95ada78756fcf64da7eee2476a8e333) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_30.mat using Guid(d95ada78756fcf64da7eee2476a8e333) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c1a74859630999c14465922d587afc55') in 0.1568153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_18.mat
  artifactKey: Guid(aed58466e1767be4986a6177659e77d5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_18.mat using Guid(aed58466e1767be4986a6177659e77d5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '061ec6b397773f58ce9e18f47e63bbfa') in 1.36444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_18.mat
  artifactKey: Guid(1bd250e368f25c34cb10c94ca1564b36) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_18.mat using Guid(1bd250e368f25c34cb10c94ca1564b36) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9a574df78cbc5ae0275454c0c9a6f01') in 0.9182585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_10.mat
  artifactKey: Guid(a0e6185f1a92ed74faa166bd1cf68a59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_10.mat using Guid(a0e6185f1a92ed74faa166bd1cf68a59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45fda939550240de1ebbd1d663038a9a') in 0.1495025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_02.mat
  artifactKey: Guid(b3781c962420bb14bbd962dbec8e10d3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_17_02.mat using Guid(b3781c962420bb14bbd962dbec8e10d3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0e3323434e9e8c5319203729f8a6235') in 0.3930775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/IdaFaber/Textures/Base/T_BODY_MaskMap.png
  artifactKey: Guid(0a6b9c5a543761245b04b2179e9c908c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_BODY_MaskMap.png using Guid(0a6b9c5a543761245b04b2179e9c908c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21a7fc220fdcb24497182421c2c82ed0') in 0.0913551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_24.png
  artifactKey: Guid(f5988f8ece52ff442b9688f5b1f0c8c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_24.png using Guid(f5988f8ece52ff442b9688f5b1f0c8c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '587b4c20a989bc6e72d3b9637601be13') in 0.0587002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR White Variant.prefab
  artifactKey: Guid(2bc95ebca5112594a9d105d31b24bcd8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR White Variant.prefab using Guid(2bc95ebca5112594a9d105d31b24bcd8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '181341228ed6dd22756839fb17861ad8') in 0.2558349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_20.mat
  artifactKey: Guid(d4c3728f487f97448850fd169544c3a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_20.mat using Guid(d4c3728f487f97448850fd169544c3a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1baf8f6773bd3b3b4660969c64a7a1c5') in 0.3123164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Meshes/Girl/SK_ROCA_FERAL_NO_FUR.fbx
  artifactKey: Guid(71898330672778c41b9ab00390293cfc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Meshes/Girl/SK_ROCA_FERAL_NO_FUR.fbx using Guid(71898330672778c41b9ab00390293cfc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eae4b926b89846eac7553dfe688e128b') in 0.280082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 400

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_14.png
  artifactKey: Guid(630c24b145d07694ca0d44d253b4e59d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_14.png using Guid(630c24b145d07694ca0d44d253b4e59d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8732686be11cde2aa710c96286cd234') in 0.0891886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Shaders/ShaderGraph/IDA_Clothes.shadergraph
  artifactKey: Guid(7a8adb964f5d0b845aa1262aff472189) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Shaders/ShaderGraph/IDA_Clothes.shadergraph using Guid(7a8adb964f5d0b845aa1262aff472189) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35f6af94f2082f6af916b4d0f3c34895') in 0.7193265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_03.png
  artifactKey: Guid(0e175155c682803438238c76dbf99adf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_03.png using Guid(0e175155c682803438238c76dbf99adf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e23fea4cb2a4d0c086e11f3c2f8b6d8') in 0.0624652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_17_02.png
  artifactKey: Guid(c148e3bf4b7fac64694e5bc5c795b21d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_F_BaseColor_17_02.png using Guid(c148e3bf4b7fac64694e5bc5c795b21d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a531a6e7ddcb40eaa0f73051864c86a3') in 0.0723094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Heatmap.png
  artifactKey: Guid(76c38942a76f600428f6a4ceb90c370a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Heatmap.png using Guid(76c38942a76f600428f6a4ceb90c370a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73571f7a38fb393ca836374945c258af') in 0.0757974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_34.png
  artifactKey: Guid(c7b8d53554bfb3d4292ee5e0f3ab228b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_34.png using Guid(c7b8d53554bfb3d4292ee5e0f3ab228b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6efe5c6e2a4c031d629087b352e192b6') in 0.0953801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_32.png
  artifactKey: Guid(db9f8ea2b51bf804db8de1f3d9456ba6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_32.png using Guid(db9f8ea2b51bf804db8de1f3d9456ba6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '728f35b71a076d9c39437898ef04fa92') in 0.0902749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_Emissive_07.png
  artifactKey: Guid(4116ef8ce4ae3db4d90299b0a7a9d940) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_Emissive_07.png using Guid(4116ef8ce4ae3db4d90299b0a7a9d940) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd50d39e250e3ff834c9421150ca93df5') in 0.0898437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_DepthMask.png
  artifactKey: Guid(8324bf7cabb9cb54691e98fd137711ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_DepthMask.png using Guid(8324bf7cabb9cb54691e98fd137711ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f4b47b2b22962344e0e7f1005128d01') in 0.0985326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_05.mat
  artifactKey: Guid(cb846047b20dd9b449bc6d15776ef05e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_05.mat using Guid(cb846047b20dd9b449bc6d15776ef05e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4773e6964994fb79827b9e54ddabe42d') in 0.231342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Green Variant.prefab
  artifactKey: Guid(a24c7503c7fa302409e94f731ff0c247) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Prefabs/Girl/SK_ROCA_FERAL_NO_FUR Green Variant.prefab using Guid(a24c7503c7fa302409e94f731ff0c247) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e80638a463319e2fe09291f98e955c8') in 0.1345335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 767

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_20.mat
  artifactKey: Guid(86cbb3088d5a53a45b660ac39b16ced1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_EYES_Mist_20.mat using Guid(86cbb3088d5a53a45b660ac39b16ced1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4afa654c8ea9f076d630bf2b9b95fb4a') in 0.1159186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_22.png
  artifactKey: Guid(23c09efaec4aabc428d84f4c8ac4f06e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_EYES_BaseColor_22.png using Guid(23c09efaec4aabc428d84f4c8ac4f06e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a851b86dc91887c9117e6b8d7e47233') in 0.0681311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_03.mat
  artifactKey: Guid(fb2756272cee8c348adbb2e687b8b98a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_09_03.mat using Guid(fb2756272cee8c348adbb2e687b8b98a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22cefaaf29a8e7f22b803bff71b761db') in 0.2912248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_02_Dirty.mat
  artifactKey: Guid(02dbfcf3c7270144cb457221f9d1678d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/MAT_HEAD_F_12_02_Dirty.mat using Guid(02dbfcf3c7270144cb457221f9d1678d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b91c0f32abdca0efa2f251cdd4bc5b5') in 0.8158592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab
  artifactKey: Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Ramp.prefab using Guid(193ad1fe34a53bb448f9e76740531510) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '789e55ede3ca79470b3622109866f8e1') in 0.0885834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Roughness.png
  artifactKey: Guid(332dbd5d9438a48458de8d159b9253c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_Roughness.png using Guid(332dbd5d9438a48458de8d159b9253c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46730dcdb338952e1a1940807c7cea44') in 0.1071172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_BaseColor.png
  artifactKey: Guid(b58576366f68d994da1e88c006aa87ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Weapons/T_ROCA_SWORD_BaseColor.png using Guid(b58576366f68d994da1e88c006aa87ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c120f3a2d6f3af08bde0d9cdfc3b6f5') in 0.0667218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/IdaFaber/Textures/Base/T_HEAD_Normal_01.png
  artifactKey: Guid(97a9d00a52e01c7449d6b43589b33ab4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_HEAD_Normal_01.png using Guid(97a9d00a52e01c7449d6b43589b33ab4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9344f01094c30c39d4d993a1b6777d9') in 0.0899547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LINGERIE_Metallic.png
  artifactKey: Guid(82023af210680f84cb9e766989235c38) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LINGERIE_Metallic.png using Guid(82023af210680f84cb9e766989235c38) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2edfb88ff56bb59666e37d51dfe1565') in 0.0922072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_06.png
  artifactKey: Guid(3a53a97d09642c24c9bc66ff6fcbd058) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_06.png using Guid(3a53a97d09642c24c9bc66ff6fcbd058) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f3b609d1830401e49cc1c43fb7e33e2') in 0.0572382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_01.png
  artifactKey: Guid(22a87410d17876c4ca203da0180106a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/T_LASHES_BaseColor_01.png using Guid(22a87410d17876c4ca203da0180106a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f61d0dbb4c857dd6d0f41d92032362a1') in 0.0678251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Floor.mat
  artifactKey: Guid(924b2eaec01bc40439c0b5338f555b8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Materials/(Mat)Floor.mat using Guid(924b2eaec01bc40439c0b5338f555b8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65f332c13bb1a05a21c25baacf798116') in 0.0799842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab
  artifactKey: Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Models/(Prb)Stairs.prefab using Guid(341e9b8977d6d734c8edeb00889eacab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5de2aa45f8a18eff54ae13c514ef81e') in 0.0599598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.134330 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab
  artifactKey: Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)MovingPlatform_Big.prefab using Guid(d00b6f15d6c6be04a849e6d8d906c243) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd58cd670f45b04677077f58d9ee6e03f') in 0.1281967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_ImpactSplash_4x3_02.psd
  artifactKey: Guid(44f9ebc047413904da60c22060aff7ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_ImpactSplash_4x3_02.psd using Guid(44f9ebc047413904da60c22060aff7ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c0465b4404714eee9ba708af9acbd8b') in 0.0741041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000358 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_02.psd
  artifactKey: Guid(b9d02287943be084abf66e6299d0985e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_GradientRamp_02.psd using Guid(b9d02287943be084abf66e6299d0985e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe3983a629105e558fc0df0ed24b179a') in 0.0488582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/CharacterManager.cs
  artifactKey: Guid(d653000bcba62c14ba9b6c5c324a4197) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/CharacterManager.cs using Guid(d653000bcba62c14ba9b6c5c324a4197) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9711f0b4cdbe0ec18c6ce697c725a55b') in 0.0583145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab
  artifactKey: Guid(9e4757aed32f476419bb6acd9168541a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/(Prb)Shooter.prefab using Guid(9e4757aed32f476419bb6acd9168541a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86ec0056c7ddcd960602eb255859eb26') in 0.0742688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralImpactComposed_4x6_01.psd
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_LateralImpactComposed_4x6_01.psd using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12cfb15e57aa140edfa9b55c941efca7') in 0.0679115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterRadial_4x4_01.psd
  artifactKey: Guid(5270f32b96ef1154d957e08144454028) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterRadial_4x4_01.psd using Guid(5270f32b96ef1154d957e08144454028) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae3f65b756ab9c5fbd50c3ada5e07a3b') in 0.0908003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamIndividual_4x4_01.psd
  artifactKey: Guid(f53a655128a87a148951e288d0740236) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_FoamIndividual_4x4_01.psd using Guid(f53a655128a87a148951e288d0740236) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b5f87395187e5961a49a617198d4561') in 0.093183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Prefabs/[PlayerAnimated].prefab
  artifactKey: Guid(cf4b3ff444aac3d479825f1fa43b6237) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Prefabs/[PlayerAnimated].prefab using Guid(cf4b3ff444aac3d479825f1fa43b6237) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af24f119653af1774a00c8d487eacef4') in 0.1738186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 153

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DirectionalImpact_01.png
  artifactKey: Guid(e2e22a84f75382c4c9f9f2187900f447) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_DirectionalImpact_01.png using Guid(e2e22a84f75382c4c9f9f2187900f447) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03212c6295ac070f12bd931860103ae2') in 0.0438674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundWaterSplash_4x4_01.psd
  artifactKey: Guid(217ce12e10831744d9021202a7a7a214) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_GroundWaterSplash_4x4_01.psd using Guid(217ce12e10831744d9021202a7a7a214) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc31389caa2ff2c59d9768f328f892c6') in 0.0753939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterMask_4x6_01_SoftRender.psd
  artifactKey: Guid(95ae7447df16b6e42b5cc58ce8d5b02b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/FlipbooksPSD/FX_TX_WaterMask_4x6_01_SoftRender.psd using Guid(95ae7447df16b6e42b5cc58ce8d5b02b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c0092260209a4fef4b5ee1f93b5d7eb') in 0.0825318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_WaterBuff.playable
  artifactKey: Guid(cd6c74f90f2f92f4b98ee15109a78c06) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/DemoScene/Timelines/FX_TL_WaterBuff.playable using Guid(cd6c74f90f2f92f4b98ee15109a78c06) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5c2a85de490b950ce52f3531cafd216') in 0.089234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.431531 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterTrail_01_Dissolve.png
  artifactKey: Guid(ef353ad5227784f4dbd32b74a571cf10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Shared/FX_TX_WaterTrail_01_Dissolve.png using Guid(ef353ad5227784f4dbd32b74a571cf10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8d9e98ca59a72479096fc0f79d35490') in 0.0836269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_03.mat
  artifactKey: Guid(87d669be8c6739147a90c1207c851ddf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_03.mat using Guid(87d669be8c6739147a90c1207c851ddf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d07c003f999a2fb380a22aa23184894') in 1.2932857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_03.png
  artifactKey: Guid(cf8ad3aa8b96e1e48b799b7df95caa43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_03.png using Guid(cf8ad3aa8b96e1e48b799b7df95caa43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87025477d90107949b864ccac46a74a8') in 0.0663644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_1.FBX
  artifactKey: Guid(009df2720acf8cf4dbd44d2c8eee23ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_G_1.FBX using Guid(009df2720acf8cf4dbd44d2c8eee23ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57b19b2ee84ae2ae0b7c445f396b222f') in 0.0826369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FallingWater_4x3_01.png
  artifactKey: Guid(93bfaddab9cc2b442b74b0b9b3a0d62b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_FallingWater_4x3_01.png using Guid(93bfaddab9cc2b442b74b0b9b3a0d62b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7630e5767e3cd3778ecd59e905ec934') in 0.0569093 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_F.FBX
  artifactKey: Guid(33c684e8316cee640a4e43e2bbccf193) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/3_Skills/M_katana_Blade@Skill_F.FBX using Guid(33c684e8316cee640a4e43e2bbccf193) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd19c6a659c5018c52ab64ed4c09971c6') in 0.0784826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000333 seconds.
  path: Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_10.mat
  artifactKey: Guid(5e65d961497050c429ce4cbe1f866b34) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Wavy/MAT_HAIR_Wavy_10.mat using Guid(5e65d961497050c429ce4cbe1f866b34) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b32d63960f0bf7f89c1677ae29e127e4') in 0.2280539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_Meditative_1.wav
  artifactKey: Guid(945908a543147ce44900af3c614df5d5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_Meditative_1.wav using Guid(945908a543147ce44900af3c614df5d5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df9315ca11769cc9e658a9fe164bc06b') in 0.2525629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_2.wav
  artifactKey: Guid(c032f3c332931894ead4e65af880184b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Appear/SFX_Appear_Chimes_2.wav using Guid(c032f3c332931894ead4e65af880184b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c49b8f2dca6a258caae2be4a63b6bf3') in 0.1642252 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_2.wav
  artifactKey: Guid(33ac96536f4e26b499448d4d4dbcbe45) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_2.wav using Guid(33ac96536f4e26b499448d4d4dbcbe45) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcbc06f572ba4e6b5d191aaf8455b559') in 0.1515598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL45_Root.FBX
  artifactKey: Guid(2d14bc95bbe14644aa72ce0a1c67cda3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_BL45_Root.FBX using Guid(2d14bc95bbe14644aa72ce0a1c67cda3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40e16da389deb14f6a1c2b0a27257ea6') in 0.0758395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterRipples_4x3_01.png
  artifactKey: Guid(0f792bfb5c8077c4ab5114f0ce52565c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_WaterRipples_4x3_01.png using Guid(0f792bfb5c8077c4ab5114f0ce52565c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b2cb1eb317cabe69ae51bc735f87e29a') in 0.0680544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_03.png
  artifactKey: Guid(18fe7598cb5f0e24889434c72d36ea3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_RainSplash_03.png using Guid(18fe7598cb5f0e24889434c72d36ea3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b2c9446134591595d2f5e284d97fa9ff') in 0.048285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_3.wav
  artifactKey: Guid(b5ae9d069a80c4f41a5b97c75d4a31b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_3.wav using Guid(b5ae9d069a80c4f41a5b97c75d4a31b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '436f25b3618bf7cc74ce2162a2581b21') in 0.1488107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_L.FBX
  artifactKey: Guid(87196e3069e553143996522f76e3bf6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/3_Skills/M_Big_Sword@Skill_L.FBX using Guid(87196e3069e553143996522f76e3bf6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14a42407766d7a098c781fe97e51f3d4') in 0.081514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Rattle_1.wav
  artifactKey: Guid(b64ba1649d4813046bdd9891a2bb0f56) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Rattle_1.wav using Guid(b64ba1649d4813046bdd9891a2bb0f56) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f08389a438fec2753afae747520af765') in 0.1352308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_17.png
  artifactKey: Guid(2cfbd21f17a576142893bb4467fb5d9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_17.png using Guid(2cfbd21f17a576142893bb4467fb5d9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06df59db246d950cc52c6ca98d83bc8c') in 0.0606275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_3.wav
  artifactKey: Guid(722a90127ee2dd34abc9e7ca12532628) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Drop_3.wav using Guid(722a90127ee2dd34abc9e7ca12532628) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4bec85121e145b44bc4d4eb4cd3a08d0') in 0.1015911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_1.wav
  artifactKey: Guid(6f49e54e36cb80b4bbbee3c8af0274a2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Big_1.wav using Guid(6f49e54e36cb80b4bbbee3c8af0274a2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81f48212bc9f1fb4266948a686e81b08') in 0.1490084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_3.wav
  artifactKey: Guid(7d0787d48dc967546b60073802b82061) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_3.wav using Guid(7d0787d48dc967546b60073802b82061) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03d96c8965ffceba056a6252f8b5528a') in 0.1387883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Cute_1.wav
  artifactKey: Guid(75085481b51abcf40beaf15083b5d18d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Cute_1.wav using Guid(75085481b51abcf40beaf15083b5d18d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af0ef39459e783fe5f11f9b3a496f7fa') in 0.1610982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_11.mat
  artifactKey: Guid(5ebec2599f80bb245af48e7d636618a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_11.mat using Guid(5ebec2599f80bb245af48e7d636618a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0eb98f77f055811d30ad166e9cb3484') in 0.1829835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000110 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Loop_ZeroHeight.FBX
  artifactKey: Guid(4d88f02647c0af344a4cfe2e69257bca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/6__Jump/M_Big_Sword@Jump_Loop_ZeroHeight.FBX using Guid(4d88f02647c0af344a4cfe2e69257bca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06ac2adab2e61d2d295820368fabc5c6') in 0.0634107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_A.FBX
  artifactKey: Guid(22b3694b2773bd6448ca54917f26ac66) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Walk_ver_A.FBX using Guid(22b3694b2773bd6448ca54917f26ac66) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8afa6b22e7282e2a86054b3884c68cb2') in 0.0739275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_B.FBX
  artifactKey: Guid(c7454e7572c353e4db8685e07bb330e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Run_ver_B.FBX using Guid(c7454e7572c353e4db8685e07bb330e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8967eae9e0f19926193bdef1376cf940') in 0.0617841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralWater_01_3x4.png
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/SrRubfish_VFX_02/Textures/Flipbooks/Water/FX_TX_LateralWater_01_3x4.png using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f857ac4614833f4e12ea34d72cb24ecb') in 0.0630516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_14.png
  artifactKey: Guid(b41284f0c44667d429554aedcd6d3575) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_14.png using Guid(b41284f0c44667d429554aedcd6d3575) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8fa58f9e4cc83369ef54bc918c98db2') in 0.069157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs
  artifactKey: Guid(476cc7d7cd9874016adc216baab94a0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs using Guid(476cc7d7cd9874016adc216baab94a0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d471cf9729761461af508b431fca204') in 0.0363417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_1.wav
  artifactKey: Guid(4b28b6c4764091c48bdcb26daf13a1ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Swoosh/SFX_Swoosh_1.wav using Guid(4b28b6c4764091c48bdcb26daf13a1ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd90a8bf58df077ce0fb74cfff05cca25') in 0.1087388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_ver_A.FBX
  artifactKey: Guid(71e29fef64e545e4e99797d012a3abb1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/4__Run/M_Big_Sword@Run_ver_A.FBX using Guid(71e29fef64e545e4e99797d012a3abb1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f3cc445e6f0a3879ba8ec50d5addd90') in 0.078535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_PianoAiry_1.wav
  artifactKey: Guid(b64c315ffaba2364ca8d4824c675368b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/STINGER/STGR_Win_PianoAiry_1.wav using Guid(b64c315ffaba2364ca8d4824c675368b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12aa00c6323ebfb245c8bda005aa79f8') in 0.2380688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Left.FBX
  artifactKey: Guid(25fc5df8807c1bc408515a6ff849366a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Left.FBX using Guid(25fc5df8807c1bc408515a6ff849366a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '728bf85f3ad50282ac2184ec7cfe29f8') in 0.0907647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Shiny_3.wav
  artifactKey: Guid(8ef60094701d1734d96e761f8a337448) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Place_Shiny_3.wav using Guid(8ef60094701d1734d96e761f8a337448) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd11da235975bd1d1a77772b6262ab9b6') in 0.1578495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_A_Root.FBX
  artifactKey: Guid(d1569c16e2b0a844599547b32d5fdcda) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/9__Idle_To_Move/M_Big_Sword@Idle_To_Jogging_ver_A_Root.FBX using Guid(d1569c16e2b0a844599547b32d5fdcda) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bfc90d32d78bd5ed8e399447c2fc6813') in 0.0740648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Loop.FBX
  artifactKey: Guid(7f4c7f8c864882146815e16ade325d3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/7__Double_Jump/M_Big_Sword@Double_Jump_Loop.FBX using Guid(7f4c7f8c864882146815e16ade325d3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb1fa9b524605c7c57ca72aa98689a28') in 0.1442747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_2_Inplace.FBX
  artifactKey: Guid(8be3e9637cdec3a409bdfe9a18dc78a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_2_Inplace.FBX using Guid(8be3e9637cdec3a409bdfe9a18dc78a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74caefe22c352a0650bb74977447fdb4') in 0.0607241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Down_StandUp.FBX
  artifactKey: Guid(3613f44b4c08cc744bca090c3ab628d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/1__Front/M_Big_Sword@Damage_Front_Down_StandUp.FBX using Guid(3613f44b4c08cc744bca090c3ab628d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2b5500a1906fddace992d546d480b68') in 0.0560849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_A_Idle.FBX
  artifactKey: Guid(a5617ef9abf458c41b357010d3971edd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/14__Idle_To_Crouch/M_katana_Blade@Idle_ver_A_To_Crouch_ver_A_Idle.FBX using Guid(a5617ef9abf458c41b357010d3971edd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd92395fa2086af99e252a9ee01c71fd9') in 0.0656497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_6.FBX
  artifactKey: Guid(8f933a988ef5cb845b864a1eb461f06e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_6.FBX using Guid(8f933a988ef5cb845b864a1eb461f06e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '598772942ac75e8b663e72a5f0493a89') in 0.0747449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_1.FBX
  artifactKey: Guid(c21f24b5df756a44f90ddeb33933b1c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_1.FBX using Guid(c21f24b5df756a44f90ddeb33933b1c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '01642de1f8b671d59dc2bcab72a9a46c') in 0.0801523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_FW_Rolling_StandUp.FBX
  artifactKey: Guid(fbb25826faf6ac04cba385f36966f8a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/5__KnockDown/M_Big_Sword@KnockDown_Front_FW_Rolling_StandUp.FBX using Guid(fbb25826faf6ac04cba385f36966f8a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '20e6a59802a024b08219aba0e11931ef') in 0.0842927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured.FBX
  artifactKey: Guid(d3762b16d75e4a5468e22a58002973aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/5__Run_Injured/M_katana_Blade@Run_Injured.FBX using Guid(d3762b16d75e4a5468e22a58002973aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b9bba934e18a6a8c7e06ec3c7da4218') in 0.061563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_Root_ver_A.FBX
  artifactKey: Guid(8be66475dbb04264c9494a8be0587c7e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/11__Run_To_Fast_Run/M_katana_Blade@Run_To-Fast_Root_ver_A.FBX using Guid(8be66475dbb04264c9494a8be0587c7e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7548042cda3e397e577c392afe6c4959') in 0.0627237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_1_ZeroHeight.FBX
  artifactKey: Guid(846826d937cf6984aa115d80d829962c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/4__Jump_Attack/M_Big_Sword@Jump_Attack_Combo_1_ZeroHeight.FBX using Guid(846826d937cf6984aa115d80d829962c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b86c2fea48fb5e0019c04db7070567d5') in 0.0529242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/3__Left/M_Big_Sword@Damage_Left_Small_ver_B.FBX
  artifactKey: Guid(53de051fe2ca1414fb699ae82a17903e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/4_Damages/3__Left/M_Big_Sword@Damage_Left_Small_ver_B.FBX using Guid(53de051fe2ca1414fb699ae82a17903e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19fad5a8fd6fe886d053829a5a7b0b30') in 0.0684865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_3_Inplace.FBX
  artifactKey: Guid(08d72b7f9d2ed9340bde002e411cf806) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/2__7Combos/M_Big_Sword@Attack_7Combo_3_Inplace.FBX using Guid(08d72b7f9d2ed9340bde002e411cf806) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ecf6cc8c89ed913cccf6ae73c41b2635') in 0.0597828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_1.wav
  artifactKey: Guid(4a5f5e03c6206504aa91e8f73f232142) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Small_1.wav using Guid(4a5f5e03c6206504aa91e8f73f232142) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79adf2c45813518c67cae4f5357ec410') in 0.1722473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_12.mat
  artifactKey: Guid(ba48885a456df3f48b97e019d94e02f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Materials/Base/Straight/MAT_HAIR_STRAIGHT_12.mat using Guid(ba48885a456df3f48b97e019d94e02f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a555415e94583c7e3e3e047e72bd8f9') in 0.1874186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000438 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_2.wav
  artifactKey: Guid(c848fb1910bfce74c8407022750591aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/Collect_Drop_Place/SFX_Object_Collect_Shiny_2.wav using Guid(c848fb1910bfce74c8407022750591aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53de92faf1b9a7f030055c644d96e198') in 0.1668149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Big.FBX
  artifactKey: Guid(048cc648efc49f04696d64e62287e4cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Big.FBX using Guid(048cc648efc49f04696d64e62287e4cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd25a28829c55f87913d68fb6cc0ed939') in 0.0749644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_ALL_Inplace.FBX
  artifactKey: Guid(eb6471cf3d130424da6ad1f474b6ff3e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_ALL_Inplace.FBX using Guid(eb6471cf3d130424da6ad1f474b6ff3e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e951c427e44e7c68522887cf9caa3c9') in 0.071927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End.FBX
  artifactKey: Guid(542a9ff56ac19674ea4275b46dc3d8d3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_End.FBX using Guid(542a9ff56ac19674ea4275b46dc3d8d3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae19ef83f9d8867fab4894a48b00fb15') in 0.0645506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_Smash.FBX
  artifactKey: Guid(8bb182ad4f73fee40aedabed124ad70c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Down_Smash.FBX using Guid(8bb182ad4f73fee40aedabed124ad70c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4c59bb9d87a45ae4f7ac2ef97142eba') in 0.0893759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_Attach.FBX
  artifactKey: Guid(40c85930c3fdf244e8b4ff5d3777808e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_1_Attach.FBX using Guid(40c85930c3fdf244e8b4ff5d3777808e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cfa1af26dea79a97e6d86d92e7ab875') in 0.0610347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_A.FBX
  artifactKey: Guid(2e0b3b17fbd4cd748b0d4b704867d5b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/1__Front/M_katana_Blade@Damage_Front_Flying_ver_A.FBX using Guid(2e0b3b17fbd4cd748b0d4b704867d5b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '966d3f2cd28a808d6e00631db9d6b0ac') in 0.0668719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Small_ver_A.FBX
  artifactKey: Guid(2a66963e5e0e0954ca474772b056c066) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/2__Back/M_katana_Blade@Damage_Back_Small_ver_A.FBX using Guid(2a66963e5e0e0954ca474772b056c066) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80fbdcb5e8ad9f34e190b433e268e031') in 0.0782465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL_ZeroHeight.FBX
  artifactKey: Guid(da3fc035328154a42a22b91f01847b4f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_ALL_ZeroHeight.FBX using Guid(da3fc035328154a42a22b91f01847b4f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a6f0223b467c0a8891ff3b27e44917f') in 0.0559778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90_Root.FBX
  artifactKey: Guid(597a272d1a16e2d44ac274e4756dbfe0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_B_To_Jogging_B_Turn_R90_Root.FBX using Guid(597a272d1a16e2d44ac274e4756dbfe0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b6a0e14cad67185cdbefab0cf04efb6') in 0.0943777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_short distance_Return.FBX
  artifactKey: Guid(7202b84e4e3611749a828e09db09346c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_short distance_Return.FBX using Guid(7202b84e4e3611749a828e09db09346c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba894c038abfefa16f10bf53de4c6094') in 0.0780204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1_Move.FBX
  artifactKey: Guid(ba791b5d7edabdc4cae9f90087485520) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1_Move.FBX using Guid(ba791b5d7edabdc4cae9f90087485520) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f4b36183473baaa20d6134980fc1f39') in 0.2482198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 139

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start_ZeroHeight_Z0.FBX
  artifactKey: Guid(5f41a7de213ec604f94de8afd3876a32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/7__Double_Jump/M_katana_Blade@Double_Jump_Start_ZeroHeight_Z0.FBX using Guid(5f41a7de213ec604f94de8afd3876a32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ec8896a5ffb18c7f5fa4cd15211b7eb') in 0.0995569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1.FBX
  artifactKey: Guid(00a28cb25fc8dad40bf3248b50123b32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/2__5Combos/M_katana_Blade@Attack_5Combo_1.FBX using Guid(00a28cb25fc8dad40bf3248b50123b32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ac8a21918411c879ab6db59f8fcbd3e') in 0.065168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_Inplace.FBX
  artifactKey: Guid(f1449fb106a8289428aa593db4bedb1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/0__3Combos/M_katana_Blade@Attack_3Combo_3_Inplace.FBX using Guid(f1449fb106a8289428aa593db4bedb1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df2e4def201b56c0bc223b189c3892d8') in 0.0656597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.FBX
  artifactKey: Guid(8862321b816049940b108a237e96fa9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.FBX using Guid(8862321b816049940b108a237e96fa9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a335bd15bc706c40e220a6a003a1282b') in 0.7063125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000107 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.FBX
  artifactKey: Guid(da3aa09e0da2b0840810a49e9a2f397e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.FBX using Guid(da3aa09e0da2b0840810a49e9a2f397e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ad573a591e47ffbcbb01dec88ef3bb9') in 0.1074078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.prefab
  artifactKey: Guid(370239a565943e94dbcf4d455779196d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.prefab using Guid(370239a565943e94dbcf4d455779196d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd83e164328c08c174d246bb8f5c3b3ba') in 0.2100354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.prefab
  artifactKey: Guid(6e1b469716b53eb4ba251f35819666db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_Merged_Wall_02.prefab using Guid(6e1b469716b53eb4ba251f35819666db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8540e7ed727bd8d953eed669ce8e4fed') in 0.9501595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Damage.FBX
  artifactKey: Guid(425316aa38ad99744b850780e1fcbfd4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/4_Damages/5__KnockDown/M_katana_Blade@KnockDown_Front_Damage.FBX using Guid(425316aa38ad99744b850780e1fcbfd4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '759e66082190fda991d10cba111084f2') in 0.1100479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Walkway_OuterCorner_01_Splines_31.fbx
  artifactKey: Guid(abc00000000006962563369851379926) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_Walkway_OuterCorner_01_Splines_31.fbx using Guid(abc00000000006962563369851379926) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43aa4ec75eaa4a1f66acab1b4df12913') in 0.2913016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Anvil.prefab
  artifactKey: Guid(abc00000000000663048425925687157) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Anvil.prefab using Guid(abc00000000000663048425925687157) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '529ec909f89fae031d6d40736c4311bc') in 0.1075111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000148 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_02.prefab
  artifactKey: Guid(abc00000000012482503717944588971) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_02.prefab using Guid(abc00000000012482503717944588971) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77566f209e0165f17edc881fdfc1e389') in 0.0824988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_05.mat
  artifactKey: Guid(abc00000000004456545258708002696) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_StoneFence_05.mat using Guid(abc00000000004456545258708002696) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f7f3f87d76b8eabe5adda6a7898985b') in 0.0705562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_01.prefab
  artifactKey: Guid(abc00000000006851179421744888992) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bookshelf_A_01.prefab using Guid(abc00000000006851179421744888992) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a2fd7c657490988b56c8802dc1d05df') in 0.0746672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.FBX
  artifactKey: Guid(56bd408f18f8b4d4b8fb45a99a52b98f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_15.FBX using Guid(56bd408f18f8b4d4b8fb45a99a52b98f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5dba65bf3fca2b4cd2723daaa27c6f18') in 0.1033687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_04.prefab
  artifactKey: Guid(abc00000000009837665421401278803) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Beam_A_04.prefab using Guid(abc00000000009837665421401278803) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4da0160aac9a87c26cc35d35cc71f8b6') in 0.082606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_03.prefab
  artifactKey: Guid(abc00000000002718345017765155898) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Wall_Door_A_03.prefab using Guid(abc00000000002718345017765155898) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a633ea96bfd32d6fcefaf86c28a7040e') in 0.0798229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_01.prefab
  artifactKey: Guid(abc00000000010984909267591331595) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CandleFat_01.prefab using Guid(abc00000000010984909267591331595) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcc6f156e238cd951bd559d247d37fbd') in 0.113396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000238 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_01.prefab
  artifactKey: Guid(abc00000000012833982667677876559) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Floor_Seam_B_01.prefab using Guid(abc00000000012833982667677876559) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aecb6a5ac5c96a2389d6b18ddb5e93d7') in 0.1140735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_08.prefab
  artifactKey: Guid(abc00000000000978784064813139366) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_08.prefab using Guid(abc00000000000978784064813139366) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ecf671cf5c5169607466a99325174528') in 0.1106496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Middle_01.prefab
  artifactKey: Guid(abc00000000009930303995638165164) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Bridge_Middle_01.prefab using Guid(abc00000000009930303995638165164) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c120ca02684711735ec930e80e8ae81b') in 0.0822603 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 40

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_03.prefab
  artifactKey: Guid(abc00000000008070227094333872325) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_CastleTower_Window_A_03.prefab using Guid(abc00000000008070227094333872325) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce1e847d231e61ec7e12bb9fdbb07797') in 0.1226997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.prefab
  artifactKey: Guid(cf9b0789e85e919479c197694c3020e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_17.prefab using Guid(cf9b0789e85e919479c197694c3020e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41b29dbdcb1c792be12d80d3a14f3a8a') in 0.0879501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000240 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_23.fbx
  artifactKey: Guid(abc00000000011179527099241745662) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/SM_DockWall_4x8_01_Splines_23.fbx using Guid(abc00000000011179527099241745662) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '20148558bd5a726c0a234251c93041fb') in 0.1379048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Individual_Bricks.mat
  artifactKey: Guid(abc00000000017760730636051215630) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/M_Individual_Bricks.mat using Guid(abc00000000017760730636051215630) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd040a63b44c9f1e48855f2e0827e23ea') in 0.0483583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.prefab
  artifactKey: Guid(5248f34b570b29c4d9cd428d786579f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_CurtainWall_35.prefab using Guid(5248f34b570b29c4d9cd428d786579f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97858856e684d09e4849a0fd88116981') in 0.2163393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong.prefab
  artifactKey: Guid(abc00000000017602426385492134554) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chain_XtraLong.prefab using Guid(abc00000000017602426385492134554) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '340268a8139ae22b37fb240e4670c0ad') in 0.0815561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_B.prefab
  artifactKey: Guid(abc00000000004350169994041876357) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobble_B.prefab using Guid(abc00000000004350169994041876357) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e72a61888b0f8f1badc41daa01b2c964') in 0.0758698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_01.prefab
  artifactKey: Guid(abc00000000015866598685110398442) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Castle_SeamHider_01.prefab using Guid(abc00000000015866598685110398442) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee6bd94c845f555e5a80e6b4d0985115') in 0.0697012 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_18.prefab
  artifactKey: Guid(abc00000000012566041333643095367) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Fence_02a_Splines_18.prefab using Guid(abc00000000012566041333643095367) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4faf406c8d912c0eb3d1345bd596b637') in 0.0633786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment_1.prefab
  artifactKey: Guid(abc00000000002353935698868763680) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chimney_Segment_1.prefab using Guid(abc00000000002353935698868763680) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0722042712d77bc2eccdafa1ed5ac09') in 0.0682853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair.prefab
  artifactKey: Guid(abc00000000013297240178136726284) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Chair.prefab using Guid(abc00000000013297240178136726284) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c65723ae9390663b27f5440dba7a5307') in 0.0591848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_12.prefab
  artifactKey: Guid(abc00000000011280800201814850625) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_ChainLink_01_Splines_12.prefab using Guid(abc00000000011280800201814850625) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd08b61c571d52af51ddc8b32ef757481') in 0.0598523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_8M.prefab
  artifactKey: Guid(abc00000000014075729282340197617) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Cobbles_8M.prefab using Guid(abc00000000014075729282340197617) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac6e08f1e2f7dd2cbfb92eb8bc0f63f7') in 0.1089086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_29.prefab
  artifactKey: Guid(abc00000000018100680694111923930) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01_Splines_29.prefab using Guid(abc00000000018100680694111923930) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '334f11d3198cc6ed84e2c341c5d11782') in 0.0897695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01.prefab
  artifactKey: Guid(abc00000000004535829560276980957) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_DockWall_4x8_01.prefab using Guid(abc00000000004535829560276980957) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57bc5328338b315d47ed85aae8079e38') in 0.0784399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_7.prefab
  artifactKey: Guid(2fea2eba55fb1dd4ebe7d12630fb7d68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_7.prefab using Guid(2fea2eba55fb1dd4ebe7d12630fb7d68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '564efc7a10040215a120de0f30a9b6cd') in 0.0604625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Front.FBX
  artifactKey: Guid(347fbfac4e6fefb42ab52b2bd96854e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/8__Dodge/M_Big_Sword@Dodge_Front.FBX using Guid(347fbfac4e6fefb42ab52b2bd96854e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3bc69a2e9a046a3e2c4d26e647c630b') in 0.0700582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_alpha.png
  artifactKey: Guid(d7efd1f1506267e49b72f71ee70d001d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Wavy/T_HAIR_WAVY_alpha.png using Guid(d7efd1f1506267e49b72f71ee70d001d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3cbebfd137864907373a25d7b7a4954e') in 0.1104143 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/14__Idle_To_Crouch/M_Big_Sword@Idle_To_Crouch_Root.FBX
  artifactKey: Guid(3c05cd84d72ed0f4bb70a9524fccf40c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/14__Idle_To_Crouch/M_Big_Sword@Idle_To_Crouch_Root.FBX using Guid(3c05cd84d72ed0f4bb70a9524fccf40c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '949cf63f26f6b2bf3f0a018b24f1ea53') in 0.0773396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.prefab
  artifactKey: Guid(df30ede524bb3004ca77db09fb3d842f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_StaticMeshActor_26.prefab using Guid(df30ede524bb3004ca77db09fb3d842f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7602f7f311b8138ccc23e5f228210283') in 0.1798835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_01.prefab
  artifactKey: Guid(abc00000000007357929331176521613) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_S_01.prefab using Guid(abc00000000007357929331176521613) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4a575af0dea019651221e2b833265ae9') in 0.0701313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch_1.prefab
  artifactKey: Guid(abc00000000004724551570809325481) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch_1.prefab using Guid(abc00000000004724551570809325481) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '852b3d6eea1ffa59887f518d6d023ba6') in 0.070223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_D.prefab
  artifactKey: Guid(abc00000000004027028008972081728) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Log_D.prefab using Guid(abc00000000004027028008972081728) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80c14921d476c951855b11a7e4feb020') in 0.0615797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_04.prefab
  artifactKey: Guid(abc00000000000991804985135909205) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_Cap_04.prefab using Guid(abc00000000000991804985135909205) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ad5978dc02900de48772fd429c8e774') in 0.0744142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_13_1.prefab
  artifactKey: Guid(abc00000000002187765442931123598) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Roof_A_13_1.prefab using Guid(abc00000000002187765442931123598) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f73cdda3108a9cefbb506bc56fd11081') in 0.0624153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000132 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_C.prefab
  artifactKey: Guid(abc00000000001333846768155401957) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Path_Slab_C.prefab using Guid(abc00000000001333846768155401957) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2b26d3906c2b49e5c2c6fd236b099ce') in 0.0530064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_02.prefab
  artifactKey: Guid(abc00000000016960943808521108304) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_LeaveDerbis_M_02.prefab using Guid(abc00000000016960943808521108304) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a039abcf9f77c3781042da6ea071400d') in 0.0590641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pumpkin.prefab
  artifactKey: Guid(abc00000000015836928811472177443) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Pumpkin.prefab using Guid(abc00000000015836928811472177443) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '546652d343f45a365cea9fd56217273b') in 0.0607679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_04.prefab
  artifactKey: Guid(abc00000000018436674492772112239) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Mountain_04.prefab using Guid(abc00000000018436674492772112239) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86b64cc9098d22126156616243f31d73') in 0.0642619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TableAndChairs.prefab
  artifactKey: Guid(abc00000000011690563593163698220) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_TableAndChairs.prefab using Guid(abc00000000011690563593163698220) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6331c017899995531d5d4570ea634831') in 0.0722163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x4.prefab
  artifactKey: Guid(abc00000000007686417641232609422) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Wall_Slope_2x4.prefab using Guid(abc00000000007686417641232609422) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3640447739c2afc40ec4f1ea800ff882') in 0.0560401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000138 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap.prefab
  artifactKey: Guid(abc00000000009141512139643502126) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Spiral_Stairs_Cap.prefab using Guid(abc00000000009141512139643502126) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '463f5840e6a2878c7e80d2c37c1ca0ec') in 0.0677148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_02.prefab
  artifactKey: Guid(abc00000000017957118347635269566) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_WildGrass_M_02.prefab using Guid(abc00000000017957118347635269566) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed557ddfd64a0f54c4c61823602463e8') in 0.1029238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/SubG_HistogramScan.shadersubgraph
  artifactKey: Guid(3d8bae22d2837a44ebf60417765fa963) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Shaders/SubG_HistogramScan.shadersubgraph using Guid(3d8bae22d2837a44ebf60417765fa963) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79e0ace7267d487858fdb76e6e66070a') in 0.0695923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabA.prefab
  artifactKey: Guid(abc00000000017965742289626602030) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SlabA.prefab using Guid(abc00000000017965742289626602030) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd7a94748d90dfdf7f36ceb95c229711') in 0.057761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Torch_A.prefab
  artifactKey: Guid(abc00000000004388197602292238737) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Torch_A.prefab using Guid(abc00000000004388197602292238737) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f7ffbab24ef3b447d38c79e1926f053') in 0.0796363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 40

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_01.prefab
  artifactKey: Guid(abc00000000018247251753517368499) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_StoneBrick_A_01.prefab using Guid(abc00000000018247251753517368499) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2474666413700762176543e867eebc97') in 0.0643324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_B.prefab
  artifactKey: Guid(abc00000000001599261601750755649) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Small_Rock_B.prefab using Guid(abc00000000001599261601750755649) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e650ef6b553a821814b147c2aa270d3a') in 0.0635376 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Mud.terrainlayer
  artifactKey: Guid(3d35e3702bb2eca48bad398e99b5e940) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Terrain/Mud.terrainlayer using Guid(3d35e3702bb2eca48bad398e99b5e940) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7cae0bca32d0507814ae5db494d003b') in 0.0488011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_S_01.prefab
  artifactKey: Guid(abc00000000014208238075552695327) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_SilverFir_S_01.prefab using Guid(abc00000000014208238075552695327) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13eb2021a1c19f0672345237622cb9ae') in 0.0666269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GodRay_Mask02.PNG
  artifactKey: Guid(453a9f5e320417b4098961e79250cae3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_GodRay_Mask02.PNG using Guid(453a9f5e320417b4098961e79250cae3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcc708d9cb7e18d22f84468f8628766a') in 0.0706218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Market_Cloth_M.PNG
  artifactKey: Guid(a0cc45f6791a57347b1ad557a6546b3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Market_Cloth_M.PNG using Guid(a0cc45f6791a57347b1ad557a6546b3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c65a54a4412c08ab13b76917c09cf2eb') in 0.0449258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_BaseColor.PNG
  artifactKey: Guid(fb775e29359a93c4bbd4ca91bee0c2b7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Default_BaseColor.PNG using Guid(fb775e29359a93c4bbd4ca91bee0c2b7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2266bc38484df7dc75ba552feb925a2e') in 0.062393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_M.PNG
  artifactKey: Guid(08e303cc6b6018346a3042d58c74e5fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeech_Detail_01_M.PNG using Guid(08e303cc6b6018346a3042d58c74e5fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7af686ad191991d5e646a863b410544c') in 0.0624883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sword_01.prefab
  artifactKey: Guid(abc00000000011290631944958838382) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Sword_01.prefab using Guid(abc00000000011290631944958838382) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c1008b3aab26c9f8c963a4e9bba0a01') in 0.0656329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A_1.prefab
  artifactKey: Guid(abc00000000016488092838365708715) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Floor_1x2_A_1.prefab using Guid(abc00000000016488092838365708715) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '948716cf4d7b7f0b3e4e1fceacca002f') in 0.0643946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch.prefab
  artifactKey: Guid(abc00000000016636803207453149333) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Prefabs/SM_Middle_Arch.prefab using Guid(abc00000000016636803207453149333) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26a83425baaab0a192b2cd0af9185b8c') in 0.0658474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_ORMH.PNG
  artifactKey: Guid(1a925cf5bda32624c9d6f48dfc014e20) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_ORMH.PNG using Guid(1a925cf5bda32624c9d6f48dfc014e20) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '199b6f11a25e661c1226c8193568d431') in 0.0705476 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Mask.PNG
  artifactKey: Guid(4e19e7c156cc10a4aab62fe011c5bcab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Mask.PNG using Guid(4e19e7c156cc10a4aab62fe011c5bcab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e112e1295aaf7bb1ee59251622835ba') in 0.0537296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_ORMH.PNG
  artifactKey: Guid(f29daa3b370939548b832e4a9299ec89) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Interior_Floor_ORMH.PNG using Guid(f29daa3b370939548b832e4a9299ec89) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d8f5c6755474a6b23645c6768de34b9') in 0.0501997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_N.PNG
  artifactKey: Guid(ca7eac214c3553f4194e20dbc5a7356a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_EuropeanBeechDetail_02_N.PNG using Guid(ca7eac214c3553f4194e20dbc5a7356a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1853230000863cb5233343ed4d29f909') in 0.070383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_ARM.PNG
  artifactKey: Guid(2ecaed89ba9876244802daa675d8650a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneDebris_ARM.PNG using Guid(2ecaed89ba9876244802daa675d8650a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f344adf13adf3038327913226e5b22d0') in 0.0767338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_Normal.PNG
  artifactKey: Guid(aaf8fc84c3572d545b0e9a039cb2656e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_road_Normal.PNG using Guid(aaf8fc84c3572d545b0e9a039cb2656e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70c6bcc6257da495603511968c3aaf58') in 0.0677739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_ARM.PNG
  artifactKey: Guid(8aee4bfc3b9b23d45a9987ab9d360a8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_RockyCliff_A_ARM.PNG using Guid(8aee4bfc3b9b23d45a9987ab9d360a8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5db8c156df6abf1ffd8a9c449ccf5116') in 0.0747959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_normal.PNG
  artifactKey: Guid(5368ae3d551f35e4db2e35cd0f1bef46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_ThatchedRoof_normal.PNG using Guid(5368ae3d551f35e4db2e35cd0f1bef46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6117e140d47586bacdaab8695e3d174') in 0.0841078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_03_N.PNG
  artifactKey: Guid(50a2a41d0a6b6a843b45d4fdabcac737) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricks_03_N.PNG using Guid(50a2a41d0a6b6a843b45d4fdabcac737) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b794f1cfe1482aedcea2e91c505dba1') in 0.0465139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_ARM.PNG
  artifactKey: Guid(8b09ba366f04d524f97b6e20191e844f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Sand_ARM.PNG using Guid(8b09ba366f04d524f97b6e20191e844f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd661780e2e1c6bf0e8d4e2bff39e0403') in 0.0612288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_basecolor.PNG
  artifactKey: Guid(9e0e6b55d521771409d8b65178d34fe9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Plaster_01_basecolor.PNG using Guid(9e0e6b55d521771409d8b65178d34fe9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f44fa61febc182383e5e11eb0facc88') in 0.0508117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_N.PNG
  artifactKey: Guid(31e557d925dac1b42bce119f0dd54055) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_SilverFir_01_N.PNG using Guid(31e557d925dac1b42bce119f0dd54055) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31adf37ebd6ba1ed66577e25d580edff') in 0.0751782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_ARM.PNG
  artifactKey: Guid(4538bb6d929ac0b49b6a6972867baa3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneWall_A_ARM.PNG using Guid(4538bb6d929ac0b49b6a6972867baa3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebd37374f3dc1d1d5ae3588bd8e91238') in 0.0554561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Forward.controller
  artifactKey: Guid(ff038c74276096843a4b1cdc5645ceb0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_Forward.controller using Guid(ff038c74276096843a4b1cdc5645ceb0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9dd57723d6afbd4f0e7030bb13a0545') in 0.0412234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_Normal.PNG
  artifactKey: Guid(0bcf3d64753ef0f4d9d0a6bf5a623fbb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Weapons_Normal.PNG using Guid(0bcf3d64753ef0f4d9d0a6bf5a623fbb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cc276d8971a70b0ff0e4f32c5708422') in 0.050889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_ForwardRight.controller
  artifactKey: Guid(70835a262512c684da90def89a1e688c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Sprint01_ForwardRight.controller using Guid(70835a262512c684da90def89a1e688c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '066272eeda10ec1beaa581569860bdd0') in 0.0377212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_BackwardLeft.controller
  artifactKey: Guid(f290f141e25553a42923c9a164832310) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Run01_BackwardLeft.controller using Guid(f290f141e25553a42923c9a164832310) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dae81bfa43dd1d92e7fa36b88980ef32') in 0.036646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_BackwardLeft.controller
  artifactKey: Guid(3568d31851fe3534f8155d8d2657fabf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanF@Walk01_BackwardLeft.controller using Guid(3568d31851fe3534f8155d8d2657fabf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc6b4226e25dfa55a803ca42298ca708') in 0.0362438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/TrampolinePlatform.cs
  artifactKey: Guid(7d3608cb60e8d3649bfacb98bfbe9833) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Nappin/PhysicsCharacterController/Scripts/Extensions/Platforms/TrampolinePlatform.cs using Guid(7d3608cb60e8d3649bfacb98bfbe9833) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '009f1bd9b07823dc09cb9e3df1a36d3b') in 0.033009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Turn01_Right [RM].controller
  artifactKey: Guid(59d819129365f904d851527080a596a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/AnimatorControllers/HumanM@Turn01_Right [RM].controller using Guid(59d819129365f904d851527080a596a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fcf52e7941126a17658ee3eb004f446') in 0.0362658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Materials/HumanBasicMotions_Floor.mat
  artifactKey: Guid(5066fedfee3f6d04a8c9931c4ca8bc3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Unity Demo Scenes/Human Basic Motions/Materials/HumanBasicMotions_Floor.mat using Guid(5066fedfee3f6d04a8c9931c4ca8bc3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ed85ce930183acd055a43a45314771a') in 0.0388068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hand Left Mask.mask
  artifactKey: Guid(b7a9bc6acd2eaa941ab64d230864a316) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Models/Avatar Masks/Hands/Human Hand Left Mask.mask using Guid(b7a9bc6acd2eaa941ab64d230864a316) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '453a8e27b1363afe7039a06979939c45') in 0.0513527 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_01_N.PNG
  artifactKey: Guid(9ba9b6b1304d3da47b665af315a9935e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_FirePit_01_N.PNG using Guid(9ba9b6b1304d3da47b665af315a9935e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d6369464ed4094da950fe734b479e4e') in 0.0471142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_N_4.PNG
  artifactKey: Guid(c37b10cbeec96b34a829a259e8cea248) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_StoneBricksLP_N_4.PNG using Guid(c37b10cbeec96b34a829a259e8cea248) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0642d53b3172e8f59547e44b4e802f86') in 0.0485299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Idle_Turn_L90.FBX
  artifactKey: Guid(c1325d4966fa1f847a428a6607304311) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Idle_Turn_L90.FBX using Guid(c1325d4966fa1f847a428a6607304311) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '363f7839b33df840960bb3a4fde60599') in 0.061053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_A.FBX
  artifactKey: Guid(5a4ac386820d55b45880ad0543f88ffd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Jog_A.FBX using Guid(5a4ac386820d55b45880ad0543f88ffd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9b936ce10c2f8918814406baa2a62b0') in 0.0727467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_B.FBX
  artifactKey: Guid(454486692aaa62c4ebab36baebb96bcd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Walk_A_to_Walk_B.FBX using Guid(454486692aaa62c4ebab36baebb96bcd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b4b5b77e3d4dda3703d396ab7c59e7b') in 0.0585638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Twitter_1.wav
  artifactKey: Guid(337d7b2e6f1cf6c469c6bd89d1e6ac8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Bird_Twitter_1.wav using Guid(337d7b2e6f1cf6c469c6bd89d1e6ac8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd309d2f27bee8b991d88fa4f0f36ea9') in 0.1363733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Meditative_1.wav
  artifactKey: Guid(6acd513eb88edcd48b45e0ebd3ce0897) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Notification/SFX_UI_Notification_Meditative_1.wav using Guid(6acd513eb88edcd48b45e0ebd3ce0897) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8f617fad66b2193ca1bd28203352daa2') in 0.1548676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Open_1.wav
  artifactKey: Guid(56a1587665cd1b8499834c1cef0f90a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Reward/SFX_UI_Reward_Open_1.wav using Guid(56a1587665cd1b8499834c1cef0f90a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25ae0e565a645871cfdabd7fc8d0eebc') in 0.1696996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_A_Turn_L90_Root.FBX
  artifactKey: Guid(81e9481807082244c8f73e58fd31cc86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Idle_To_Walk_A_Turn_L90_Root.FBX using Guid(81e9481807082244c8f73e58fd31cc86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8795de1b4c45250b5ec754a399c587a') in 0.0642545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Idle_Turn_R90.FBX
  artifactKey: Guid(6f1652d828b74b34aa495e75e65822a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Walk_A_To_Idle_Turn_R90.FBX using Guid(6f1652d828b74b34aa495e75e65822a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f418e652f1b190bd11ecd54a4e059f79') in 0.0666545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_FR45_Root.FBX
  artifactKey: Guid(581774daaeda4d54889c975491ab46b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_FR45_Root.FBX using Guid(581774daaeda4d54889c975491ab46b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38d0c438cc125933ef31a4d724cadac6') in 0.0756794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Run_A_Root.FBX
  artifactKey: Guid(1eb6c725854579740880d402a73bcd8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Jog_B_to_Run_A_Root.FBX using Guid(1eb6c725854579740880d402a73bcd8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d12efa3663901eb4617f45891683bfb') in 0.0668182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_To_Idle.FBX
  artifactKey: Guid(51318d1c62df1c341aa6900ccd9d8d91) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/Inplace/M_Big_Sword@Crouch_To_Idle.FBX using Guid(51318d1c62df1c341aa6900ccd9d8d91) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06a9f2acaf6b4671febab4c97a8f4599') in 0.0813561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_B_to_Run_A_Root.FBX
  artifactKey: Guid(552dd8bc5430aaa41813970a042ec901) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Run_B_to_Run_A_Root.FBX using Guid(552dd8bc5430aaa41813970a042ec901) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '29f12fe7e6d674322353e7959e02d27d') in 0.0736632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Run_A_Root.FBX
  artifactKey: Guid(3542741ace601564d9f8914b642f84bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/RootMotion/M_Big_Sword@Turn_in_place_Idle_to_Run_A_Root.FBX using Guid(3542741ace601564d9f8914b642f84bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f823c3032ebdd3556ac00e4107bafa8a') in 0.0704884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_Idle_Turn_Root.FBX
  artifactKey: Guid(feae869cde9e629439c8f589372b03d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/13__Crouch/RootMotion/M_Big_Sword@Crouch_Idle_Turn_Root.FBX using Guid(feae869cde9e629439c8f589372b03d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8263e2eb5d0440576dea3004b02c94e6') in 0.0541683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_L90.FBX
  artifactKey: Guid(1e382d35ee6f5314c9f3f84e431bde2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Jogging_A_To_Jogging_A_Turn_L90.FBX using Guid(1e382d35ee6f5314c9f3f84e431bde2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8b6c21495004c6b5c6a2a56bb1aaa56') in 0.0677021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Walk_B.FBX
  artifactKey: Guid(f8864f50360d5654083d93cab5535bb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Idle_to_Walk_B.FBX using Guid(f8864f50360d5654083d93cab5535bb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19c9b80b9e71e096aa75abf6e5887dde') in 0.073781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_Turn_L90.FBX
  artifactKey: Guid(ebeb91d017b5854459addfe35cc207bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_B_Turn_L90.FBX using Guid(ebeb91d017b5854459addfe35cc207bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '733090e2fe7d3d3292f7cbce8802d629') in 0.0608969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90.FBX
  artifactKey: Guid(b46fd0c5e802b604c8ae97814b3fe566) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90.FBX using Guid(b46fd0c5e802b604c8ae97814b3fe566) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efb6edd0c6049ccecd09a640cf170110') in 0.0607696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_B_Root.FBX
  artifactKey: Guid(e3cf994fc30af6a42b90add15724366b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/10__Move_to_Idle/Walk_To_Idle/M_katana_Blade@Walk_ver_B_To_Idle_ver_B_Root.FBX using Guid(e3cf994fc30af6a42b90add15724366b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f0415006f352f14f5afcb190e397563') in 0.0728482 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BL45.FBX
  artifactKey: Guid(e6d5cfd1f4873ed47af1fc6607953f6c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/B/M_Big_Sword@Jogging_8Way_verB_BL45.FBX using Guid(e6d5cfd1f4873ed47af1fc6607953f6c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b22a6b478c0069fcd2206cc2d22e86e') in 0.0688477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_B_Root.FBX
  artifactKey: Guid(986da400b35391f459f84594a86569c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_B_Root.FBX using Guid(986da400b35391f459f84594a86569c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be6d132bffccd9f3b9194764be0342af') in 0.0598556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_B.FBX
  artifactKey: Guid(2c73c7fb9440b944eaebec258aebb16a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_B.FBX using Guid(2c73c7fb9440b944eaebec258aebb16a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '043e9db7134bd12e391e2cd53771e661') in 0.0716488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L45.FBX
  artifactKey: Guid(89509ccfdd4b303478f0ef080f4f9c55) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/2__Walk/B/M_Big_Sword@Walk_ver_B_Front_L45.FBX using Guid(89509ccfdd4b303478f0ef080f4f9c55) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '620b91dd4aac156ea145ebee209b416d') in 0.0674885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_vol2.FBX
  artifactKey: Guid(9a74e8c2539586f4bb7217f56a208ac9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/3__Jogging/A/M_Big_Sword@Jogging_8Way_verA_R90_vol2.FBX using Guid(9a74e8c2539586f4bb7217f56a208ac9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '638096dd14e5262f27fafade717eedb1') in 0.0586706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_L90_Root.FBX
  artifactKey: Guid(877919ffa0728c34f92331e343c2e100) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_L90_Root.FBX using Guid(877919ffa0728c34f92331e343c2e100) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e27950c2e2f2ce4b41817b9becd89559') in 0.0711506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Walk_B_Turn_R90.FBX
  artifactKey: Guid(64040d7d4d438a943a84f5df92495ae9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Walk_B_To_Walk_B_Turn_R90.FBX using Guid(64040d7d4d438a943a84f5df92495ae9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b41783ec493af00cb3d0a0dbdf81224') in 0.1164157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_B_Root.FBX
  artifactKey: Guid(8463d108305c45a43ab4263f39217128) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_B_Root.FBX using Guid(8463d108305c45a43ab4263f39217128) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca759ae6fffda77b1c6ead3e60e3fc0e') in 0.0656286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_A.FBX
  artifactKey: Guid(ab72cf0acc165a14f8a45fb72a7922d1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Idle_A_to_Walk_A.FBX using Guid(ab72cf0acc165a14f8a45fb72a7922d1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5820ac7946bba8005a42c19d42df831') in 0.0670825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Run_A.FBX
  artifactKey: Guid(04703e604bae40e4f8e09a9a7502dc09) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Walk_A_to_Run_A.FBX using Guid(04703e604bae40e4f8e09a9a7502dc09) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '579a35f91ae41a3a99c7e8563ea388dc') in 0.0788841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_A_to_Run_A.FBX
  artifactKey: Guid(c395b150de2e4194d9b2e0c7a29010e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Turn_in_place_Jog_A_to_Run_A.FBX using Guid(c395b150de2e4194d9b2e0c7a29010e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0071c78e85169eb9dbc5427f6efcf11a') in 0.0730344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Run_A_to_Run_B_Root.FBX
  artifactKey: Guid(7288de5a05790104db7acd1f533b2e16) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Run_A_to_Run_B_Root.FBX using Guid(7288de5a05790104db7acd1f533b2e16) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0db66cb22c3030bc28abce0457ff996d') in 0.1401063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Run_A_Root.FBX
  artifactKey: Guid(c0846483f9b9f6f418ab5097429ed316) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Run_A_Root.FBX using Guid(c0846483f9b9f6f418ab5097429ed316) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb7d9edc3d358cb5c6ff25f24dc72e51') in 0.070769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_A_Root.FBX
  artifactKey: Guid(8f59bc1f37f28184997ab1a8c9c77873) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_Idle_To_Idle_ver_A_Root.FBX using Guid(8f59bc1f37f28184997ab1a8c9c77873) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80b97f6329623285f2c24074465f51a2') in 0.0577442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle_Turn.FBX
  artifactKey: Guid(0c21ecd7414c2b144920d7009c0ac936) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_Idle_Turn.FBX using Guid(0c21ecd7414c2b144920d7009c0ac936) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54d0c1f13dbc44b32c79437a9b1a9405') in 0.057769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_A_Root.FBX
  artifactKey: Guid(13db5af2a485c3a4f82a82b3c823f0d5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_A_to_Walk_A_Root.FBX using Guid(13db5af2a485c3a4f82a82b3c823f0d5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39121f48d20cbf9c77ac6260c5e2e4c8') in 0.0697039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Run_B_Root.FBX
  artifactKey: Guid(05b75d2becbb7dd4986ba76ea0badad9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Turn_in_place_Walk_B_to_Run_B_Root.FBX using Guid(05b75d2becbb7dd4986ba76ea0badad9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58f5347e051fb8103b7a4304badff9cf') in 0.0588927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_L90.FBX
  artifactKey: Guid(0d7df80851d662e4c95d14989f1d3c93) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/Inplace/M_katana_Blade@Crouch_ver_A_L90.FBX using Guid(0d7df80851d662e4c95d14989f1d3c93) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc7d202440e796013f05d013f411ee59') in 0.0556568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_L90_Root.FBX
  artifactKey: Guid(94d4db9bc64b7384eaa183d8dfb4730d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/13__Crouch/RootMotion/M_katana_Blade@Crouch_ver_B_L90_Root.FBX using Guid(94d4db9bc64b7384eaa183d8dfb4730d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ab279ba25d84f802392b1d478a8f689') in 0.065303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_F_Root.FBX
  artifactKey: Guid(5da905ecaca2d494894564e481290d6d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_F_Root.FBX using Guid(5da905ecaca2d494894564e481290d6d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c045153df741aa30dc13528c4ee22fa') in 0.0652021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_Root.FBX
  artifactKey: Guid(44f15519ee1b49f42aa09afa6c3b1364) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/B/M_katana_Blade@Walk_ver_B_Back_Root.FBX using Guid(44f15519ee1b49f42aa09afa6c3b1364) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cbeca7133530ed43818533749d511d84') in 0.0603969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_Root_vol2.FBX
  artifactKey: Guid(39134a3168848464b9decc90c7047916) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_R90_Root_vol2.FBX using Guid(39134a3168848464b9decc90c7047916) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e234b8dbccb4dcf658ee5a5e56ceff3') in 0.0545786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_vol2.FBX
  artifactKey: Guid(38e8999cb2d1167479e3551ffdf3b167) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_L90_vol2.FBX using Guid(38e8999cb2d1167479e3551ffdf3b167) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '031c014f2ab72c9be5e82315996277c1') in 0.070146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FL45_Root.FBX
  artifactKey: Guid(9791ba6001af41f4195c7ec27fe28603) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/B/M_katana_Blade@Jogging_8Way_verB_FL45_Root.FBX using Guid(9791ba6001af41f4195c7ec27fe28603) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb48768b92f1360b5bb27a7ef14b6929') in 0.0609769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_Root.FBX
  artifactKey: Guid(80c5992fa46974f4a8108ece87f48747) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_Root.FBX using Guid(80c5992fa46974f4a8108ece87f48747) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f5ce050a7f2ff0413b29f6cad41e2d3') in 0.0586814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45.FBX
  artifactKey: Guid(6cae46fabedc7a543a0b3fa22418260f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_R45.FBX using Guid(6cae46fabedc7a543a0b3fa22418260f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac4aa969c2c4be78e797b324cbfb0377') in 0.0708774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleSlim_02.fbx
  artifactKey: Guid(abc00000000008667928271786110402) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Candle/SM_CandleSlim_02.fbx using Guid(abc00000000008667928271786110402) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36df4d091b2ce3e807a46d80385be71e') in 0.064985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_04.fbx
  artifactKey: Guid(abc00000000017336289664919542949) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Foliage/SM_MossClump_04.fbx using Guid(abc00000000017336289664919542949) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94f2378bdf3283fa66ad1e604caf9524') in 0.0547722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Candles.mat
  artifactKey: Guid(0c38fb87629211f4abd162b10be82344) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_Candles.mat using Guid(0c38fb87629211f4abd162b10be82344) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a2ea8a610d1485a6820f0692aae3f3b') in 0.0468377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_04.mat
  artifactKey: Guid(abc00000000015116042325195701470) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Materials/BrickKit/M_BrickKit_04.mat using Guid(abc00000000015116042325195701470) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0233a4c4bd437c7453e1d5d87de5b588') in 0.0555289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_B.fbx
  artifactKey: Guid(abc00000000000897497455397467399) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Brick_Kit/SM_Tile_B.fbx using Guid(abc00000000000897497455397467399) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c93239bc2b5e2bcf1875db138968994') in 0.0599525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_B.fbx
  artifactKey: Guid(abc00000000018184872645282958289) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_B.fbx using Guid(abc00000000018184872645282958289) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b88e8b8f1683435d6a32632a417040af') in 0.0722289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Large_Keg.fbx
  artifactKey: Guid(abc00000000011073078328663990163) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_Large_Keg.fbx using Guid(abc00000000011073078328663990163) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '719dc09d2eaf9ac29fb632212efc4467') in 0.0673848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/SM_Mountain_02.fbx
  artifactKey: Guid(abc00000000014225391774255828542) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Mountains/SM_Mountain_02.fbx using Guid(abc00000000014225391774255828542) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f6c7cbb9a713e5b307b14e4dddc265d') in 0.0799061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_E.fbx
  artifactKey: Guid(abc00000000015337714486565425494) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_Path_Slab_E.fbx using Guid(abc00000000015337714486565425494) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5fce5e4509595373d2870b07fbc22a01') in 0.0578976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_FirePit_01.fbx
  artifactKey: Guid(abc00000000011047987496704904159) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/SM_FirePit_01.fbx using Guid(abc00000000011047987496704904159) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e43e77f3c94642839294b4f510e6f56') in 0.0840041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_04.fbx
  artifactKey: Guid(abc00000000012919198202473986404) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/SM_RockCliff_04.fbx using Guid(abc00000000012919198202473986404) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab59b1d14caaca2eaceef4de019bbc01') in 0.0651102 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Social/Conversation/<EMAIL>
  artifactKey: Guid(ef7fc1c2505f93948addc245824a58b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Social/Conversation/<EMAIL> using Guid(ef7fc1c2505f93948addc245824a58b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ba8b2777b9c315cad6bafc006fe4dfe') in 0.0561444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Left.fbx
  artifactKey: Guid(1ee3692ad357102419809ccbcace4969) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_Left.fbx using Guid(1ee3692ad357102419809ccbcace4969) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36cc3688d45055fdd197314206d12213') in 0.0670959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Backward.fbx
  artifactKey: Guid(b7ff898919f115a4095d79af0129756f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/HumanF@Run01_Backward.fbx using Guid(b7ff898919f115a4095d79af0129756f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aefc85fd45dd07556271aa2788760cb4') in 0.1285434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_End_01.fbx
  artifactKey: Guid(abc00000000007441763246330456931) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Stone_Fence/SM_Wall_Stone_End_01.fbx using Guid(abc00000000007441763246330456931) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffd9dab515a03057583e626cf9e07d79') in 0.0942685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_BackwardRight.fbx
  artifactKey: Guid(11d5093c8a733f64099488183b5e3f9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Walk/HumanF@Walk01_BackwardRight.fbx using Guid(11d5093c8a733f64099488183b5e3f9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b82a8c662c28e69bee42e2036a2414a') in 0.0584244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Wooden_Generic_1.wav
  artifactKey: Guid(e8b444b9df6fa774487c3c15e57a6be2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Generic/SFX_UI_Click_Wooden_Generic_1.wav using Guid(e8b444b9df6fa774487c3c15e57a6be2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62508cca9c777946d16a16fc0ee9c0f2') in 0.1457113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_Left.fbx
  artifactKey: Guid(f194a505473e7aa469db0682217fa7f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Sprint/HumanM@Sprint01_Left.fbx using Guid(f194a505473e7aa469db0682217fa7f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b28b169eed8fb0956c7381afb8db64d4') in 0.0558078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_3.wav
  artifactKey: Guid(8edfab0cc7063394c9eb1867748593fb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Zen Games Sound Effects and Music Pack/AUDIO/SFX/UI/Click/Buy/SFX_UI_Click_Buy_3.wav using Guid(8edfab0cc7063394c9eb1867748593fb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ce5480136c97a27095731247a9a44f9') in 0.165679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_A.fbx
  artifactKey: Guid(abc00000000011233488550000352576) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_A.fbx using Guid(abc00000000011233488550000352576) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2402bb3f1e33dd86a90e3b1c668d3b3') in 0.0584524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/MI_LighterPlanks.mat
  artifactKey: Guid(318b9bc48e8fcb348992b0a16b45f261) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Gangways/Materials/MI_LighterPlanks.mat using Guid(318b9bc48e8fcb348992b0a16b45f261) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8d775c20e8ac7f8cd66fbf3747c3cf8c') in 0.0589937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Wheel.fbx
  artifactKey: Guid(abc00000000001699974602126586081) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/carts/SM_Wheel.fbx using Guid(abc00000000001699974602126586081) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd53ff48e1a8e7ba57d5fb5e7c220e51b') in 0.070932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Stab_Algae.mat
  artifactKey: Guid(fb68fadf48b3f004db3f1c95301b75ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/DockWalls/Materials/MI_Stab_Algae.mat using Guid(fb68fadf48b3f004db3f1c95301b75ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0d6cefdca51b42da8a48c34b545a9b3') in 0.0590253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(ddd4abb9fc853384f9228e2f08713a42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Flags/Materials/MI_WoodPlanks.mat using Guid(ddd4abb9fc853384f9228e2f08713a42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2422d4b15f0ad53148bd811fce914f63') in 0.0750101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_03_Fix.fbx
  artifactKey: Guid(abc00000000015190288119272703675) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/CurtainWall/SM_Curtain_Wall_03_Fix.fbx using Guid(abc00000000015190288119272703675) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d9b361441912e016708a7f2e0b31699') in 0.0701231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Table_Round.fbx
  artifactKey: Guid(abc00000000006177254847872094410) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Table_Round.fbx using Guid(abc00000000006177254847872094410) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8357cbbb6a6244b598cd6a6a26cc3bed') in 0.0774958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_02.fbx
  artifactKey: Guid(abc00000000010374555400725940265) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Bookshelf_A_02.fbx using Guid(abc00000000010374555400725940265) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b53af2de46260d5152a515eb05174056') in 0.0800966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_Patch_A.fbx
  artifactKey: Guid(abc00000000005954298790750212985) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Cobblestone_Road/SM_Cobble_Patch_A.fbx using Guid(abc00000000005954298790750212985) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91f8edcaaec677d3673d1aad4b526636') in 0.0614564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Drawer_01.fbx
  artifactKey: Guid(abc00000000011830769169353056912) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Furniture/SM_Drawer_01.fbx using Guid(abc00000000011830769169353056912) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a1919839dababe805e4a1d3b9f2f135') in 0.0909172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Sleeve_Beam.fbx
  artifactKey: Guid(abc00000000011285892567647563141) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/SM_Rope_Sleeve_Beam.fbx using Guid(abc00000000011285892567647563141) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79fe04ac2d417b98a695b542270c1af1') in 0.0763262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Materials/MI_RockMossy_02.mat
  artifactKey: Guid(6157e481782bf57488fb4a2b1c7a4b48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Materials/MI_RockMossy_02.mat using Guid(6157e481782bf57488fb4a2b1c7a4b48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b2b99518cebe519362679401393bb8b') in 0.0603637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Large_Rock_C.fbx
  artifactKey: Guid(abc00000000003278795028303492669) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/SM_Large_Rock_C.fbx using Guid(abc00000000003278795028303492669) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e64287cc2dc3b6b0e1a90d28c964ff4f') in 0.0858874 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Left [RM].fbx
  artifactKey: Guid(f76f0d054afdd5845a03ab8eea5b79b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Female/Movement/Run/RootMotion/HumanF@Run01_Left [RM].fbx using Guid(f76f0d054afdd5845a03ab8eea5b79b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0e4237599abb594975f0b1abab3a30f') in 0.0818424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_05.fbx
  artifactKey: Guid(abc00000000005835147023475290979) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_05.fbx using Guid(abc00000000005835147023475290979) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf6811fdb32598389fa00d3bc46e5aba') in 0.0714403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_03.fbx
  artifactKey: Guid(abc00000000003223940734874005722) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_Seam_A_03.fbx using Guid(abc00000000003223940734874005722) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '710bca58be29b38aa20409ca9acef639') in 0.0738643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_Forward [RM].fbx
  artifactKey: Guid(9259db9271971e54ab8991d4aa53aae2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Walk/RootMotion/HumanM@Walk01_Forward [RM].fbx using Guid(9259db9271971e54ab8991d4aa53aae2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd144110c697e5d5dc3437ba2d4efd389') in 0.0749821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Wall_A_02.fbx
  artifactKey: Guid(abc00000000005021577805417894602) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Wall_A_02.fbx using Guid(abc00000000005021577805417894602) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a672df04121337588ab7d29cb1683631') in 0.1183206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_03.fbx
  artifactKey: Guid(abc00000000008005408412542062591) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_WallTopper_A_03.fbx using Guid(abc00000000008005408412542062591) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e29b115a066580de5632463f913eedbd') in 0.0775027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Long_Loose.fbx
  artifactKey: Guid(abc00000000015159835768985048996) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Chains/SM_Chain_Long_Loose.fbx using Guid(abc00000000015159835768985048996) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '29a7efe809e3cab81cac3a7c4e7fc76c') in 0.081237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/MI_StoneFence_03.mat
  artifactKey: Guid(f148bdea4a0b729449db631c6824850e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Materials/MI_StoneFence_03.mat using Guid(f148bdea4a0b729449db631c6824850e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b44c5cfe2953d916847674eeaf275306') in 0.0695911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_A_01.fbx
  artifactKey: Guid(abc00000000013380253915414767488) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_A_01.fbx using Guid(abc00000000013380253915414767488) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '046f841e5e1b65dc859be7f664c1802e') in 0.0910957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_B_01.fbx
  artifactKey: Guid(abc00000000000738221277618963195) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/WindowsandDoors/SM_DoorFrame_B_01.fbx using Guid(abc00000000000738221277618963195) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27045272a343417b01d0e7c1b10e81cc') in 0.0687952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_03.fbx
  artifactKey: Guid(abc00000000016317670818143957573) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/SM_CastleTower_Floor_A_03.fbx using Guid(abc00000000016317670818143957573) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '09170a7623bb789a3a87ec2b6725212e') in 0.0845045 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/Materials/No Name.mat
  artifactKey: Guid(fab27ba52f2bea04880009c942798b4a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Ropes/Materials/No Name.mat using Guid(fab27ba52f2bea04880009c942798b4a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc635e998b0c544b5abe1fb3de439597') in 0.068165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Cloth_Red.mat
  artifactKey: Guid(837a5d6257aa8914082eaf5dbfb76e6e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Castle/Materials/MI_Cloth_Red.mat using Guid(837a5d6257aa8914082eaf5dbfb76e6e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37c8b55bae2af39ab56f2f9eaca516c6') in 0.073097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/Materials/MI_Candles.mat
  artifactKey: Guid(15993487c91b1bf4dbb045e6eba52b3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Props/Candles/Materials/MI_Candles.mat using Guid(15993487c91b1bf4dbb045e6eba52b3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c522d1afe45753c7c767f266da557403') in 0.0737935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_04.fbx
  artifactKey: Guid(abc00000000003295069275363201041) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/StoneBricks/SM_StoneBrick_A_04.fbx using Guid(abc00000000003295069275363201041) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1af28ecd11786c737904250ef25ee5d') in 0.0841653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/Materials/MI_RockMossy_02.mat
  artifactKey: Guid(1f8646351c2f4074097cee87e5c68e78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Rock_Kit/Small/Materials/MI_RockMossy_02.mat using Guid(1f8646351c2f4074097cee87e5c68e78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb2f32acff0af77a56f2bbafff3ba119') in 0.0661116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Chimney_Pots.fbx
  artifactKey: Guid(abc00000000009294446879474617862) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Chimney/SM_Chimney_Pots.fbx using Guid(abc00000000009294446879474617862) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d5d2b4aa535a687d7d0fac35eb56c34') in 0.0844754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_2x4M.fbx
  artifactKey: Guid(abc00000000000919784258492635482) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_2x4M.fbx using Guid(abc00000000000919784258492635482) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6827a6083717959fa3dcd972186afaf3') in 0.0568635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_02.fbx
  artifactKey: Guid(abc00000000005010285347631916683) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Wall_02.fbx using Guid(abc00000000005010285347631916683) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76bd38e1f9aea1a43a9d87a239839ebc') in 0.0595347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_4x4M.fbx
  artifactKey: Guid(abc00000000009215638693371940384) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/SM_Floor_4x4M.fbx using Guid(abc00000000009215638693371940384) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e88cfda6ecbfd883579cf0048b230da') in 0.0635714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_01.fbx
  artifactKey: Guid(abc00000000018435303392903131793) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_01.fbx using Guid(abc00000000018435303392903131793) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd876030c29c771c1d9b755a9fa101af0') in 0.1047559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_10.fbx
  artifactKey: Guid(abc00000000012895904377051102923) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_10.fbx using Guid(abc00000000012895904377051102923) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43137fadd6283ace360544daad7dadfc') in 0.0898189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickStoneWall_Disp.mat
  artifactKey: Guid(d369ae8a8feacc147b4019855c84fe43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_BrickStoneWall_Disp.mat using Guid(d369ae8a8feacc147b4019855c84fe43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a36102d227b35aa3d432115c2fa80177') in 0.060837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000255 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/No Name.mat
  artifactKey: Guid(62204df537d9eeb42805507dd8f1cf2d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/No Name.mat using Guid(62204df537d9eeb42805507dd8f1cf2d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4930791c742f1626f54035b1ed4a8286') in 0.0714082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Detail_01.mat
  artifactKey: Guid(abc00000000017452062649247477783) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/EuropeanBeech/Materials/M_EuropeanBeech_Detail_01.mat using Guid(abc00000000017452062649247477783) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a57239be3f74dc77f10551fed6d38574') in 0.0359611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(9c0299840d6b09048af997c4b73a4455) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Castle_Gate/Materials/MI_WoodPlanks.mat using Guid(9c0299840d6b09048af997c4b73a4455) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c49fa981fcb7e7466a83cc7706f042bd') in 0.0682029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000302 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_StoneFence_02.mat
  artifactKey: Guid(700b31f091006494d8ab8f94cfec0dc0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Hall_1/Materials/MI_StoneFence_02.mat using Guid(700b31f091006494d8ab8f94cfec0dc0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '842d38c18bb8e7a36eb33ac90a3b8fb9') in 0.067842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_S_03.fbx
  artifactKey: Guid(abc00000000006715593976625348224) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Scanned_Foliage/Foliage/WildGrass/Meshes/SM_WildGrass_S_03.fbx using Guid(abc00000000006715593976625348224) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '116cc86420a22553d035ec9bf6783100') in 0.0582575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_01.fbx
  artifactKey: Guid(abc00000000000846264389797601758) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_01.fbx using Guid(abc00000000000846264389797601758) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19910f328504c915f24d6cc9f4da8be1') in 0.086977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_LargeRoof_01.fbx
  artifactKey: Guid(abc00000000006171966154812730560) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/Roofs/SM_LargeRoof_01.fbx using Guid(abc00000000006171966154812730560) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ca041c27ccb239e15b06035744db3a3') in 0.0838611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/Materials/MI_WoodPlanks.mat
  artifactKey: Guid(9c648974b585ffd43b831656ce8530f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/WoodenFloor/Materials/MI_WoodPlanks.mat using Guid(9c648974b585ffd43b831656ce8530f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5930831c45560ce99fdcf09cebb6a4b') in 0.0655301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_13.fbx
  artifactKey: Guid(abc00000000003353823138000406597) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_A_13.fbx using Guid(abc00000000003353823138000406597) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4f35423b5ea6f860c4c04b1df2f42a1') in 0.091732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_02.fbx
  artifactKey: Guid(abc00000000014896330484403731562) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/A/SM_Roof_Cap_02.fbx using Guid(abc00000000014896330484403731562) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d267e1b3134a42d33eb67bacc5d2825') in 0.0883852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_01.fbx
  artifactKey: Guid(abc00000000008901608432938706450) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Walls/A/SM_StoneWall_B_01.fbx using Guid(abc00000000008901608432938706450) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f5b05a26d7b1c27ab0112a983b30309') in 0.0836625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/No Name.mat
  artifactKey: Guid(a7f4b85cf1d001841961736ed831fb0c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/No Name.mat using Guid(a7f4b85cf1d001841961736ed831fb0c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb28a79cf2b806eaef48c61844f84492') in 0.063013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Roof_Tiles.mat
  artifactKey: Guid(9cd79605f45b87b45b3f8ea08cf3d782) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Village/Building_Kit/Roofs/B/Materials/MI_Roof_Tiles.mat using Guid(9cd79605f45b87b45b3f8ea08cf3d782) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e05759e13ca2bc18c96370611b2cee6') in 0.0529067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_03.fbx
  artifactKey: Guid(abc00000000007172394478762078587) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Meshes/Castle_Kit/Buildings/A/Windows/SM_CastleTower_Window_A_03.fbx using Guid(abc00000000007172394478762078587) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a385edf11917d30821709e53efc5e326') in 0.118417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Left.fbx
  artifactKey: Guid(9f9dbe8815370164d8a2214328af4f13) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Kevin Iglesias/Human Animations/Animations/Male/Movement/Run/HumanM@Run01_Left.fbx using Guid(9f9dbe8815370164d8a2214328af4f13) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e831fb20e7e4d37b6dc44c5ae9bada91') in 0.055393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 120

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_R90_Root.FBX
  artifactKey: Guid(a1f3a6aaf8692be4a888d7622a314047) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/RootMotion/M_katana_Blade@Jogging_A_To_Idle_ver_A_Turn_R90_Root.FBX using Guid(a1f3a6aaf8692be4a888d7622a314047) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96eddb40b3a0d01f3430b79dcf852bd0') in 0.0571632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FR45.FBX
  artifactKey: Guid(1e226be9729714f43acd16e61115b23e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/3__Jogging/A/M_katana_Blade@Jogging_8Way_verA_FR45.FBX using Guid(1e226be9729714f43acd16e61115b23e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '154e07adbfe2b5cb27f7fd1e28d62994') in 0.0718977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_A.FBX
  artifactKey: Guid(25972b3153f15cd449092b1c699834d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/12__Turn_in_place/Inplace/M_Big_Sword@Turn_in_place_Jog_A_to_Jog_A.FBX using Guid(25972b3153f15cd449092b1c699834d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1311fbd591cca714e8399f09c3152267') in 0.1208447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_Turn_R90.FBX
  artifactKey: Guid(36c5bd736c47509429ec30bdb81064a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/12__Turn_in_place/Inplace/M_katana_Blade@Idle_A_Turn_R90.FBX using Guid(36c5bd736c47509429ec30bdb81064a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58c753dc7143b7d4e8d2012efb86f71b') in 0.0697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90.FBX
  artifactKey: Guid(419ae4620afd8ed4e90c0bbc096cef7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/2__Walk/A/M_katana_Blade@Walk_ver_A_Front_L90.FBX using Guid(419ae4620afd8ed4e90c0bbc096cef7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be71cf230ad92106e0dcdabf94d3cf43') in 0.0681593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_C.PNG
  artifactKey: Guid(ed038a6cb0045314b824ad0a3638b648) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/Textures(1K)/T_Flat_C.PNG using Guid(ed038a6cb0045314b824ad0a3638b648) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ca7ac144969d729ee6794f901fce423') in 0.0708734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_07.png
  artifactKey: Guid(721f94fd3388aff4db35ced9fd7be68b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/IdaFaber/Textures/Base/Mist/T_EYES_MIST_BaseColor_07.png using Guid(721f94fd3388aff4db35ced9fd7be68b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45f045566220f02efb29299ed55a2cfe') in 0.0775902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_1.FBX
  artifactKey: Guid(6bddcdfebb3a83e46991aa42b76c7259) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/2_Attacks/0__3Combos/M_Big_Sword@Attack_3Combo_1.FBX using Guid(6bddcdfebb3a83e46991aa42b76c7259) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b875978f810803c3611daf54f9e65b1') in 0.0856005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 142

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90.FBX
  artifactKey: Guid(aba94f124c18251418abeb1ae4cfa785) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/1_Movements/9__Idle_To_Move/M_katana_Blade@Idle_A_To_Jogging_A_Turn_R90.FBX using Guid(aba94f124c18251418abeb1ae4cfa785) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0968bbc3a08b18b1f585d9a79275a37') in 0.1104095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 315

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3.FBX
  artifactKey: Guid(dca154178f0c4c14da932e25559a6a76) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Katana_Blade/2_Attacks/4__Jump_Attack/M_katana_Blade@Jump_Attack_Combo_3.FBX using Guid(dca154178f0c4c14da932e25559a6a76) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd94f9f64d5e5867c6f1bfbae4c213074') in 0.0765079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 316

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL90_Root.FBX
  artifactKey: Guid(88f3ab9989e8291498e1966fae9ea909) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Grruzam Powerful Sword Animation(Great Sword, Katana)/Animation/M_Big_Sword/1_Movements/17__Idle_To_8Way_Walk_ALL/M_Big_Sword@Idle_A_To_Walk_FL90_Root.FBX using Guid(88f3ab9989e8291498e1966fae9ea909) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b86ca3ce7335ef243dea14a0ff3258e') in 0.1018774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 328

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_16.prefab
  artifactKey: Guid(2ff457f88b6db29459a7858ce81d7e8a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Hivemind/MedievalKingdom/HDRP(Default)/Art/MergedMeshesAndPrefabs/SM_MERGED_BP_InterchangableSpline_C_16.prefab using Guid(2ff457f88b6db29459a7858ce81d7e8a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12dc3e437bda9b7890f0987f8cd35145') in 0.0874191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0